const BaseController = require('controllers/base');
const PcSelfFetchService = require('services/api/ebiz/PcSelfFetchService');

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    ctx.setJsVar('allowAccessOrderShipment', await this.checkIsInOrderShipmentAllowList(ctx))
    await this.setIsWestcake(ctx);
    await ctx.render('fetch/index.html');
  }

  async getSelfFetch(ctx) {
    const { kdtId, query, rootKdtId: headKdtId, adminId } = ctx;
    const { selfFetchNo, shopId } = query;
    const params = {
      selfFetchNo,
      kdtId,
      headKdtId,
      adminId,
      shopId: shopId || 0
    };
    const data = await new PcSelfFetchService(ctx).queryVerifySelfFetch(params);
    ctx.success(data);
  }
}

module.exports = IndexController;
