const { format } = require('date-fns');
const OrderBaseController = require('controllers/base/order');
const QueryService = require('services/query');
const { get } = require('lodash');
const {
  checkUnifiedHqStore,
  checkUnifiedPartnerStore,
  checkUnifiedBranchStore
} = require('@youzan/utils-shop');
const SalesOrganizationService = require('services/sales-organization');
const SalesmanService = require('services/salesman-service');
const DeliveryWayService = require('services/delivery-way');
const PayWayService = require('services/pay-way');
const StockConfigService = require('services/stock-config');
const MarkPayService = require('services/mark-pay');
const MemberStateService = require('services/member-state');
const SupplyWarehouseService = require('services/supply-warehouse');
const CreateMemberService = require('services/create-member');
const PromoterOrderStatisticApi = require('services/ebiz/video/PromoterOrderStatisticApi');
const SalesChannelMgrService = require('services/shopcenter/SalesChannelMgrService');
const QttPartyService = require('services/qtt/PartyProvider');

class IndexController extends OrderBaseController {
  async getIndexHtml(ctx) {
    const service = new QueryService(ctx);
    const { kdtId } = ctx;
    const salesId = 44537; // 44537 销售员旗舰版插件 ID
    let queryKdtId;
    const shopInfo = ctx.getState('shopInfo');
    const { shopRole } = shopInfo;
    let isShowMultiStore;
    let upgradeType = null;
    let upgradeLogs;
    let supportBrandClassify = false;
    queryKdtId = kdtId;

    if (shopRole === 2) {
      queryKdtId = shopInfo.rootKdtId;
    }

    try {
      upgradeLogs = await service.getShopUpgradeType(kdtId);
    } catch (err) {
      ctx.log.warn({
        msg: '获取店铺升级记录信息失败',
        noThrow: true
      });
    }
    if (upgradeLogs && upgradeLogs.length > 0) {
      upgradeType = upgradeLogs[0].upgradeType;
    }
    ctx.setJsVar('upgradeType', upgradeType);

    try {
      isShowMultiStore = await service.getIsShowMultiStore(ctx.kdtId);
      const salesclerkAbility = await this.checkSalesclerkAbility();
      const state = await service.checkApp(queryKdtId, salesId);
      if (state || salesclerkAbility) {
        ctx.setJsVar('sales_status', state || salesclerkAbility);
      }
    } catch (e) {
      isShowMultiStore = 0;
    }

    ctx.setJsVar('isShowMultiStore', +isShowMultiStore === 1);

    await Promise.all([
      this.setCommonVariables(ctx), // 销售员融合升级白名单
      this.setShopSupplyMode(ctx),
      this.setIsWestcake(ctx),
      this.setIsQuyeShop(ctx),
      this.queryExpressDelivery(ctx),
      this.setShopProdVersion(ctx),
      this.initTradeUserPrivacyDataConfig(ctx),
      this.checkHasQttParty(ctx)
    ]);

    supportBrandClassify = await this.grayRelease('ChainDBrandClassify', this.getRootKdtId());
    ctx.setGlobal('supportBrandClassify', supportBrandClassify);

    // 订单发货白名单
    ctx.setJsVar('allowAccessOrderShipment', await this.checkIsInOrderShipmentAllowList(ctx));

    await ctx.render('query/index.html');
  }

  async checkShopElectronWayBill(ctx) {
    const ELECTRON_WAY_BILL = 'electron_way_bill';
    const { value: enableStatus } = await this.queryShopConfig(ctx, ELECTRON_WAY_BILL);
    // 电子面单从来没开启过，自动开启
    if (!enableStatus) {
      const { kdtId } = ctx;
      const { userInfo } = ctx.getLocalSession();
      await this.setShopConfig(ctx, {
        value: '1',
        kdtId,
        key: ELECTRON_WAY_BILL,
        operator: {
          id: userInfo.userId,
          name: userInfo.nickName,
          fromApp: 'retail-node-order',
          type: 1
        }
      });
    }
    ctx.json(0, 'ok', enableStatus || '1');
  }

  // 检查销售员插件能力
  async checkSalesclerkAbility() {
    const { kdtId, adminId } = this.ctx;
    const salesService = new QueryService(this.ctx);

    const salesmanAbility = await salesService.getSalesclerkAbility({
      kdtId,
      adminId,
      appName: 'retail-node-order'
    });
    return (
      salesmanAbility && (salesmanAbility.ability === 'ultimate' || salesmanAbility.boughtUltimate)
    );
  }

  async getTxtFile(ctx) {
    const { startTime } = ctx.getQueryData();
    const queryService = new QueryService(ctx);

    const time = +startTime || Date.now();
    // 只有一家店铺需要这个，硬编码了 302701 storeCode
    const fileName = `302701${format(new Date(time), 'YYYYMMDD')}.txt`;
    ctx.headers['Content-Type'] = 'text/plain; charset=utf-8';

    try {
      const infos = await queryService.getErpInfos(time, ctx.kdtId);
      const result = Buffer.from(infos.result);

      ctx.attachment(fileName);
      ctx.body = result;
    } catch (err) {
      ctx.body = '导出失败';
    }
  }

  async getLiteStore(ctx) {
    const { kdtId, adminId } = ctx;
    const liteStore = await ctx.getLiteStore(kdtId, adminId);

    ctx.success({ liteStore: get(liteStore, 'items', []) });
  }

  async getSalesOrganization(ctx) {
    const { kdtId, userId } = ctx;
    const retailSource = this.appName;
    const { query = {} } = ctx.getQueryData();
    const salesOrganizationService = new SalesOrganizationService(ctx);
    const salesOrganizationList = await salesOrganizationService.getSalesOrganizationList({
      // extends 中解析JSON数据
      ...JSON.parse(query),
      kdtId,
      hqKdtId: kdtId,
      adminId: userId,
      retailSource
    });
    ctx.success(salesOrganizationList);
  }

  async getSalesmanList(ctx) {
    const { adminId } = ctx;
    const salesmanService = new SalesmanService(ctx);
    const { salesKdtId, pageNo: page = 1, pageSize = 20 } = ctx.getQueryData();
    const salesmanList = await salesmanService.getSalesmanList({
      kdtId: salesKdtId,
      adminId,
      page: +page,
      pageSize: +pageSize,
      roleIds: [18, 49], // 获取普通导购和POS导购
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success(salesmanList);
  }

  async getDeliveryWayList(ctx) {
    const { adminId } = ctx;
    const deliveryWayService = new DeliveryWayService(ctx);
    const { salesKdtId } = ctx.getQueryData();
    const deliveryWayList = await deliveryWayService.getDeliveryWayList({
      kdtId: salesKdtId,
      adminId,
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success({ list: deliveryWayList });
  }

  async getSelfFetchPoint(ctx) {
    const {
      adminId,
      request: { body }
    } = ctx;
    const deliveryWayService = new DeliveryWayService(ctx);
    const { salesKdtId, itemInfoParamListString = [] } = body;
    const SelfFetchPointList = await deliveryWayService.getSelfFetchPointList({
      hasAddress: false,
      kdtId: salesKdtId,
      checkDeliveryType: 3, // 配送方式为自提
      itemInfoParamListString,
      storeReserveTag: true,
      adminId,
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success({ list: SelfFetchPointList });
  }

  async queryStockConfig(ctx) {
    const { adminId } = ctx;
    const { salesKdtId } = ctx.getQueryData();
    const res = await new StockConfigService(ctx).queryStockConfig({
      adminId,
      kdtId: salesKdtId,
      operatorId: adminId,
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success(res);
  }

  async getPayWayList(ctx) {
    const { adminId } = ctx;
    const payWayService = new PayWayService(ctx);
    const { salesKdtId } = ctx.getQueryData();
    const PayWayList = await payWayService.getPayWayList({
      kdtId: salesKdtId,
      adminId,
      page: 1,
      pageSize: 50,
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success({ list: PayWayList });
  }

  async getMarkPayList(ctx) {
    const { adminId } = ctx;
    const markPayService = new MarkPayService(ctx);
    const { salesKdtId } = ctx.getQueryData();
    const MarkPayList = await markPayService.getMarkPayList({
      kdtId: salesKdtId,
      adminId,
      page: 1,
      pageSize: 50,
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success({ list: MarkPayList });
  }

  async getMemberState(ctx) {
    const { adminId } = ctx;
    const memberStateService = new MemberStateService(ctx);
    const { salesKdtId, buyerMobile } = ctx.getQueryData();
    const memberState = await memberStateService.getMemberState({
      kdtId: salesKdtId,
      keyword: buyerMobile,
      adminId,
      page: 1,
      pageSize: 50,
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success({ memberState });
  }

  async getSupplyWarehouseList(ctx) {
    const {
      adminId,
      request: { body }
    } = ctx;
    const { shopRole } = ctx.getLocalSession('shopInfo');
    const supplyWarehouseService = new SupplyWarehouseService(ctx);
    const supplyWarehouseList = await supplyWarehouseService.getSupplyWarehouseList({
      ...body,
      shopRole,
      adminId,
      retailSource: 'WEB-RETAIL-AJAX'
    });
    ctx.success({ supplyWarehouseList });
  }

  async createMember(ctx) {
    const { adminId } = ctx;
    const createMemberService = new CreateMemberService(ctx);
    const { salesKdtId, buyerMobile, salesmanId } = ctx.getQueryData();
    const params = {
      kdtId: salesKdtId,
      mobile: buyerMobile,
      adminId,
      retailSource: 'WEB-RETAIL-AJAX'
    };
    if (salesmanId) {
      params.salesId = salesmanId;
    }
    const ret = await createMemberService.createMember(params);
    const { mobilePhone: mobile, yzUid: buyerId } = ret;
    ctx.success({ mobile, buyerId, name: mobile });
  }

  /**
   * 获取催付渠道
   * @param ctx
   * @returns {Promise<void>}
   */
  async getOrderPayPromptChannelList(ctx) {
    const { orderNo } = ctx.getQueryData();
    const { kdtId } = ctx;
    const params = {
      orderNo,
      kdtId
    };
    const data = await new QueryService(ctx).getConversationChannels(params);
    ctx.json(0, 'ok', data);
  }

  /**
   * 检查是否是二次催单提醒
   * @param ctx
   * @returns {Promise<void>}
   */
  async checkRepeatSend(ctx) {
    const { kdtId } = ctx;
    const { channel, orderNo } = ctx.getPostData();
    const params = {
      channel,
      kdtId,
      orderNo
    };
    const data = await new QueryService(ctx).isShowRemindOrderPopups(params);
    ctx.json(0, 'ok', data);
  }

  /**
   * 发送催付通知
   * @param ctx
   * @returns {Promise<void>}
   */
  async postPromptOrder(ctx) {
    const { kdtId, userId: adminId } = ctx;
    const { conversationId, channel, mobile, orderNo } = ctx.getPostData();
    const params = {
      adminId,
      conversationId,
      kdtId,
      channel,
      mobile,
      orderNo
    };
    await new QueryService(ctx).promptOrderToPay(params);
    ctx.json(0, 'ok');
  }

  async queryShopPromoterList(ctx) {
    const { kdtId } = ctx;
    const shopInfo = ctx.getLocalSession('shopInfo');
    const rootKdtId = get(shopInfo, 'rootKdtId');
    const { nickName } = ctx.getQueryData();
    const res = await new PromoterOrderStatisticApi(ctx).queryShopPromoterList({
      selectKdtId: kdtId,
      rootKdtId,
      nickName: nickName === '' ? null : nickName
    });
    ctx.success(res);
  }

  async queryOpenedSalesChannel(ctx) {
    const { kdtId, adminId } = ctx;
    const { isLiteOnlineStoreManager = false } = ctx.getQueryData();
    const shopInfo = ctx.getLocalSession('shopInfo');
    const { rootKdtId } = shopInfo;
    const salesChannelMgrService = new SalesChannelMgrService(ctx);
    const liteStore = await ctx.getLiteStore(kdtId, adminId);
    /**
     * 高级版网店
     */
    if (isLiteOnlineStoreManager) {
      const data = await salesChannelMgrService.queryOpenedSalesChannelByBu({
        kdtId,
        adminId,
        buKdtId: get(liteStore, 'items[0]', {}).storeKdtId,
        isIncludeThirdParty: true
      });
      return ctx.success(data);
    }
    /**
     * 总部、合伙人
     */
    if (checkUnifiedHqStore(shopInfo) || checkUnifiedPartnerStore(shopInfo)) {
      const data = await salesChannelMgrService.queryOpenedSalesChannelByMu({
        kdtId,
        adminId,
        muKdtId: rootKdtId,
        isIncludeThirdParty: true
      });
      return ctx.success(data);
    }
    /**
     * 门店、网店
     */
    if (checkUnifiedBranchStore(shopInfo)) {
      const data = await salesChannelMgrService.queryOpenedSalesChannelByBu({
        kdtId,
        adminId,
        buKdtId: kdtId,
        isIncludeThirdParty: true
      });
      return ctx.success(data);
    }

    return ctx.success([]);
  }

  /**
   * 检查店铺是否是否开通过群团团插件
   * party存在即已开通
   */
  async checkHasQttParty(ctx) {
    const { kdtId } = ctx;

    let hasQttParty = false;
    try {
      const res = await new QttPartyService(ctx).queryPartyByShopId(kdtId);
      hasQttParty = Boolean(res.partyId);
    } catch (e) {
      ctx.log.warn({
        msg: '检查店铺是否是否开通过群团团插件错误',
        noThrow: true
      });
    }

    ctx.setGlobal('hasQttParty', hasQttParty);
  }

  /**
   * 获取新电子面单新老共存期间的配置
   */
  async getNewElectronWayBillTipConfig(ctx) {
    const newElectronWayBillTipConfig = await ctx.apolloClient.getConfig({
      appId: 'retail-node-base',
      namespace: 'retail-node-base.whitelist',
      key: 'new-electron-way-bill-tip-config'
    });

    return ctx.success(newElectronWayBillTipConfig);
  }
}

module.exports = IndexController;
