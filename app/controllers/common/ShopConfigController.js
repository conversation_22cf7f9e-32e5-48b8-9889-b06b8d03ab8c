const BaseController = require('controllers/base');
const ShopConfigReadService = require('../../services/api/shopcenter/ShopConfigReadService');
const ShopConfigWriteService = require('../../services/api/shopcenter/ShopConfigWriteService');

class ShopConfigController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '店铺配置';
  }

  async getShopConfigs(ctx) {
    const { kdtId } = ctx;
    const { configs } = ctx.query;
    this.validator.required(configs, '配置项不能为空');

    const params = JSON.parse(configs);
    const result = await new ShopConfigReadService(ctx).queryShopConfigs(kdtId, params);
    return ctx.json(0, 'OK', result);
  }

  async getShopConfigsByKdtId(ctx) {
    const { configs, kdtId = ctx.kdtId } = ctx.query;
    this.validator.required(configs, '配置项不能为空');

    const params = JSON.parse(configs);
    const result = await new ShopConfigReadService(ctx).queryShopConfigs(kdtId, params);
    return ctx.json(0, 'OK', result);
  }

  async updateShopConfigs(ctx) {
    const { kdtId } = ctx;
    const userInfo = ctx.getLocalSession('userInfo');
    const { configs } = ctx.request.body;
    this.validator.required(configs, '配置项不能为空');

    const params = {
      kdtId,
      operator: {
        id: userInfo.userId,
        name: userInfo.nickName,
        type: 1,
        fromApp: 'wsc-pc-v4',
      },
      configs: JSON.parse(configs),
    };
    const result = await new ShopConfigWriteService(ctx).setShopConfigs(params);
    return ctx.json(0, 'OK', result);
  }
}

module.exports = ShopConfigController;
