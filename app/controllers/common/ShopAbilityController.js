const BaseController = require('controllers/base');
const AbilityReadService = require('../../services/shop/AbilityReadService');

class ShopAbilityController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '店铺配置';
  }

  async getShopAbilityByKdtId(ctx) {
    const { abilityCode, kdtId = ctx.kdtId } = ctx.query;
    this.validator.required(abilityCode, '能力key不能为空');

    const result = await new AbilityReadService(ctx).queryShopAbilityInfo(kdtId, abilityCode);
    return ctx.json(0, 'OK', result);
  }

}

module.exports = ShopAbilityController;
