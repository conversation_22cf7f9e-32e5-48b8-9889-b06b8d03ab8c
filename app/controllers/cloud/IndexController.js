const BaseController = require('controllers/base');
const AppstoreAuthService = require('services/cloud/AppstoreAuthService');

class IndexController extends BaseController {
  async useApp(ctx) {
    const service = new AppstoreAuthService(ctx);
    const { kdtId, adminId, query } = ctx;

    const result = await service.useApp({
      kdtId,
      userId: adminId,
      appId: query.appId
    });

    ctx.success(result);
  }
}

module.exports = IndexController;
