const BaseController = require('controllers/base');

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    const { path: requestPath = '', headers: requestHeaders = {} } = ctx || {};
    const requestFromFulfillmentApp =
      requestPath.includes('/v2/order/fulfilldetail') &&
      (requestHeaders.referer || '').includes('/fulfillment/order-shipment');

    if (requestFromFulfillmentApp) {
      return ctx.redirect('/v2/order/order-shipment-detail');
    }

    await this.initTradeUserPrivacyDataConfig(ctx);

    await ctx.render('fulfilldetail/index.html');
  }
}

module.exports = IndexController;
