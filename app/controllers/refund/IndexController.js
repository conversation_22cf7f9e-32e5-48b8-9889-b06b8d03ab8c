const BaseController = require('controllers/base');
const StockService = require('services/stock');
const { format } = require('date-fns');
const BatchRefundQueryService = require('../../services/rpbatch/BatchRefundQueryService');

class IndexController extends BaseController {
  async getRefundWhereabortsHtml(ctx) {
    await ctx.render('refund-whereabouts/index.html');
  }

  // 一键入库
  async passToStock(ctx) {
    const {
      kdtId,
      userId: operatorId,
      request: {
        body: { refundId },
      },
    } = ctx;
    const data = {
      idempotentNo: '' + Date.now(),
      kdtId,
      operatorId,
      businessTime: format(new Date(), 'YYYY-MM-DD HH:mm:ss'),
      biz: 'retail-node-order',
      refundOrderNo: refundId,
    };
    const result = await new StockService(ctx).onceReturnStock(data);
    ctx.json(0, 'ok', result);
  }

  async queryHasWeChatComplaint(ctx) {
    const { refundIdList } = ctx.request.body;
    const result = await new BatchRefundQueryService(ctx).queryHasWeChatComplaint({
      refundIdList
    });
    ctx.json(0, 'ok', result);
  }
}

module.exports = IndexController;
