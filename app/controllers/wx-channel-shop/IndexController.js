const BaseController = require('controllers/base');

const MultiPlatDeliveryQueryFacade = require('services/wx-channel/MultiPlatDeliveryQueryFacade.js');
const WeShopWaybillTemplateService = require('services/wx-channel/WeShopWaybillTemplateService.js');
const MultiPlatWaybillOrderQueryFacade = require('services/wx-channel/MultiPlatWaybillOrderQueryFacade.js');
const MultiPlatWaybillOrderOperateFacade = require('services/wx-channel/MultiPlatWaybillOrderOperateFacade.js');
const ShopAddressOuterService = require('services/shop/ShopAddressOuterService.js');

class WxChannelShopIndexController extends BaseController {
  get sourceInfo() {
    return {
      bizSource: 'retail-node-order'
    };
  }

  async getMultiPlatExpressInfos(ctx) {
    const { salesKdtId, channelType, mpId } = ctx.validator({
      salesKdtId: ctx.joi.number(),
      channelType: ctx.joi.number(),
      mpId: ctx.joi.number()
    });

    const result = await new MultiPlatDeliveryQueryFacade(ctx).queryExpressInfos({
      kdtId: salesKdtId,
      channelType,
      mpId,
      sourceInfo: this.sourceInfo
    });
    return ctx.success(result);
  }

  async getMultiPlatPageSites(ctx) {
    const { salesKdtId, channelType, mpId, yzExpressId, options, page, pageSize } = ctx.validator({
      salesKdtId: ctx.joi.number(),
      channelType: ctx.joi.number(),
      mpId: ctx.joi.number(),
      yzExpressId: ctx.joi.number(),
      options: ctx.joi.object().default({}),
      page: ctx.joi.number(),
      pageSize: ctx.joi.number()
    });

    const result = await new MultiPlatDeliveryQueryFacade(ctx).queryPageSites(
      {
        kdtId: salesKdtId,
        channelType,
        mpId,
        options,
        yzExpressId,
        sourceInfo: this.sourceInfo
      },
      {
        page,
        pageSize
      }
    );
    return ctx.success(result);
  }

  async queryExpressProductTypes(ctx) {
    const { salesKdtId, channelType, mpId, yzExpressId } = ctx.validator({
      salesKdtId: ctx.joi.number(),
      channelType: ctx.joi.number(),
      mpId: ctx.joi.number(),
      yzExpressId: ctx.joi.number(),
      page: ctx.joi.number(),
      pageSize: ctx.joi.number()
    });

    const result = await new MultiPlatDeliveryQueryFacade(ctx).queryExpressProductTypes(
      {
        kdtId: salesKdtId,
        channelType,
        mpId,
        expressId: yzExpressId,
        sourceInfo: this.sourceInfo
      }
    );
    return ctx.success(result);
  }

  async queryPageMonthlyCards(ctx) {
    const { salesKdtId, channelType, mpId, yzExpressId, page, pageSize } = ctx.validator({
      salesKdtId: ctx.joi.number(),
      channelType: ctx.joi.number(),
      mpId: ctx.joi.number(),
      yzExpressId: ctx.joi.number(),
      page: ctx.joi.number(),
      pageSize: ctx.joi.number()
    });

    const result = await new MultiPlatDeliveryQueryFacade(ctx).queryPageMonthlyCards(
      {
        kdtId: salesKdtId,
        channelType,
        mpId,
        expressId: yzExpressId,
        sourceInfo: this.sourceInfo
      },
      {
        page,
        pageSize
      }
    );
    return ctx.success(result);
  }

  async getWaybillDeliveryTemplate(ctx) {
    const { salesKdtId, expressId, mpId } = ctx.validator({
      salesKdtId: ctx.joi.number(),
      expressId: ctx.joi.number(),
      mpId: ctx.joi.number()
    });
    const result = await new WeShopWaybillTemplateService(ctx).getDeliveryTemplate({
      kdtId: salesKdtId,
      expressId,
      mpId,
      sourceInfo: this.sourceInfo
    });
    return ctx.success(result);
  }

  async createMultiPlayWaybillOrder(ctx) {
    const {
      salesKdtId,
      expressId,
      order,
      mpId,
      channelType,
      sender,
      thirdShopId,
      thirdAcctId
    } = ctx.validator({
      salesKdtId: ctx.joi.number(),
      expressId: ctx.joi.number(),
      order: ctx.joi.object(),
      mpId: ctx.joi.number(),
      channelType: ctx.joi.number(),
      sender: ctx.joi.object(),
      thirdShopId: ctx.joi.string(),
      thirdAcctId: ctx.joi.string()
    });
    // 非必填字段
    const {
      thirdSiteCode,
      monthlyCardId,
      thirdProductTypeId,
    } = ctx.request.body;

    const result = await new MultiPlatWaybillOrderOperateFacade(ctx).createOrder({
      thirdSiteCode,
      expressId,
      order,
      mpId,
      channelType,
      kdtId: salesKdtId,
      sender,
      thirdShopId,
      thirdAcctId,
      monthlyCardId,
      thirdProductTypeId,
      sourceInfo: this.sourceInfo
    });
    return ctx.success(result);
  }

  async getMultiPlayWaybillOrderPrintMessage(ctx) {
    const { salesKdtId, printNum, channelType, thirdTemplateId, thirdWaybillId, mpId } =
      ctx.validator({
        salesKdtId: ctx.joi.number(),
        printNum: ctx.joi.object(),
        channelType: ctx.joi.number(),
        thirdTemplateId: ctx.joi.string(),
        thirdWaybillId: ctx.joi.string(),
        mpId: ctx.joi.number()
      });

    const result = await new MultiPlatWaybillOrderQueryFacade(ctx).getOrderPrint({
      printNum,
      channelType,
      thirdTemplateId,
      kdtId: salesKdtId,
      thirdWaybillId,
      mpId,
      sourceInfo: this.sourceInfo
    });
    return ctx.success(result);
  }

  async updateMultiPlayWaybillOrderPrintStatus(ctx) {
    const { salesKdtId, channelType, expressNo, expressId, mpId, printType, thirdWaybillId } =
      ctx.validator({
        salesKdtId: ctx.joi.number(),
        channelType: ctx.joi.number(),
        expressNo: ctx.joi.string(),
        expressId: ctx.joi.number(),
        printType: ctx.joi.number(),
        mpId: ctx.joi.number(),
        thirdWaybillId: ctx.joi.string()
      });
    const result = await new MultiPlatWaybillOrderOperateFacade(ctx).orderPrintSuccessNotify({
      channelType,
      expressNo,
      expressId,
      kdtId: salesKdtId,
      mpId,
      printType,
      thirdWaybillId,
      sourceInfo: this.sourceInfo
    });
    return ctx.success(result);
  }

  async getDeliveryAddressList(ctx) {
    const { targetKdtId, pageNum, pageSize, keyword, addressTypeValues } = ctx.validator(
      ctx.joi.object({
        targetKdtId: ctx.joi.number(),
        pageNum: ctx.joi.number(),
        pageSize: ctx.joi.number(),
        keyword: ctx.joi.string(),
        addressTypeValues: ctx.joi.array()
      })
    );

    const params = {
      kdtId: targetKdtId,
      keyword,
      pageNum,
      pageSize,
      addressTypeValues
    };

    const result = await new ShopAddressOuterService(ctx).queryShopAddressList(params);
    return ctx.success(result);
  }
}

module.exports = WxChannelShopIndexController;
