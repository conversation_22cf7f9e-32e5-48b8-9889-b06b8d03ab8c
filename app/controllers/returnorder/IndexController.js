const { checkRetailMinimalistShop, checkUnifiedShop } = require('@youzan/utils-shop');
const BaseController = require('controllers/base');
const ReturnOrderService = require('services/returnorder');
class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    const shopInfo = ctx.getLocalSession('shopInfo');

    // 连锁L、连锁D, 跳转到 /v4/trade/refunds。https://jira.qima-inc.com/browse/ONLINE-634974
    if (checkUnifiedShop(shopInfo) || checkRetailMinimalistShop(shopInfo)) {
      return ctx.redirect('/v4/trade/refunds');
    }

    const service = new ReturnOrderService(ctx);

    const isBatch = await service.isInBatchWhiteList(ctx.kdtId);
    ctx.setJsVar('batchRefund', isBatch);
    await ctx.render('returnorder/index.html');
  }
}

module.exports = IndexController;
