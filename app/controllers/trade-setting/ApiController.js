const BaseController = require('controllers/base');
const ShopAddressServiceV2 = require('services/api/shopcenter/ShopAddressServiceV2');
const MallTradeSettingService = require('services/trade-manager/TradeSettingService');
const ShopConfigWriteService = require('services/api/shopcenter/ShopConfigWriteService');
const ShopConfigReadService = require('services/api/shopcenter/ShopConfigReadService');


const FAST_ORDER_ENUM = {
  FAST: 1,
  STEP: 2,
  SKU: 3,
  RETAIL_SHELF: 4
}

const ShopConfig = {
  'order_ump': 'payment_setting_order_display_activity'
}

class ApiController extends BaseController {
  async queryDefaultShopAddress(ctx) {
    const { kdtId } = ctx;
    // [kdtId, 1] 中的 1 代表 addressType = 1 => 即退货地址
    const result = await new ShopAddressServiceV2(ctx).queryDefaultShopAddress([kdtId, 1]);
    ctx.success(result);
  }

  async editSettingByKdtId(ctx) {
    const { kdtId } = ctx;
    const {
      useInvoice,
      invoiceProviderCode,
      invoiceTimeLimit,
      invoiceType,
      invoiceContent
    } = ctx.request.body;
    const result = await new MallTradeSettingService(ctx).editSettingByKdtId({
      useInvoice,
      invoiceProviderCode,
      invoiceTimeLimit,
      invoiceType,
      invoiceContent,
      source: 'merchant-pc',
      kdtId,
    });
    ctx.success(result);
  }

  /**
  * @description 获取极速下单设置
  *  该接口主要是包装店铺配置的逻辑（提供给B端使用，C端如需使用请单独查询店铺配置接口）
  */
  async getGetFastTradeSettingJson() {
    const { ctx } = this;

    const kdtId = ctx.query.targetKdtId || ctx.query.kdtId || ctx.kdtId
    const getAll = [
      new MallTradeSettingService(ctx).getFastTradeSetting({ kdtId }),
      new ShopConfigReadService(ctx).queryShopConfigs(kdtId, [ShopConfig.order_ump]),
    ]

    const result = await Promise.all(getAll).then(([fastTradeSetting, shopConfig]) => {
      const config = { ...(fastTradeSetting || {}) };
      const result = { type: [] }
      const orderUmpConfig = shopConfig.configs[ShopConfig.order_ump] || '0'
      result.settingShow = orderUmpConfig
      result.settingStatus = +config.settingStatus

      if (+config.type) {
        result.type.push(+config.type)
      }

      // 做了层转化 1=>4
      if (+config.retailShelfDirectOrder) {
        result.type.push(FAST_ORDER_ENUM.RETAIL_SHELF)
      }
      return result;
    });

    ctx.success(result);
  }

  /** @description 极速下单设置编辑接口 */
  async postEditFastTradeSettingJson() {
    const { ctx } = this;
    const userInfo = ctx.getLocalSession('userInfo');
    const { settingStatus = 0, type = [], settingShow = '0', targetKdtId } = ctx.getPostData();
    const kdtId = targetKdtId || ctx.kdtId

    const dto = { settingStatus, kdtId, type: 0, retailShelfDirectOrder: 0 };
    if (type.includes(FAST_ORDER_ENUM.SKU)) {
      dto.type = FAST_ORDER_ENUM.SKU
    }

    // 兼容商详场景,做了层转化 4=>1
    if (type.includes(FAST_ORDER_ENUM.RETAIL_SHELF)) {
      dto.retailShelfDirectOrder = 1
    }

    const setAll = [new MallTradeSettingService(ctx).editFastTradeSetting(dto)];

    // 营销活动展示
    const params = {
      kdtId,
      key: ShopConfig.order_ump,
      value: settingShow,
      operator: {
        id: userInfo.userId,
        name: userInfo.nickName,
        type: 1,
        fromApp: 'wsc-pc-v4',
      },
    };
    setAll.push(new ShopConfigWriteService(ctx).setShopConfig(params));
    await Promise.all(setAll);
    ctx.success('设置成功');
  }
}

module.exports = ApiController;
