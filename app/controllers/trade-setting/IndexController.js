const BaseController = require('controllers/base');
const InvoiceProviderService = require('services/invoice');
const ShopShelfPageGrayReleaseService = require('services/shelf/ShopShelfPageGrayReleaseService');

class IndexController extends BaseController {
  async getIndexHtmlAndLog(ctx) {
    // 下面是用来收集老链接的 流量信息的, 勿删 2019.08.05 @zhengzixiao
    // eslint-disable-next-line no-console
    console.log('LOG: old trade-setting URL');

    await this.getIndexHtml(ctx);
  }

  async getIndexHtml(ctx) {
    const { kdtId } = ctx;

    await this.queryExpressDelivery(ctx);
    const invoiceProviderList = await new InvoiceProviderService(ctx)
      .queryInvoiceProviderList({ kdtId: ctx.kdtId })
      .catch(() => {
        ctx.log.warn({
          msg: '电子发票服务商查询失败',
          noThrow: true
        });
      });
    const invoiceTypeConfig = await ctx.apolloClient.getConfig({
      appId: 'wsc-pc-trade',
      namespace: 'wsc-pc-trade.application',
      key: 'invoice_config'
    });
    const { hasOldPage } = await new ShopShelfPageGrayReleaseService(ctx).isHit(kdtId);
    ctx.setGlobal('isUsedShelfOrderPage', hasOldPage);
    ctx.setJsVar('invoiceProviderList', invoiceProviderList || []);
    ctx.setJsVar('invoiceTypeConfig', invoiceTypeConfig);
    await ctx.render('trade-setting/index.html');
  }
}

module.exports = IndexController;
