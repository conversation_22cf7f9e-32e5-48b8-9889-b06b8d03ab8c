const BaseController = require('controllers/base');
const WaybillService = require('../../services/waybill/WaybillService');

class IndexController extends BaseController {
  get traceId() {
    return this.ctx.traceCtx.rootId;
  }

  // 新电子面单取消呼叫
  async cancelCall(ctx) {
    const { kdtId } = ctx;
    const { orderNo, packId } = ctx.request.body;
    const params = {
      kdtId: +kdtId,
      orderNo,
      packId,
      cancelDoorPickUp: true,
      sourceId: 1002 // 标记来源 - wsc
    };

    const result = await new WaybillService(ctx)
      .waybillOrderConfirm(params)
      .catch(error => ({ error, params, traceId: this.traceId }));

    ctx.json(0, 'ok', result);
  }

  // 新电子面单重新呼叫
  async retryCall(ctx) {
    const { kdtId } = ctx;
    const { orderNo, packId, preStartTime, preEndTime, productCode } = ctx.request.body;
    const params = {
      orderNo,
      packId,
      preStartTime,
      preEndTime,
      productCode,
      kdtId: +kdtId,
      cancelDoorPickUp: false,
      sourceId: 1002 // 标记来源 - wsc
    };
    const result = await new WaybillService(ctx)
      .waybillOrderConfirm(params)
      .catch(error => ({ error, params, traceId: this.traceId }));
    ctx.json(0, 'ok', result);
  }

  // 重新打印-查询打印报文接口
  async getPrintData(ctx) {
    const { kdtId } = ctx;
    const params = {
      ...ctx.request.body,
      kdtId: +kdtId,
      sourceId: 1002 // 标记来源 - wsc
    };
    const result = await new WaybillService(ctx).queryPrintData(params);
    ctx.json(0, 'ok', result);
  }

  // 打印模板获取
  async getWayBillPrintTemplate(ctx) {
    const { kdtId } = ctx;
    const { paymentType, expressId, brandCode = null } = ctx.getQueryData();
    const params = {
      kdtId,
      paymentType: +paymentType,
      expressId: +expressId,
      brandCode
    };
    const result = await new WaybillService(ctx).listPrintTemplate(params);
    ctx.json(0, 'ok', result);
  }

  // 取消寄件
  async cancelDelivery(ctx) {
    const { kdtId, userId } = ctx;
    const params = {
      kdtId,
      adminId: userId,
      ...ctx.request.body
    };
    const result = await new WaybillService(ctx).cancelDelivery(params).catch(error => {
      return {
        error,
        params,
        traceId: this.traceId
      };
    });

    ctx.json(0, 'ok', result);
  }
}

module.exports = IndexController;
