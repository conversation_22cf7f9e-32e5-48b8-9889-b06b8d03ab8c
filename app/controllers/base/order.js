const BaseController = require('controllers/base');

const WhitelistService = require('services/ebiz/salesman/SalesmanWhiteOrGrayApiService');

class OrderBaseController extends BaseController {
  /**
   * 检查销售员账号融合白名单
   */
  async checkSalesmanMergeWhiteList() {
    const { kdtId } = this.ctx;
    return await new WhitelistService(this.ctx).inWhiteList(['chain_salesman_merge', kdtId]);
  }

  async setCommonVariables(ctx) {
    let isInWhiteList = false;

    try {
      // 销售员融合升级白名单
      isInWhiteList = await this.checkSalesmanMergeWhiteList();
    } catch (err) {
      ctx.log.warn({
        msg: '销售员融合白名单请求失败',
        noThrow: true
      });
    }

    ctx.setJsVar('isSalesmanUpgrade', isInWhiteList);
  }
}

module.exports = OrderBaseController;
