const { RetailBaseController, service: BaseService } = require('@youzan/retail-node-base');
const {
  checkUnifiedOnlineBranchStore,
  checkRetailHqStore,
  checkRetailSingleStore,
  checkChainStore
} = require('@youzan/utils-shop');
const uuid = require('uuid');
const ShopService = require('services/shop');
const ShopConfigReadService = require('services/api/shopcenter/ShopConfigReadService');
const ShopConfigWriteService = require('services/api/shopcenter/ShopConfigWriteService');
const ProdReadService = require('services/shopcenter/shopprod/ProdReadService.js');
const SalesChannelMgrService = require('services/shopcenter/SalesChannelMgrService');
const _ = require('lodash');

const ShopConfigKeys = require('../../../shared/keys/shop-config');

const supplyModeConfigKey = 'online_subshop_supply_mode';

const expressDeliveryKey = 'express_delivery';

const STANDALONE_SALE_STOCK_KEY = 'sub_online_stock_model';
const PLANNED_STOCK_KEY = 'retail_subshop_plan_stock_config';

const REQUEST_ID = Symbol.for('retail-node-order#requestId');

class BaseController extends RetailBaseController {
  /**
   * 当前请求唯一的 requestId，一般作为调用后端接口时的参数使用
   * @returns {string}
   */
  get requestId() {
    if (!this[REQUEST_ID]) {
      this[REQUEST_ID] = uuid.v4();
    }
    return this[REQUEST_ID];
  }

  get appName() {
    return 'retail-node-order';
  }

  init() {
    this.initEnv(this.ctx);
  }

  /**
   * 初始化nodeEnv
   * @memberof PCBaseController
   */
  initEnv(ctx) {
    const nodeEnv = ctx.env;
    ctx.setGlobal('nodeEnv', nodeEnv);
  }

  async setIsQuyeShop(ctx) {
    const shopService = new ShopService(ctx);
    const isQuyeShop = (await shopService.getShopConfig(ctx.kdtId, 'STOCK', 'SERIAL_TYPE')) || {};

    ctx.setJsVar('isQuyeShop', isQuyeShop.value === 2);
  }

  async setIsWestcake(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    const isWestcake = await ctx.checkInWhiteList('retailOrderWestcake', shopInfo.rootKdtId);
    ctx.setJsVar('isWestcake', +isWestcake);
  }

  async setShopSupplyMode(ctx) {
    const { kdtId } = ctx;
    const shopInfo = ctx.getState('shopInfo');
    const isUnifiedOnlineBranchStore = checkUnifiedOnlineBranchStore(shopInfo);
    if (!isUnifiedOnlineBranchStore) {
      return;
    }
    const shopSupplyMode = await new ShopConfigReadService(ctx).queryShopConfig(
      kdtId,
      supplyModeConfigKey
    );
    ctx.setJsVar('shopSupplyMode', +shopSupplyMode.value);
  }

  queryShopConfig(ctx, key) {
    const { kdtId } = ctx;
    return new ShopConfigReadService(ctx).queryShopConfig(kdtId, key);
  }

  setShopConfig(ctx, params) {
    return new ShopConfigWriteService(ctx).setShopConfig(params);
  }

  async queryExpressDelivery(ctx) {
    const { kdtId } = ctx;
    const expressDelivery = await new ShopConfigReadService(ctx).queryShopConfig(
      kdtId,
      expressDeliveryKey
    );
    ctx.setJsVar('expressDelivery', +_.get(expressDelivery, 'value', 1));
  }

  async queryStandaloneAndPlannedStock(ctx) {
    const { kdtId } = ctx;
    // 独立销售库存模式，计划库存
    const [{ configs: stockConfig = {} } = {}, supportPlanStockWhitelist = ''] = await Promise.all([
      new ShopConfigReadService(ctx).queryShopConfigs(kdtId, [
        STANDALONE_SALE_STOCK_KEY,
        PLANNED_STOCK_KEY
      ]),
      // 是否开启支持计划库存设置白名单
      ctx.apolloClient.getConfig({
        appId: 'retail-node-supplychain',
        namespace: 'retail-node-supplychain.integrate',
        key: 'plan-stock-setting'
      })
    ]);
    ctx.setJsVar('stockConfig', {
      allowPlannedStock:
        supportPlanStockWhitelist.includes(kdtId) || supportPlanStockWhitelist.includes('100%'),
      // 独立销售库存模式
      standaloneSaleStock: +stockConfig?.sub_online_stock_model === 1,
      // 计划库存
      plannedStock: +JSON.parse(stockConfig?.retail_subshop_plan_stock_config ?? '{}').state === 1
    });
  }

  /**
   * 通用的自动拉分页数据的函数
   * @param {object} params
   * @param {Function} params.fetch 拉数据的函数
   * @param {Function} params.needPullAll 函数, 用于判断是否需要继续拉取剩下的数据, 因为每个接口的数据结构都不一样, 所以这个需要额外传递
   * @param {object} params.getTotal 用于获取数据总量
   * @param {object} params.pageSize
   * @param {object} params.reducer 对数据进行聚合的 reducer
   */
  async pullAll({ fetch, params, needPullAll, getParams, getTotal, pageSize, reducer, pullTimes }) {
    const defaultNeedPullAll = data => _.get(data, 'pagination.totalItems') > pageSize;

    const defaultGetParams = page => {
      return Object.assign({}, params, { page });
    };

    const defaultReducer = (acc, curr) => {
      return Object.assign(
        {},
        {
          items: curr.items.concat(acc.items || []),
          paginator: {
            page: 1,
            pageSize: 10,
            totalItems: _.get(acc, 'paginator.totalItems', 0) + curr.paginator.totalItems
          }
        }
      );
    };

    const defaultGetTotal = data => _.get(data, 'pagination.totalItems');

    const firstPage = await fetch(params);
    const isNeedPullAll = (needPullAll || defaultNeedPullAll)(firstPage);
    const total = (getTotal || defaultGetTotal)(firstPage);
    const promises = [];
    let data;
    if (isNeedPullAll) {
      const max = pullTimes || Math.trunc(total / pageSize) + 1;
      for (let i = 2; i <= max; i++) {
        const nextParam = (getParams || defaultGetParams)(i);
        promises.push(fetch(nextParam));
      }

      const dataSets = await Promise.all(promises);
      data = dataSets.reduce(reducer || defaultReducer, firstPage);
    }

    return data || firstPage;
  }

  /**
   * 获取总店kdtId
   * @returns rootKdtId
   */
  getRootKdtId() {
    const { rootKdtId } = this.ctx.getState('shopInfo');
    return rootKdtId || this.ctx.kdtId;
  }

  /**
   * 店铺kdtId是否在白名单中
   * @param {String} key cp key
   * @param {String} kdtId cp kdtId
   * @returns Promise<boolean>
   */
  grayRelease(key, kdtId) {
    return new BaseService.shop.ShopConfigService(this.ctx).checkInGrayReleaseByKdtId(key, kdtId);
  }

  /**
   * 设置店铺版本
   */
  async setShopProdVersion(ctx) {
    const { kdtId } = ctx;
    const shopProdVersions = await new ProdReadService(ctx).queryShopProdVersions(kdtId);
    // 若返回多个版本，判断当前店铺版本
    shopProdVersions.sort((pre, after) => after.beginTime - pre.beginTime);
    const shopProdVersion =
      shopProdVersions.length < 2
        ? shopProdVersions[0]
        : shopProdVersions.filter(version => version.lifecycleStatus !== 'prepare')[0];
    ctx.setJsVar('prodVersion', shopProdVersion);
  }

  async checkIsInOrderShipmentAllowList(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    // const kdtId =
    //   checkRetailSingleStore(shopInfo) || checkRetailHqStore(shopInfo)
    //     ? ctx.kdtId
    //     : shopInfo.rootKdtId; // BU 使用总部 ID 匹配
    const kdtId = ctx.kdtId; // 与 pc-shared 白名单同步，暂时使用精确匹配而不是总部匹配，pc-shared 暂未支持 BU 根据总部匹配

    const config =
      (await ctx.apolloClient.getConfig({
        appId: 'retail-node-fulfillment',
        namespace: 'retail-node-fulfillment.config',
        key: 'order_shipment_entry'
      })) || '';
    const stringifyConfig = JSON.stringify(config);
    if ([kdtId, '100%'].some(pattern => stringifyConfig.includes(pattern))) {
      return true;
    }
    return false;
  }

  async tryToRedirectToFulfillment(ctx, tabPath) {
    if (await this.checkIsInOrderShipmentAllowList(ctx)) {
      return ctx.redirect(`/fulfillment/order-shipment${tabPath ? `#/${tabPath}` : ''}`);
    }
  }

  /**
   * 解析apollo配置的切流决策
   * apollo的文本配置以半角逗号分隔，百分比值写在第一项，其次白名单，最后黑名单
   * 遇到0或者0%会判为不匹配
   * '%1,491391,603367,-160,-11998'
   * const useNew = isInGrayReleaseByKdtId(ctx, { namespace: '', key: '' }, kdtId);
   */
  matchGrayConfig(config, kdtId) {
    config = String(config);
    const kdtIdList = config.split(',');
    // 先判断0或者黑名单的情况
    if (kdtIdList.includes('0') || kdtIdList.includes('0%') || kdtIdList.includes('-' + kdtId)) {
      return false;
    } else if (kdtIdList.includes(String(kdtId))) {
      // kdtId全匹配
      return true;
    } else if (config.indexOf('%') > 0) {
      // 百分比判断
      const percentArr = kdtIdList
        .filter(singleConfig => {
          return singleConfig.endsWith('%');
        })
        .map(singleConfig => {
          return singleConfig.slice(0, singleConfig.length - 1);
        });
      if (percentArr && percentArr.length) {
        // 只取第一个百分比配置
        const onlyPercent = Number(percentArr[0]);
        return !!(onlyPercent >= 0 && onlyPercent <= 100 && Number(kdtId) % 100 <= onlyPercent);
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  getIsSupportMultiChannel() {
    try {
      return new SalesChannelMgrService(this.ctx).isHitWhiteList(this.ctx.kdtId);
    } catch (e) {
      return false;
    }
  }

  /**
   * 初始化交易的用户隐私信息展示配置
   */
  async initTradeUserPrivacyDataConfig(ctx) {
    let value = true;
    try {
      const shopInfo = ctx.getState('shopInfo');
      const kdtId = shopInfo.rootKdtId || shopInfo.kdtId;
      const shopConfigs = await new ShopConfigReadService(ctx).queryShopConfigs(kdtId, [
        ShopConfigKeys.tradeUserPrivacyInfoDisplay
      ]);
      value = shopConfigs.configs[ShopConfigKeys.tradeUserPrivacyInfoDisplay] === '1';
    } catch (err) {
      ctx.log.warn(err);
    }

    ctx.setJsVar('tradeUserPrivacyInfoDisplay', value);
  }
}

module.exports = BaseController;
