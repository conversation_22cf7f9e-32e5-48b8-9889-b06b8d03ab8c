const BaseController = require('controllers/base');
const ShopService = require('services/shop');

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    await this.setShopSupplyMode(ctx);

    const shopService = new ShopService(ctx);
    const res = await shopService.getShopConfig(
      ctx.kdtId,
      'PRODUCT',
      'manufacture_management'
    );
    ctx.setJsVar('isManufactureOn', (res && res.value) === 1);

    await ctx.render('process-setting/index.html');
  }
}

module.exports = IndexController;
