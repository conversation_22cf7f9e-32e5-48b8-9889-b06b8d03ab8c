const OrderBaseController = require('controllers/base/order');
const OrderDetail = require('services/order-detail');
const OrderSearchService = require('services/search/OrderSearchService');
const RefundQueryService = require('services/fx/RefundQueryService');
const QrcodeService = require('services/qrcode/QrcodeService');
const ShopConfigReadService = require('services/api/shopcenter/ShopConfigReadService');
const SellerRefundService = require('services/ebiz/seller/SellerRefundService');
const InvoiceProviderService = require('services/invoice');

class IndexController extends OrderBaseController {
  async getIndexHtml(ctx) {
    const service = new OrderDetail(ctx);
    const isShowMultiStore = await service.getIsShowMultiStore(ctx.kdtId);
    const invoiceProviderList = 
    await new InvoiceProviderService(ctx)
    .queryInvoiceProviderList({ kdtId: ctx.kdtId })
    .catch((err) => {
      console.error(err);
    });

    ctx.setJsVar('isShowMultiStore', +isShowMultiStore === 1);
    ctx.setJsVar('invoiceProviderList', invoiceProviderList || []);

    await this.setCommonVariables(ctx);
    await this.setIsWestcake(ctx);
    await this.setIsQuyeShop(ctx);
    await this.setShopProdVersion(ctx);
    await this.getRefundStockConfig(ctx);
    await Promise.all([this.initTradeUserPrivacyDataConfig(ctx)]);

    await ctx.render('order-detail/index.html');
  }

  async getRefundStockConfig(ctx) {
    const config = await new ShopConfigReadService(ctx).queryShopConfigs(ctx.kdtId, [
      'online_refund_stock_type',
      'offline_refund_stock_type'
    ]);

    ctx.setJsVar('refundStockConfig', config.configs);
  }

  async getQrCode(ctx) {
    const result = await new QrcodeService(ctx).qrCode(ctx.query.text);
    const base64 = global.Buffer.from(result).toString('base64');
    return ctx.success(`data:image/png;base64,${base64}`);
  }
  // 分销订单查询
  async getShareOfRefundFee(ctx) {
    const { orderNo, orderItemId, refundFee, kdtId } = ctx.query;
    this.validator.required(orderNo, '订单编号不能为空');
    this.validator.required(orderItemId, '采购单商品 orderItemId 不能为空');
    const data = await new RefundQueryService(ctx).getFxRefundableFeeByPurchaseOrder({
      kdtId,
      orderItemId,
      orderNo,
      refundFee: +refundFee
    });
    return ctx.json(0, 'OK', data);
  }
  /** 正向获取用户敏感信息明文 */
  async getBlurredInfo(ctx) {
    const { kdtId } = ctx;
    const { orderNo, searchKey } = ctx.getPostData();
    const params = {
      kdtId,
      orderNo,
      searchKey
    };
    const data = await new OrderSearchService(ctx).blurredInfoSearch(params);
    ctx.json(0, 'ok', data);
  }

  /**
   * 获取随心购订单单商品每期可退款余额
   */
  async getIssueRefundableFee(ctx) {
    const { kdtId } = ctx;
    const { orderNo, itemId } = ctx.query;
    const data = await new SellerRefundService(ctx).getIssueRefundableFee({
      kdtId,
      itemId,
      orderNo
    });
    ctx.json(0, 'ok', data);
  }
}

module.exports = IndexController;
