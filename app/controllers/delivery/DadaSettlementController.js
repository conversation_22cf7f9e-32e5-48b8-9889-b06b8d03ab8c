const BaseController = require('controllers/base');
const DadaSettlementService = require('services/api/delivery/DadaSettlementService');

class DadaSettlementController extends BaseController {
  getCommonParams(ctx) {
    const { kdtId, adminId } = ctx;
    let { deliveryChannel } = ctx.query;
    if (!deliveryChannel) {
      deliveryChannel = ctx.request.body.deliveryChannel;
    }
    const { mobile, nickName } = ctx.getLocalSession('userInfo');
    const { shopRole } = ctx.getLocalSession('shopInfo');
    return {
      loginKdtId: kdtId,
      fromApp: this.appName,
      deliveryChannel,
      requestId: this.requestId,
      operatorPhone: mobile,
      operatorName: nickName,
      operatorId: adminId,
      role: shopRole
    };
  }

  async getSettlementParams(ctx) {
    const params = this.getCommonParams(ctx);
    const result = await new DadaSettlementService(ctx).getSettlementParams({
      ...params,
      kdtId: ctx.query.kdtId
    });
    ctx.success(result);
  }

  async refreshSettlement(ctx) {
    const params = this.getCommonParams(ctx);
    const result = await new DadaSettlementService(ctx).refreshSettlement(params);
    ctx.success(result);
  }

  async deleteSettlement(ctx) {
    const params = this.getCommonParams(ctx);
    const result = await new DadaSettlementService(ctx).deleteSettlement({
      ...params,
      kdtId: ctx.request.body.kdtId
    });
    ctx.success(result);
  }
}

module.exports = DadaSettlementController;
