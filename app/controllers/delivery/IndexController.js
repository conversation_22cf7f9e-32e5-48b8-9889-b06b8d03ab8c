const _ = require('lodash');
const {
  checkUnifiedHqStore,
  checkRetailMinimalistHqStore,
  fxSupplierRole,
  branchStoreRole,
  checkSingleStore,
  checkUnifiedShop,
} = require('@youzan/utils-shop');
const ShopConfigKeys = require('constants/shop-config');
const BaseController = require('controllers/base');
const SupplierService = require('../../services/delivery/SupplierService.js');
const DeviceService = require('../../services/delivery/device.js');
const ShippingService = require('../../services/delivery/ShippingService.js');
const ItemQueryService = require('../../services/api/ic/ItemQueryService');
const FxGoodsQueryService = require('../../services/api/fx/FxGoodsQueryService');
const OnlineDeliveryTemplateApi = require('../../services/api/retail/product/OnlineDeliveryTemplateApi');
const HQStoreSearchService = require('../../services/api/retail/shop/HQStoreSearchService');
const ShopConfigReadService = require('../../services/api/shopcenter/ShopConfigReadService');
const ShopConfigWriteService = require('../../services/api/shopcenter/ShopConfigWriteService');
const TemplateService = require('../../services/api/ic/TemplateService');
const DeliverySettingService = require('../../services/api/ic/DeliverySettingService');
const QrcodeService = require('services/qrcode/QrcodeService');
const WeappQRCodeService = require('services/api/channels/WeappQRCodeService');

const selfWriteOffKey = 'self_write_off_type';
const fromApp = 'retail-node-order';
const supplyModeConfigKey = 'online_subshop_supply_mode';

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    const { kdtId, adminId } = ctx;
    const hasEnter = await this.getBaseInfo(ctx);
    ctx.setJsVar('supplier', !!hasEnter);
    const isDeviceNew = await this.getDeviceNew(ctx);
    ctx.setGlobal('isDeviceNew', !!isDeviceNew);
    await this.setIsWestcake(ctx);

    const shopInfo = ctx.getState('shopInfo');
    const isUnifiedHqStore = checkUnifiedHqStore(shopInfo);
    const isRetailMinimalistHqStore = checkRetailMinimalistHqStore(shopInfo);

    // 「连锁 3.0 总部 || 极简版总部」 要查体系下所有网店 + 供货店
    if (isUnifiedHqStore || isRetailMinimalistHqStore) {
      const params = {
        kdtId,
        pageNo: 1,
        shopRoleList: [fxSupplierRole, branchStoreRole],
        pageSize: 300,
        adminId: ctx.adminId
      };

      const pageSize = 300;

      const suppliers = await this.pullAll({
        fetch: argus => new HQStoreSearchService(ctx).search(argus),
        params,
        pageSize,
        getTotal: data => _.get(data, 'paginator.totalCount'),
        needPullAll: data => _.get(data, 'paginator.totalCount', 0) > pageSize,
        reducer: (acc, curr) => {
          return Object.assign(
            {},
            {
              items: curr.items.concat(acc.items || []),
              total: _.get(acc, 'paginator.totalCount', 0) + curr.paginator.totalCount
            }
          );
        },
        getParams: pageNo => Object.assign({}, params, { pageNo }),
        pullTimes: 4
      });

      ctx.setGlobal(
        '_supplierKdtIds',
        _.get(suppliers, 'items', [])
          .map(({ storeKdtId }) => storeKdtId)
          .concat(kdtId)
      );
    }

    await this.setShopSupplyMode(ctx);

    const localFeeWeight = await ctx.checkInWhiteList('local_fee_weight', kdtId, false);
    ctx.setJsVar('localFeeWeight', localFeeWeight);

    // 有没有开启履约备货提醒白名单
    const isShowAutoPrintTicket = await ctx.checkInWhiteList('delivery_stock_up', kdtId, false);
    ctx.setJsVar('isShowAutoPrintTicket', isShowAutoPrintTicket);
    //白名单是否支持餐道配送
    const isSupportCandao = await ctx.checkInWhiteList('support_candao', kdtId, false);
    ctx.setJsVar('isSupportCandao', isSupportCandao);

    // 是否计划库存转实物库存白名单商家
    const syncPlanResult = await ctx.checkInWhiteList('warehouse-sync-plan', kdtId, false);
    ctx.setJsVar({ isSyncPlanStore: syncPlanResult });

    // 独立销售库存模式，计划库存
    await this.queryStandaloneAndPlannedStock(ctx);

    // 获取Lite网店数量
    const list = await ctx.getLiteStore(kdtId, adminId);
    ctx.setJsVar({ liteStoreList: _.get(list, 'items', []) });

    // 获取暂停配送时段的白名单
    // TODO: 小程序上线(8.10)之后就可以删除
    const keys = await ctx.apolloClient.getConfig({
      appId: 'retail-node-order',
      namespace: 'retail-node-order.delivery',
      key: 'show-suspend-delivery-time'
    });
    ctx.setJsVar('showSuspendDeliveryTimeWhitelist', keys || '');

    /** 连锁L版本 */
    const isUnifiedShop = checkUnifiedShop(shopInfo);
    let inNewConfigPageV2 = false;
    if (isUnifiedShop) {
      // 白名单：点击配送管理，是否跳转到新页面
      const configInNewConfigPageV2 = await ctx.apolloClient.getConfig({
        appId: 'retail-node-fulfillment',
        namespace: 'retail-node-fulfillment.config',
        key: 'in_new_config_page_v2'
      });

      inNewConfigPageV2 = this.matchGrayConfig(configInNewConfigPageV2, ctx.kdtId);
    }
    ctx.setJsVar('inNewConfigPageV2', inNewConfigPageV2);

    /** 库存模式 */
    const stockModelConfig = await new ShopConfigReadService(ctx).queryShopConfig(
      ctx.kdtId,
      ShopConfigKeys.StockModelConfigKey
    );
    ctx.setJsVar('shopStockModel', +stockModelConfig.value);

    // 时段限单的白名单
    const orderLimitKeys = await ctx.apolloClient.getConfig({
      appId: 'retail-node-fulfillment',
      namespace: 'retail-node-fulfillment.config',
      key: 'order_time_limit'
    });
    const isSupportOrderLimit = this.matchGrayConfig(
      orderLimitKeys,
      ctx.getState('shopInfo').rootKdtId || ctx.kdtId
    );
    ctx.setJsVar('isSupportOrderLimit', isSupportOrderLimit);

    await this.setShopProdVersion(ctx);

    await ctx.render('delivery/index.html');
  }

  async getBaseInfo(ctx) {
    const service = new SupplierService(ctx);
    return await service.getBaseInfo(ctx.kdtId);
  }

  async getDeviceNew(ctx) {
    const { kdtId, userId: adminId } = ctx;
    try {
      const service = new DeviceService(ctx);
      const result = await service.getDeviceNew({ kdtId, adminId });
      return result;
    } catch (err) {
      return false;
    }
  }

  async getGoods(ctx) {
    const {
      kdtId,
      request: { body }
    } = ctx;
    const { id: deliveryTemplateId, supplierKdtIds } = body;
    const page = 1;
    const pageSize = 300;

    let data = {};

    // 「连锁 3.0 总部」 要查体系下所有网店 + 供货店, supplierKdtIds 只有在前端是 3.0 总部的时候才会传递
    const queryIds = supplierKdtIds || [kdtId];

    const dataResp = [];
    const fxGoodsCountResp = [];
    queryIds.forEach(kdtId => {
      const dataByKdtId = new ItemQueryService(ctx).listSimpleByDeliveryTemplate({
        kdtId,
        deliveryTemplateId,
        page,
        pageSize
      });
      dataResp.push(dataByKdtId);

      const countByKdtId = new FxGoodsQueryService(ctx).getGoodsNumByFxDeliveryTemplateId(
        +kdtId,
        +deliveryTemplateId
      );
      fxGoodsCountResp.push(countByKdtId);
    });

    const dataReducer = (acc, curr) => {
      return Object.assign(
        {},
        {
          items: curr.items.concat(acc.items || []),
          paginator: {
            page: 1,
            pageSize: 10,
            totalCount: _.get(acc, 'paginator.totalCount', 0) + curr.paginator.totalCount
          }
        }
      );
    };
    const countReducer = (acc, curr) => {
      return acc + curr[+deliveryTemplateId];
    };
    const dataSets = await Promise.all(dataResp);
    const fxGoodsDataSets = await Promise.all(fxGoodsCountResp);

    data = dataSets.reduce(dataReducer, {});

    data.fxGoodsNumber = fxGoodsDataSets.reduce(countReducer, 0);

    ctx.success(data);
  }

  async getWriteOffConfig(ctx) {
    const { subKdtId } = ctx.query;
    const queryKdtId = subKdtId || ctx.kdtId;

    const shelfWriteOffConfig = await new ShopConfigReadService(ctx).queryShopConfig(
      queryKdtId,
      selfWriteOffKey
    );

    return ctx.success(!!+shelfWriteOffConfig.value);
  }

  async setWriteOffConfig(ctx) {
    const {
      kdtId,
      request: { body }
    } = ctx;
    const { selfWriteOffType, subKdtId } = body;
    const queryKdtId = subKdtId || kdtId;
    const { id, staffName: name } = ctx.getLocalSession('userInfo');

    const result = await new ShopConfigWriteService(ctx).setShopConfig({
      kdtId: queryKdtId,
      key: selfWriteOffKey,
      value: selfWriteOffType,
      operator: {
        fromApp: 'multistore-center',
        name,
        id,
        type: 1
      }
    });

    return ctx.success(result);
  }

  async getTemplateParams(ctx, { kdtId, id, ...others }, extra = {}) {
    const shopInfo = ctx.getLocalSession('shopInfo');
    const params = {
      kdtId: +kdtId,
      operatorKdtId: +kdtId
    };
    id && (params.templateId = id);
    // tips: 处理接口越权，如果有总店id传总店Id，没有总店id则传当前店铺id。
    params.headId = shopInfo.rootKdtId || ctx.kdtId;

    return { ...others, ...params, ...extra };
  }

  /**
   * 注意特殊逻辑：
   * useCount 字段, 通过另外的接口单独查询, 然后拼装在一起
   */
  async listTemplates(ctx) {
    const { query, kdtId, adminId } = ctx;
    const { rootKdtId } = ctx.getLocalSession('shopInfo') || {};
    const { shopId, pageSize, page, templateName } = query;
    const data = await new TemplateService(ctx).listTemplates({
      headId: rootKdtId || kdtId,
      kdtId: shopId,
      pageSize,
      page,
      templateName,
      fromApp
    });

    const { items } = data || { items: [] };
    if (items.length === 0) {
      return ctx.success(data);
    }
    const deliveryTemplateIds = items.map(({ id }) => id);
    /** 获取所有模板的使用情况 */
    const usedData =
      (await new OnlineDeliveryTemplateApi(ctx)
        .countByDeliveryTemplateIds({
          adminId,
          kdtId,
          deliveryTemplateIds,
          retailSource: 'WEB-RETAIL-AJAX'
        })
        .catch(e => {
          for (const i of data.items) {
            delete i.useCount;
          }
          console.error(e);
        })) || {};
    const { useCount = {} } = usedData;
    /**
     * 使用次数为 0 的, 不会返回, 所以 useCount 这个 dict 有可能为空
     */
    if (Object.keys(useCount).length === 0) {
      return ctx.success(data);
    }
    const updatedItems = items.map(data => ({
      ...data,
      useCount: useCount[data.id] || 0
    }));
    const updatedData = {
      ...data,
      items: updatedItems
    };
    ctx.success(updatedData);
  }

  async replicate(ctx) {
    const {
      request: {
        body: { id, kdtId }
      }
    } = ctx;
    const params = await this.getTemplateParams(ctx, { id, kdtId });
    const data = await new TemplateService(ctx).replicate(params);
    return ctx.success(data);
  }

  async deleteTemp(ctx) {
    const {
      request: {
        body: { id, kdtId }
      }
    } = ctx;
    const params = await this.getTemplateParams(ctx, { id, kdtId });
    await new TemplateService(ctx).deleteTemp(params);
    return ctx.success();
  }

  async getSettingV3(ctx) {
    const { kdtId } = ctx.query;
    const params = await this.getTemplateParams(ctx, { kdtId }, { fromApp });
    const data = await new DeliverySettingService(ctx).getSettingV3(params);
    return ctx.success(data);
  }

  async updateSetting(ctx) {
    const { calcType, useHqCalcType, kdtId } = ctx.request.body;
    const params = await this.getTemplateParams(
      ctx,
      { calcType, useHqCalcType, kdtId },
      { fromApp }
    );
    await new DeliverySettingService(ctx).updateSetting(params);
    return ctx.success();
  }

  async getScanCodes(ctx) {
    const { kdtId } = ctx;
    const { selfFetchId, selfPointKdtId } = ctx.query;
    const shopInfo = ctx.getLocalSession('shopInfo');
    let rootKdtId = kdtId;
    // 如果是多网店, 返回 rootKdtId
    if (!checkSingleStore(shopInfo)) {
      rootKdtId = shopInfo.rootKdtId;
    }
    const resultCodes = {
      qr_code: '',
      weapp_code: ''
    };

    // 优化使用发货方店铺 kdtId
    const h5Url = `https://h5.youzan.com/wsctrade/orderlist/selffetch?kdt_id=${rootKdtId}&offline_id=0&self_fetch_id=${selfFetchId}&self_point_kdt_id=${selfPointKdtId}`;
    const weappPath = 'packages/trade/order/selffetch-list/index';

    try {
      const result = await new QrcodeService(ctx).qrCode(h5Url);
      const base64 = global.Buffer.from(result).toString('base64');
      resultCodes.qr_code = `data:image/png;base64,${base64}`;
    } catch (err) {
      ctx.log.warn({
        msg: '获取h5自提二维码出错',
        noThrow: true
      });
    }

    try {
      const data = await new WeappQRCodeService(ctx).wxaGetCodeUltra({
        kdtId: rootKdtId,
        hyaLine: false,
        page: 'pages/common/blank-page/index',
        params: {
          page: weappPath,
          kdtId, // 当前店铺的 kdtId
          guestKdtId: kdtId, // 当前店铺的 kdtId
          alias: `kdt_id=${kdtId}&shopId=${kdtId}&offlineId=0&selfFetchId=${selfFetchId}&selfPointKdtId=${selfPointKdtId}`
        }
      });

      if (data && data.imageBase64) {
        resultCodes.weapp_code = `data:image/png;base64,${data.imageBase64}`;
      }
    } catch (err) {
      ctx.log.warn({
        msg: '无法获取小程序自提二维码',
        noThrow: true
      });
    }

    return ctx.success(resultCodes);
  }

  /**
   * 获取铺货/供货模式
   * @param {object} ctx
   */
  async queryShopSupplyMode(ctx) {
    const { kdtId } = ctx;
    const { warehouseId } = ctx.query;
    const result = await new ShopConfigReadService(ctx).queryShopConfig(
      warehouseId || kdtId,
      supplyModeConfigKey
    );
    return ctx.success(result);
  }

  async addFeedBack(ctx) {
    const { kdtId } = ctx;
    const { reasonList } = ctx.request.body;
    const userInfo = ctx.getLocalSession('userInfo');
    const operator = {
      role: 'seller',
      operatorPhone: userInfo.mobile,
      operatorId: userInfo.userId,
      operatorName: userInfo.nickName,
    };
    const result = await new ShippingService(ctx).addFeedBack({
      kdtId,
      feedBackReasonRequestList: reasonList,
      operator,
    });
    ctx.json(0, 'ok', result);
  }
}

module.exports = IndexController;
