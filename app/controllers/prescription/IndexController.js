const {
  checkRetailOfflineBranchStore,
  checkUnifiedShop,
  ShopAbility,
  checkOnlineBranchStore
} = require('@youzan/utils-shop');
const { ShopAbilityUtil } = require('@youzan/plugin-shop-ability');
const BaseController = require('controllers/base');
const UnifiedCertQueryService = require('services/cert/UnifiedCertQueryService');
const AbilityReadService = require('services/shop/AbilityReadService');
const { checkUnifiedBranchStore } = require('@youzan/utils-shop/lib');

class IndexController extends BaseController {
  async checkShopCertedAndSetData(ctx) {
    const { kdtId } = ctx;
    let kdtIdParam = kdtId;
    const shopInfo = ctx.getLocalSession('shopInfo') || {};
    const isLite = await new ShopAbilityUtil(ctx).checkAbilityValid({
      keys: [ShopAbility.OnlineGoodsNewUiAbility]
    });
    // 高级版 && 多渠道 && 分店
    if (isLite && (await this.getIsSupportMultiChannel()) && checkUnifiedBranchStore(shopInfo)) {
      const isPos = await new ShopAbilityUtil(ctx).checkAbilityValid({
        keys: [ShopAbility.PosAbility]
      });
      if (
        /** 没有 POS 能力 且 有线上能力(这里的判断在前面 isOnlineOpen 已经前置掉了) 的分店 */
        !isPos
      ) {
        // 使用总部的认证状态
        kdtIdParam = shopInfo.rootKdtId || kdtId;
        // 是否是没有 pos 能力的多渠道高级版店铺
        ctx.setJsVar('isMultiChannelLiteNoPosStore', true);
      }
    }
    const certResult = await new UnifiedCertQueryService(ctx).queryQualMsg(kdtIdParam);
    return certResult;
  }

  async getIndexHtml(ctx) {
    const { kdtId } = ctx;
    const shopInfo = ctx.getState('shopInfo');
    const isUnifiedOfflineBranchStore = checkRetailOfflineBranchStore(shopInfo);
    const isUnifiedShop = checkUnifiedShop(shopInfo);
    const isOnlineOpen = checkOnlineBranchStore(shopInfo);
    // 连锁 L pos门店不能访问处方药插件
    if (!isUnifiedShop || (isUnifiedOfflineBranchStore && !isOnlineOpen)) {
      await ctx.redirect(ctx.helper.URL.site(`/v4/dashboard`, 'store'));
      return;
    }

    // 下面的逻辑限制了当前店铺是：总部 || 有线上能力的店铺
    const appId = 445208;
    const abilityKey = 'prescription_ability';
    // 注入认证状态和应用状态
    const certResult = await this.checkShopCertedAndSetData(ctx);
    const abilityStatus = await new AbilityReadService(ctx).queryShopAbilityInfo(kdtId, abilityKey);
    ctx.setJsVar('certResult', certResult);
    ctx.setJsVar('appId', appId);
    ctx.setJsVar('isValid', abilityStatus.valid);
    await ctx.render('prescription/index.html');
  }

  async getExportHtml(ctx) {
    await ctx.render('prescription/export.html');
  }
}

module.exports = IndexController;
