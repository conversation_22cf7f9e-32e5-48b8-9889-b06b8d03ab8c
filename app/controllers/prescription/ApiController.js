const BaseController = require('controllers/base');
const PrescriptionManagementService = require('services/prescription/PrescriptionManagementService');
const RetailMiscExportService = require('services/misc/RetailMiscExportService');

class ApiController extends BaseController {
  /**
   * 列表
   * @param {*} ctx
   */
  async list(ctx) {
    const { kdtId, adminId, query } = ctx;
    const {
      pageNo = 1,
      pageSize = 20,
      startTime,
      endTime,
      buKdtId,
      orderNo,
      rxNo,
      rxStatus,
    } = query;

    const searchParam = {};

    if (startTime) searchParam.startTime = startTime;
    if (endTime) searchParam.endTime = endTime;
    if (buKdtId) searchParam.buKdtId = buKdtId;
    if (orderNo) searchParam.orderNo = orderNo;
    if (rxNo) searchParam.rxNo = rxNo;
    if (rxStatus) searchParam.rxStatus = rxStatus;

    const result = await new PrescriptionManagementService(ctx).invokeWithSource('search', {
      kdtId,
      adminId,
      pageNo,
      pageSize,
      searchParam,
    });

    ctx.json(0, 'ok', result);
  }

  /**
   * 详情
   *
   * @param {*} ctx
   */
  async detail(ctx) {
    const { kdtId, adminId, query } = ctx;
    const { rxNo, buKdtId } = query;

    const result = await new PrescriptionManagementService(ctx).invokeWithSource('detail', {
      kdtId,
      adminId,
      rxNo,
      buKdtId: +buKdtId
    });

    ctx.json(0, 'ok', result);
  }

  /**
   * 审核
   *
   * @param {*} ctx
   */
  async review(ctx) {
    const { kdtId, adminId } = ctx;
    const { buKdtId, failureReason, rxNo, reviewResult, id } = ctx.request.body;

    const result = await new PrescriptionManagementService(ctx).invokeWithSource('review', {
      kdtId,
      buKdtId,
      adminId,
      failureReason,
      id,
      rxNo,
      reviewResult
    });

    ctx.json(0, 'ok', result);
  }

  /**
   * 订单导出
   *
   * @param {*} ctx
   */
  async exportRecords(ctx) {
    const { kdtId, adminId } = ctx;
    const { params } = ctx.request.body;

    const result = await new PrescriptionManagementService(ctx).invokeWithSource('export', {
      kdtId,
      adminId,
      searchParam: params
    });

    ctx.json(0, 'ok', result);
  }

  /**
   * 导出列表
   *
   * @param {*} ctx
   */
  async exportList(ctx) {
    const { kdtId, adminId, query } = ctx;
    const { pageNo = 1, pageSize = 20 } = query;

    const result = await new RetailMiscExportService(ctx).queryExportTasks({
      kdtId,
      adminId,
      pageNo,
      pageSize,
    });

    ctx.json(0, 'ok', result);
  }

  // FIXME: MOCK 专用，发布前删除
  async mock(ctx) {
    const { kdtId, adminId } = ctx;
    const { scene, orderNo, rxNo, buKdtId } = ctx.request.body;

    const result = await new PrescriptionManagementService(ctx).invokeWithSource('mock', {
      kdtId,
      buKdtId,
      adminId,
      scene,
      orderNo,
      rxNo,
      outInquiryId: 'mock 专用',
      outRxNo: 'mock 专用',
      reason: 'mock 专用',
      rxImgUrl: 'https://b.yzcdn.cn/retail/prescription/prescription-sample.png'
    });

    ctx.json(0, 'ok', result);
  }
}

module.exports = ApiController;
