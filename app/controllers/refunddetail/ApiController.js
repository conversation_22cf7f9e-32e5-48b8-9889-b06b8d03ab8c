const BaseController = require('controllers/base');
const RefundOperateBySellerService = require('services/api/ebiz/RefundOperateBySellerService.js');
const SellerExchangeService = require('services/api/ebiz/SellerExchangeService.js');

const appName = 'retail-node-order';

class IndexController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '售后详情';
  }

  // 操作人
  get operator() {
    const { ctx } = this;
    const operator = {
      role: 'seller',
      operatorId: ctx.adminId
    };
    return operator;
  }

  // 来源
  get source() {
    const { ctx } = this;
    const source = {
      app: appName,
      clientIp: ctx.firstXff,
      from: appName
    };
    return source;
  }

  /**
   * 兼容云业务身份 - 该逻辑由 iron 移植过来
   */
  injectRefundScContext(orderNo) {
    const { ctx } = this;
    const userInfo = ctx.getLocalSession('userInfo');

    try {
      const params = {
        kdtId: ctx.kdtId,
        userId: userInfo.id,
        clientType: 'pc',
        pageName: 'orderRefund',
        orderNo
      };

      ctx.registerServiceChain('business_identity', params);
    } catch (e) {
      ctx.log.warn('同意退款注入 sc 上下文失败：injectRefundScContext');
    }
  }

  // 发布留言
  async postMessage(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId, message, extInfo } = ctx.request.body;

    const result = await new RefundOperateBySellerService(ctx).refundMessage({
      kdtId,
      orderNo,
      refundId,
      message,
      extInfo,
      operator: this.operator,
      source: this.source
    });
    return ctx.success(result);
  }

  // 退款前请求
  async preCheck(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundType, refundId = null } = ctx.query;

    const result = await new RefundOperateBySellerService(ctx).preCheck({
      kdtId,
      orderNo,
      refundType,
      refundId
    });
    return ctx.success(result);
  }

  // 同意退款
  async accept(ctx) {
    const { kdtId } = ctx;
    const {
      refundId,
      orderNo,
      name,
      mobile,
      address,
      id,
      province,
      city,
      county,
      region,
      areaCode,
      telephone,
      outAddressId
    } = ctx.request.body;
    const params = {
      kdtId,
      operator: this.operator,
      source: this.source,
      scOperatorStr: this.scOperatorStr
    };

    refundId && (params.refundId = refundId);
    orderNo && (params.orderNo = orderNo);
    name && (params.name = name);
    params.mobile = mobile || '';
    address && (params.address = address);
    id && (params.id = id);
    province && (params.province = province);
    city && (params.city = city);
    county && (params.county = county);
    region && (params.region = region);
    areaCode && (params.areaCode = areaCode);
    telephone && (params.telephone = telephone);
    outAddressId &&
      Object.assign(params, { bizExtension: { OUT_AFTER_SALE_ADDRESS_ID: outAddressId } });

    this.injectRefundScContext(params.orderNo);

    const result = await new RefundOperateBySellerService(ctx).accept(params);
    return ctx.success(result);
  }

  // 拒绝退款
  async reject(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId, remark } = ctx.request.body;

    const result = await new RefundOperateBySellerService(ctx).reject({
      kdtId,
      orderNo,
      refundId,
      remark,
      operator: this.operator,
      source: this.source,
      scOperatorStr: this.scOperatorStr
    });
    return ctx.success(result);
  }

  // 确认收货并退款
  async sign(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId } = ctx.request.body;

    this.injectRefundScContext(orderNo);

    const result = await new RefundOperateBySellerService(ctx).sign({
      kdtId,
      orderNo,
      refundId,
      operator: this.operator,
      source: this.source,
      scOperatorStr: this.scOperatorStr
    });
    return ctx.success(result);
  }

  // 拒绝确认收货(退货流程)
  async unsign(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId } = ctx.request.body;

    const result = await new RefundOperateBySellerService(ctx).unsign({
      kdtId,
      orderNo,
      refundId,
      remark: '未收货，拒绝退款',
      operator: this.operator,
      source: this.source,
      scOperatorStr: this.scOperatorStr
    });
    return ctx.success(result);
  }

  /**
   * 同意换货
   * @param {Context} ctx
   */
  // 这个暂时没用到，预留
  // async exchangeAgree(ctx) {
  //   const { kdtId } = ctx;
  //   const params = {
  //     ...ctx.request.body,
  //     kdtId,
  //     operator: this.operator,
  //     source: this.source,
  //     clientIp: ctx.firstXff
  //   };
  //
  //   const result = await new SellerExchangeService(ctx).exchangeAgree(params);
  //   return ctx.success(result);
  // }

  /**
   * 拒绝换货
   * @param {Context} ctx
   */
  async exchangeReject(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId, remark, version } = ctx.request.body;

    const result = await new SellerExchangeService(ctx).exchangeReject({
      kdtId,
      orderNo,
      refundId,
      remark,
      operator: this.operator,
      clientIp: ctx.firstXff,
      version
    });
    return ctx.success(result);
  }

  /**
   * 确认收货并发货(换货流程)
   * @param {Context} ctx
   */

  async deliveryExchangeGoods(ctx) {
    const { kdtId } = ctx;
    const { orderNo, companyCode, logisticsNo, refundId, version } = ctx.request.body;

    const result = await new SellerExchangeService(ctx).exchangeGoodsReturnAgree({
      kdtId,
      orderNo,
      refundId,
      companyCode,
      logisticsNo,
      operator: this.operator,
      clientIp: ctx.firstXff,
      version
    });
    return ctx.success(result);
  }

  /**
   * 拒绝确认收货(换货流程)
   * @param {Context} ctx
   */

  async exchangeUnsign(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId, version, remark } = ctx.request.body;

    const result = await new SellerExchangeService(ctx).exchangeGoodsReturnReject({
      kdtId,
      orderNo,
      refundId,
      remark,
      operator: this.operator,
      clientIp: ctx.firstXff,
      version
    });
    return ctx.success(result);
  }
}

module.exports = IndexController;
