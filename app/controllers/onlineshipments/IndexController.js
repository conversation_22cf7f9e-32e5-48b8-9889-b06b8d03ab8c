const BaseController = require('controllers/base');
const OnlineShipmentsService = require('services/onlineshipments');
const ShopBaseReadOuterService = require('../../services/general/GeneralShopBasicInfomationService');
const PrintReceiptService = require('../../services/api/delivery/PrintReceipt');

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    // 原 销售发货 尝试重定向到 订单发货
    if (await this.tryToRedirectToFulfillment(ctx)) return;

    const service = new OnlineShipmentsService(ctx);
    const isShowMultiStore = await service.getIsShowMultiStore(ctx.kdtId);

    // 44537 销售员旗舰版插件 ID
    const state = await service.checkApp(ctx.kdtId, 44537);

    if (state) {
      ctx.setJsVar('sales_status', state);
    }

    ctx.setJsVar('isShowMultiStore', +isShowMultiStore === 1);

    await this.setShopSupplyMode(ctx);

    await this.setIsWestcake(ctx);

    await this.queryExpressDelivery(ctx);

    await ctx.render('onlineshipments/index.html');
  }
  async queryShopBaseInfo(ctx) {
    const result = await new ShopBaseReadOuterService(ctx).queryShopBaseInfo(ctx.kdtId);

    return ctx.success(result);
  }
  // 打印拣货小票
  async printReceipt(ctx) {
    const {
      adminId,
      kdtId,
      request: { body }
    } = ctx;
    const { adminName, billNo, bizType } = body;
    const params = {
      adminName,
      billNo,
      bizType,
      retailSource: 'retail-node-order',
      adminId,
      kdtId
    };
    const result = await new PrintReceiptService(ctx).printReceipt(params);
    ctx.success(result);
  }
}

module.exports = IndexController;
