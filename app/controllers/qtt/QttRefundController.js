const BaseController = require('controllers/base');
const QttRefundService = require('services/qtt/QttRefundService');

class QttRefundController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '售后详情';
  }

  // 校验退款佣金
  async checkRefundCommission(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId = null } = ctx.query;

    const result = await new QttRefundService(ctx).checkRefundCommission({
      kdtId,
      orderNo,
      refundId
    });
    return ctx.success(result);
  }

  // 只退佣金
  async refundCommission(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId, refundCommission, promoteFee, inviteFee, privateFee } =
      ctx.request.body;

    const result = await new QttRefundService(ctx).refundCommission({
      kdtId,
      orderNo,
      refundId,
      refundCommission,
      promoteFee,
      inviteFee,
      privateFee
    });
    return ctx.success(result);
  }

  // 校验是否可以取消退款
  async refuseCheck(ctx) {
    const { kdtId } = ctx;
    const { orderNo, refundId } = ctx.request.body;

    const result = await new QttRefundService(ctx).refuseCheck({
      kdtId,
      orderNo,
      refundId
    });
    return ctx.success(result);
  }
}

module.exports = QttRefundController;
