const BaseController = require('controllers/base');
const QttOrderService = require('services/qtt/QttOrderService');

class QttOrderController extends BaseController {
  constructor(ctx) {
    super(ctx);
    this.ctx.biz = '订单详情';
  }

  // 校验订单
  async checkQttOrder(ctx) {
    const { kdtId } = ctx;
    const { orderNo } = ctx.query;

    const result = await new QttOrderService(ctx).checkQttOrder({
      kdtId,
      orderNo
    });
    return ctx.success(result);
  }

  // PC订单群团团信息
  async orderDetailForPc(ctx) {
    const { kdtId } = ctx;
    const { orderNo } = ctx.query;

    const result = await new QttOrderService(ctx).orderDetailForPc({
      kdtId,
      orderNo
    });
    return ctx.success(result);
  }

  // 存储团长备注
  async saveRemarkForPC(ctx) {
    const { kdtId, adminId } = ctx;
    const { orderNo, detail } = ctx.getPostData();

    const params = {
      kdtId,
      orderNo,
      detail,
      markType: 5,
      operatorId: adminId
    };

    const result = await new QttOrderService(ctx).saveRemarkForPC(params);
    return ctx.success(result);
  }
}

module.exports = QttOrderController;
