const BaseController = require('controllers/base');
const GeneralOrderExportService = require('../../services/api/trade/GeneralOrderExportService');
const OperationCertService = require('../../services/shopcenter/OperationCertService');
const OperationRecordService = require('../../services/shopcenter/OperationRecordService');

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    await ctx.render('exportList/index.html');
  }

  async queryPrivateUrl(ctx) {
    const { kdtId, userId } = ctx;
    const { id, category, operationType } = ctx.request.body;

    // 检查是否通过短信校验
    const captchaResult = await new OperationCertService(ctx).getOperationCertMethod({
      kdtId,
      operationType,
      userId
    });

    if (captchaResult.method === 'NO_CERT') {
      const params = {
        kdtId: ctx.kdtId,
        adminId: userId,
        exportId: id,
        category // 'order',
      };

      const result = await new GeneralOrderExportService(ctx).queryPrivateUrl(params);
      // 创建下载记录
      await new OperationRecordService(ctx).createRecord({
        kdtId,
        operationType,
        operatorId: userId,
        operateObjectId: id
      });
      return ctx.success(result);
    }

    return ctx.success('');
  }

  // 获取下载记录
  async getDownloadList(ctx) {
    const { kdtId } = ctx;
    const { page, pageSize, operationTypes, operateObjectId } = ctx.query;
    const result = await new OperationRecordService(ctx).listRecords({
      page: +page,
      pageSize: +pageSize,
      operationTypes: JSON.parse(operationTypes || '[]'),
      kdtId,
      operateObjectId
    });
    return ctx.success(result);
  }
}

module.exports = IndexController;
