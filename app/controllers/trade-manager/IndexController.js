const BaseController = require('controllers/base');

const OrderOperationService = require('services/trade-manager/OrderOperationService');

class IndexController extends BaseController {
  async getOrderOperationReason(ctx) {
    const { kdtId, adminId, query } = ctx;
    const { method, orderNo } = query;

    const result = await new OrderOperationService(ctx)
      .getOrderOperationReason({
        kdtId,
        adminId,
        operationMethod: method,
        orderNo,
      });
    ctx.success(result);
  }
}

module.exports = IndexController;
