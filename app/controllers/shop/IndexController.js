const BaseController = require('controllers/base');
const ShopBaseInfoService = require('services/api/shopcenter/ShopBaseInfoService');

class IndexController extends BaseController {
  async getQueryShopBaseInfo(ctx) {
    const { targetKdtId } = ctx.query;
    const res = await new ShopBaseInfoService(ctx).queryShopBaseInfo(targetKdtId);
    ctx.success(res);
  }
}

module.exports = IndexController;
