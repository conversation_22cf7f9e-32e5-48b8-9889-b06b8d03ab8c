const BaseController = require('controllers/base');
const WaybillService = require('../../services/delivery/WaybillService');

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    // 如果电子面单版本为2，则跳转到v4批量发货
    try {
      const waybillVersion = await new WaybillService(ctx).getWaybillVersion(ctx.kdtId);
      if (waybillVersion === 2) {
        return ctx.redirect('/v4/trade/delivery/batch');
      }
    } catch (error) {
      console.log(error);
    }

    await Promise.all([
      this.setShopSupplyMode(ctx),
      this.queryExpressDelivery(ctx),
      this.initTradeUserPrivacyDataConfig(ctx)
    ]);
    await ctx.render('batchshipments/index.html');
  }
}

module.exports = IndexController;
