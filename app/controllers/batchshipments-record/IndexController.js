const BaseController = require('controllers/base');
const WaybillService = require('../../services/delivery/WaybillService');

class IndexController extends BaseController {
  async getIndexHtml(ctx) {
    // 请求当前店铺电子面单版本
    try {
      const waybillVersion = await new WaybillService(ctx).getWaybillVersion(ctx.kdtId);
      ctx.setGlobal('waybillVersion', waybillVersion);
    } catch (error) {
      console.log(error);
    }
    await ctx.render('batchshipments-record/index.html');
  }
}

module.exports = IndexController;
