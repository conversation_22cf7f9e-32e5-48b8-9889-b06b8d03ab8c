const BaseService = require('../base/index');

class ShopService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.shop.api.global.service.RetailConfigService';
  }

  /**
   * 获取店铺配置
   *
   * @param {Number} kdtId
   * @param {String} biz
   * @param {String} bizKey
   * @param {Boolean} defaultValue
   */
  async getShopConfig(kdtId, biz, bizKey, defaultValue = false) {
    return await this.invoke(
      this.SERVICE_NAME,
      'queryOne',
      [
        {
          kdtId,
          business: biz,
          key: bizKey,
          retailSource: 'WEB_RETAIL_NODE'
        }
      ],
      { default: { value: defaultValue } }
    );
  }
}

module.exports = ShopService;
