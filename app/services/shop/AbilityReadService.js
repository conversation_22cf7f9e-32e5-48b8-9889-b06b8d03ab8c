const BaseService = require('../base/index');

class AbilityReadService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopprod.api.service.ability.AbilityReadService';
  }

  /**
   * 查询店铺能力
   *
   * @param {*} kdtId
   * @param {*} abilityCode
   * @returns
   */
  async queryShopAbilityInfo(kdtId, abilityCode) {
    return await this.invokeMethod('queryShopAbilityInfo', [kdtId, abilityCode]);
  }
}

module.exports = AbilityReadService;
