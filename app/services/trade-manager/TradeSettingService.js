const BaseService = require('../base/index');


/**
 * com.youzan.ebiz.mall.trade.seller.api.service.setting.TradeSettingService
 */
class TradeSettingService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.mall.trade.seller.api.service.setting.TradeSettingService';
  }

  get BUSINESS_SERVICE_NAME() {
    return 'com.youzan.trade.business.setting.api.TradeSettingService';
  }

  /**
  * 获取极速下单设置
  *  该接口主要是包装店铺配置的逻辑（提供给B端使用，C端如需使用请单独查询店铺配置接口）
  * @link http://zanapi.qima-inc.com/site/service/view/1482859
  * @param {Object} dto - 极速下单配置
  * @param {number} dto.kdtId - 店铺ID
  * @return {Promise}
  */
  async getFastTradeSetting(dto) {
    return this.invoke(this.BUSINESS_SERVICE_NAME, 'getFastTradeSetting', [dto]);
  }

  /**
  * 极速下单设置编辑接口
  * @link http://zanapi.qima-inc.com/site/service/view/1482860
  * @param {Object} dto - 极速下单配置
  * @param {number} dto.settingStatus - 开关状态
  *  0:关闭,1:开启
  * @param {number} dto.kdtId - 店铺ID
  * @param {number} dto.type - 设置值
  * @return {Promise}
  */
  async editFastTradeSetting(dto) {
    return this.invoke(this.BUSINESS_SERVICE_NAME, 'editFastTradeSetting', [dto]);
  }

  /**
   *  交易设置-查询
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/141485
   *
   *  @param {object} request -
   *  @param {number} request.kdtId -
   *  @return {object}
   */
  async getSettingByKdtId(request) {
    return this.invokeMethod('get', [request]);
  }

  /**
   *  交易设置-更新
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/141487
   *
   *  @param {Object} request -
   *  @param {number} request.editPriceType - 订单改价类型。 0:整单改价。1:商品改价
   *  @param {number} request.realNameAuthMaxTimePerBuyerPerDay - 每天单个买家实名认证最大次数
   *  @param {number} request.kdtId -
   *  @param {number} request.receiverNameCheck - 收货人地址姓名校验 0:关闭 1:开启
   *  @param {number} request.isRealNameAuthOpen - 是否实名认证开启
   *  @param {number} request.limitRealNameAuthMaxTimePerBuyerPerDay - 店铺级交易设置:是否开启每天单个买家实名校验最大次数限制
   *  @param {string} request.source - 请求来源，暂未定义，仅预留以后用作接口调用日志统计使用
   *  @param {boolean} request.isOpenSignMore - 是否开启单品多运
   *  @param {number} request.supportOverseas - 支持海外地址配送 0:不支持 1:支持
   *  @param {number} request.orderExpireTime - 订单取消时间（分）
   *  @param {number} request.expressAutoConfirm - 发货后确认收货时间 （天）
   *  @param {number} request.apply_refund_after_sale - 买家申请售后限制 （天）
   *  @return {Promise}
   */
  async editSettingByKdtId(request) {
    return this.invokeMethod('update', [request]);
  }
}

module.exports = TradeSettingService;
