const BaseService = require('../base/index');

class OrderOperationService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trademanager.biz.api.service.OrderOperationService';
  }

  /**
   * 查询订单操作内容
   * http://zanapi.qima-inc.com/site/service/view/1175437
   *
   * @param {*} params
   * @returns
   */
  async getOrderOperationReason(params) {
    return this.invokeMethod('getOrderOperationReason', [params]);
  }
}

module.exports = OrderOperationService;
