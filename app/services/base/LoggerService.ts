const { RetailBaseService } = require('@youzan/retail-node-base');

/**
 * 注入日志服务
 */
class LoggerService extends RetailBaseService {
  public ctx: any;
  private startTime: number = 0;

  /**
   * 获取操作人信息
   * @return {object} 操作人信息
   */
  getOperator() {
    const userInfo = this.ctx.getLocalSession('userInfo');
    return {
      operatorPhone: userInfo.mobile,
      operatorId: userInfo.id,
      operatorName: userInfo.nickName,
    };
  }

  /**
   * 上报服务请求日志
   * @param {string} serviceName 服务名
   * @param {string} methodName 方法
   * @param {any[]} args 请求参数
   */
  public reportDubboStart(serviceName: string, methodName: string, args = []) {
    const { ctx } = this;
    const traceId = ctx.traceCtx && ctx.traceCtx.rootId;
    const extra: any = {
      serviceName: serviceName.substr(serviceName.lastIndexOf('.') + 1),
      methodName,
      kdtId: ctx.kdtId,
    };

    this.startTime = Date.now();
    const tag = `[${traceId}] - actionStart=${serviceName}.${methodName}, args=${JSON.stringify(
      args,
    )}  operator=${JSON.stringify(this.getOperator())}`;
    ctx.logger.info(tag, null, extra);
  }

  /**
   * 上报服务响应日志
   * @param {string} serviceName 服务名
   * @param {string} methodName 方法
   * @param {object} result 响应数据
   */
  public reportDubboEnd(serviceName: string, methodName: string, result: any) {
    const { ctx } = this;
    const traceId = ctx.traceCtx && ctx.traceCtx.rootId;

    const extra: any = {
      serviceName: serviceName.substr(serviceName.lastIndexOf('.') + 1),
      methodName,
      kdtId: ctx.kdtId,
    };
    // @ts-ignore
    const res = typeof result === 'string' ? result : JSON.stringify(result || {});
    const tag = `[${traceId}] - actionEnd=${serviceName}.${methodName},
      costTime=${Date.now() - this.startTime}, 
      result=${res}
      operator=${JSON.stringify(this.getOperator())}
    `;
    ctx.logger.info(tag, null, extra);
  }
}

export default LoggerService;
