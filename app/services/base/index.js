import LoggerService from './LoggerService';

const _ = require('lodash');

const SOURCE = 'WEB_RETAIL_NODE';

class BaseService extends LoggerService {
  getShopRole(ctx) {
    const shopInfo = ctx.getState('shopInfo');
    const shopType = _.get(shopInfo, 'shopType', null);
    const shopRole = _.get(shopInfo, 'shopRole', null);
    if (shopRole === 0 && shopType === 7) {
      return 'retail-single-store';
    }
  }

  /**
   * 封装了 invoke, 不需要手动传入 SERVICE_NAME 了,
   * 会自动从实例上读取
   */
  async invokeMethod(...args) {
    // 用 ...args, 是因为, invoke 方法可能接收 options 参数
    // 详见 http://gitlab.qima-inc.com/retail-web/retail-node-base/blob/master/app/lib/invoke.js#L4
    return await this.invoke(this.SERVICE_NAME, ...args);
  }

  /**
   * 封装带 source / retailSource 的 invoke 方法
   *
   * @param {*} method
   * @param {*} params
   * @returns
   */
  async invokeWithSource(method, params) {
    return await this.invokeMethod(method, [{
      ...params,
      source: SOURCE,
      retailSource: SOURCE,
    }]);
  }
}

module.exports = BaseService;
