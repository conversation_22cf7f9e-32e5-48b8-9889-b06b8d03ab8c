const BaseService = require('../base/index');

/**
 * 查询门店预定设置接口
 */
class DeliveryWayService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trade.misc.api.service.ShopSettingService';
  }

  async getDeliveryWayList(requestParams) {
    return await this.invoke(this.SERVICE_NAME, 'queryStoreReserveSetting', [requestParams]);
  }

  async getSelfFetchPointList(requestParams) {
    return await this.invoke(
      'com.youzan.retail.ofc.dispatcher.api.service.dispatch.DispatchSupplyService',
      'getStoreReserveSelfFetchPoints',
      [requestParams]
    );
  }
}

module.exports = DeliveryWayService;
