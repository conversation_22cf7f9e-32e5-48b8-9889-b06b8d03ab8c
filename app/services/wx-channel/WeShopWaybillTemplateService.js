const BaseService = require('../base/index');

/* com.youzan.ebiz.video.channels.trade.api.service.weshop.WeShopWaybillTemplateService -  */
class WeShopWaybillTemplateService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.trade.api.service.weshop.WeShopWaybillTemplateService';
  }

  /**
   *  获取快递公司电子面单模版
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1453037
   *
   *  @param {Object} request - 查询请求
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.mpId - 小店mpId
   *  @param {number} request.expressId - 快递公司id
   *  @return {Promise}
   */
  async getDeliveryTemplate(request) {
    return this.invokeMethod('getDeliveryTemplate', [request]);
  }
}

module.exports = WeShopWaybillTemplateService;
