const BaseService = require('../base/index');

/* com.youzan.ebiz.video.channels.trade.api.service.dc.MultiPlatWaybillOrderOperateFacade -  */
class MultiPlatWaybillOrderOperateFacade extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.trade.api.service.dc.MultiPlatWaybillOrderOperateFacade';
  }

  /**
   *  电子面单取号
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1453066
   *
   *  @param {Object} request - 取号请求
   *  @param {string} request.thirdSiteCode - 网点编码
   *  @param {Object} request.receiver - 收件人信息
   *  @param {number} request.kdtId - 店铺ID
   *  @param {string} request.thirdShopId - 外部店铺id（从查询开通账号信息接口获取）
   *  @param {Object} request.sender - 寄件人信息
   *  @param {number} request.channelType - 对接平台渠道. 见ChannelAccountType.
   *  @param {string} request.remark - 备注
   *  @param {number} request.mpId - 小店mpId
   *  @param {number} request.expressId - 物流公司id（有赞侧）
   *  @param {string} request.thirdAcctId - 电子面单账号id
   *  @param {Object} request.order - 订单信息
   *  @param {Object} request.returnAddress - 退货地址
   *  @param {string} request.monthlyCardId - 月结账号 直营店铺有效
   *  @param {number} request.thirdProductTypeId - 产品类型ID 直营店铺有效，默认为1
   *  @return {Promise}
   */
  async createOrder(request) {
    return this.invokeMethod('createOrder', [request]);
  }

  /**
             *  电子面单打印成功通知
*  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1453210 
*
             *  @param {Object} request - 请求参数
             *  @param {string} request.thirdWaybillId - 三方电子面单id
             *  @param {number} request.printType - 0.首次打印（默认值）
 1.重新打印
             *  @param {number} request.kdtId - 店铺ID
             *  @param {string} request.expressNo - 快递单号
             *  @param {number} request.channelType - 对接平台渠道. 见ChannelAccountType.
             *  @param {number} request.mpId - 小店mpId
             *  @param {number} request.expressId - 物流公司id（有赞侧）
             *  @return {Promise}
             */
  async orderPrintSuccessNotify(request) {
    return this.invokeMethod('orderPrintSuccessNotify', [request]);
  }
}

module.exports = MultiPlatWaybillOrderOperateFacade;
