const BaseService = require('../base/index');

/* com.youzan.ebiz.video.channels.trade.api.service.dc.MultiPlatDeliveryQueryFacade -  */
class MultiPlatDeliveryQueryFacade extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.trade.api.service.dc.MultiPlatDeliveryQueryFacade';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1453704
   *
   *  @param {Object} request -
   *  @param {Object} request.sourceInfo -
   *  @param {string} request.sourceInfo.bizSource - 业务来源
   *  @param {Object} request.extension -
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.channelType - 对接平台渠道. 见ChannelAccountType.
   *  @param {number} request.mpId - 小店mpId
   *  @return {Promise}
   */
  async queryExpressInfos(request) {
    return this.invokeMethod('queryExpressInfos', [request]);
  }

  /**
   *  
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1453705 
   *
   *  @param {Object} request - 
   *  @param {Object} request.sourceInfo - 
   *  @param {Object} request.extension - 
   *  @param {number} request.yzExpressId - 有赞物流商id 必填
   *  @param {number} request.kdtId - 店铺ID
   *  @param {Object} request.options - 选填
   *  @param {number} request.channelType - 对接平台渠道. 见ChannelAccountType.
   *  @param {number} request.mpId - 小店mpId
   *  @param {Object} pageRequest - 
   *  @param {number} pageRequest.pageSize - 页长
 必填项
   *  @param {number} pageRequest.page - 页码
 必填项
   *  @return {Promise}
   */
  async queryPageSites(request, pageRequest) {
    return this.invokeMethod('queryPageSites', [request, pageRequest]);
  }

  /**
   *  查询物流公司产品类型
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1468184
   *
   *  @param {Object} request - 请求参数
   *  @param {Object} request.sourceInfo -
   *  @param {string} request.sourceInfo.bizSource - 业务来源
   *  @param {Object} request.extension -
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.channelType - 对接平台渠道. 见ChannelAccountType.
   *  @param {number} request.mpId - 小店mpId
   *  @param {number} request.expressId - 有赞物流商id 必填
   *  @return {Promise}
   */
  async queryExpressProductTypes(request) {
    return this.invokeMethod('queryExpressProductTypes', [request]);
  }

  /**
   *  分页查询月结账号
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1468063 
   *
   *  @param {Object} request - 请求参数
   *  @param {Object} request.sourceInfo - 
   *  @param {Object} request.extension - 
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.channelType - 对接平台渠道. 见ChannelAccountType.
   *  @param {number} request.mpId - 小店mpId
   *  @param {number} request.expressId - 有赞物流商id 必填
   *  @param {Object} pageRequest - 分页参数
   *  @param {number} pageRequest.pageSize - 页长 必填项
   *  @param {number} pageRequest.page - 页码 必填项
   *  @return {Promise}
   */
  async queryPageMonthlyCards(request, pageRequest) {
    return this.invokeMethod('queryPageMonthlyCards', [request, pageRequest]);
  }
}

module.exports = MultiPlatDeliveryQueryFacade;
