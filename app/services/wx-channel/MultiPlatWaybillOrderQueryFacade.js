const BaseService = require('../base/index');

/* com.youzan.ebiz.video.channels.trade.api.service.dc.MultiPlatWaybillOrderQueryFacade -  */
class MultiPlatWaybillOrderQueryFacade extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.ebiz.video.channels.trade.api.service.dc.MultiPlatWaybillOrderQueryFacade';
  }

  /**
   *  获取电子面单打印报文
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1453035
   *
   *  @param {Object} request - 查询请求
   *  @param {string} request.thirdWaybillId - 三方电子面单id
   *  @param {Object} request.printNum - 打印数量
   *  @param {integer} request.printNum.curNum - 当前张数
   *  @param {integer} request.printNum.sumNum - 总张数
   *  @param {number} request.kdtId - 店铺ID
   *  @param {number} request.channelType - 对接平台渠道. 见ChannelAccountType.
   *  @param {string} request.thirdTemplateId - 三方面单模板ID
   *  @param {number} request.mpId - 小店mpId
   *  @return {Promise}
   */
  async getOrderPrint(request) {
    return this.invokeMethod('getOrderPrint', [request]);
  }
}

module.exports = MultiPlatWaybillOrderQueryFacade;
