const BaseService = require('../base/index');

class WaybillService extends BaseService {
  SERVICE_NAME = 'com.youzan.delivery.service.waybill.WaybillService';

  DELIVERY_SERVICE_NAME = 'com.youzan.retail.trademanager.biz.api.service.DeliveryService';

  /**
   * 新电子面单顺丰取消呼叫重新呼叫接口
   * zanAPI文档地址：http://zanapi.qima-inc.com/site/service/view/1449102
   * @param {*} param -
   * @param {boolean} param.cancelDoorPickUp - 是否取消上门取件
   * @param {boolean} param.kdtId - 商家id
   * @param {string} param.orderNo - 订单号
   * @param {number} param.preStartTime - 预期上门取件开始时间
   * @param {number} param.preEndTime - 预期上门取件结束时间
   * @param {string} param.packId - 包裹单号
   * @param {string} param.productCode - 产品编码
   * @param {number} param.sourceId - 渠道
   * @return {Promise}
   */
  async waybillOrderConfirm(param) {
    return this.invoke(this.SERVICE_NAME, 'waybillOrderConfirm', [param]);
  }

  /**
   * 重新打单-查询打印报文
   * zanAPI文档地址：http://zanapi.qima-inc.com/site/service/view/1448587
   * @param {object} param -
   * @param {boolean} param.kdtId - 商家id
   * @param {string} param.orderNo - 订单号
   * @param {string} param.packId - 包裹单号
   * @param {string} param.templateUrl - 模板url
   * @param {number} param.sourceId - 来源
   * @return {Promise}
   */
  async queryPrintData(param) {
    return this.invoke(this.SERVICE_NAME, 'queryPrintData', [param]);
  }

  /**
   * 打印模板获取
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1448484
   * @param {object} param -
   * @param {number} param.kdtId - 商家id
   * @param {number} param.expressId - 快递id
   * @param {number} param.paymentType - 结算方式
   * @param {string} param.brandCode - 品牌code
   * @return {Promise}
   */
  async listPrintTemplate(param) {
    return this.invoke(this.SERVICE_NAME, 'listPrintTemplate', [param]);
  }

  // 零售取消寄件
  async cancelDelivery(param) {
    return this.invoke(this.DELIVERY_SERVICE_NAME, 'cancelDelivery', [param]);
  }
}

module.exports = WaybillService;
