const BaseService = require('services/base');

/* com.youzan.trade.orderexport.api.v2.service.GeneralOrderExportService -  */

class GeneralOrderExportService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.orderexport.api.v2.service.GeneralOrderExportService';
  }

  /**
   *  获取真实url
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/989813
   *
   *  @param {Object} query -
   *  @param {string} query.filename -
   *  @param {string} query.url -
   *  @return {Promise}
   */
  async queryPrivateUrl(query) {
    return this.invokeMethod('queryPrivateUrl', [query]);
  }
}

module.exports = GeneralOrderExportService;
