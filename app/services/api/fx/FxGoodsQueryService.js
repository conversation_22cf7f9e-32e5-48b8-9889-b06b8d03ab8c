const BaseService = require('../../base');

/**
 * 分销商品查询
 */
class FxGoodsQueryService extends BaseService {
  /**
   * FxGoodsQueryService
   */
  get SERVICE_NAME() {
    return 'com.youzan.fx.goods.service.FxGoodsQueryService';
  }

  /**
   * 获取使用此分销运费模版的商品数量
   * api: http://zanapi.qima-inc.com/site/service/view/140363
   * @param {number} kdtId
   * @param {number} deliveryTemplateId 运费模版 id
   */
  async getGoodsNumByFxDeliveryTemplateId(kdtIds, deliveryTemplateId) {
    return await this.invokeMethod('getGoodsNumByFxDeliveryTemplateId', [kdtIds, [deliveryTemplateId]]);
  }
}

module.exports = FxGoodsQueryService;
