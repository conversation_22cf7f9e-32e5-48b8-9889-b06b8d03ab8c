const BaseService = require('../../base');

/**
 * 微信小程序码
 */
class WeappQRCodeService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.channels.apps.service.WeappQRCodeService';
  }

  /**
   * 根据 path, 返回微信小程序码(scene 最长为可见字符 32 位)
   * @param {number} kdtId
   * @return {Promise<Object>}
   * 文档: http://zanapi.qima-inc.com/site/service/view/307439
   */
  async wxaGetCodeUnlimit({ kdtId, page, scene }) {
    return await this.invokeMethod('wxaGetCodeUnlimit', [{ kdtId, page, scene }]);
  }

  /**
   * 无限获取微信小程序码 (scene 字段也无长度限制)
   * @param {Object} params
   * @param {number} params.kdtId - 外侧 kdtId, 微商城和单店, 传自己的, 连锁版, 传总部的
   * @param {boolean} params.hyaLine - 是否透明底色
   * @param {string} params.page - 'pages/common/blank-page/index' 写死的落地页
   * @param {Object} params.params - {kdtId: (微商城和单店, 与外侧保持一致, 大网店与多网店, \
   * 哪个店铺调用就传哪个的 kdtId), guestKdtId: 与内侧保持一致即可, page: 例如 'packages/goods/detail/index'}
   * http://zanapi.qima-inc.com/site/service/view/266047
   * @return {Promise}
   */
  async wxaGetCodeUltra(params) {
    return await this.invokeMethod('wxaGetCodeUltra', [params]);
  }
}

module.exports = WeappQRCodeService;
