const BaseService = require('../../base');

/**
 * 达达自结算相关服务
 */
class DadaSettlementService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.delivery.service.TakeoutChannelService';
  }

  /**
   * 获取授权参数
   * @doc http://zanapi.qima-inc.com/site/service/view/1003304
   * @param {*} params
   */
  async getSettlementParams(params) {
    return await this.invokeMethod('getAuthorizeParams', [params]);
  }

  /**
   * 刷新授权结果
   * @doc http://zanapi.qima-inc.com/site/service/view/1003305
   * @param {*} params
   */
  async refreshSettlement(params) {
    return await this.invokeMethod('refreshAuthorizeResult', [params]);
  }

  /**
   * 解除授权
   * @doc http://zanapi.qima-inc.com/site/service/view/1003306
   * @param {*} params
   */
  async deleteSettlement(params) {
    return await this.invokeMethod('deAuthorize', [params]);
  }
}

module.exports = DadaSettlementService;
