const BaseService = require('../../base');

/**
 * 商品搜索相关接口
 */
class ItemQueryService extends BaseService {
  /**
   * SERVICE_NAME
   */
  get SERVICE_NAME() {
    return 'com.youzan.ic.service.ItemQueryService';
  }
  /**
   * 根据口袋通id和运费模版id分页查询商品列表
   * api: http://zanapi.qima-inc.com/site/service/view/114877
   * @param {object} params
   * @param {number | string} params.kdtId
   * @param {number | string} params.deliveryTemplateId 运费模版id
   * @param {number | string} params.page
   * @param {number | string} params.pageSize 最大 300
   */
  async listSimpleByDeliveryTemplate(params) {
    return await this.invokeMethod('listSimpleByDeliveryTemplate', [params]);
  }
}

module.exports = ItemQueryService;
