const BaseService = require('../../base');

class PcSelfFetchService extends BaseService {
  get SERVICE_NAME() {
    return "com.youzan.ebiz.mall.trade.seller.api.service.selffetch.pc.PcSelfFetchService";
  }

  /**
   *  核销详情页
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/504480
   *
   *  @param {Object} verifyQuerySelfFetch - 查询入参
   *  @param {number} verifyQuerySelfFetch.headKdtId - 总部id
   *  @param {string} verifyQuerySelfFetch.selfFetchNo - 自提码
   *  @param {number} verifyQuerySelfFetch.kdtId - 当前kdtId
   *  @param {number} verifyQuerySelfFetch.adminId - 操作人id
   *  @param {number} verifyQuerySelfFetch.shopId - 网点id
   *  @return {Promise}
   */
  async queryVerifySelfFetch(verifyQuerySelfFetch) {
    return this.invokeMethod("queryVerifySelfFetch", [verifyQuerySelfFetch]);
  }
}

module.exports = PcSelfFetchService;
