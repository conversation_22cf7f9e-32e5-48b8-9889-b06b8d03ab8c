const BaseService = require('../../base');

class ShopAddressServiceV2 extends BaseService {
  /**
   * SERVICE_NAME
   */
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shop.service.address.ShopAddressServiceV2';
  }
  /**
   * 查询默认地址
   * http://zanapi.qima-inc.com/site/service/view/67807
   * https://doc.qima-inc.com/pages/viewpage.action?pageId=42805843
   * @param params
   * @param param.kdtId
   * @param param.addressType 地址类型，1:退货地址，2:发票地址，4:发货地址
   */
  async queryDefaultShopAddress(params) {
    // { raw: true } 参数, 会让 retail-node-base 框架不对 Dubbo 返回的数据进行处理, 直接原样返回
    return await this.invokeMethod('queryDefaultShopAddress', params, { raw: true });
  }
}

module.exports = ShopAddressServiceV2;
