const BaseService = require('services/base');

class ShopBaseInfoService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.outer.service.shop.ShopBaseReadOuterService';
  }

  /**
   * 查询店铺基础信息
   *
   * @param {*} kdtId
   * @returns
   */
  async queryShopBaseInfo(kdtId) {
    return await this.invokeMethod('queryShopBaseInfo', [kdtId]);
  }
}

module.exports = ShopBaseInfoService;
