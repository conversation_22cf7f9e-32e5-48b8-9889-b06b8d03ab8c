const BaseService = require('services/base');

class ShopConfigWriteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopconfig.api.service.ShopConfigWriteService';
  }

  /**
   *  修改单个店铺配置
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/76614 
   *
   *  @param {Object} shopConfigSetRequest - {@link ShopConfigSetRequest}
   *  @param {number} shopConfigSetRequest.kdtId - 店铺kdtId
   *  @param {string} shopConfigSetRequest.value - 配置value
   *  @param {string} shopConfigSetRequest.key - 配置key
   *  @param {Object} shopConfigSetRequest.operator - 操作人信息
   *  @param {string} shopConfigSetRequest.operator.fromApp - 来源应用
   *  @param {string} shopConfigSetRequest.operator.name - 操作人名称
   *  @param {integer} shopConfigSetRequest.operator.id - 操作人账号id
   *  @param {integer} shopConfigSetRequest.operator.type - 操作人账号类型，1商家 2有赞员工 3未知 4系统
   *  @return {Promise}
  */
  async setShopConfig(shopConfigSetRequest) {
    return this.invokeMethod('setShopConfig', [shopConfigSetRequest]);
  }
}

module.exports = ShopConfigWriteService;
