const BaseService = require('../../../base');

/**
 * 商品搜索相关接口
 */
class OnlineDeliveryTemplateApi extends BaseService {
  /**
   * SERVICE_NAME
   */
  get SERVICE_NAME() {
    return 'com.youzan.retail.product.search.api.service.OnlineDeliveryTemplateApi';
  }
  /**
   * 根据 templateIds 查询使用情况
   */
  async countByDeliveryTemplateIds(params) {
    return await this.invokeMethod('countByDeliveryTemplateIds', [params]);
  }
}

module.exports = OnlineDeliveryTemplateApi;
