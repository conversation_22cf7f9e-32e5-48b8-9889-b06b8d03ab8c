const BaseService = require('../../../base');

class HQStoreSearchService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.shop.api.hq.service.HQStoreSearchService';
  }

  async search({ kdtId, shopRoleList, adminId, pageSize, pageNo }) {
    return await this.invoke(this.SERVICE_NAME, 'search', [{ kdtId, shopRoleList, adminId, pageSize, pageNo, retailSource: 'WEB-RETAIL-AJAX' }]);
  }
}

module.exports = HQStoreSearchService;
