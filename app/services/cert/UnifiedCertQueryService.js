const BaseService = require('../base/index');

class UnifiedCertQueryService extends BaseService {
  /**
   * 查询店铺认证状态
   *
   * @param {number} kdtId
   * @returns
   */
  async queryQualMsg(kdtId) {
    const sourceIdType = 'KDT_ID';
    const unifiedCertType = 1;

    const result = await this.payInvoke({
      service: 'youzan.pay.customer.api.cert.UnifiedCertQueryService',
      method: 'queryPrincipalMsg',
      data: {
        sourceId: '' + kdtId, // 接口要求传 string 类型
        sourceIdType,
        unifiedCertType
      }
    });

    return result;
  }
}

module.exports = UnifiedCertQueryService;
