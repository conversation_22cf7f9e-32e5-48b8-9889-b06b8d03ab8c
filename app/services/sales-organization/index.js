const BaseService = require('../base/index');

/**
 * 销售单元相关接口
 */
class SalesOrganizationService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.shop.api.hq.service.HQStoreSearchService';
  }

  async getSalesOrganizationList(requestParams) {
    return await this.invoke(this.SERVICE_NAME, 'searchWithDataPermission', [requestParams]);
  }
}

module.exports = SalesOrganizationService;
