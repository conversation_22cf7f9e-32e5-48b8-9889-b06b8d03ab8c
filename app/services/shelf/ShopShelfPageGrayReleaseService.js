/* com.youzan.retail.trade.misc.api.service.shelf.ShopShelfPageGrayReleaseService -  */

const BaseService = require('services/base');

class ShopShelfPageGrayReleaseService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trade.misc.api.service.shelf.ShopShelfPageGrayReleaseService';
  }

  /**
   *
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1499289
   *
   *  @param {number} kdtId -
   *  @return {Promise}
   */
  async isHit(kdtId) {
    return this.invoke(this.SERVICE_NAME, 'isHit', [kdtId]);
  }
}

module.exports = ShopShelfPageGrayReleaseService;
