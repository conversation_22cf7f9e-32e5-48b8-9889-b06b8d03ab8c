const BaseService = require('services/base');

class StockConfigService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.trade.misc.api.service.TradeStockSettingService';
  }

  /**
   * 查询店铺负库存配置
   * @param {Object} params -
   * @return {Promise}
   */
  async queryStockConfig(params) {
    return this.invoke(this.SERVICE_NAME, 'queryStockSetting', [params]);
  }
}

module.exports = StockConfigService;
