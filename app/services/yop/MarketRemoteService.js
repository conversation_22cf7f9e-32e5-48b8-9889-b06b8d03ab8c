const BaseService = require('services/base');

class MarketRemoteService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.yop.api.MarketRemoteService';
  }

  /**
   * 获取应用的
   * @param {*} kdtId
   * @param {*} appId
   * @returns
   */
  async getAppStatus(kdtId, appId) {
    return await this.invokeMethod('findApplicationStatus', [
      kdtId,
      appId,
    ]);
  }
}

module.exports = MarketRemoteService;
