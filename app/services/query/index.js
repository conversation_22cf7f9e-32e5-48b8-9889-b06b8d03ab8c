const { service } = require('@youzan/retail-node-base');
const BaseService = require('services/base');
const _ = require('lodash');

class QueryService extends BaseService {
  get SHOP_FRONT_SERVICE() {
    return 'com.youzan.salesman.center.application.api.setting.ShopFrontService';
  }

  async getSalesclerkAbility({ kdtId, adminId, appName }) {
    const result = await this.invoke(
      this.SHOP_FRONT_SERVICE,
      'getAbility',
      [
        {
          kdtId,
          adminId,
          retailSource: appName
        }
      ],
      { default: { boughtUltimate: false, ability: 'free' } }
    );

    return result;
  }

  async getIsShowMultiStore(kdtId) {
    return await this.clientCall(
      'trade.order.orderChecker.checkIsSubscribedMultiStore',
      {
        kdt_id: _.toString(kdtId)
      },
      'get',
      {
        default: 0
      }
    );
  }

  async checkApp(kdtId, appId) {
    const result = await new service.yop.MarketService(this.ctx).fetchAppInfo(
      kdtId,
      appId
    );

    if (result && result.effectiveTime) {
      return {
        appName: result.appName,
        appId: result.appId
      };
    }
    return false;
  }

  async getErpInfos(startTime, kdtId) {
    const params = {
      kdtId,
      startTime
    };

    return await this.invoke(
      'com.youzan.retail.trademanager.biz.api.service.erp.ThirdErpOrderService',
      'getERPOrderInfos',
      [params]
    );
  }

  async getShopUpgradeType(kdtId) {
    const params = {
      kdtId,
      status: 2
    };

    return await this.invoke(
      'com.youzan.shopcenter.outer.service.shop.ShopUpgradeReadOuterService',
      'queryShopUpgradeLogs',
      [params]
    );
  }

  /**
   * 获取催付消息渠道
   *  zanAPI文档地址 http://zanapi.qima-inc.com/site/service/view/1101598
   *  @param {Object} request -
   *  @return {Promise}
   */
  async getConversationChannels(request) {
    return await this.invoke(
      'com.youzan.ebiz.mall.trade.seller.api.service.prompt.SellerOrderPromptService',
      'getConversationChannels',
      [request]);
  }

  /**
   * B端商家点击催付是否弹窗进行二次提醒操作
   * ZAN API地址：http://zanapi.qima-inc.com/site/service/view/1093186
   * @param {Object} params
   * @return {Promise<any>}
   */
   async isShowRemindOrderPopups(params) {
     return await this.invoke(
       'com.youzan.ebiz.mall.trade.seller.api.service.order.OrderProcessService',
       'isShowRemindOrderPopups',
       [params]);
   }
  
  /**
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1095313
   * 发送催付通知
   * @param {Object} params
   * @return {Promise}
   */
     async promptOrderToPay(params) {
       return await this.invoke('com.youzan.ebiz.mall.trade.seller.api.service.prompt.SellerOrderPromptService',
         'pushMessagePromptOrderToPay',
         [params]);
    }
}

module.exports = QueryService;
