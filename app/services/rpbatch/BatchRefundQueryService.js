const BaseService = require('services/base');
/**
 * com.youzan.trade.rpbatch.api.query.BatchRefundQueryService -
 **/
class BatchRefundQueryService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.trade.rpbatch.api.query.BatchRefundQueryService';
  }

  /**
   *  校验退款单是否存在微信投诉交互上与批量操作不同，所以单独提供接口
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1469983
   *
   *  @param {Object} params -
   *  @param {Array.<Array>} params.refundIdList[] -
   *  @param {Array} params.refundIdList[] -
   *  @return {Promise}
   */
  async queryHasWeChatComplaint(params) {
    return this.invokeMethod('queryHasWeChatComplaint', [params]);
  }
}

module.exports = BatchRefundQueryService;
