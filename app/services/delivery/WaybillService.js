const BaseService = require('../base/index');

/**
 * WaybillService
 */
class WaybillService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.delivery.service.waybill.WaybillService';
  }

  /**
   * 电子面单版本获取
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1446485
   * @param {number} kdtId - 商家id
   * @return {Promise}
   */
  async getWaybillVersion(kdtId) {
    return this.invoke(this.SERVICE_NAME, 'getWaybillVersion', [kdtId]);
  }
}

module.exports = WaybillService;
