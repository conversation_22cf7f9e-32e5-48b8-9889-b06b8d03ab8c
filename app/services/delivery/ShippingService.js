const BaseService = require('../base/index');

/**
 * com.youzan.secured.SecuredService
 */
class ShippingService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.secured.shipping.api.ShippingService';
  }

  /**
   *  有赞寄件使用反馈
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1596264
   *
   *  @param {Object} feedBackRequest -
   *  @param {number} feedBackRequest.kdtId - 店铺ID
   *  @param {Object} feedBackRequest.operator - 操作员
   *  @param {string} feedBackRequest.operator.operatorPhone -
   *  @param {integer} feedBackRequest.operator.operatorId -
   *  @param {string} feedBackRequest.operator.operatorName -
   *  @param {Array.<Object>} feedBackRequest.feedBackReasonRequestList[] - 反馈意见
   *  @param {string} feedBackRequest.feedBackReasonRequestList[].reason - 反馈原因
   *  @param {string} feedBackRequest.feedBackReasonRequestList[].remark - 备注
   *  @return {Promise}
   */
  async addFeedBack(feedBackRequest) {
    return this.invoke(this.SERVICE_NAME, 'addFeedBack', [feedBackRequest]);
  }
}

module.exports = ShippingService;
