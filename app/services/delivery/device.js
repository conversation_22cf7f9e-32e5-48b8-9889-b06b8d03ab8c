const BaseService = require('../base/index');

/**
 * 供货商相关接口
 */
class DeviceService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.peripheral.api.service.PeripheralApi';
  }

  /**
   * 判断店铺是否新版的设置小票
   * @param {Object} param
   * 文档:
   */
  async getDeviceNew(param) {
    return this.invoke(this.SERVICE_NAME, 'useNewCreateProcess', [param]);
  }
}

module.exports = DeviceService;
