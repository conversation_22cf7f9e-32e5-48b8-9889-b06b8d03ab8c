const BaseService = require('services/base');

class StockService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.retail.ofc.api.service.stock.StockService';
  }

  /**
   * 一键入库接口
   * zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1168949
   *
   * @param {Object} params -
   * @return {Promise}
   */
  async onceReturnStock(params) {
    return this.invoke(this.SERVICE_NAME, 'onceReturnStock', [params]);
  }
}

module.exports = StockService;
