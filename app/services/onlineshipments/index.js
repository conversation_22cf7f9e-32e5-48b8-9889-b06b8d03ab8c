const { service } = require('@youzan/retail-node-base');
const BaseService = require('services/base');
const _ = require('lodash');

class OnlineShipmentsService extends BaseService {
  async getIsShowMultiStore(kdtId) {
    return await this.clientCall(
      'trade.order.orderChecker.checkIsSubscribedMultiStore',
      {
        kdt_id: _.toString(kdtId)
      },
      'get',
      {
        default: 0
      }
    );
  }

  async checkApp(kdtId, appId) {
    const result = await new service.yop.MarketService(this.ctx).fetchAppInfo(
      kdtId,
      appId
    );

    if (result && result.effectiveTime) {
      return {
        appName: result.appName,
        appId: result.appId
      };
    }
    return false;
  }
}

module.exports = OnlineShipmentsService;
