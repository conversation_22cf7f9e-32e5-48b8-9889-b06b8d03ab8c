const BaseService = require('../base');

/**
 * 群团团退款
 */
class QttOrderService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.qtt.order.api.service.OrderServiceProvider';
  }

  /**
   *  判断是否是群团团订单，排除分销采购单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1397024
   *
   *  @param {Object} request -
   *  @param {string} request.orderNo -
   *  @param {number} request.kdtId -
   *  @param {string} request.fromApp -
   *  @param {string} request.operator -
   *  @return {Promise}
   */
  async checkQttOrder(request) {
    return this.invokeMethod('checkQttOrder', [request]);
  }

  /**
   *  PC订单群团团信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1400609
   *
   *  @param {Object} request -
   *  @param {string} request.orderNo - 订单号
   *  @param {number} request.kdtId - 店铺id
   *  @param {string} request.fromApp -
   *  @param {string} request.operator -
   *  @return {Promise}
   */
  async orderDetailForPc(request) {
    return this.invokeMethod('orderDetailForPc', [request]);
  }

  /**
   *  修改备注
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1462743
   *
   *  @param {Object} request -
   *  @param {string} request.orderNo - 订单号
   *  @param {number} request.kdtId - 订单所在的店铺id
   *  @param {string} request.fromApp -
   *  @param {number} request.markType - 备注类型，1备注-团长备注，2备注-私密备注，3备注-买家备注，5备注-团长给团员的备注
   *  @param {string} request.detail - 内容
   *  @param {number} request.operatorId - 操作人id
   *  @param {string} request.operator -
   *  @return {Promise}
   */
  async saveRemarkForPC(request) {
    return this.invokeMethod('saveRemarkForPC', [request]);
  }
}

module.exports = QttOrderService;
