const BaseService = require('../base');

class PartyProvider extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.qtt.note.api.party.IPartyProvider';
  }

  /**
   *  根据店铺id查询对应的partyId
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1361609
   *
   *  @param {number} shopId -
   *  @return {Promise}
   */
  async queryPartyByShopId(shopId) {
    return this.invokeMethod('queryPartyByShopId', [shopId]);
  }
}

module.exports = PartyProvider;
