const BaseService = require('../base');

/**
 * 群团团退款
 */
class QttRefundService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.qtt.order.api.service.RefundServiceProvider';
  }

  /**
   *  校验退款佣金
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1395673
   *
   *  @param {Object} request - 入参
   *  @param {string} request.orderNo - 订单号
   *  @param {number} request.kdtId - 店铺id
   *  @param {string} request.refundId - 退款单号
   *  @return {Promise}
   */
  async checkRefundCommission(request) {
    return this.invokeMethod('checkRefundCommission', [request], {
      timeout: 10000
    });
  }

  /**
   *  只退佣金
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1398670
   *
   *  @param {Object} request - 入参
   *  @param {string} request.orderNo - 订单号
   *  @param {number} request.kdtId - 店铺id
   *  @param {number} request.promoteFee -
   *  @param {boolean} request.refundCommission - 是否退佣金
   *  @param {number} request.inviteFee -
   *  @param {string} request.refundId - 退款单号
   *  @param {number} request.privateFee -
   *  @return {Promise}
   */
  async refundCommission(request) {
    return this.invokeMethod('refundCommission', [request], {
      timeout: 10000
    });
  }

  /**
   *  校验是否可以取消退款
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1398671
   *
   *  @param {Object} request - 入参
   *  @param {string} request.orderNo - 订单号
   *  @param {number} request.kdtId - 店铺id
   *  @param {string} request.refundId - 退款单号
   *  @return {Promise}
   */
  async refuseCheck(request) {
    return this.invokeMethod('refuseCheck', [request], {
      timeout: 10000
    });
  }
}

module.exports = QttRefundService;
