const BaseService = require('../base');

const DEFAULT_QRCODE_OPTIONS = {
  size: 200,
  fg_color: '000000',
  bg_color: 'ffffff',
  case: 1,
  margin: 10,
  level: 0,
  hint: 2,
  ver: 2,
};

/**
 * 二维码生成接口
 */
class QrcodeService extends BaseService {
  /**
   * url
   * @return {*}
   * @constructor
   */
  get URL() {
    return this.getConfig('QRCODE_URL');
  }

  /**
   * 获取二维码
   * 文档：https://doc.qima-inc.com/pages/viewpage.action?pageId=4326255
   * @param {string} text
   * @param {Object} options
   * @return {string}
   */
  async getQrcode(text, options) {
    const qrcode = await this.httpCall({
      url: this.URL,
      dataType: 'stream',
      data: {
        txt: text,
        ...DEFAULT_QRCODE_OPTIONS,
        ...options,
      },
    });

    return qrcode;
  }

  /**
   * 获取二维码(buffer模式)
   * 文档：https://doc.qima-inc.com/pages/viewpage.action?pageId=4326255
   * @param {string} text
   * @param {Object} options
   * @return {stream}
   */
  async qrCode(text, options) {
    const result = await this.httpCall({
      url: this.URL,
      dataType: 'arraybuffer',
      data: {
        txt: text,
        ...DEFAULT_QRCODE_OPTIONS,
        ...options,
      },
    });
    return result;
  }
}

module.exports = QrcodeService;
