const BaseService = require('services/base');
/* com.youzan.shopcenter.outer.service.shop.ShopBaseReadOuterService -  */

class ShopBaseReadOuterService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.outer.service.shop.ShopBaseReadOuterService';
  }

  /**
   *  查询店铺基础信息
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/850202
   *
   *  @param {number} kdtId - 店铺kdtId
   *  @return {Promise}
   */
  async queryShopBaseInfo(kdtId) {
    return this.invokeMethod('queryShopBaseInfo', [kdtId]);
  }
}

module.exports = ShopBaseReadOuterService;
