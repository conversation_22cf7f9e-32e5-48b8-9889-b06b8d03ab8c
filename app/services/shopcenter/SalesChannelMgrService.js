const BaseService = require('services/base');

/** com.youzan.shopcenter.shopfront.api.service.channel.SalesChannelMgrService -  */
class SalesChannelMgrService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.channel.SalesChannelMgrService';
  }

  /**
   *  查询管理单元下属店铺开通的销售渠道聚合（去重）
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1295942
   *
   *  @param {Object} request -
   *  @param {number} request.muKdtId - 管理单元（总部/合伙人）
   *  @param {number} request.kdtId - 当前登录店铺
   *  @param {number} request.adminId - 当前登录员工
   *  @param {boolean} request.isIncludeThirdParty - 是否包含三方渠道
   *  @return {Promise}
   */
  async queryOpenedSalesChannelByMu(request) {
    return this.invoke(this.SERVICE_NAME, 'queryOpenedSalesChannelByMu', [request]);
  }

  /**
   *  查询店铺开通的销售渠道
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1295941
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId - 当前登录的kdt
   *  @param {number} request.adminId - 当前登录的员工
   *  @param {number} request.buKdtId - 分店kdt
   *  @param {boolean} request.isIncludeThirdParty - 是否包含三方渠道
   *  @return {Promise}
   */
  async queryOpenedSalesChannelByBu(request) {
    return this.invoke(this.SERVICE_NAME, 'queryOpenedSalesChannelByBu', [request]);
  }

  /**
   *  是否命中销售渠道白名单
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1295943
   *
   *  @param {number} kdtId - 当前登录店铺
   *  @return {Promise}
   */
   async isHitWhiteList(kdtId) {
    return this.invoke(this.SERVICE_NAME, 'isHitWhiteList', [kdtId]);
  }

}

module.exports = SalesChannelMgrService;
