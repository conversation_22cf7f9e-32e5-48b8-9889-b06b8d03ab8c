const BaseService = require('services/base');

/** com.youzan.shopcenter.shopfront.api.service.operate.OperationCertService -  */
class OperationCertService extends BaseService {
  get SERVICE_NAME() {
    return 'com.youzan.shopcenter.shopfront.api.service.operate.OperationCertService';
  }

  /**
   *  查询操作的认证方式
   *  zanAPI文档地址: http://zanapi.qima-inc.com/site/service/view/1354318
   *
   *  @param {Object} request -
   *  @param {number} request.kdtId -
   *  @param {string} request.operationType - 操作类型
   *  @param {number} request.userId -
   *  @return {Promise}
   */
  async getOperationCertMethod(request) {
    return this.invoke(this.SERVICE_NAME, 'getOperationCertMethod', [request]);
  }
}

module.exports = OperationCertService;
