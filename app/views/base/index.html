{% extends 'common/spa.html' %}

{% block base_style %}
  <!-- DLL 样式 -->
  {{ loadCss('dll/stable') | safe }}
  {{ loadCss('dll/mutable') | safe }}
  {% if commonCssName %}
    <link rel="stylesheet" href="{{ URL.site(commonCssName, 'cdn_static_retail') }}" media="all">
  {% else %}
    <link rel="stylesheet" href="{{ URL.site('/style/common.20190114190915.css', 'cdn_static_retail') }}" media="all">
  {% endif %}
{% endblock %}

{% block base_script %}
  <!-- DLL 脚本 -->
  {{ loadJs('dll/stable') | safe }}
  {{ loadJs('dll/mutable') | safe }}
{% endblock %}