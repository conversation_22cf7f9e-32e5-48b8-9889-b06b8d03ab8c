module.exports = [
  // 退款前请求
  ['GET', '/v2/order/refunddetail/preCheck.json', 'refunddetail.ApiController', 'preCheck'],
  // 发布留言
  ['POST', '/v2/order/refunddetail/message.json', 'refunddetail.ApiController', 'postMessage'],
  // 同意退款
  ['POST', '/v2/order/refunddetail/accept.json', 'refunddetail.ApiController', 'accept'],
  // 拒绝退款
  ['POST', '/v2/order/refunddetail/reject.json', 'refunddetail.ApiController', 'reject'],
  // 确认收货并退款
  ['POST', '/v2/order/refunddetail/sign.json', 'refunddetail.ApiController', 'sign'],
  // 拒绝确认收货(退款流程)
  ['POST', '/v2/order/refunddetail/unsign.json', 'refunddetail.ApiController', 'unsign'],
  // // 同意换货
  // [
  //   'POST',
  //   '/v2/order/refunddetail/exchangeAgree.json',
  //   'refunddetail.ApiController',
  //   'exchangeAgree',
  // ],
  // 拒绝换货
  [
    'POST',
    '/v2/order/refunddetail/exchangeReject.json',
    'refunddetail.ApiController',
    'exchangeReject'
  ],
  // 确认收货并发货(换货流程)
  [
    'POST',
    '/v2/order/refunddetail/deliveryExchangeGoods.json',
    'refunddetail.ApiController',
    'deliveryExchangeGoods'
  ],
  // 拒绝确认收货(换货流程)
  [
    'POST',
    '/v2/order/refunddetail/exchangeUnsign.json',
    'refunddetail.ApiController',
    'exchangeUnsign'
  ],
  // 查询下一单
  // ['GET', '/v2/order/refunddetail/queryNext.json', 'refunddetail.ApiController', 'queryNext'],
  // 一键入库
  ['POST', '/v2/order/refund/pass-to-stock.json', 'refund.IndexController', 'passToStock'],

  [
    'POST',
    '/v2/order/refund/operate/queryHasWeChatComplaint.json',
    'refund.IndexController',
    'queryHasWeChatComplaint',
  ],
];
