// eslint-disable-next-line node/no-extraneous-require
const { registerApp } = require('@youzan/micro-app-plugin');

module.exports = [
  [
    'GET',
    registerApp('@retail-node-order/query', '/v2/order/query'),
    'query.IndexController',
    'getIndexHtml'
  ],
  [
    'GET',
    registerApp('@retail-node-order/return-order', '/v2/order/returnorder'),
    'returnorder.IndexController',
    'getIndexHtml'
  ],

  ['GET', '/v2/order/fetch', 'fetch.IndexController', 'getIndexHtml'],
  ['GET', '/v2/order/onlineshipments', 'onlineshipments.IndexController', 'getIndexHtml'],
  // 批量发货
  ['GET', '/v2/order/batchshipments', 'batchshipments.IndexController', 'getIndexHtml'],
  // 批量发货记录
  [
    'GET',
    '/v2/order/batchshipmentsrecord',
    'batchshipments-record.IndexController',
    'getIndexHtml'
  ],
  ['GET', '/v2/order/refunddetail', 'refunddetail.IndexController', 'getIndexHtml'],
  ['GET', '/v2/order/orderdetail', 'order-detail.IndexController', 'getIndexHtml'],
  ['GET', '/v2/order/fulfilldetail', 'fulfilldetail.IndexController', 'getIndexHtml'],
  ['GET', '/v2/order/guide-delivery', 'guide-delivery.IndexController', 'getIndexHtml'],
  // /v2/order/order-shipment-detail 用于承接 retail-node-fulfillment 过来的流量
  // 为了解决灰度期间，“订单发货” “销售发货” 的侧边栏高亮问题，与 /v2/order/fulfilldetail 是同一页面
  ['GET', '/v2/order/order-shipment-detail', 'fulfilldetail.IndexController', 'getIndexHtml'],
  ['GET', '/v2/order/vicescreen', 'vicescreen.IndexController', 'getIndexHtml'],
  /* 下面的路由很乱
   * 但是历史遗留因素, 又不能删掉, 怕别的地方有引用
   * 所以选择在 console 里打日志, 以便看看有没有流量
   * 如果持续没有流量, 会删掉
   */
  ['GET', '/v2/order/tradesettingsingle', 'trade-setting.IndexController', 'getIndexHtmlAndLog'],
  ['GET', '/v2/order/tradesettingchain', 'trade-setting.IndexController', 'getIndexHtmlAndLog'],
  ['GET', '/v2/order/exportList', 'exportList.IndexController', 'getIndexHtml'],
  ['GET', '/v2/order/api/txtFile', 'query.IndexController', 'getTxtFile'],

  /** 怀疑不再使用 */
  ['GET', '/v2/order/outerdetail', 'outer.IndexController', 'getDetailHtml'],
  /** 怀疑不再使用 */
  ['GET', '/v2/order/refundwhereabouts', 'refund.IndexController', 'getRefundWhereabortsHtml'],
  /** 怀疑不再使用 */
  ['GET', '/v2/order/outerrefund', 'outer.IndexController', 'getRefundDetailHtml'],

  [
    'GET',
    ['/v2/order/delivery', '/v2/order/delivery-rules', '/v2/order/express', '/v2/order/selffetch'],
    'delivery.IndexController',
    'getIndexHtml'
  ],

  // 换货单详情
  ['GET', '/v2/order/exchange/detail', 'exchange.IndexController', 'getIndexHtml'],

  // 配套工具-订单作业管理
  ['GET', '/v2/order/process-setting', 'process-setting.IndexController', 'getIndexHtml'],

  // lite网店信息
  ['GET', '/v2/order/query/getLiteStore.json', 'query.IndexController', 'getLiteStore']
];
