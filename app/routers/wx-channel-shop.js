module.exports = [
  [
    'GET',
    '/v2/order/api/wx-channel-shop/get-multi-plat-express-info.json',
    'wx-channel-shop.IndexController',
    'getMultiPlatExpressInfos'
  ],
  [
    'GET',
    '/v2/order/api/wx-channel-shop/get-multi-plat-page-sites.json',
    'wx-channel-shop.IndexController',
    'getMultiPlatPageSites'
  ],
  [
    'GET',
    '/v2/order/api/wx-channel-shop/query-express-product-types.json',
    'wx-channel-shop.IndexController',
    'queryExpressProductTypes'
  ],
  [
    'GET',
    '/v2/order/api/wx-channel-shop/query-page-monthly-cards.json',
    'wx-channel-shop.IndexController',
    'queryPageMonthlyCards'
  ],
  [
    'GET',
    '/v2/order/api/wx-channel-shop/get-waybill-delivery-template.json',
    'wx-channel-shop.IndexController',
    'getWaybillDeliveryTemplate'
  ],
  [
    'POST',
    '/v2/order/api/wx-channel-shop/create-multi-plat-waybill-order.json',
    'wx-channel-shop.IndexController',
    'createMultiPlayWaybillOrder'
  ],
  [
    'GET',
    '/v2/order/api/wx-channel-shop/get-multi-plat-waybill-order-print-message.json',
    'wx-channel-shop.IndexController',
    'getMultiPlayWaybillOrderPrintMessage'
  ],
  [
    'POST',
    '/v2/order/api/wx-channel-shop/update-multi-plat-waybill-order-print-status.json',
    'wx-channel-shop.IndexController',
    'updateMultiPlayWaybillOrderPrintStatus'
  ],
  // 获取小店订单发货地址
  [
    'GET',
    '/v2/order/api/wx-channel-shop/get-delivery-address-list.json',
    'wx-channel-shop.IndexController',
    'getDeliveryAddressList'
  ]
];
