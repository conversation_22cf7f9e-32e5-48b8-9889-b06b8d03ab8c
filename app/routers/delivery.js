module.exports = [
  ['POST', '/v2/order/delivery/getGoods.json', 'delivery.IndexController', 'getGoods'],
  ['GET', '/v2/order/delivery/getSettingV3.json', 'delivery.IndexController', 'getSettingV3'],
  ['POST', '/v2/order/delivery/updateSetting.json', 'delivery.IndexController', 'updateSetting'],
  ['GET', '/v2/order/delivery/listTemplates.json', 'delivery.IndexController', 'listTemplates'],
  ['POST', '/v2/order/delivery/replicate.json', 'delivery.IndexController', 'replicate'],
  ['POST', '/v2/order/delivery/deleteTemp.json', 'delivery.IndexController', 'deleteTemp'],
  [
    'GET',
    '/v2/order/delivery/getWriteOffConfig.json',
    'delivery.IndexController',
    'getWriteOffConfig'
  ],
  [
    'POST',
    '/v2/order/delivery/setWriteOffConfig.json',
    'delivery.IndexController',
    'setWriteOffConfig'
  ],
  ['GET', '/v2/order/delivery/getScanCodes.json', 'delivery.IndexController', 'getScanCodes'],
  [
    'GET',
    '/v2/order/delivery/queryShopSupplyMode.json',
    'delivery.IndexController',
    'queryShopSupplyMode'
  ],
  // 打印拣货小票
  ['POST', '/v2/order/delivert/printReceipt.json','onlineshipments.IndexController','printReceipt'],
  /** 达达自结算相关接口 */
  [
    'GET',
    '/v2/order/delivery/dada/getSettlementParams.json',
    'delivery.DadaSettlementController',
    'getSettlementParams'
  ],
  [
    'POST',
    '/v2/order/delivery/dada/refreshSettlement.json',
    'delivery.DadaSettlementController',
    'refreshSettlement'
  ],
  [
    'POST',
    '/v2/order/delivery/dada/deleteSettlement.json',
    'delivery.DadaSettlementController',
    'deleteSettlement'
  ],
  /** 达达自结算相关接口 end */
  ['POST', '/v2/order/addFeedBack.json', 'delivery.IndexController', 'addFeedBack'],
];
