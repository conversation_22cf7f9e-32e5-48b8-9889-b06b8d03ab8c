import React, { Component } from 'react';
import { Notify, BlockLoading } from 'zent';
import { isEmpty, find, isNil } from 'lodash';
import { Button as SamButton } from '@youzan/sam-components';
import { isHqStore, isPartnerStore, ShopAbility, isOfflineBranchStore } from '@youzan/utils-shop';
import { request } from '@youzan/retail-utils';
import { checkAbilityValid } from '@youzan/shop-ability';
import { cloud, cloudHook, CloudCtx } from '@youzan/ranta-cloud-react';
import {
  OrderManageListBeforeTableRender,
  OrderManageListBeforeFilter,
  Order,
  OrderManageListBeforeFilterReset
} from '@youzan-cloud/cloud-biz-types';

import useCodeScanner from 'common/hooks/use-code-scanner';
import queryHistory from 'common/query-history';
import OrderList from 'components/order-list';
import EmptyBody from 'components/table-empty-body';

import {
  defaultQueryFilterRf,
  defaultPageInfo,
  defaultSaleWayStore
} from 'common/constants/default';
import withOrderFilter from 'components/order-filter/with-order-filter';
import * as api from 'route/query/api';
import { thirtyDaysTimeValues } from 'common/constants';

import { ShowCustomExportDialog, DefaultOrderTime } from 'route/query/constants';
import { CustomExportDialog } from 'route/query/export/custom-export-dialog';
import Filter from './filter';
import OrderTabs from './tabs';
import config from './config';
import { GuidanceAlert } from '../../compoment/guidance-alert';
import WaimaiProdExpired from '../../compoment/waimai-prod-expried';

const exportLink = '/v2/order/exportList';

const needResetItems = { orderType: { orderState: -1 } };

const hasOfflineAbility = checkAbilityValid(ShopAbility.OfflineStoreManageAbility);

// 总店有销售渠道筛选
if (isHqStore || isPartnerStore) {
  // @ts-ignore
  needResetItems.shopKdtId = {
    buyWay: -1,
    orderType: -1,
    orderState: -1,
    expressType: -1
  };
}

// 默认form value
const defaultFilterValue = {
  ...defaultQueryFilterRf,
  selfFetchId: -1,
  shopKdtIds: [],
  salesId: '',
  marketingType: -1,
  orderTime: DefaultOrderTime
};

if (!isHqStore && !isPartnerStore) {
  // @ts-ignore
  defaultFilterValue.cashierId = -1;
}

@withOrderFilter
@cloud()
export default class ChainOrderList extends Component<any> {
  state = {
    loading: false,
    data: [],
    saleStoreList: defaultSaleWayStore,
    selfFetchStoreList: [],
    salers: [],
    cloudColumns: [],
    ...defaultPageInfo
  };

  init = true;

  orderSource = null;

  ctx!: CloudCtx<ChainOrderList>;

  /**
   * beforeTableRender
   * @desc 订单列表渲染前触发。可以用来修改/增加/减少列
   */
  @cloud('beforeTableRender', 'hook', { allowMultiple: true })
  beforeTableRender = cloudHook<OrderManageListBeforeTableRender>();

  /**
   * beforeFilter
   * @desc 订单筛选前触发
   */
  @cloud('beforeFilter', 'hook', { allowMultiple: true })
  beforeFilter = cloudHook<OrderManageListBeforeFilter>();

  /**
   * orderManageList
   * @desc 订单列表数据
   */
  @cloud('orderManageList', 'data')
  orderManageList: Order[];

  /**
   * beforeFilterReset
   * @desc 订单筛选重置前触发
   */
  @cloud('beforeFilterReset', 'hook', { allowMultiple: true })
  beforeFilterReset = cloudHook<OrderManageListBeforeFilterReset>();

  componentDidMount() {
    // 总部版要请求门店数据
    this.fetchSelfFetchList();
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useCodeScanner((barcode: any, evt: any) => this.listenCodeScanner(barcode, evt));

    window.mark?.log();

    try {
      const { cloud } = this.ctx;

      // 门店这里的列表渲染非标，改造成可以排序的话成本比较高，咨询了三方，暂时不需要排序，后续有需求再加，先支持开放
      // @ts-ignore
      cloud.invoke('beforeTableRender', { columns: [] }).then((configs: any) => {
        const res: any = [];

        if (configs.length > 0) {
          configs.forEach((config: any) => {
            const { columns: returnColumns = [] } = config || {};

            if (!Array.isArray(returnColumns) || returnColumns.length === 0) return;

            returnColumns.forEach(column => {
              if (res.find(v => v.key === column.key)) return;
              const { title, name, width, hidden } = column;
              res.push({
                key: name,
                width,
                hidden,
                title
              });
            });
          });
        }
        this.setState({ cloudColumns: res });
      });
    } catch (err) {
      // error
    }
  }

  fetchSelfFetchList = (alias = '') => {
    request({
      url: 'youzan.retail.trade.selffetchpoint.name/1.0.0/query',
      data: { alias }
    })
      .then((res = []) => {
        const selfFetchStoreList = [
          { text: '全部', value: -1 },
          ...res.map(({ id: value, alias: text }: any) => ({ text, value }))
        ];
        this.setState({ selfFetchStoreList });
      })
      .catch(err => {
        Notify.error(err.msg || '获取提货地址列表失败！');
      });
  };

  // input 监听扫码枪
  listenCodeScanner = (barcode: any, evt: any) => {
    const parentElement = evt && evt.target && evt.target.parentElement;
    if (
      parentElement &&
      parentElement.classList.contains('express-content-item_goods-scan-input')
    ) {
      // 忽略扫码发货
      return;
    }

    api
      .convertBarcode(barcode)
      .then(res => {
        this.props.change('searchLabel', 'order_no');
        this.props.change('searchField', res);
        // @ts-ignore
        if (document.activeElement.name === 'searchField') {
          setTimeout(() => this.fetch());
        }
      })
      .catch(e => Notify.error(e.msg));
  };

  // 清空筛选条件
  onReset = () => {
    queryHistory.setQuery('');
    this.init = true;
    this.setState({ data: [], pageNo: 1 });
  };

  handleBeforeFilterReset = () => {
    const { cloud } = this.ctx;
    return new Promise(resolve => {
      cloud
        .invoke('beforeFilterReset')
        .then(() => resolve(true))
        .catch(() => resolve(false));
    });
  };

  // 获取url上的附加参数
  getExtraParamsFromUrl = () => {
    const {
      tuanId,
      buyer_id: buyerId,
      goodId,
      saleWay,
      isBuGoodsId,
      shopKdtIds
    } = queryHistory.getQuery();
    const extraParams: any = {};

    // 如果有tuanId 则是通过查看同团id按钮进入的
    if (!isNil(tuanId)) {
      extraParams.tuanId = tuanId;
    }

    // 如果有buyerId 则是通过会员页面查看会员订单进入的
    if (!isNil(buyerId)) {
      extraParams.buyerId = buyerId;
    }

    // 如果有goodId 则是通过商品(网店||门店)列表进入的
    if (goodId) {
      extraParams.goodIdList = [goodId];
      // 总部传参销售渠道
      if (isHqStore || isPartnerStore) {
        // @ts-ignore
        extraParams.saleWay = +saleWay;
      }
    }
    if (isBuGoodsId) {
      extraParams.isBuGoodsId = isBuGoodsId;
    }

    if (shopKdtIds) {
      let realShopKdtIds = shopKdtIds || [];
      realShopKdtIds = Array.isArray(realShopKdtIds) ? realShopKdtIds : [realShopKdtIds];
      extraParams.shopKdtIds = realShopKdtIds;
    }

    return extraParams;
  };

  // 筛选
  onSearch = () => {
    const params = {
      pageNo: 1,
      ...this.getExtraParamsFromUrl()
    };

    this.props.saveParamToUrl(params);
    this.fetch(params);
  };

  // 点击弹出报表按钮
  onExport = (exportInfo: any) => this.checkTime() && this.openExportDialog(exportInfo);

  // check时间是否已选
  checkTime() {
    const { startTime, endTime } = this.props.getFilterParams();

    const isValid = startTime && endTime;
    !isValid && Notify.error('请选择下单时间的起止范围!');
    return isValid;
  }

  // 导出报表弹窗
  openExportDialog(exportInfo: any) {
    const filterParams = this.props.getFilterParams();
    const { saleStoreList } = this.state;
    const { shopKdtId, shopKdtIds } = filterParams;

    if (shopKdtIds) {
      let realShopKdtIds = shopKdtIds || [];
      realShopKdtIds = Array.isArray(realShopKdtIds) ? realShopKdtIds : [realShopKdtIds];
      filterParams.shopKdtIds = realShopKdtIds;
    }

    /** url 额外信息 */
    const {
      /**
       * 导出带上隐藏的 buyerId
       * @see https://jira.qima-inc.com/browse/ONLINE-698794
       */
      buyer_id: buyerId
    } = queryHistory.getQuery();
    const urlParams = {
      buyerId
    };

    const displayInfo = {
      ...urlParams,
      ...filterParams,
      ...exportInfo
    };

    const checkedShop = find(saleStoreList, { value: shopKdtId });
    if (checkedShop && (isHqStore || isPartnerStore)) {
      displayInfo.saleWayShopName = checkedShop.text;
    }

    return displayInfo;
  }

  // 无查询url时显示空
  renderEmptyBody = () =>
    this.state.loading ? (
      <BlockLoading loading />
    ) : (
      <EmptyBody
        size="large"
        text={
          <div className="empty-30-search">
            <div className="empty-30-search__text">查看最近30天的订单</div>
            <div className="empty-30-search__btn">
              <SamButton onClick={this.searchLatestThirtyDays} name="马上查看" />
            </div>
          </div>
        }
      />
    );

  // 查询最近30天
  searchLatestThirtyDays = () => {
    this.props.change('orderTime', thirtyDaysTimeValues);
    this.fetch({
      startTime: thirtyDaysTimeValues[0],
      endTime: thirtyDaysTimeValues[1]
    });
  };

  // 请求数据
  fetch = async (params = {}) => {
    const { allValues } = this.props;
    const { pageNo, pageSize } = this.state;
    const ajaxParams = {
      pageNo,
      pageSize,
      ...this.props.getFilterParams(),
      ...this.getExtraParamsFromUrl(),
      ...params
    };

    // 处理三方beforeFilter
    try {
      const { cloud } = this.ctx;
      // @ts-ignore
      const cloudFilterFields: any = await cloud.invoke('beforeFilter');
      if (cloudFilterFields.length > 0) {
        // 初始化 tc_extra
        ajaxParams.tc_extra = {};
      }
      // 多插件，需要处理每一项
      cloudFilterFields.forEach((cloudFilterField: any) => {
        const cloudFilter = cloudFilterField?.customTags || {};
        const customTags: Record<string, any> = {};

        Object.keys(cloudFilter).forEach(cloudKey => {
          const { list } = cloudFilter[cloudKey];
          // 目前只会拿取list的第一个值
          if (list && list.length > 0) {
            const [firstValue] = list; // 使用数组解构获取第一个值
            customTags[cloudKey] = firstValue;
          }
        });

        // 添加自定义标签
        Object.keys(customTags).forEach(tags => {
          ajaxParams.tc_extra[`custom_tags.${tags}`] = customTags[tags];
        });
      });
    } catch (err) {
      // 错误处理可以在这里添加
    }

    this.setState({ loading: true });
    let { shopKdtIds } = ajaxParams;
    if (shopKdtIds && shopKdtIds.length === 1 && shopKdtIds[0] === -1) {
      shopKdtIds = [];
    }
    ajaxParams.shopKdtIds = shopKdtIds;

    api
      .newFetchOrderList(ajaxParams)
      .then((resp = {}) => {
        try {
          const { data = [] } = resp as any;
          this.orderManageList = data.map((item: any) => {
            const { orderNo, extra } = item?.mainOrderInfo || {};
            const { customTags } = extra || {};
            return {
              orderNo,
              customTags
            };
          });
        } catch (err) {
          // 错误处理可以在这里添加
        }
        // @ts-ignore
        !isEmpty(resp) && this.setState(resp);
      })
      .catch(err => {
        Notify.error(err.msg || '数据请求失败！');
      })
      .finally(() => {
        this.init = false;
        this.orderSource = allValues.orderSource;
        this.setState({ loading: false });
      });
  };

  // 翻页操作
  handlePageChange = ({ current }: { current: number }) => {
    this.props.saveParamToUrl({ pageNo: current });
    this.setState(
      {
        pageNo: current,
        ...this.getExtraParamsFromUrl()
      },
      this.fetch
    );
  };

  handleNewOrderCreate = () => {
    this.props.history.push('/new-order');
  };

  onSelffetchPointFilterChange = (key: string) => {
    this.fetchSelfFetchList(key);
  };

  onSelffetchPointChange = () => {
    this.fetchSelfFetchList();
  };

  render() {
    const { allValues } = this.props;
    const {
      pageNo: current,
      pageSize,
      total,
      loading,
      data,
      saleStoreList,
      // @ts-ignore
      pureSaleStores,
      selfFetchStoreList,
      salers,
      cloudColumns
    } = this.state;
    const pageInfo = {
      current,
      pageSize,
      total,
      onChange: this.handlePageChange
    };

    const showEmpty = data.length === 0 && this.init && isEmpty(queryHistory.getQuery());

    return (
      <>
        {isOfflineBranchStore && <WaimaiProdExpired />}
        <GuidanceAlert />
        {isHqStore && hasOfflineAbility && (
          <SamButton
            className="new-order-btn"
            type="primary"
            name="新建订单"
            hide
            onClick={this.handleNewOrderCreate}
          >
            新建订单
          </SamButton>
        )}
        <Filter
          onSearch={this.onSearch}
          onReset={this.onReset}
          onBeforeReset={this.handleBeforeFilterReset}
          onExport={this.onExport}
          exportLink={exportLink}
          loading={loading}
          defaultValue={defaultFilterValue}
          allValues={allValues}
          needResetItems={needResetItems}
          saleStoreList={saleStoreList}
          selfFetchStoreList={selfFetchStoreList}
          salers={salers}
          pureSaleStores={pureSaleStores}
          onSelffetchPointFilterChange={this.onSelffetchPointFilterChange}
          onSelffetchPointChange={this.onSelffetchPointChange}
          filterRange="query"
          renderCustomExportDialog={
            ShowCustomExportDialog
              ? ({ getFilterDisplayInfo }: Record<string, any>) => (
                  <CustomExportDialog getFilterDisplayInfo={getFilterDisplayInfo} />
                )
              : null
          }
        />

        <OrderTabs
          orderType={allValues?.orderType}
          orderState={allValues?.orderState}
          orderSource={allValues?.orderSource}
          onChange={value => {
            this.props.change('orderState', value);
            this.fetch({ orderState: value });
          }}
        />

        {showEmpty ? (
          this.renderEmptyBody()
        ) : (
          // @ts-ignore
          <OrderList
            config={config as any}
            listData={data}
            loading={loading}
            pageInfo={pageInfo as any}
            cloudColumns={cloudColumns}
            reload={this.fetch}
            orderSourceParam={this.orderSource as any}
          />
        )}
      </>
    );
  }
}
