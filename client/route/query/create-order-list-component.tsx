import React, { Component } from 'react';
import { Notify, BlockLoading } from 'zent';
import { Button as SamButton } from '@youzan/sam-components';
import { isEmpty, find, pick, keys } from 'lodash';
import { global } from '@youzan/retail-utils';
import { Loader } from '@youzan/content-loader-react';
import { cloud, cloudHook, CloudCtx } from '@youzan/ranta-cloud-react';
import {
  OrderManageListBeforeTableRender,
  OrderManageListBeforeFilter,
  Order
} from '@youzan-cloud/cloud-biz-types';

import { IOrderListConfig } from 'definition/order-list-config';
import { IFilterConfig } from 'definition/filter-config';
import { IOrderListPageInfo } from 'definition/order-list';

import useCodeScanner from 'common/hooks/use-code-scanner';
import queryHistory from 'common/query-history';
import { thirtyDaysTimeValues, needResetItems } from 'common/constants';
import { defaultQueryFilterRf, defaultPageInfo } from 'common/constants/default';
import { ChannelType } from 'common/constants/common';

import OrderList from 'components/order-list';
import Filter from 'components/order-filter/filter';
import EmptyBody from 'components/table-empty-body';
import withOrderFilter from 'components/order-filter/with-order-filter';
import { WithOrderFilterProps } from 'components/order-filter/types';

import * as api from 'route/query/api';

import { AlertAdvertisement } from '@youzan/payback-ads';
import '@youzan/payback-ads/lib/index.css';

import { GuidanceAlert } from './compoment/guidance-alert';
import { CustomExportDialog } from './export/custom-export-dialog';
import { ShowCustomExportDialog } from './constants';

const { KDT_ID } = global;

const exportLink = '/v2/order/exportList';
const extraParamsKeys = ['tuanId', 'buyer_id', 'saleWay', 'goodIdList'];

const initPageInfo = {
  ...defaultPageInfo,
  pageNo: queryHistory.getQuery().pageNo
};

export default function createOrderListComponent({
  config,
  filterConfig,
  searchType,
  defaultFilterValue = defaultQueryFilterRf,
  shouldShowGuidanceAlert = true,
  shouldShowShopOpenGift = false
}: {
  /** 表格配置 */
  config: IOrderListConfig;
  /** 筛选表单配置 */
  filterConfig: IFilterConfig;
  /** 订单搜索配置 */
  searchType: string;
  /** 筛选表单默认值 */
  defaultFilterValue?: Record<string, any>;
  /** 是否显示广告 */
  shouldShowGuidanceAlert?: boolean;
  /** 是否显示开店增值礼包-转付费弹窗 */
  shouldShowShopOpenGift?: boolean;
}) {
  @withOrderFilter
  @cloud()
  class List extends Component<WithOrderFilterProps> {
    state = {
      loading: false,
      data: [],
      ...initPageInfo,
      storeList: [],
      cashierList: [],
      salers: [],
      dialogVisible: false,
      cloudColumns: []
    };

    init = true;

    orderSource = null;

    ctx!: CloudCtx<any>;

    /**
     * beforeTableRender
     * @desc 订单列表渲染前触发。可以用来修改/增加/减少列
     */
    @cloud('beforeTableRender', 'hook', { allowMultiple: true })
    beforeTableRender = cloudHook<OrderManageListBeforeTableRender>();

    /**
     * beforeFilter
     * @desc 订单筛选前触发
     */
    @cloud('beforeFilter', 'hook', { allowMultiple: true })
    beforeFilter = cloudHook<OrderManageListBeforeFilter>();

    /**
     * orderManageList
     * @desc 订单列表数据
     */
    @cloud('orderManageList', 'data')
    orderManageList: Order[];

    componentDidMount() {
      this.fetchFilterData();
      // eslint-disable-next-line
      useCodeScanner((barcode: string, evt: KeyboardEvent) => this.listenCodeScanner(barcode, evt));

      window.mark?.log();

      try {
        const { cloud } = this.ctx;

        // 门店这里的列表渲染非标，改造成可以排序的话成本比较高，咨询了三方，暂时不需要排序，后续有需求再加，先支持开放
        // @ts-ignore
        cloud.invoke('beforeTableRender', { columns: [] }).then((configs: any[]) => {
          const res: any[] = [];

          if (configs.length > 0) {
            configs.forEach((config: any) => {
              const { columns: returnColumns = [] } = config || {};

              if (!Array.isArray(returnColumns) || returnColumns.length === 0) return;

              returnColumns.forEach((column: any) => {
                if (res.find(v => v.key === column.key)) return;
                const { title, name, width, hidden } = column;
                res.push({
                  name,
                  width,
                  hidden,
                  title
                });
              });
            });
          }
          this.setState({ cloudColumns: res });
        });
      } catch (err) {
        // error
      }
    }

    // input 监扫码枪
    listenCodeScanner = (barcode: string, evt: KeyboardEvent) => {
      const parentElement = evt && evt.target && (evt.target as HTMLElement).parentElement;
      if (
        parentElement &&
        parentElement.classList.contains('express-content-item_goods-scan-input')
      ) {
        // 忽略扫码发货
        return;
      }

      api
        .convertBarcode(barcode)
        .then(res => {
          this.props.change('searchLabel', 'order_no');
          this.props.change('searchField', res);
          if ((document.activeElement as HTMLInputElement).name === 'searchField') {
            setTimeout(() => this.fetch());
          }
        })
        .catch(e => Notify.error(e.msg));
    };

    fetchFilterData = () => {
      const salesmanQuery = {
        pageSize: 300,
        roleIds: JSON.stringify([18]),
        biz: 'retail',
        pageNo: 1,
        subKdtId: KDT_ID
      };
      api
        .fetchSalesman(salesmanQuery)
        .then((salersData: Record<string, any>) => {
          this.setState({
            salers: salersData.items
          });
        })
        .catch(err => {
          Notify.error(err.msg || '获取数据失败!');
        });
    };

    /**
     * 获取url上的附加参数
     * @memberof List
     */
    getExtraParamsFromUrl = () => {
      const urlQueryData = queryHistory.getQuery();
      if (urlQueryData.goodId) {
        urlQueryData.goodIdList = [urlQueryData.goodId];
      }
      return pick(urlQueryData, extraParamsKeys);
    };

    // 筛选
    onSearch = (useCurrentPage: boolean) => {
      const pageNo = useCurrentPage === true ? this.state.pageNo : 1;
      this.props.saveParamToUrl({ pageNo });
      this.setState(() => ({ pageNo }), this.fetch);
    };

    // 点击弹出报表按钮
    onExport = (exportInfo: Record<string, any>) =>
      this.checkTime() && this.openExportDialog(exportInfo);

    // check时间是否已选
    checkTime() {
      const { startTime, endTime } = this.props.getFilterParams();
      const isValid = startTime && endTime;
      !isValid && Notify.error('请选择起止时间!');
      return isValid;
    }

    // 导出报表弹窗
    openExportDialog(exportInfo: Record<string, any> = {}) {
      const { salers } = this.state;
      const filterParams = this.props.getFilterParams();
      const { salesId } = filterParams;
      const salesInfo = (find(salers, { adminId: salesId }) || {}) as Record<string, any>;

      /** url 额外信息 */
      const {
        /**
         * 导出带上隐藏的 buyerId
         * @see https://jira.qima-inc.com/browse/ONLINE-698794
         */
        buyer_id: buyerId
      } = queryHistory.getQuery();
      const urlParams = {
        buyerId
      };

      return {
        ...urlParams,
        ...filterParams,
        cashierName: exportInfo.cashierName,
        storeName: exportInfo.storeName,
        salesName: salesInfo.staffName || '',
        tableIdName: exportInfo.tableIdName
      };
    }

    // 无查询url时显示空
    renderEmptyBody = () => {
      return this.state.loading ? (
        <BlockLoading loading />
      ) : (
        <EmptyBody
          size="large"
          text={
            <div className="empty-30-search">
              <div className="empty-30-search__text">查看最近30天的订单</div>
              <div className="empty-30-search__btn">
                <SamButton onClick={this.searchLatestThirtyDays} name="马上查看" />
              </div>
            </div>
          }
        />
      );
    };

    // 查询最近30天
    searchLatestThirtyDays = () => {
      this.props.change('orderTime', thirtyDaysTimeValues);
      setTimeout(() => this.fetch());
    };

    // 请求数据
    fetch = async (params = {}) => {
      this.setState({ loading: true });

      const { pageNo, pageSize } = this.state;

      // 使用 Record<string, any> 类型来允许动态添加属性
      const ajaxParams: Record<string, any> = {
        pageNo,
        pageSize,
        ...this.props.getFilterParams(),
        ...this.getExtraParamsFromUrl(),
        ...params
      };

      // 处理三方beforeFilter
      try {
        const { cloud } = this.ctx;
        // @ts-ignore
        const cloudFilterFields: any = await cloud.invoke('beforeFilter');
        if (cloudFilterFields.length > 0) {
          // 初始化 tc_extra
          ajaxParams.tc_extra = {};
        }
        // 多插件，需要处理每一项
        cloudFilterFields.forEach((cloudFilterField: any) => {
          const cloudFilter = cloudFilterField?.customTags || {};
          const customTags: Record<string, any> = {};

          Object.keys(cloudFilter).forEach(cloudKey => {
            const { list } = cloudFilter[cloudKey];
            // 目前只会拿取list的第一个值
            if (list && list.length > 0) {
              const [firstValue] = list; // 使用数组解构获取第一个值
              customTags[cloudKey] = firstValue;
            }
          });

          // 添加自定义标签
          Object.keys(customTags).forEach(tags => {
            ajaxParams.tc_extra[`custom_tags.${tags}`] = customTags[tags];
          });
        });
      } catch (err) {
        // 错误处理可以在这里添加
      }

      api
        .newFetchOrderList(ajaxParams)
        .then((resp: Record<string, any> = {}) => {
          try {
            const { data = [] } = resp;
            this.orderManageList = data.map((item: any) => {
              const { orderNo, extra } = item?.mainOrderInfo || {};
              const { customTags } = extra || {};
              return {
                orderNo,
                customTags
              };
            }) as unknown as Order[];
          } catch (err) {
            // 错误处理可以在这里添加
          }
          this.setState(resp);
        })
        .catch(err => {
          Notify.error(err.msg || '数据请求失败！');
        })
        .finally(() => {
          this.init = false;
          this.orderSource = this.props.allValues.orderSource;
          this.setState({ loading: false });
        });
    };

    onReset = () => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      queryHistory.setQuery('');
      this.init = true;
      this.setState({ data: [], pageNo: 1 });
    };

    // 翻页操作
    handlePageChange = ({ current }: IOrderListPageInfo) => {
      this.props.saveParamToUrl({ pageNo: current });
      this.setState({ pageNo: current }, this.fetch);
    };

    render() {
      const { loading, data, salers, cloudColumns } = this.state;
      const { allValues = {} } = this.props;
      const showEmpty = data.length === 0 && this.init && isEmpty(queryHistory.getQuery());
      let saleWayKey;
      if (ChannelType[allValues.saleWay]) {
        saleWayKey = ChannelType[allValues.saleWay].toUpperCase();
      }

      return (
        <>
          {shouldShowGuidanceAlert ? (
            <AlertAdvertisement needAuth source="retail-order-list">
              <GuidanceAlert />
            </AlertAdvertisement>
          ) : null}
          <Filter
            onSearch={this.onSearch}
            onReset={this.onReset}
            onExport={this.onExport}
            exportLink={exportLink}
            loading={loading}
            allValues={allValues}
            defaultValue={defaultFilterValue}
            needResetItems={needResetItems}
            salers={salers}
            searchType={searchType}
            saleWay={saleWayKey}
            fieldConfig={filterConfig}
            filterRange="query"
            renderCustomExportDialog={
              ShowCustomExportDialog
                ? ({ getFilterDisplayInfo }: Record<string, any>) => (
                    <CustomExportDialog getFilterDisplayInfo={getFilterDisplayInfo} />
                  )
                : null
            }
            shouldInitSubmitAnyway
          />
          {showEmpty ? (
            this.renderEmptyBody()
          ) : (
            <OrderList
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              config={config}
              listData={data}
              loading={loading}
              cloudColumns={cloudColumns}
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              pageInfo={{
                ...pick(this.state, keys(defaultPageInfo)),
                onChange: this.handlePageChange
              }}
              reload={this.fetch}
              orderSourceParam={this.orderSource!}
            />
          )}
          {shouldShowShopOpenGift ? (
            <>
              <Loader url="/v4/assets/output" content="security/shop-open-gift" />
              <Loader
                url="/v4/assets/output"
                content="security/guarantee-subsidy-ad-dialog"
                props={{
                  sceneNo: 'ORDER_LIST_POPUP'
                }}
              />
            </>
          ) : null}
        </>
      );
    }
  }

  return List;
}
