// 订单列表
import { createMicroApp } from 'common/fns/create';
import React from 'react';
import ReactDOM from 'react-dom';
import { provide } from '@youzan/content-loader-react';

import Routers, { NewLayoutAppHeader } from './routers';

import './style.scss';

provide('React', React);
provide('ReactDOM', ReactDOM);

createMicroApp({
  App: Routers,
  withRegisterForm: true,
  AppHeader: NewLayoutAppHeader,
  name: 'store_order_query'
});
