import * as React from 'react';
import { But<PERSON>, InlineLoading, <PERSON> } from 'zent';
import { formatDatetime, setUrlDomain } from '@youzan/retail-utils';
import { BatchTaskStatus } from '@youzan/zan-hasaki';
import { ExpressType } from 'common/constants/common';
import { ReportList } from '@youzan/export-center';
import queryHistory from 'common/query-history';
import { WaybillVersionEnum } from '@youzan/order-domain-pc-components/es/delivery-dialog/types';
import { isRetailChainStore, isRetailHqStore } from '@youzan/utils-shop';
import TaskDetailDialog from '../../batchshipments/batch-choose/task-detail-dialog';
import {
  queryBatchtaskListUrl,
  queryBatchtaskDetailUrl,
  selectDeliveryQueryBatchtaskListUrl,
  selectDeliveryQueryBatchtaskDetailUrl
} from './api';
import '../index.scss';

interface IBaseReportData {
  /** 任务ID */
  batchTaskId: string;
  /** 任务状态 */
  batchTaskStatus: number;
  /** 申请人 */
  operatorName: string;
  /** 总任务数量 */
  deliverTotalCount: number;
  /** 等待任务数量 */
  deliverWaitCount: number;
  /** 成功任务数量 */
  deliverSuccessCount: number;
  /** 失败任务数量 */
  deliverFailureCount: number;
  /** 创建时间 */
  createTime: number;
  /** 发货时间 */
  deliverTime: number;
  [key: string]: any;
  exportParams: any;
}

interface IReportData extends IBaseReportData {
  exportParams: {
    text: string;
    value: string | number;
  }[];
}

export function ShipmentList() {
  const { expressType } = queryHistory.getQuery();
  let listUrl = queryBatchtaskListUrl;
  let queryUrl = queryBatchtaskDetailUrl;
  if (expressType === ExpressType.City) {
    listUrl = queryBatchtaskListUrl;
    queryUrl = queryBatchtaskDetailUrl;
  } else if ([ExpressType.SelfFetch, ExpressType.Express].includes(expressType as any)) {
    listUrl = selectDeliveryQueryBatchtaskListUrl;
    queryUrl = selectDeliveryQueryBatchtaskDetailUrl;
  }
  const isExpress = expressType === ExpressType.Express;
  // 零售连锁总部直接展示，否则根据当前电子面单版本判断
  const isShowNewBatchDeliveryEntrance =
    isExpress &&
    ((isRetailChainStore && isRetailHqStore) || _global.waybillVersion === WaybillVersionEnum.New);

  const formatReportData = (data: IBaseReportData): IReportData => {
    return {
      ...data,
      serialNo: data.batchTaskId,
      exportParams: [
        {
          text: '订单数量：',
          value: data.deliverTotalCount
        },
        {
          text: '发货时间：',
          value: formatDatetime(data.deliverTime) || '-'
        }
      ]
    };
  };

  function handleQueryParams(params: { serialNo: string }) {
    return {
      batchTaskId: params.serialNo
    };
  }

  function handleFetchParams(params: { pageNo: number; pageSize: number }) {
    return {
      ...params,
      expressType
    };
  }

  const renderHeader = (data: IReportData): React.ReactNode => {
    return (
      <>
        <div>创建时间：{formatDatetime(data.createTime) || '-'}</div>
        <div>申请人：{data.operatorName}</div>
      </>
    );
  };

  const renderFooter = (data: IReportData, extra: any): React.ReactNode => {
    const {
      batchTaskId,
      batchTaskStatus,
      deliverTotalCount,
      deliverWaitCount,
      deliverSuccessCount,
      deliverFailureCount
    } = data;

    const { query } = extra;

    return (
      <>
        {batchTaskStatus === BatchTaskStatus.Processing ? (
          <div className="export-item_footer_text">
            <span className="export-item_footer_text_icon">
              <InlineLoading loading icon="circle" iconSize={16} />
            </span>
            订单批量发货中{' '}
            <b>
              {deliverTotalCount - deliverWaitCount}/{deliverTotalCount}
            </b>
            <Link
              href={undefined}
              onClick={() => {
                query();
              }}
            >
              刷新
            </Link>
          </div>
        ) : (
          <div className="export-item_footer_text">
            批量发货完成，共 <b>{deliverTotalCount}</b> 个发货单，发货成功{' '}
            <b>{deliverSuccessCount}</b> 个
            {!!deliverFailureCount && (
              <>
                ，发货失败 <b className="export-item_footer_text_error">{deliverFailureCount}</b> 个
              </>
            )}
            。
          </div>
        )}
        <div className="export-item_footer_operation">
          <TaskDetailDialog batchTaskId={batchTaskId} expressType={expressType as ExpressType}>
            {({ onClick }) => (
              <Button
                type="primary"
                onClick={() => {
                  onClick();
                }}
              >
                查看详情
              </Button>
            )}
          </TaskDetailDialog>
        </div>
      </>
    );
  };

  return (
    <div>
      {isShowNewBatchDeliveryEntrance && (
        <div style={{ width: '1140px', margin: '0 auto 12px' }}>
          本页仅展示【批量无需物流】操作记录,
          <Link
            onClick={() => {
              window.open(setUrlDomain(`/v4/trade/delivery/batch`, 'store'));
            }}
          >
            点此查看【批量在线下单】操作记录
          </Link>
        </div>
      )}
      <ReportList
        title=""
        desc=""
        emptyData="暂无订单批量发货记录"
        listUrl={listUrl}
        queryUrl={queryUrl}
        formatReportData={formatReportData}
        handleQueryParams={handleQueryParams}
        handleFetchParams={handleFetchParams}
        renderHeader={renderHeader}
        renderFooter={renderFooter}
      />
    </div>
  );
}
