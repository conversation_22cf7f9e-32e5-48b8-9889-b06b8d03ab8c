import React, { useState, useCallback, useMemo, useEffect } from 'react';
import { HashRouter as Router } from 'react-router-dom';
import { Notify, BlockLoading, Placeholder } from 'zent';
import { get, isNil } from 'lodash';
import { Nav, PageBlock } from '@youzan/retail-components';
import { isRetailSingleStore, isPHShop } from '@youzan/utils-shop';
import { AlertAdvertisement } from '@youzan/payback-ads';
import { Loader } from '@youzan/content-loader-react';
import '@youzan/payback-ads/lib/index.css';

import queryHistory from 'common/query-history';
import { checkIsNewFxOrder } from 'common/helper';
import { orderTypeValue } from 'common/constants/models';
import { createAppHeaders } from 'common/fns/new-layout';

import { OrderBasicInfo } from 'components/details-cpn/basic-info';
import OrderState from 'components/details-cpn/order-state';
import OrderInfo from 'components/details-cpn/order-info';
import GoodsList from 'components/details-cpn/goods-list';
import QttGroupInfo from 'components/details-cpn/qtt-group-info';
import FxGoodsList from 'components/details-cpn/fx-goods-list';
import PackInfo from 'components/details-cpn/pack-info';
import ChildList from 'components/details-cpn/child-list';
import PaymentInfo from 'components/details-cpn/payment-info';
import VirtualTickets from 'components/details-cpn/virtual-tickets';

import * as processSettingApi from 'route/process-setting/api';

import * as api from './api';
import './style.scss';

const { Container, Page } = PageBlock;
const { GIFT } = orderTypeValue;

const navs = [{ name: '订单详情', path: '/' }];

function AppNav() {
  return <Nav list={navs} type="bread" />;
}

const { NewLayoutAppHeader, AppHeader } = createAppHeaders({
  Nav: AppNav,
  Router
});

export { NewLayoutAppHeader };

const OrderStateConfig = isPHShop
  ? {
      withRemark: false,
      withStar: false,
      disabledOperationList: [/** 屏蔽钱款去向 */ 'refund_fund']
    }
  : {};

const isShowShopOpenGift = isRetailSingleStore;

function OrderDetail() {
  const [needRequest, setNeedRequest] = useState(true);
  const [orderInfo, setOrderInfo] = useState({});
  const [refundInfo, setRefundInfo] = useState({});
  const [loading, setLoading] = useState(true);
  const [isQttOrder, setIsQttOrder] = useState(false);
  const [qttOrderInfo, setQttOrderInfo] = useState({});

  // 重置请求
  const reload = useCallback(() => setNeedRequest(true), []);

  // 请求订单详情
  useEffect(() => {
    if (!needRequest) return;

    const orderNo = get(queryHistory.getQuery(), 'order_no');

    if (!orderNo) {
      // 没有订单号，跳转到订单列表
      setTimeout(() => {
        window.location.href = '/v2/order/query';
      }, 1500);

      return Notify.error('没有传入订单号');
    }

    setLoading(true);

    Promise.all([
      api.fetchRefundInfo({ orderNo }),
      api.fetchOrderDetail({ orderNo, needExchangeItemInfo: false }),
      api.checkQttOrder({ orderNo })
    ])
      .then(res => {
        setRefundInfo(res?.[0].data ?? []);
        setIsQttOrder(res?.[2]);

        const orderDetail = res?.[1] ?? {};

        // 是否换货2.0
        const isOfflineExchangeFlowV2 = !isNil(
          orderDetail?.mainOrderInfo?.extra?.aTTR_OFFLINE_EXCHANGE_FLOW
        );
        if (
          isOfflineExchangeFlowV2 &&
          Array.isArray(orderDetail?.itemInfo) &&
          orderDetail?.itemInfo.length > 0
        ) {
          orderDetail.itemInfo = orderDetail.itemInfo.filter(item => !item.isExchangeItem);
        }

        setOrderInfo(orderDetail);

        // 如果是群团团订单，再请求群团团的详情
        if (res?.[2]) {
          api.getOrderDetailForPc({ orderNo }).then(res => {
            setQttOrderInfo(res);
          });
        }
      })
      .catch(err => {
        Notify.error(err.msg || '订单详情数据获取失败');
      })
      .finally(() => {
        setLoading(false);
        setNeedRequest(false);
      });
  }, [needRequest]);

  const showRemind = () => {
    const { invoiceProviderList = [] } = _global.business || {};
    return !invoiceProviderList.length;
  };

  const orderType = useMemo(() => get(orderInfo, 'mainOrderInfo.orderType'), [orderInfo]);
  const isNewFxOrder = useMemo(() => checkIsNewFxOrder(orderInfo), [orderInfo]);

  const [isProcessSettingOn, setIsProcessSettingOn] = useState(true);

  useEffect(() => {
    processSettingApi.getProcessSettingConfig().then(data => {
      setIsProcessSettingOn(data);
    });
  }, []);

  useEffect(() => {
    window.mark?.log();
  }, []);

  return (
    <Page>
      <Router>
        <AppHeader />
      </Router>
      {/* {showRemind() && <Alert style={{ margin: '16px 16px 0' }} type="warning">电子发票服务已支持开具全电发票，如需使用，可订购票易通发票 <a href="//yingyong.youzan.com/cloud-app-detail/10003280"  target="_blank">去订购</a></Alert>} */}
      <Container>
        {loading ? (
          <BlockLoading loading={loading}>
            <Placeholder.TextBlock rows={20} dashed />
          </BlockLoading>
        ) : (
          <>
            <AlertAdvertisement needAuth source="retail-order-detail" />
            <OrderBasicInfo orderInfo={orderInfo} />
            {/* eslint-disable-next-line react/jsx-props-no-spreading */}
            <OrderState
              {...OrderStateConfig}
              orderInfo={orderInfo}
              qttOrderInfo={qttOrderInfo}
              reload={reload}
              isQttOrder={isQttOrder}
            />
            <OrderInfo orderInfo={orderInfo} reload={reload} isShowTip={showRemind()} />
            <PackInfo orderInfo={orderInfo} reload={reload} />
            {orderType === GIFT && <ChildList orderInfo={orderInfo} reload={reload} />}
            {/* 群团团订单信息 */}
            {!isNewFxOrder && isQttOrder && qttOrderInfo.note && (
              <QttGroupInfo orderInfo={orderInfo} qttOrderInfo={qttOrderInfo} reload={reload} />
            )}
            {isNewFxOrder ? (
              <FxGoodsList
                orderInfo={orderInfo}
                refundInfo={refundInfo}
                reload={reload}
                isProcessSettingOn={isProcessSettingOn}
              />
            ) : (
              <GoodsList
                orderInfo={orderInfo}
                refundInfo={refundInfo}
                reload={reload}
                isProcessSettingOn={isProcessSettingOn}
              />
            )}
            <PaymentInfo
              orderInfo={orderInfo}
              isNewFxOrder={isNewFxOrder}
              isQttOrder={isQttOrder}
            />
            <VirtualTickets data={orderInfo?.virtualTicketInfoList ?? []} />
            {isShowShopOpenGift && (
              <>
                <Loader
                  url="/v4/assets/output"
                  content="security/shop-open-gift"
                  props={{ pageSource: 'order-detail' }}
                />
                <Loader
                  url="/v4/assets/output"
                  content="security/guarantee-subsidy-ad-dialog"
                  props={{
                    sceneNo: 'ORDER_DETAIL_POPUP'
                  }}
                />
              </>
            )}
          </>
        )}
      </Container>
    </Page>
  );
}

export default OrderDetail;
