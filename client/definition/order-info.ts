/** 不完善的自己补充一下 */
import type {
  RefundOrderInfo,
  RefundStateAssemblyList
} from 'components/opt-components/buyer-refund/content/interface';
import { DeliveryChannelType } from 'common/constants/common';
import { ExchangeFlowType } from 'common/constants/order';

type OrderNo = string;
type KdtId = number;
type Unit = string;
type Name = string;
type Num = number;

export interface IRetailSku {
  requestSkuId: number;
  unit: Unit;
  /** e.g. 'P200221120818952' */
  skuNo: string;
  itemId: number;
  retailSkuId: number;
  price: number;
  num: Num;
  stockNum: number;
  combineNumRatio: {
    relatedCombineNum: number;
    combineNum: number;
  };
  /** e.g. 'qiao商品库商品' */
  name: Name;
  stockMeter: number;
  /** e.g. '1.1' */
  specifications: string;
}

export type IRetailSkuList = IRetailSku[];

/** 是否发货, 不传即不显示发货标识 */
export enum ExpressStateEnum {
  /** 未发货 */
  UnDelivered = 0,
  /** 已发货 */
  Delivered = 1,
  /** 第三方配送发货状态(同城配送属于第三方配送) */
  ThirdParty = 2
}

export enum EOrderItemProcessErrorCode {
  Manufacture = 'manufacture',
  AllotList = 'allot_list',
  Allot = 'allot',
  Bom = 'bom'
}

export enum ExchangeStatus {
  /** 换货成功 */
  ExchangeSuccess = 1,
  Exchanging = 0
}

export interface IOrderItemInfo {
  /**
   * @Deprecated 订单号
   */
  orderNo: string;

  /**
   * @Deprecated 商品店铺信息
   */
  kdtId: number;

  /**
   * 订单商品ID
   * 新交易返回的是新交易tc_item_id
   * 老交易返回的为item_id
   */
  itemId: number;

  /**
   * itemId格式化 (前端对于Long类型的值存在精度缺失问题，所以需要一个string值)
   */
  itemIdStr: string;

  /**
   * 履约单号
   */
  fulfillNos: string[];

  /** @Deprecated */
  fulfillNo: string;

  /**
   * 商品实付金额
   */
  realPay: number;

  /**
   * 商品ID
   */
  goodsId: number;

  /**
   * 商品信息
   */
  goodsInfo: string;

  /**
   * 商品数量
   */
  num: number;

  /**
   * 已发货数量
   */
  deliveryedNum: number;

  /** *
   * 商品skuid
   */
  skuId: number;

  /**
   * 商品sku
   */
  sku: string;

  /**
   * 描述
   */
  skuDesc: string;

  /**
   * 属性详情
   */
  specifications: string;

  /**
   * 商品skucode
   */
  skuCode: string;

  /**
   * 商品编号
   */
  goodsNo: string;

  /**
   * @Deprecated 商品留言
   */

  buyerMemo: string;

  /**
   * 商品留言(解析后)
   */
  buyerMemoDesc: string;

  /**
   * 商品维权状态
   */
  feedback: number;

  /** @Deprecated */
  goodsRefund: number;

  /** @Deprecated */
  postageRefund: number;

  /**
   * 商品原价 TODO
   */
  originUnitPrice: number;

  /**
   * 现商品单价，减去了商品优惠。以分为单位
   */
  unitPrice: number;

  /**
   * 商品应付金额
   */
  payPrice: number;

  /**
   * 商品吊牌价（标准售价）
   */
  standardPrice: number;

  /**
   * 商品快照页面
   */
  goodsSnapUrl: string;

  /**
   * 图片地址
   */
  imgUrl: string;

  /**
   * 商品名称
   */
  title: string;

  /**
   * 商品单位
   */
  unit: string;

  /**
   * 退款状态
   */
  refundState: string;

  /**
   * 退款状态文案
   */
  refundStateDesc: string;

  /**
   * 退款ID
   */
  refundId: string;

  /**
   * 退款金额
   */
  refundFee: number;

  /**
   * 退款的某个商品数量
   */
  refundNum: number;

  /**
   * 是否发货 （0-未发货 1-已发货 2-三方配送发货状态） 不传即不显示发货标识
   */
  hasExpressed: ExpressStateEnum;

  /**
   * TODO: 后端也未找到定义, 可能需要删掉
   */
  distStatusDesc: string;

  /**
   * 商品换货状态
   */
  exchangeStatus: ExchangeStatus;

  /**
   * 商品换货状态描述
   */
  exchangeStatusDesc: string;

  /**
   * 换货流程版本
   */
  exchangeType?: ExchangeFlowType;

  /**
   * 换货单号
   */
  exchangeNo?: string;

  /** 是否是网店订单 */
  isOnlineOrder?: boolean;

  /** 是否新网店换货流程 */
  isNewExchangeFlow?: boolean;

  /**
   * 商品级别换货单号列表
   */
  exchangeGoodsIds: string[];

  /** 商品级别, 是否有新换货单 */
  containsNewExchange?: boolean;

  /**
   * 商品发货状态
   */
  deliveryStatusDesc: string;

  /**
   * 最近待发货期次
   */
  latestDeliveryTermDesc: string;

  /**
   * 最近待发货时间
   */
  latestDeliveryTimeDesc: string;

  /**
   * 1 待接单 2 待取货
   */
  takeOutExpressState: number;

  /**
   * 三方配送状态说明
   */
  takeOutExpressStateDesc: string;

  /**
   * TODO
   * 扩展字段
   */
  extra: string;

  periodAlias: string;

  /**
   * 商品类型
   */
  goodsType: number;

  /**
   * 是否赠品
   */
  isPresent: boolean;

  /**
   * 判断是否是称重商品
   */
  pricingStrategy: number;

  tags: Record<string, boolean>;

  tagsInfo: string;

  /**
   * 周期购配送时间维度描述
   * @see
   */
  distTimeDimStr: string;

  /**
   * 周期购配送时间模式描述
   */
  distTimeModeStr: string;

  /** @Deprecated */
  warehouseId: number;

  /** @Deprecated */
  warehouseName: string;

  warehouseIds: number[];

  warehouseNames: string[];

  /**
   * 工序列表
   * List<BomProcessDTO>
   */
  processes: any[];

  /**
   * 当前工序
   * BomProcessDTO
   */
  currentProcess: any;

  /**
   * 商品图标
   */
  // List<Icon> icons = Lists.newArrayList();

  /**
   * 操作按钮
   * List<Operation> operations = Lists.newArrayList();
   * 需要补全
   */
  operations: IOperations;

  /**
   * 店铺映射关系
   */
  goodsMapping: string;

  /**
   * 商品优惠信息
   * List<GoodsActivityDetail>
   */
  goodsActivities: any[];

  /**
   * 待领取礼单数
   */
  giftToBeReceivedNum: number;

  /** 是否参与了改价 */
  isReduce: boolean;

  /** 改价金额 */
  reducePrice: number;

  /**
   * 是不是唯一码商品 */
  isSerialItem: boolean;

  /**
   * 商品唯一码信息 */
  serialNos: string[];

  /**
   * 唯一码商品状态及对应唯一码列表
   */
  statusAndUniqueCodeMap: Record<number, string[]>;

  /**
   * 唯一码商品状态及对应唯一码列表
   * any - SimpleScuInfo
   */
  statusAndSimpleScuInfoMap: Record<number, any[]>;

  /**
   * 改价总价
   */
  currentTotalAmount: number;

  /** any - RelatedRetailSkuInfo */
  relatedRetailSkuInfos: IRetailSkuList;

  /**
   * 属性值的信息
   */
  propertiesInfoList: any[];

  /** 采购单的订单条目级别价格信息 any - PurchaseOrderItemPriceInfo * */
  purchaseOrderItemPriceInfo: any;

  /**
   * 商品差价信息
   * any - PriceDiffInfo
   */
  priceDiffInfo: any[];

  /**
   * 差价合计
   */
  totalDiffAmount: number;

  /**
   * 是否支持差价
   */
  supportDiff: boolean;

  /**
   * 商品品牌名
   */
  brandName: string;

  /**
   * 作业单号
   */
  processNo?: string;

  /**
   * 加工状态
   */
  processStatus?: number;

  /**
   * 加工状态描述
   */
  processStatusDesc?: string;

  /**
   * 制作店铺ID
   */
  produceKdtId?: number;

  /**
   * 制作店铺名称
   */
  produceShopName?: string;

  /**
   * 加工操作Code
   */
  processOperationCode?: string;

  /**
   * 加工操作描述
   */
  processOperationDesc?: string;

  /**
   * 加工作业错误信息
   */
  processOperationErrorInfo?: {
    errorCode: EOrderItemProcessErrorCode;
    errorMessage: string;
    paramJson: string;
  };
  /**
   * 商品发货状态
   */
  deliveryStatus: number;
  /** 是否换货item */
  isExchangeItem: boolean;
}

/**
 * 会在全局搜到
 * client/components/opt-components/type-map.js => optTypeMap
 * 后续可能会陆续迁移到 TS
 * */
export enum OptType {
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  BUTTON = 'operation',
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  BUTTON_LINK = 'button_link',
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  LINK = 'link',
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  LINK_BUTTON = 'link_button',
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  TEXT = 'text'
}

export type IItemInfo = IOrderItemInfo[];

export interface IRemarkInfo {
  orderNo: OrderNo;
  kdtId: KdtId;
  sellerMemoDesc: string;
  buyerMemoDesc: string;
  buyerMemo: string;
  sellerStar: number;

  /** 卖家备注 */
  sellerMemo: string;
  sellerRemarkPics?: string[];

  deliveryRemark: string;
}

export interface IOperation {
  displayAtOtherKdt: boolean;
  /** e.g. 'delivery' */
  code: string;
  disabled: boolean;
  /** e.g. '发货' */
  text: string;
  priority: number;
  /** e.g. 'operation' */
  type: OptType;

  /** 根据代码猜测类型 */
  desc: string[];
}

export type IOperations = IOperation[];

export interface IMainOrderInfoTags {
  STOCK_DEDUCTED: boolean;
  ISMEMBER: boolean;
  DELIVERY_OFC_ORDER: boolean;
  IS_SECURED_TRANSACTIONS: boolean;
  MESSAGE_NOTIFY: boolean;
  IS_PAYED: boolean;
  FEEDBACK: boolean;
}

export interface IMainOrderInfo {
  orderNo: OrderNo;
  /** e.g. '1582602171853' */
  createTime?: number;
  /** e.g. '待发货' */
  newStateDesc?: string;
  /** e.g. '网店' */
  saleWayDesc?: string;
  isPeriodBuy?: boolean;
  icons?: [];
  tagsInfo?: IMainOrderInfoTags;
  /** e.g. '普通订单' */
  orderTypeDesc?: string;
  /** e.g. '待发货' */
  stateDesc?: string;
  /** e.g. '同城配送' */
  expressTypeDesc?: string;
  newState?: number;
  state?: number;
  saleWay?: number;
  orderType?: number;
  expressType?: number;
  expressTime?: number;

  /** 扩展字段 */
  extraInfo?: string;
  /** 解析后的拓展字段 */
  extra?: Record<string, unknown> & {
    BIZ_ORDER_ATTRIBUTE?: string;
  };

  /** 未知来源 */
  storeId?: number;

  /** 未找到来源, 但是在代码中有用到 */
  isOversold?: boolean;

  /** 是否拆单 */
  isDivided?: boolean;

  /** 桌号 */
  tableNum?: number | null | undefined | string;
  feedback?: number;

  /** 只会出现在订单列表的 mainOrderInfo */
  saleWayShopName?: string;
  /** 只会出现在订单列表的 mainOrderInfo */
  shopName?: string;
  /** 只会出现在订单列表的 mainOrderInfo, e.g. 'R2020030513170711382023682426' */
  /** 只会出现在订单列表的 mainOrderInfo */
  outBizNo?: string;
  kdtId: KdtId;
  /** 只会出现在订单列表的 mainOrderInfo, e.g. 201 */
  buyWay?: number;

  /** 收银员相关 */
  cashierId?: string;
  cashierName?: string;

  /** 付款时间 */
  payTime?: number;
  /** 付款状态 */
  payState?: number;

  /** TODO: 未找到来源 */
  buyWayDesc?: string;

  /** 风控相关字段 */
  riskDesc?: string;
  riskTips?: string;

  /** 订单渠道来源 */
  channelType?: DeliveryChannelType;

  /** 处方单号 */
  prescriptionNo?: string;

  /** 是否催单 */
  reminderStatus?: number;

  /** 分销员/导购员信息 */
  marketingTypeDesc?: string;
  /** 收获地址是否变更 */
  isModifyLogistics?: boolean;

  /** 导购员 */
  salesmanInfo?: {
    name: string;
    phone: string;
    id: number;
  };

  /** 新的网店换货流程 */
  isNewExchangeFlow?: boolean;
}

interface ICommissionSettle {
  isNeedTax: boolean; // 是否需要缴税
  settleStatus: number; // 结算状态
  settledStartAt: number; // 结算开始时间
  settledEndAt: number; // 结算到账时间
}
export interface IQttNote {
  noteId: number;
  noteTitle: string;
  supplierId: number;
  supplierLogo: string;
  supplierName: string;
  founderId: number;
  founderLogo: string;
  founderName: string;
  promoterId: number;
  promoterName: string;
  promoterPhone?: string;
  promoterLogo: string;
  promoterYzId: number;
  promoterYzRole: number;
  promoterIsNeedTax: boolean;
  promoterFee: number;
  promoterTaxFee: number;
  promoterSettleStatus: number;
  promoterFeeAfterFee: number;
  promoterTaxSignStatus: number;
  inviterId: number;
  inviterName: string;
  inviterPhone: string;
  inviterLogo: string;
  inviterYzId: number; // 邀请团长yzId
  inviterYzRole: number; // 邀请团长身份
  inviterIsNeedTax: boolean; // 邀请人是否有税
  inviterFee: number; // 邀请佣金
  inviterTaxFee: number; // 邀请缴纳个税
  inviterSettleStatus: number; // 结算状态，101未结算，102结算成功
  inviterFeeAfterFee: number; // 邀请到手佣金（税后）
  inviterTaxSignStatus: number; // 个税签约状态，0未签约、1已签约、2无需签约
  inviterCommissionSettle?: ICommissionSettle; // 帮卖佣金结算信息
  promoterCommissionSettle?: ICommissionSettle; // 帮卖佣金结算信息
  settleType: number; // 结算模式，1-确认收货后结算；2-每月10号结算上月确认收货订单
}

export interface IQttOrder {
  founderToMemberMark: string;
  orderNo: string;
  outBizNo?: string;
  participateNo: number;
}

export interface IQttRefundRecord {
  feeRefundStatusStr: string;
  fee: number;
  feeName: string;
  partyName: string;
  refundAt: string;
  refundStatus: boolean;
  refundId: string;
}

export interface IQttOrderInfo {
  note: IQttNote;
  order: IQttOrder;
  refundRecords: IQttRefundRecord[];
}

export interface IPackDistItemInfoShape {
  /** e.g. '2722702983371948107' */
  itemId: string;
  num: number;
  weight: number;
}

export interface IPackItem {
  pricingStrategy: number;
  /** e.g. '基佬紫,大大大,短款,布丁,新增属性值2,你好' */
  skuDesc: string;
  itemId: number;
  /** e.g. 'P200224244732961' */
  goodsNo: string;
  realPay: number;
  num: Num;
  unitPrice: number;
  /** e.g. '川川属性商品' */
  title: string;
  icons: [];
  originUnitPrice: number;
  feedback: number;
  kdtId: KdtId;
  /** e.g. '件' */
  unit: string;
  operations: [];
  /** e.g. 'https://img.yzcdn.cn/uploadFiles/2020/02/24/FjU1BFLOEiFUuKHZF-2KMilYy2cq.jpg' */
  imgUrl: string;
  isSerialItem: false;
  supportDiff: false;
  /** e.g. 'P200224651059778' */
  skuCode: string;
  /** e.g. 'https://h5.youzan.com/v2/showcase/goodsSnapRedirect/urlByOrder?orderNo=E20200225175955079300001&alias=2orow2gt6lsj4&goodsId=*********&snapKey=5fc378352cae29dfa9034f53a12d43c7'; */
  goodsSnapUrl: string;
}

export interface IPackTakeoutExpressDetail {
  /** e.g. '' */
  transporterName: string;
  /** e.g. '' */
  transporterPhone: string;
  /** 需要完善 */
  data: any[];
  /** 应该是枚举, 需要完善 */
  deliveryChannel: 1 | 2;

  /** TODO: 未找到数据, 需要完善 */
  deliveryNo: string;

  /** 呼叫方式 */
  callTypeDesc?: string;
  /** 服务商 */
  companyName?: string;
}

export interface IPackExtraInfo {
  /** 发货人 */
  sentName?: string;
  sentPhone?: string;
}

export interface IPackageInfo {
  isModifyExpressTemp: false;
  /** e.g. '202002251849100000100793' */
  packId: string;
  sendType: number;
  expressTime: number;
  distItemInfo: IPackDistItemInfoShape[];
  /** e.g. '202002251759590000330793' */
  deliveryNo: string;
  version: number;
  shopId: number;
  /** e.g. '商家自行配送' */
  sendTypeDesc: string;
  operations: IOperation[];
  extraInfo: IPackExtraInfo;
  /** e.g. '同城配送' */
  expressTypeDesc: string;
  /** e.g. ['2722702983371948107'] */
  itemIds: string[];
  state: number;
  items: IPackItem[];
  expressType: number;
  warehouseId: number;
  takeoutExpressDetail: IPackTakeoutExpressDetail;

  /** TODO: 未找到数据, 后面需要完善 */
  expressDetail: any;
  /** TODO: 未找到数据, 猜的 */
  packCount: number;

  userName?: string;
  warehouseName?: string;

  deliveryRemark?: string;
}

export type IPacks = IPackageInfo[];

export interface IAutoCallInfo {
  /** shape 待补充 */
  operations: any[];
  isShow: boolean;
  /* 呼叫时间 */
  autoDeliveryTime?: number;
  autoDeliveryDesc?: string;
}

export interface IOrderExpressInfo {
  autoCallInfo: IAutoCallInfo;
  orderSuccessTime: number;
  packs: IPacks;
}

export interface ISelfFetchInfo {
  /** 省市区 */
  province: string;
  city: string;
  county: string;

  /** e.g. '-' */
  addressDetail: string;
  /** e.g. '-' */
  userTel: string;
  /** e.g. '-' */
  userName: string;
  /** e.g. '-' */
  name: Name;
  userTime: string;
}

export interface IHistroyAddress {
  address: IAddress[];
  addressModifyTime: number;
  role: string;
}
interface IAddress {
  province: string;
  city: string;
  district: string;
  detail: string;
}

export interface IOrderAddressInfo {
  selfFetchInfo: ISelfFetchInfo;
  deliveryStartTime: number;
  deliveryEndTime: number;
  /** e.g. '02月25日 17:00-18:00' */
  expectDeliveryTimeDesc: string;
  /** e.g. '锦鲤' */
  receiverName: string;
  /** e.g. '{"deliveryTimeSpan":"hour","areaCode":"330106","lon":120.09442668494401,"lat":30.263296113638457}' */
  addressExtra: string;
  /** e.g. '-' */
  selfFetchTime: string;
  /** e.g. '13333333333' */
  receiverTel: string;

  // 从 api 请求参数找来的
  orderNo: string;
  deliveryCountry: string;
  deliveryCity: string;
  deliveryStreet: string;
  deliveryProvince: string;
  deliveryDistrict: string;
  deliveryPostalCode: string;

  /** 自提点ID */
  selfFetchId?: number;

  // 下面是猜的
  allowModify: boolean;
  forbidModifyReason: string;
  isFirstModify: boolean;
  lastModifyTime: string;

  /** 是否开启无码核销 */
  selfFetchVerifyType: number;

  /** 隐私号 */
  privacyRecipientPhone?: string;
  /** 备用号码 */
  backupRecipientPhone?: string;

  /** 收获地址变更 */
  isModifyLogistics?: boolean;
  /** 收获地址变更历史 */
  orderHistoryAddress?: IHistroyAddress[];
}

export interface IFulfillOrder {
  orderNo: OrderNo;
  /** e.g. '同城配送' */
  distTypeDesc: string;
  prepareState: number;
  endTime: number;
  /** e.g. '待发货' */
  fulfillStatusDesc: string;
  /** e.g. '202002251143020000110120' */
  deliveryNo: string;
  kdtId: number;
  postFee: number;
  realDeliveryFee: number;
  /** e.g. '仓库A杭州' */
  warehouseName: string;
  flowType: number;
  distType: number;
  deliveryMemo: string;
  /** e.g. '202002251142522990000020218' */
  fulfillNo: string;
  /** e.g. '没有维权' */
  refundStatusDesc: string;
  invalid: number;
  fulfillStatus: number;
  warehouseId: number;

  /** 履约单商品总计 */
  fulfillTotalPayment: number;
  /** 是否多仓 */
  multiWarehouse: boolean;
}

export interface IOrderSourceInfo {
  /** e.g. 'E20200305140435024400001' */
  orderNo: OrderNo;
  kdtId: KdtId;
  isOfflineOrder: boolean;
  /** e.g. '其他' */
  orderSource: string;
  /** e.g. '{"clientIp":"127.0.0.1","isOnlineOrder":false,"newSource":"{\\"platformEnum\\":\\"OTHER\\",\\"wxEntranceEnum\\":\\"DIRECT_BUY\\"}","source":"ANDROID-RETAILHD-5.40.0T_03042002"}' */
  originSource: string;
  orderMark: string;
}

export interface IBuyerInfo {
  /** e.g. 0 */
  customerType: number;
  /** e.g. 'E20200305140435024400001' */
  orderNo: string;
  /** e.g. 42870433 */
  kdtId: number;
  nickName: string;
  name: string;
  customerName: string;
  /** e.g. 0 */
  customerId: 0;
  /** e.g. 567971060 */
  buyerId: number;
  /** 拼团购的订单才有的字段 */
  groupIsHeader: 0 | 1;

  /** TODO: 没见到数据, 是猜的 */
  buyerPhone: string;
}

export interface ISupplierInfo {
  // 供应商名称
  supplierName: string;
  // 供应商alias
  supplierAlias: string;
  // 供应商店铺id
  supplierKdtId: number;
}

export interface IPaymentInfo {
  // 是否三方卡券支付订单
  isCouponPay: boolean;

  /** e.g. 0 */
  postage: number;
  /** e.g. false */
  isUnifiedCashierPay: boolean;
  /**
   * 支付流水号
   * e.g. ['200305131707000134']
   */
  innerTransactionNumberList: string[];
  /** e.g. 1960 */
  totalDiscount: number;
  /** e.g. 8340 */
  realPay: number;
  /** e.g. 0 */
  isFreePostage: number;
  /** e.g. 8340 */
  payment: number;
  /**
   * 外部订单号 (储值余额支付类型的订单会出现)
   * e.g. ['200305131707000134']
   */
  outerTransactionNumberList: string[];
  /** e.g. 0 */
  fxPayAmount: number;
  /** e.g. 0 */
  fxPay: number;
  /** e.g. false */
  oldPrintTicket: boolean;

  pointsPrice: number; // 积分订单
  collectPointsPrice: number; // 集点兑换订单

  /** 不知道这个是啥，总之出现了 */
  purchaseOrderPaymentInfo: IPurchaseOrderPaymentInfo;

  /** 分阶段支付详情 */
  phasePayDetailList: IPhasePayDetail[];

  /** 实收金额 */
  totalPayment: number;
}

export interface IPhasePayDetail {
  /** 应付金额 */
  pay: number;
  /** 实付金额 */
  realPrice: number;

  /** 支付状态 */
  status: number;

  /** 支付时间相关时间戳 */
  payEndTime: number;
  payStartTime: number;
}

export interface IPurchaseOrderPaymentInfo {
  fxRebateFee: number;
  supPostage: number;
  orderFxPatchPay: number;
  fxPostage: number;
}

export interface IBuyerInvoiceInfo {
  invoiceServiceOpeningStatus: boolean;

  /** 来源未知, 在代码里用到了 */
  invoiceTitle: string;
  /** 来源未知, 在代码里用到了 */
  receivingMailbox: string;
  /** 来源未知, 在代码里用到了 */
  taxId: string;
  invoiceType?: number | string;
  phone?: string;
  address?: string;
  bankAccount?: string;
  openingBankName?: string;
}

/** 订单换货信息 */
export interface IOrderExchangeInfo {
  /** 换货原因 */
  reason?: string;
  /** 换货订单号 */
  exchangeNo: string;
  /** 换货说明 */
  explanation: string;
  /** 多退少补 ture：多退 false：少补 */
  isRefund: boolean;
  /** 是否新换货单 */
  isRetailExchange?: boolean;
  /** 支付金额（is_refund=false才有值) */
  realPrice: number;
  /** 中台换货单号 */
  refundId: string;
  /** 原订单号 */
  sourceOrderNo: string;
  /** 退款金额 （is_refund=true才有值） */
  refundFee?: number;
  /** 商品数量 */
  itemNum?: number;
}

/** 订单反结账信息 */
export interface ITransactionReversalInfo {
  // 反结账原因
  reason?: string;
  // 反结账操作人
  operatorName?: string;
  // 反结账时间
  refundSuccessTime?: string;
  // 反结账原单号
  oriO?: string;
}

/** 老单子：订单换货信息 */
export interface IExchangeGoodsDetail {
  /** 该单子换货多少次 */
  exchangeGoodsIds: string[];
  /** 是否换货成功 */
  exchangeStatus: boolean;
}

export interface IOrderInfo {
  itemInfo: IItemInfo;
  remarkInfo: IRemarkInfo;
  mainOrderInfo: IMainOrderInfo;
  operations: IOperations;
  orderExpressInfo: IOrderExpressInfo;
  orderAddressInfo: IOrderAddressInfo;
  fulfillOrder: IFulfillOrder;
  sourceInfo: IOrderSourceInfo;
  buyerInfo: IBuyerInfo;
  paymentInfo: IPaymentInfo;
  refundOrderInfo: RefundOrderInfo;
  goodsSupplierInfo?: ISupplierInfo;

  /** 只在订单详情中出现 */
  buyerInvoiceInfo: IBuyerInvoiceInfo;
  outBizNo: string;
  fulfillNos: string[];
  storeName: string;
  saleName: string;
  serviceSalesmanName: string;
  refundPhaseAndTypeDesc: string;
  transactionReversalInfo?: ITransactionReversalInfo;

  /** 在 order-state 中出现 */
  isFulfillOrder: boolean;
  microTransferInfo: IMicroTransferInfo;
  orderDetailStateTips: { stateAssemblyList: RefundStateAssemblyList };
  refundStateAssemblyList: RefundStateAssemblyList;
  refundStateProgress: number;
  hideSteps: boolean;
  tips: Record<string, unknown>;

  /** 在 pack-info 中出现 */
  activePackId: string;
  hideNav: boolean;

  /** 订单换货信息 */
  exchangeInfo?: IOrderExchangeInfo;
  exchangeGoodsDetail?: IExchangeGoodsDetail;

  /** 用户隐私信息解密 key */
  encryptStr?: string;

  /** 订单中的有外卖商品没有关联有赞的商品 */
  includeMockItem?: boolean;

  /**
   * 换货类型
   */
  exchangeType?: ExchangeFlowType;
}

export interface IMicroTransferInfo {
  microTransferAbility: boolean;
}

export interface IApplyChangeDispatchParams {
  orderNo: string;
  fulfillNo?: string;
  warehouseId?: number;
  areaCode?: string;
}

export interface IMultiOrderInfo {
  otherSubOrders: Record<string, unknown>[]; // 以后补充
  mainOrderNo: string;
  subOrderNos: string[];
  paymentInfo: Record<string, unknown>; // 以后补充
  orderType: number;
}

export enum OrderManageListTableOperateColumnBtnEnum {
  /** 发货 */
  Send = 'send',
  /** 取消订单 */
  CancelOrder = 'cancelOrder',
  /** 催付(提醒付款) */
  RemindPay = 'remindPay',
  /** 修改物流 */
  ModifyLogistics = 'modifyLogistics'
}
