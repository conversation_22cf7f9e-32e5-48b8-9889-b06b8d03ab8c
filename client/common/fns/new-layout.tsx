import * as React from 'react';
import { ComponentType } from 'react';
import { createApp as originalCreateApp } from '@youzan/retail-utils';

/**
 * 兼容新布局的工具方法们
 */

export const checkIsNewLayout = () => (window as any)._nav_global?.isNewLayout || false;

/**
 * 返回:
 *
 * - NewLayoutAppHeader: 新布局的 header
 * - AppHeader: 老布局的 header; 切到新布局时, 需要隐藏
 */
export function createAppHeaders({ Nav, Router }: { Nav: ComponentType; Router: ComponentType }) {
  function NewLayoutAppHeader() {
    return (
      <Router>
        <Nav />
      </Router>
    );
  }

  function AppHeader() {
    return checkIsNewLayout() ? null : <Nav />;
  }

  return {
    NewLayoutAppHeader,
    AppHeader
  };
}

/**
 * 兼容新布局的 createApp
 */
export function createApp(options: {
  App: React.ComponentType;
  selector?: string;
  AppHeader: React.ComponentType;
  headerSelector?: string;
}): void {
  const { App, AppHeader, selector, headerSelector } = options;
  if (checkIsNewLayout()) {
    originalCreateApp(App, selector || '#js-react-container');
    originalCreateApp(AppHeader, headerSelector || '#app-header-breadcrumb');
  } else {
    originalCreateApp(App, selector);
  }
}
