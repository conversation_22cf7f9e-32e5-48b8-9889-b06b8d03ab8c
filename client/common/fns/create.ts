import { pageHelp } from '@youzan/retail-utils';
import { defineApp } from '@youzan/micro-app-react';
import { createNewLayoutApp } from 'common/create-app';
import { unmountComponentAtNode } from 'react-dom';

function createMicroApp({
  App,
  name,
  withReg<PERSON>Form,
  AppHeader
}: {
  App: React.ComponentType;
  name: string;
  AppHeader: React.ComponentType;
  withRegisterForm?: boolean;
}) {
  const create: (container?: any) => void = container => {
    pageHelp(name);
    createNewLayoutApp({
      App,
      withRegisterForm,
      AppHeader,
      container
    });
  };

  if (window._global.enableMicroApp) {
    defineApp({
      async mount({ container }) {
        create(container);
      },
      async unmount({ container }) {
        unmountComponentAtNode(container.querySelector('#js-react-container'));
        unmountComponentAtNode(document.querySelector('#app-header-breadcrumb'));
      }
    });
  } else {
    create();
  }
}

export { createMicroApp };
