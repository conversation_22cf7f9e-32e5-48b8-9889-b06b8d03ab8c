import { isEqual } from 'lodash';
/**
 * 将数组里key值相同的项通过fn进行合并
 */
export function compressBy<T extends Record<string, any>>(
  objArray: T[],
  key: string,
  fn = (a: T, b?: T): T => ({ ...a, ...b })
): T[] {
  return objArray.reduce((pre, current) => {
    let isExisted = false;
    const currentArray = pre.map(item => {
      if (isEqual(item[key], current[key])) {
        isExisted = true;
        return fn(item, current);
      }
      return item;
    }, []);

    if (!isExisted) {
      return currentArray.concat(fn(current));
    }
    return currentArray;
  }, []);
}
