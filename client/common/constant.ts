import { global } from '@youzan/retail-utils';
import {
  isUnifiedHqStore,
  isHqStore,
  isRetailMinimalistShop,
  isUnifiedShop,
  isRetailSingleStore,
  checkAbilityValid,
  ShopAbility
} from '@youzan/utils-shop';

const { URL, KDT_ID } = global;

const baseUploadUrl = `${URL.store}/v2/api/materials`;

/* 图片token获取 */
export const TOKEN_URL = `${baseUploadUrl}/token`;

/* 提取网路图片 */
export const FETCH_URL = `${baseUploadUrl}/pubimage`;

/* 默认图片 */
export const DEFAULT_IMG = `${URL.imgqn}/public_files/2017/08/30/63a8d28bce4ca2e5d081e1e69926288e.jpg`;

/**
 * 总部
 * 不是连锁极简版
 * 不是3.0总部
 * 下单店铺不是当前店铺
 *
 * 这个历史逻辑是错误的
 */
/**
 * @returns 为true则不可操作备注和加星
 * @param {*} orderkdtId 订单的kdtId
 */
export const notOptRemarkAndStar = (orderkdtId?: number): boolean =>
  isHqStore && !isRetailMinimalistShop && !isUnifiedHqStore && orderkdtId !== KDT_ID;

/** 同城呼叫支持同时呼叫 */
export const SupportParallelCallInLocalDelivery = isUnifiedShop || isRetailSingleStore;

/** 支持配送商配送范围 */
export const SupportVendorDeliveryScope =
  isUnifiedShop || isRetailSingleStore || isRetailMinimalistShop;

export const HasProductMasterDataManageAbility = checkAbilityValid(
  ShopAbility.ProductMasterDataManageAbility
);

/** 当前店铺是否具有商品主档能力 */
export const IsUnityModel =
  checkAbilityValid(ShopAbility.ProductMasterDataManageAbility) /* 商品主档能力标识 */ ||
  checkAbilityValid(ShopAbility.OnlineGoodsNewUiAbility); /* 此能力自带商品主档能力 */
