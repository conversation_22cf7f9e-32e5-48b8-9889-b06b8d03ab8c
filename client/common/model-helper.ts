import type { Dictionary } from 'lodash';

import { camelCase } from 'lodash';
import { orderTypeValue } from 'common/constants/models';

function createHelper(valueObj: Dictionary<number>): Record<string, Function> {
  const valueHelper: Record<string, Function> = {};
  Object.keys(valueObj).forEach(key => {
    const fnName = camelCase(`is_${key}`);
    valueHelper[fnName] = (value: number): boolean => value === valueObj[key];
  });
  return valueHelper;
}

export const orderTypeHelper = createHelper(orderTypeValue);
