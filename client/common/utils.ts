import { values } from 'lodash';

import {
  checkLiteOnlineStoreManager,
  ShopAbility,
  checkProdVersionIsAdvanced
} from '@youzan/utils-shop';
import { checkAbilityValid } from '@youzan/shop-ability';
import { div, times } from '@youzan/retail-utils';
import { get } from 'lodash';
import { DeliveryChannel } from 'route/delivery/delivery-setting/localdelivery/container/fields/delivery-way/const';
import { CallStrategiesChangePayload } from 'route/delivery/delivery-setting/localdelivery/container/fields/delivery-way/call-strategies/type';

export const IsLiteOnlineStoreManager = checkLiteOnlineStoreManager(
  get(window, '_global.business.userInfo.roles')
);

export const returnByGlobal = (
  config: Record<string, string | undefined>,
  _global: Record<string, unknown>
): string | undefined => {
  let rst: string | undefined;
  Object.keys(config).some(key => {
    if (_global[key]) {
      rst = config[key];
      return true;
    }
    return false;
  });
  return rst;
};

export const easyDownload = (url: string, filename: string): void => {
  const a = document.createElement('a');
  a.href = url;
  a.download = filename || '';
  a.click();
};

export const createSelectOptions = (
  data: Record<number | string, string>
): Array<{
  value: string;
  text: string;
}> => {
  const options = Object.entries(data)
    .map(([value, text]) => ({
      value,
      text
    }))
    .sort((a, b) => +a.value - +b.value);
  return options;
};

// TODO: 这个 any 是个深坑, 晚点再改
export const mergeArrayBy = (key: string, ...args: any[][]): any[] => {
  const tempSet: Record<any, any> = {};
  const arrFirst = args[0];
  arrFirst.forEach(ele => {
    tempSet[ele[key]] = {};
  });
  args.forEach(arr => {
    arr.forEach(ele => {
      const tempKey = ele[key];

      tempSet[tempKey] = { ...tempSet[tempKey], ...ele };
    });
  });
  return values(tempSet);
};

/**
 * 在数组 index 处, 插入元素
 */
export function insert<T>(array: T[], index: number, ...items: T[]): T[] {
  return [...array.slice(0, index), ...items, ...array.slice(index)];
}

/**
 * 判断店铺是否为高级版
 */
export const IsAdvancedVersion = checkProdVersionIsAdvanced(
  get(window, '_global.business.prodVersion')
);

/**
 * 判断是否有某个店铺能力
 */
export const haveShopAbility = (ability: ShopAbility) => {
  return IsAdvancedVersion ? checkAbilityValid(ability) : true;
};

/**
 * 检查呼叫策略中是否设置了智选配送商
 */
export function checkHasIntelligent(changed: CallStrategiesChangePayload) {
  if (
    (changed?.enablePreferredPartner &&
      changed.preferredPartner?.deliveryChannel === DeliveryChannel.IntelligentDeliveryChannel) ||
    changed.preferredPartnerInSeq?.deliveryChannel === DeliveryChannel.IntelligentDeliveryChannel ||
    changed.fallbackPartnerInSeq?.deliveryChannel === DeliveryChannel.IntelligentDeliveryChannel
  ) {
    return true;
  }
  return false;
}

export function safeJsonParse(data: string | undefined) {
  if (typeof data !== 'string') return data;
  try {
    return JSON.parse(data);
  } catch (e) {
    return undefined;
  }
}

export const transformFenToYuan = (amount: number) => {
  return div(amount, 100);
};

export const transformYuanToFen = (amount: number) => {
  return times(amount, 100);
};
