const useCodeScanner = (callback: Function): void => {
  let buffer: string[] = [];
  let prevTimer: undefined | number;

  document.addEventListener('keydown', e => {
    // 如果 buffer 有内容, 说明在连续输入
    if (buffer.length > 0) {
      clearTimeout(prevTimer);

      if (buffer.length > 4 && e.key === 'Enter') {
        e.preventDefault();
        callback(buffer.join(''), e);
      }
    }
    /**
     * to developer: 聪明的你可能会觉得下面这样很奇怪, 事件没被触发的话,
     * 应该根本不执行到下面才对, 如果事件被触发了, 应该一定是有 key 的才对,
     * 为什么我还要判断 e.key 是否存在?
     *
     * answer: 因为在某些浏览器里(Chrome、Safari), input 自动填充事件, 会触发一个没有 key、code 属性的 keydown 事件
     * (但是 isTrusted 并不一致, 各个浏览器在这里的实现是不一样的, 所以不要太依赖 isTrusted)
     */
    if (e.key?.length && e.key.length < 2) {
      buffer.push(e.key);
    }
    prevTimer = window.setTimeout(() => {
      buffer = [];
    }, 20);
  });
};

export default useCodeScanner;
