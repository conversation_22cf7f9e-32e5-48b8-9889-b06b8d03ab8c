/**
 * 从 react-use useAsyncFn 修改来
 * @see https://github.com/streamich/react-use/blob/master/docs/useAsyncFn.md
 */

import { DependencyList, useCallback, useRef, useState } from 'react';

import useMountedState from '../use-mounted-state';

export type AsyncState<T> =
  | {
      isLoading: boolean;
      error?: undefined;
      value?: undefined;
    }
  | {
      isLoading: true;
      error?: Error | undefined;
      value?: T;
    }
  | {
      isLoading: false;
      error: Error;
      value?: undefined;
    }
  | {
      isLoading: false;
      error?: undefined;
      value: T;
    };

type FunctionReturningPromise = (...args: any[]) => Promise<any>;

type PromiseType<T extends Promise<any>> = T extends Promise<infer R> ? R : never;

type StateFromFunctionReturningPromise<T extends FunctionReturningPromise> = AsyncState<
  PromiseType<ReturnType<T>>
>;

export type AsyncFnReturn<T extends FunctionReturningPromise = FunctionReturningPromise> = [
  StateFromFunctionReturningPromise<T>,
  T
];

export default function useAsyncFn<T extends FunctionReturningPromise>(
  fn: T,
  deps: DependencyList = [],
  initialState: StateFromFunctionReturningPromise<T> = { isLoading: false }
): [StateFromFunctionReturningPromise<T>, T] {
  const lastCallId = useRef(0);
  const isMounted = useMountedState();
  const [state, setState] = useState<AsyncState<any>>(initialState);

  const callback = useCallback((...args: Parameters<T>): ReturnType<T> => {
    const callId = ++lastCallId.current;

    if (!state.isLoading) {
      setState(prevState => ({ ...prevState, isLoading: true }));
    }

    return fn(...args).then(
      value => {
        isMounted() && callId === lastCallId.current && setState({ value, isLoading: false });
        return value;
      },
      error => {
        isMounted() && callId === lastCallId.current && setState({ error, isLoading: false });
        throw error;
      }
    ) as ReturnType<T>;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);

  return [state, callback as unknown as T];
}
