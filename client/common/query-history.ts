import { createHashHistory } from 'history';
import * as queryString from 'query-string';
import { mapValues } from 'lodash';
import { deepJsonParse } from '@youzan/retail-utils';

const history = createHashHistory();

const queryHistory = {
  getQuery: (): Record<string, unknown> => {
    const { search } = history.location;
    let searchData = {};
    if (search !== '') {
      searchData = deepJsonParse(queryString.parse(search), true);
    }
    return searchData;
  },
  setQuery: (filterParams: Record<string, unknown>): void => {
    const argsUrl: string = queryString.stringify(filterParams);
    history.replace({
      pathname: '/',
      search: argsUrl
    });
  },
  clearQuery: (defaultParams: any): any => {
    const { search } = history.location;
    let searchData = {};
    if (search !== '') {
      searchData = deepJsonParse(queryString.parse(search), true);
    }
    const initSearch = defaultParams || mapValues(searchData, () => '');
    queryHistory.setQuery(initSearch);
    return initSearch;
  }
};

export default queryHistory;
