import type { Contains } from 'definition/common';
import type { IOrderInfo, IPaymentInfo } from 'definition/order-info';

import * as React from 'react';
import { get } from 'lodash';
import styled from 'styled-components';
import { ExpressType } from '@youzan/zan-hasaki';
import { convertFenToYen } from 'common/fns/format';
import { DeliveryChannelType } from 'common/constants/common';

const DeletedPrice = styled.p`
  color: #969799;
  text-decoration: line-through;
`;

/**
 * 判断是否为三方卡券支付订单
 *
 * @returns {boolean}
 */
export function checkIsCouponPayOrder(paymentInfo: IPaymentInfo | undefined): boolean {
  if (!paymentInfo) return false;
  return paymentInfo.isCouponPay;
}

/**
 * 判断是否为「第三方订单」
 */
export function isOuterOrder(
  orderInfo: Pick<IOrderInfo, 'mainOrderInfo' | 'refundOrderInfo'>
): boolean {
  return (
    +get(orderInfo, 'mainOrderInfo.bizType') === 7 ||
    +get(orderInfo, 'refundOrderInfo.bizType') === 7
  );
}

/**
 * 判断是否为需要 快递发货 的三方订单(例如管易云)
 */
export const isThirdPartyDelivery = (
  orderInfo: Pick<IOrderInfo, 'mainOrderInfo' | 'refundOrderInfo'>
): boolean => {
  return isOuterOrder(orderInfo) && orderInfo.mainOrderInfo?.expressType === ExpressType.Express;
};

/**
 * 管易云订单
 */
export function isGYYOrder(orderInfo: IOrderInfo): boolean {
  return (
    orderInfo.mainOrderInfo?.channelType === DeliveryChannelType.GYY ||
    orderInfo.refundOrderInfo?.channelType === DeliveryChannelType.GYY
  );
}

/**
 * 判断是否为「餐道」
 */
export function isCandaoOrder(
  orderInfo: Pick<IOrderInfo, 'mainOrderInfo' | 'refundOrderInfo'>
): boolean {
  return (
    +get(orderInfo, 'mainOrderInfo.channelType') === DeliveryChannelType.Candao ||
    +get(orderInfo, 'refundOrderInfo.channelType') === DeliveryChannelType.Candao
  );
}

/**
 * 是否为美团外卖订单
 *
 * @param orderInfo
 * @returns
 */
export function checkIsMeituanOrder(channelType?: number): boolean {
  return channelType === DeliveryChannelType.Meituan;
}

/**
 * 是否为小红书担保支付订单
 *
 * @param orderInfo
 * @returns
 */
export function checkIsXhsLocalLife(orderInfo: any): boolean {
  const attrStr = get(orderInfo, 'extra.bIZ_ORDER_ATTRIBUTE', '{}');

  try {
    const attr = JSON.parse(attrStr);

    return attr.MULTI_PLAT_OUT_CHANNEL === 'XHS_MINIAPP_LOCAL_LIFE';
  } catch (error) {
    return false;
  }
}

/**
 * 是否为美团外卖订单
 *
 * @param orderInfo
 * @returns
 */
export function isMeituanOrder(orderInfo: Pick<IOrderInfo, 'mainOrderInfo'>): boolean {
  return checkIsMeituanOrder(orderInfo.mainOrderInfo.channelType);
}

export function checkIsElemeOrderByChannelType(channelType?: number): boolean {
  return channelType === DeliveryChannelType.Eleme;
}

export function checkIsTiktokSxtOrderByChannelType(channelType?: number): boolean {
  return channelType === DeliveryChannelType.TiktokSxt;
}

/**
 * 是否为美团闪购订单
 *
 * @param orderInfo
 * @returns
 */
export function checkIsMeituanShangouOrder(channelType?: number): boolean {
  return channelType === DeliveryChannelType.MeituanShangou;
}

/**
 * 是否为京东外卖渠道
 *
 * @param channelType
 * @returns
 */
export function checkIsJdwmOrder(channelType?: number): boolean {
  return channelType === DeliveryChannelType.JDWM;
}

/**
 * 是否为京东外卖订单
 *
 * @param orderInfo
 * @returns
 */
export function isJdwmOrder(orderInfo: Pick<IOrderInfo, 'mainOrderInfo'>): boolean {
  return checkIsJdwmOrder(orderInfo?.mainOrderInfo?.channelType);
}

/**
 * 是否为美团闪购订单
 *
 * @param orderInfo
 * @returns
 */
export function isMeituanShangouOrder(orderInfo: Pick<IOrderInfo, 'mainOrderInfo'>): boolean {
  return checkIsMeituanShangouOrder(orderInfo?.mainOrderInfo?.channelType);
}

/**
 * 是否为抖音随心团订单
 *
 * @param orderInfo
 * @returns
 */
export function isTiktokSxtOrder(orderInfo: Pick<IOrderInfo, 'mainOrderInfo'>): boolean {
  return checkIsTiktokSxtOrderByChannelType(orderInfo?.mainOrderInfo?.channelType);
}

/**
 * 是否为饿了么订单
 *
 * @param orderInfo
 * @returns
 */
export function checkIsElemeOrder(orderInfo: Pick<IOrderInfo, 'mainOrderInfo'>): boolean {
  return checkIsElemeOrderByChannelType(orderInfo?.mainOrderInfo?.channelType);
}

/**
 * 是否为美团平台订单
 */
export function checkIsMeituanPlatformOrder(channelType?: number): boolean {
  return checkIsMeituanOrder(channelType) || checkIsMeituanShangouOrder(channelType);
}

/**
 * 是否展示标准价
 */
export function showStandardPrice(
  goodsInfo: Contains<{ originUnitPrice: number; standardPrice: number }>
): boolean {
  const isQuye = get(_global, 'business.isQuyeShop', false);
  const { standardPrice, originUnitPrice } = goodsInfo;

  if (isQuye && standardPrice && originUnitPrice !== standardPrice) {
    return true;
  }

  return false;
}

/**
 * 获取商品展示的划线价格信息
 */
export function getGoodsShowPrice(
  goodsInfo: Contains<{
    unitPrice: number;
    originUnitPrice: number;
    standardPrice: number;
    isExchangeItem: boolean;
    icons: {
      code: string;
    }[];
  }>
): JSX.Element | null {
  const { unitPrice, originUnitPrice, isExchangeItem } = goodsInfo;

  if (isExchangeItem) {
    /** SPU 换货换入商品不展示原价 */
    return null;
  }

  if (showStandardPrice(goodsInfo)) {
    return <DeletedPrice>{convertFenToYen(goodsInfo.standardPrice)}</DeletedPrice>;
  }

  if (unitPrice !== originUnitPrice) {
    return <DeletedPrice>{convertFenToYen(originUnitPrice)}</DeletedPrice>;
  }

  return null;
}

/**
 * 获取商品"当前"(销售)单价
 */
export function getCurrentPrice(
  goodsInfo: Contains<{
    payPrice: number;
    reducePrice: number;
    originUnitPrice: number;
    unitPrice: number;
    isReduce: boolean;
    isExchangeItem: boolean;
    icons: { code: string }[];
  }>
) {
  if (goodsInfo.isExchangeItem) {
    // SPU 换货换入商品
    return goodsInfo.originUnitPrice ? `-${convertFenToYen(goodsInfo.originUnitPrice)}` : '';
  }

  return convertFenToYen(
    // 有 改价的展示改价，否则展示现在的单价
    goodsInfo.isReduce ? goodsInfo.reducePrice : goodsInfo.unitPrice
  );
}

/** 获取商品实付总价 */
export function getExchangePrice(
  goodsInfo: Contains<{
    realPay: number;
    payPrice: number;
    isExchangeItem: boolean;
  }>
) {
  if (goodsInfo.isExchangeItem) {
    // SPU 换货换入商品
    return goodsInfo.payPrice ? `-${convertFenToYen(goodsInfo.payPrice)}` : '';
  }

  return convertFenToYen(goodsInfo.realPay);
}
