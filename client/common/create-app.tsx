import * as React from 'react';
import { isObject } from 'lodash';
import { RegisterForm } from '@youzan/retail-form';
import { initCheckToken } from '@youzan/retail-utils';
import { render } from 'react-dom';

import { checkIsNewLayout } from './fns/new-layout';

export interface CreateAppConfig {
  withRegisterForm?: boolean;
  selector?: string;
  container?: HTMLElement;
}

export default function createApp(
  App: React.ComponentType,
  config?: CreateAppConfig | string
): void {
  let withRegisterForm = false;
  let selector = '#app-container';
  let targetContainer: ParentNode = document;

  if (typeof config === 'string') {
    selector = config;
  }

  if (isObject(config)) {
    const { withRegisterForm: withRF, selector: sel, container } = config;
    withRegisterForm = withRF ?? withRegisterForm;
    selector = sel ?? selector;
    if (container) {
      targetContainer = container;
    }
  }

  const container = targetContainer.querySelector(selector);
  if (container) {
    initCheckToken();
    const Component = withRegisterForm ? (
      <RegisterForm>
        <App />
      </RegisterForm>
    ) : (
      <App />
    );
    render(Component, container);
  }
}

export function createNewLayoutApp({
  App,
  selector,
  withRegisterForm,
  AppHeader,
  headerSelector,
  container
}: {
  App: React.ComponentType;
  selector?: string;
  withRegisterForm?: boolean;
  AppHeader: React.ComponentType;
  headerSelector?: string;
  container?: HTMLElement;
}) {
  if (checkIsNewLayout()) {
    createApp(App, {
      withRegisterForm,
      selector: selector || '#js-react-container',
      container
    });
    createApp(AppHeader, headerSelector || '#app-header-breadcrumb');
  } else {
    createApp(App, { withRegisterForm: true });
  }
}
