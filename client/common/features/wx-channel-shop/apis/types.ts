/**
 * com.youzan.ebiz.video.channels.trade.api.param.dc.ExpressQueryRequest
 */
export interface IExpressQueryRequest {
  /** 销售店铺ID */
  salesKdtId?: number;
  /** 对接平台渠道. 见ChannelAccountType. */
  channelType?: number;
  /** 小店mpId */
  mpId?: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.ExpressAccountDTO
 */
export interface IExpressAccountDTO {
  kdtId?: number;
  /** 商家在某外部平台注册的店铺id(返回的是外部数据,理论上全局唯一) */
  outShopId: string;
  mpId?: number;
}

export enum CooperateType {
  /** 加盟 */
  Join = 'JOIN',
  /** 直营 */
  Direct = 'DIRECT'
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.ExpressAggregateDTO
 */
export interface IExpressAggregateDTO {
  cooperateType?: CooperateType;
  /** 物流公司名称 */
  expressName: string;
  /** 外部物流公司id */
  outExpressId?: string;
  /** 商家账号相关信息 */
  expressAccountInfo: IExpressAccountDTO;
  /** 物流公司id(有赞侧） */
  expressId: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.dc.ExpressSiteQueryRequest.QueryOptions
 */
interface IQueryOptions {
  /** 查询该店在对应渠道开通的所有网点信息 */
  queryAllSites?: boolean;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.dc.ExpressSiteQueryRequest
 */
export interface IExpressSiteQueryRequest {
  /** 有赞物流商id 必填 */
  yzExpressId?: number;
  /** 销售店铺ID */
  salesKdtId?: number;
  /** 选填 */
  options?: IQueryOptions;
  /** 对接平台渠道. 见ChannelAccountType. */
  channelType?: number;
  /** 小店mpId */
  mpId?: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.PageRequest
 */
export interface IPageRequest {
  /** 页长  必填项 */
  pageSize?: number;
  /** 页码  必填项 */
  page?: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.ExpressSiteAccountDTO.WaybillBalanceDTO
 */
export interface IWaybillBalanceDTO {
  /** 已取消 */
  canceled?: number;
  /** 已回收 */
  recycled?: number;
  /** 剩余单量 */
  available: number;
  /** 已使用 */
  used?: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.ExpressSiteAccountDTO
 */
export interface IExpressSiteAccountDTO {
  /** 网点账号状态 1 可用 2 不可用(可能有很多,没法枚举，先通过描述字段透出)  see {@link com.youzan.ebiz.video.channels.trade.api.common.MultiPlatExpressAccountStatusEnum} */
  accountStatus?: number;
  /** 商家在该物流公司下某网点分配的面单账号id(返回的是外部数据) */
  outWaybillAccountId?: string;
  /** 余额信息 */
  balanceInfo: IWaybillBalanceDTO;
  /** 网点账号状态描述 */
  accountStatDesc?: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.ExpressSiteDTO.AddressDTO
 */
export interface IAddressDTO {
  province?: string;
  city?: string;
  street?: string;
  district?: string;
  detailAddress?: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.ExpressSiteDTO.ContactDTO
 */
export interface IContactDTO {
  phone?: string;
  name?: string;
  mobile?: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.ExpressSiteDTO
 */
export interface IExpressSiteDTO {
  /** 状态描述 */
  statusDesc?: string;
  /** 地址 */
  address: IAddressDTO;
  /** 联系人 */
  contact: IContactDTO;
  /** 网点id(外部) */
  outSiteId: string;
  /** 网点名称 */
  siteName?: string;
  /** 网点编号(外部) */
  outSiteNo: string;
  /** 网点状态 1 正常 2 异常  see {@link  com.youzan.ebiz.video.channels.trade.api.common.MultiPlatExpressSiteStatusEnum} */
  status?: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.SiteAggregateDTO
 */
export interface ISiteAggregateDTO {
  /** 网点开通账号信息 */
  siteAccountInfo: IExpressSiteAccountDTO;
  /** 网点详情 */
  siteInfo: IExpressSiteDTO;
}

export interface IMonthlyCardIdDTO {
  accountStatDesc: string;
  accountStatus: number;
  monthlyCardId: string;
  outWaybillAccountId: string;
}

export interface IProductTypeDTO {
  expressId: number;
  outProductTypeId: number;
  outProductTypeName: string;
}

/**
 * com.youzan.api.common.response.Paginator
 * Created by xuzhaopeng on 4/27/16.
 */
export interface IPaginator {
  pageSize?: number;
  page?: number;
  totalCount?: number;
}

/**
 * com.youzan.api.common.vo.ListWithPaginatorVO
 * Created by xuzhaopeng on 4/27/16.
 */
export interface IListWithPaginatorVO<T> {
  paginator?: IPaginator;
  items?: T[];
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.weshop.WaybillDeliveryTemplateGetRequest
 * 快递公司电子面单获取请求
 */
export interface IWaybillDeliveryTemplateGetRequest {
  extension?: Record<string, unknown>;
  /** 销售店铺ID */
  salesKdtId?: number;
  /** 对接平台渠道. 见ChannelAccountType. */
  channelType?: number;
  /** 小店mpId */
  mpId?: number;
  /** 快递公司id */
  expressId?: number;
}
/**
 * com.youzan.ebiz.video.channels.trade.api.model.weshop.WaybillTemplateOptionDTO
 * 电子面单模板信息选项
 */
export interface IWaybillTemplateOptionDTO {
  /** 是否选用此信息 */
  isOpen?: boolean;
  /** 信息类型，填0-7 */
  optionId?: number;
  /** 打印字号 */
  fontSize?: number;
  /** 字体是否加粗 */
  isBold?: boolean;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.weshop.WaybillTemplateDTO
 * 电子面单模板信息
 */
export interface IWaybillTemplateDTO {
  /** 模板类型 默认填 "single" */
  templateType?: string;
  /** 订单号 */
  isDefault?: boolean;
  /** 模板名，同一快递公司下不可重复 */
  templateName: string;
  /** 模板创建时间 */
  createTime?: string;
  /** 模板描述 默认填 "一联单标准模板" */
  templateDesc?: boolean;
  /** 模板信息选项，总数必须为8个，顺序按照数组序号 */
  options?: IWaybillTemplateOptionDTO[];
  /** 模板更新时间 */
  updateTime?: string;
  /** 三方模板id */
  thirdTemplateId: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.weshop.WaybillDeliveryTemplateResponse
 * 快递公司的电子面单模板信息
 */
export interface IWaybillDeliveryTemplateResponse {
  /** 三方物流公司id */
  thirdExpressId?: string;
  /** 模板信息列表 */
  templates: IWaybillTemplateDTO[];
  /** 该快递公司的默认模板id */
  defaultTemplateId?: string;
  /** 物流公司id */
  expressId?: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.WaybillOrderAddressDTO
 * 电子面单地址信息
 */
export interface IWaybillOrderAddressDTO {
  /** 详细地址 */
  address?: string;
  /** 省 */
  province?: string;
  /** 市 */
  city?: string;
  /** 街道 */
  street?: string;
  /** 姓名 */
  name?: string;
  /** 联系电话 */
  mobile?: string;
  /** 区 */
  county?: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.WaybillOrderOrderItemDTO
 * 电子面单中订单的订单条目
 */
export interface IWaybillOrderOrderItemDTO {
  /** 订单商品个数 */
  itemCnt?: number;
  /** 订单商品ID（订单条目ID） */
  orderItemId?: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.WaybillOrderOrderDTO
 * 电子面单中的订单信息
 */
export interface IWaybillOrderOrderDTO {
  /** 订单号 */
  orderNo?: string;
  /** 订单商品信息 */
  orderItemInfos?: IWaybillOrderOrderItemDTO[];
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.dc.WaybillOrderCreateRequest
 * 电子面单创建请求
 */
export interface IWaybillOrderCreateRequest {
  extension?: Record<string, unknown>;
  /** 网点编码 */
  thirdSiteCode?: string;
  /** 收件人信息 */
  receiver?: IWaybillOrderAddressDTO;
  /** 销售店铺ID */
  salesKdtId?: number;
  /** 备注 */
  remark?: string;
  /** 对接平台渠道. 见ChannelAccountType. */
  channelType?: number;
  /** 小店mpId */
  mpId?: number;
  /** 退货地址 */
  returnAddress?: IWaybillOrderAddressDTO;
  /** 外部店铺id（从查询开通账号信息接口获取） */
  thirdShopId?: string;
  /** 寄件人信息 */
  sender?: IWaybillOrderAddressDTO;
  /** 物流公司id（有赞侧） */
  expressId?: number;
  /** 电子面单账号id */
  thirdAcctId?: string;
  thirdProductTypeId?: number;
  monthlyCardId?: string;
  /** 订单信息 */
  order?: IWaybillOrderOrderDTO;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.WaybillOrderCreateResponse
 * 电子面单创建结果
 */
export interface IWaybillOrderCreateResponse {
  /** 三方电子面单id */
  thirdWaybillId: string;
  /** 快递单号 */
  expressNo: string;
  /** 物流公司id（有赞侧） */
  expressId: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.dc.WaybillOrderPrintGetRequest.PrintNum
 */
export interface IPrintNum {
  /** 当前张数 */
  curNum?: number;
  /** 总张数 */
  sumNum?: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.dc.WaybillOrderPrintGetRequest
 * 电子面单报文获取请求
 */
export interface IWaybillOrderPrintGetRequest {
  extension?: Record<string, unknown>;
  /** 三方电子面单id */
  thirdWaybillId?: string;
  /** 打印数量 */
  printNum?: IPrintNum;
  /** 销售店铺ID */
  salesKdtId?: number;
  /** 对接平台渠道. 见ChannelAccountType. */
  channelType?: number;
  /** 三方面单模板ID */
  thirdTemplateId?: string;
  /** 小店mpId */
  mpId?: number;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.model.dc.WaybillOrderPrintGetResponse
 * 电子面单获取打印报文结果
 */
export interface IWaybillOrderPrintGetResponse {
  /** 电子面单打印报文，打单时传给打印机使用 */
  printInfo: string;
}

/**
 * com.youzan.ebiz.video.channels.trade.api.param.dc.WaybillOrderPrintNotifyRequest
 * 电子面单打印报文成功通知请求
 */
export interface IWaybillOrderPrintNotifyRequest {
  /** 三方电子面单id */
  thirdWaybillId?: string;
  /** 0.首次打印（默认值）  1.重新打印 */
  printType?: number;
  /** 销售店铺ID */
  salesKdtId?: number;
  /** 快递单号 */
  expressNo?: string;
  /** 对接平台渠道. 见ChannelAccountType. */
  channelType?: number;
  /** 小店mpId */
  mpId?: number;
  /** 物流公司id（有赞侧） */
  expressId?: number;
}

/**
 * com.youzan.shopcenter.outer.entity.request.ShopAddressPageQueryRequest
 * 店铺地址分页查询请求
 */
export interface IShopAddressPageQueryRequest {
  addressTypeValues?: number[];
  targetKdtId?: number;
  /** 分页大小 */
  pageSize?: number;
  keyword?: string;
  /** 页码 */
  pageNum?: number;
}

/**
 * com.youzan.shopcenter.outer.entity.result.PageQueryResult 
 * 分页查询结果

 Created by shibin on 2018/8/23.
 */
export interface IPageQueryResult<T> {
  /** 总数 */
  total?: number;
  /** 数据列表 */
  data?: T[];
  /** 分页大小 */
  pageSize?: number;
  /** 当前页码 */
  pageNum?: number;
}

/**
 * com.youzan.shopcenter.outer.entity.info.ShopAddressInfo
 * 店铺地址信息
 */
export interface IShopAddressInfo {
  /** 省市区 */
  area?: string;
  /** 更新时间 */
  updatedTime?: string;
  /** 是否发货地址 */
  isShipAddress?: boolean;
  /** 详细地址 */
  address?: string;
  /** kdtId */
  kdtId?: number;
  /** 市，eg：杭州市 */
  city?: string;
  /** 是否默认退款地址 */
  isDefaultReturnAddress?: boolean;
  /** 是否默认发货地址 */
  isDefaultInvoiceAddress?: boolean;
  /** 联系人 */
  contactName?: string;
  /** 区，eg：西湖区 */
  county?: string;
  /** 是否发货地址 */
  isInvoiceAddress?: boolean;
  /** 国家码标识 */
  countryIndex?: number;
  /** 座机号码 */
  telephone?: string;
  /** 地址id */
  addressId: number;
  /** 是否退款地址 */
  isReturnAddress?: boolean;
  /** 座机区号 */
  areaCode?: string;
  /** 手机号 */
  mobilePhone?: string;
  /** 省，eg：浙江省 */
  province?: string;
  /** 是否已删除 true：已删除 false：未删除 */
  isDeleted?: boolean;
  /** 地区类型 */
  regionType?: string;
  /** 国家码 */
  countryCode?: string;
  /** 分机号 */
  extensionNumber?: string;
  /** 区域id */
  countyId?: string;
  /** 创建时间 */
  createdTime?: string;
}
