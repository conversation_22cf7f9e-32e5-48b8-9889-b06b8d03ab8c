import { MutableRefObject, useRef } from 'react';
import { WxChannelShopPrinter } from 'wx-channel-shop-printer';
import useAsyncFn from '../../hooks/use-async-fn';
import useWillUnmount from '../../hooks/use-will-unmount.ts';

export function useWxChannelShopPrinter() {
  const printerRef = useRef<WxChannelShopPrinter>() as MutableRefObject<WxChannelShopPrinter>;

  if (!printerRef.current) {
    printerRef.current = new WxChannelShopPrinter();
  }

  const [printerListState, syncPrinterList] = useAsyncFn(
    () => {
      return printerRef.current.getPrinterList();
    },
    [],
    {
      isLoading: false,
      value: []
    }
  );

  useWillUnmount(() => {
    printerRef.current.disconnect();
  });

  return {
    printerListState,
    syncPrinterList
  };
}
