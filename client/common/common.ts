interface LabelType {
  text: string;
  value: number;
}

/** 给数据类型添加全部选项 */
export const formatArrayByAddAll = (originArr: LabelType[]): LabelType[] => {
  const allOption = {
    text: '全部',
    value: -1
  };
  return [allOption, ...originArr];
};

/** 将map类型转为下拉框需要的类型 */
export function transformTextMapToOptions<K, V>(
  textMap: Map<K, V>
): {
  text: V;
  value: K;
}[] {
  return Array.from(textMap).map(([key, text]) => ({
    text,
    value: key
  }));
}
