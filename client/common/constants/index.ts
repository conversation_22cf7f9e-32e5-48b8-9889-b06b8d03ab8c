import { startOfToday, endOfToday, subYears } from 'date-fns';
import { formatDatetime } from '@youzan/retail-utils';
import { isUnifiedShop } from '@youzan/utils-shop';
import { transformTextMapToOptions } from '@youzan/zan-hasaki';

// 初始化获取列表数据
export const initGetListParam = {
  order_label: 'order_no',
  order_label_value: '',
  start_time: '', // 下单开始时间
  end_time: '', // 下单结束时间
  choose_days: -1,
  type: 'all',
  state: 'all',
  express_type: 'all',
  feedback: 'all',
  buy_way: 'all',
  period_send_time: '', // 周期购送达日期
  sale_way: 'all',
  star: 'all',
  cashier_id: '', // 收银员
  store_id: '', // 归属网点
  delivery_start_time: '', // 送达开始时间
  delivery_end_time: '', // 送达结束时间
  delivery_choose_days: -1,
  page_no: 1,
  page_size: 20,
  goods_id: '',
  goods_title: '',
  tuanId: '',
  buyer_id: ''
};

// filter需重置的item
export const needResetItems = {
  saleWay: {
    buyWay: -1,
    orderType: -1,
    orderState: -1,
    expressType: -1
  },
  orderType: { orderState: -1 }
};

// 筛选类目
export const orderLabelMap = {
  order_no: '订单编号',
  outer_transaction_number: '外部单号',
  user_name: '收货人姓名',
  tel: '收货人手机号',
  offline_order_no: '离线订单编号',
  buyer_phone: '买家手机号'
};

export const onlineOrderLabelMap = {
  order_no: '订单编号',
  outer_transaction_number: '外部单号',
  user_name: '收货人姓名',
  tel: '收货人手机号',
  buyer_phone: '买家手机号'
};

// 订单类型
export const typeMap = {
  all: {
    all: '全部',
    normal: '普通订单',
    cashier: '扫码收款订单',
    tuan: '多人拼团订单',
    fenxiao: '分销订单',
    hotel: '酒店订单',
    pointstore: '积分兑换订单',
    peerpay: '代付订单',
    gift: '送礼订单',
    wish: '心愿订单',
    feedback: '维权订单',
    period: '周期购订单',
    knowledge: '知识付费订单',
    online_scan_buy: '扫码购订单',
    joint: '好友拼单'
  },
  online: {
    all: '全部',
    normal: '普通订单',
    tuan: '多人拼团订单',
    fenxiao: '分销订单',
    hotel: '酒店订单',
    pointstore: '积分兑换订单',
    peerpay: '代付订单',
    gift: '送礼订单',
    wish: '心愿订单',
    feedback: '维权订单',
    period: '周期购订单',
    knowledge: '知识付费订单',
    online_scan_buy: '扫码购订单',
    joint: '好友拼单'
  },
  offline: {
    all: '全部',
    normal: '普通订单',
    cashier: '扫码收款订单'
  }
};

// 订单状态(根据订单类型联动)
export const stateMap = {
  all: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  normal: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  fenxiao: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  gift: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  pointstore: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  wish: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  peerpay: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  period: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  knowledge: {
    all: '全部',
    topay: '待付款',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  hotel: {
    all: '全部',
    topay: '待付款',
    tosend: '待接单',
    send: '已接单',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  cashier: {
    all: '全部',
    topay: '待付款',
    tosend: '已付款',
    success: '交易成功',
    cancel: '交易关闭'
  },
  tuan: {
    all: '全部',
    topay: '待付款',
    totuan: '待成团',
    tosend: '待发货',
    send: '已发货',
    success: '交易成功',
    cancel: '交易关闭',
    refunding: '退款中'
  },
  feedback: {
    all: '全部',
    sellertodo: '退款中',
    feedback_closed: '退款结束'
  },
  offline: {
    all: '全部',
    topay: '待付款',
    success: '交易成功',
    cancel: '交易关闭'
  }
};

// 付款方式
export const buyWayMap = {
  all: {
    all: '全部',
    wxpay_store: '微信支付',
    alipay: '支付宝付款',
    umpay: '银行卡付款',
    prepaidcard: '储值余额付款',
    of_cash: '现金',
    mark_pay: '标记收款',
    codpay: '货到付款',
    peerpay: '找人代付',
    presentpay: '领取赠品',
    couponpay: '优惠兑换',
    giftpay: '礼品卡付款'
  },
  online: {
    all: '全部',
    wxpay_store: '微信支付',
    alipay: '支付宝付款',
    umpay: '银行卡付款',
    prepaidcard: '储值余额付款',
    codpay: '货到付款',
    peerpay: '找人代付',
    presentpay: '领取赠品',
    couponpay: '优惠兑换',
    giftpay: '礼品卡付款'
  },
  offline: {
    all: '全部',
    wxpay_store: '微信支付',
    alipay: '支付宝付款',
    umpay: '银行卡付款',
    prepaidcard: '储值余额付款',
    of_cash: '现金',
    mark_pay: '标记收款'
  }
};

// 老版支付方式参数(导出列表使用)
export const buyWayOldMap = {
  all: '全部',
  wxpay: '微信支付',
  aliwap: '支付宝付款',
  umpay: '银行卡付款',
  prepaidcard: '储值余额付款',
  of_cash: '现金',
  mark: '标记收款',
  codpay: '货到付款',
  peerpay: '找人代付',
  presentpay: '领取赠品',
  couponpay: '优惠兑换',
  giftpay: '礼品卡付款',
  transfer_to_public: '对公转账'
  // mark_pay_xhs_pay: '小红书-担保支付'
};

// 支付方式新参数转老参数
export const newBuyWayToOldMap = {
  wxpay_store: 'wxpay',
  alipay: 'aliwap',
  mark_pay: 'mark'
};

// 物流方式
export const expressTypeMap = {
  all: {
    all: '全部',
    express: '快递',
    city: '同城配送',
    selffetch: '自提'
  },
  online: {
    all: '全部',
    express: '快递',
    city: '同城配送',
    selffetch: '自提'
  },
  offline: { all: '全部' }
};

// 导出报表类型
export const exportTypeMap = {
  default: '普通报表',
  account_check: '对账单',
  peerpay: '代付对账单'
};

// 日期快速选择配置
export const dateRangePickerPreset = [
  {
    text: '今',
    value: [startOfToday(), endOfToday()]
  },
  {
    text: '昨',
    value: 1
  },
  {
    text: '最近7天',
    value: 7
  },
  {
    text: '最近30天',
    value: 30
  }
];

export const YearlyDateRangePickerPreset = [
  {
    text: '今',
    value: [startOfToday(), endOfToday()]
  },
  {
    text: '昨',
    value: 1
  },
  {
    text: '近7天',
    value: 7
  },
  {
    text: '近30天',
    value: 30
  },
  {
    text: '近1年',
    value: [subYears(startOfToday(), 1), endOfToday()]
  }
];

function getFutureDateRange(futureOffset: number) {
  const getDateStr = (date: Date) => {
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
      date.getDate()
    ).padStart(2, '0')}`;
  };
  const now = new Date();
  const startDate = new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000);
  const endDate = new Date(now.getTime() + futureOffset * 24 * 60 * 60 * 1000);
  return [`${getDateStr(startDate)} 00:00:00`, `${getDateStr(endDate)} 23:59:59`];
}
export const futureDateRangePickerPreset = [
  {
    text: '今',
    value: [startOfToday(), endOfToday()]
  },
  {
    text: '明',
    value: getFutureDateRange(1)
  },
  {
    text: '未来7天',
    value: getFutureDateRange(7)
  },
  {
    text: '未来30天',
    value: getFutureDateRange(30)
  }
];

export const dateRangePickerProps = {
  preset: dateRangePickerPreset,
  format: 'YYYY-MM-DD HH:mm:ss'
};

export const futureDateRangePickerProps = {
  preset: futureDateRangePickerPreset,
  format: 'YYYY-MM-DD HH:mm:ss'
};

export const datePickerDisableSevenDays = {
  disabledDate: (val: string | number | Date): boolean => {
    const today = new Date().getTime();
    const valDate = new Date(val).getTime();
    return (valDate - today) / (1000 * 3600 * 24) > 6;
  }
};

// 销售渠道
export const saleWayMap = {
  all: '全部',
  online: '网店',
  offline: '门店'
};

// 加星订单
export const starOrderMap = {
  all: '不限',
  have: '加星'
};

// 退款状态
export const feedbackMap = {
  all: {
    all: '全部',
    sellertodo: '退款中',
    feedback_closed: '退款结束'
  },
  online: {
    all: '全部',
    sellertodo: '退款中',
    feedback_closed: '退款结束'
  },
  offline: { all: '全部' }
};

// 退款维权退款状态map
export const safeGuardsTextMap = {
  1: '退款申请等待卖家确认',
  10: '卖家不同意协议，等待买家修改',
  20: '退款申请达成，等待买家退货',
  30: '买家已退货，等待卖家确认收货',
  40: '卖家未收到货，不同意退款申请',
  60: '退款成功',
  50: '退款关闭',
  1001: '退款处理中'
};

// 需要封装keyword[xxx]的变量
export const keywordArr = [
  'period_send_time',
  'start_time',
  'end_time',
  'delivery_start_time',
  'delivery_end_time'
];

// 订单物流类型(业务用)
export const expressTypeNumMap = {
  0: '快递',
  1: '自提',
  2: '同城配送'
};

// 日期常量
const nowDate = new Date();
nowDate.setHours(0);
nowDate.setMinutes(0);
nowDate.setSeconds(0);
export const NOW = Number(nowDate);
export const NOWDATE = Number(new Date());
export const ONE_DAY = 1000 * 60 * 60 * 24;

// 查看最近30天订单filterData
export const thirtyDaysFilterData = {
  chooseDays: 30,
  value: [formatDatetime(NOW - 29 * ONE_DAY), formatDatetime(NOWDATE)]
};

// 最近30天 time区间  毫秒单位
export const thirtyDaysTimeValues = [NOW - 29 * ONE_DAY, NOWDATE];

export const datePickerDefaultTime = ['00:00:00', '23:59:59'];

// 活动订单类型(订单导出列表用)
export const activeTypeMap = {
  period: '周期购订单',
  pointsExchange: '积分兑换订单'
};

// 退款类型映射
export const refundTypeMap = {
  only_refund: '仅退款',
  refund_and_return: '退货退款'
};

// 退款状态映射
export const refundStateMap = {
  wait_seller_confirm_apply: '退款申请等待卖家确认',
  wait_buyer_modify: '卖家不同意协议，等待买家修改',
  wait_buyer_return_goods: '退款申请达成，等待买家退货',
  wait_seller_confirm_receive: '买家已退货，等待卖家确认收货',
  seller_refuse_apply: '卖家未收到货，不同意退款申请',
  refund_success: '退款成功',
  refund_close: '退款关闭'
};

export const csStatusMap = {
  not_involved: '未介入',
  has_involved: '已介入'
};

// 订单类型常量
// TODO: 进行完善
export const orderTypeMap = { GIFT_ORDER: 1 };

export const orderStateValue = {
  ALL: -1,
  TO_PAY: 3,
  TO_SEND: 5,
  PAID: 5,
  RECEIVED: 6,
  SEND: 6,
  TO_TUAN: 50,
  SUCCESS: 100,
  CACEL: 99,
  REFUNDING: 600001,
  REFUNDING_CLOSED: 600002
};

export const deliveryChannelMap = {
  1: '达达',
  2: '蜂鸟'
};

// 发货单详情，商品无需发货的原因
export const noExpressReasonDesc = {
  0: '',
  1: '全额退款',
  2: '订单取消'
};

let IS_UNIFIED_SHOP = isUnifiedShop; // eslint-disable-line

// ------mock s--------
// IS_UNIFIED_SHOP = true;
// ------mock e--------

export { IS_UNIFIED_SHOP };

// 店铺升级类型
export const shopUpgradeTypes = {
  // 大网店升级 -> 3.0
  UnitedHqStoreTo30: 5,
  // 多网店 -> 3.0
  SepHqStoreTo30: 6,
  // 多网店门店 -> 3.0
  SepBranchStoreTo30: 61
};

export const PRINT_BIZ_PICKING = 'DeliveryPicking'; // 拣货小票biz
export const PRINT_BIZ_SENDING = 'DeliverySending'; // 发货小票biz

export const writeOffVerifyType = {
  /** 必须进行有码核销，所以不应该展示「发货方式」 */
  WITH_VERIFY_CODE: 'WITH_VERIFY_CODE',
  /** 允许进行无码核销, 需要展示「发货方式」 */
  WITHOUT_VERIFY_CODE: 'WITHOUT_VERIFY_CODE'
};

export enum SyncSelfFetchInfo {
  UnSync,
  Sync
}

export const syncSelfFetchInfoTextMap = new Map([
  [SyncSelfFetchInfo.Sync, '开启'],
  [SyncSelfFetchInfo.UnSync, '关闭']
]);

export const syncSelfFetchInfoTextMapOptions = transformTextMapToOptions(syncSelfFetchInfoTextMap);
