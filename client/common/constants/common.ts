import { isPHShop, ShopAbility, ShopAbilityStatus } from '@youzan/utils-shop';
import { transformTextMapToOptions } from '@youzan/zan-hasaki/es/util';

/**
 * 销售渠道
 */

export enum ChannelType {
  /** 全部 */
  All = -1,
  /** 网店 */
  Online = 0,
  /** 门店 */
  Offline = 1
}

/**
 * 销售渠道文本与值的映射
 */

/** 普惠的销售渠道 */
export const PHChannelTypeTextMap = new Map([
  [ChannelType.All, '全部'],
  [ChannelType.Online, '小程序'],
  [ChannelType.Offline, '门店']
]);

/** 零售的销售渠道 */
export const RetailChannelTypeTextMap = new Map([
  [ChannelType.All, '全部'],
  [ChannelType.Online, '网店'],
  [ChannelType.Offline, '门店']
]);

/** 销售渠道, 统一出口 */
export const channelTypeTextMap = isPHShop ? PHChannelTypeTextMap : RetailChannelTypeTextMap;

/**
 * 销售渠道文本与值的数组对象
 */

export const channelTypeOptions = transformTextMapToOptions(channelTypeTextMap);

/**
 * 销售渠道销售发货专用
 */
export enum ChannelTypeOnlineshipments {
  /** 全部 */
  All = 'ALL',
  /** 网店 */
  YouzanOnline = 'YOUZAN_ONLINE',
  /** 饿了么 */
  ElemeFood = 'ELEME_FOOD',
  /** 美团 */
  MeituanFood = 'MEITUAN_FOOD',
  /** 餐道 */
  Candao = 'CAN_DAO',
  /** 门店预定 */
  YouzanStoreReserve = 'YOUZAN_STORE_RESERVE'
}

/**
 * 销售渠道文本与值的映射，销售发货专用
 */

export const channelTypeTextMapOnelineshipments = new Map([
  [ChannelTypeOnlineshipments.All, '全部'],
  [ChannelTypeOnlineshipments.YouzanOnline, '网店'],
  [ChannelTypeOnlineshipments.ElemeFood, '饿了么'],
  [ChannelTypeOnlineshipments.MeituanFood, '美团'],
  [ChannelTypeOnlineshipments.Candao, '餐道'],
  [ChannelTypeOnlineshipments.YouzanStoreReserve, '门店预定']
]);

/**
 * 销售渠道文本与值的数组对象，销售发货专用
 */

export const channelTypeOptionsOnlineshipments = transformTextMapToOptions(
  channelTypeTextMapOnelineshipments
);

/**
 * 支付方式
 */
export enum OrderPayWayType {
  /** 全部 */
  All = -1,
  /** 微信支付 */
  WxPayStore = 600001,
  /** 支付宝-商家扫 */
  AlipayMerchant = 204,
  /** 支付宝-用户付 */
  AlipayCustomer = 205,
  /** 支付宝 - 花呗 */
  AlipayHuabei = 4095,
  /** 货到付款 */
  CodPay = 9,
  /** 优惠兑换 */
  CouponPay = 16,
  /** 礼品卡付款 */
  GiftPay = 600008,
  /** 标记付款 */
  MarkPay = 600004,
  /** 现金 */
  OfCash = 201,
  /** 找人代付 */
  PeerPay = 7,
  /** 储值余额付款 */
  PrepaidCard = 600007,
  /** 领取赠品 */
  PresentPay = 15,
  /** 银行卡付款 */
  UmPay = 600003,
  /** 组合支付 */
  CombPay = 202,
  /** 农行商E付 */
  AbcEPay = 4093,
  /** 外部支付 */
  OutPay = 203,
  /** 对公转账 */
  CorporatePay = 206,
  /** 有赞云支付 */
  YouzanYunPay = 55,
  /** 云闪付支付 */
  QuickPay = 56,
  /** 建行支付 */
  CcbPay = 57,
  /** 数币支付 */
  EcnyPay = 58,
  /** 标记支付-换货 */
  MarkExchangePay = 995,
  /** 小红书-担保支付 */
  XhsLocalLife = 114
}

export const OrderBuyWayMap: { [propName: number]: string } = {
  [OrderPayWayType.All]: '全部',
  [OrderPayWayType.WxPayStore]: '微信支付',
  [OrderPayWayType.AlipayMerchant]: '支付宝-商家扫',
  [OrderPayWayType.AlipayCustomer]: '支付宝-用户付',
  [OrderPayWayType.AlipayHuabei]: '支付宝 - 花呗 ',
  [OrderPayWayType.CouponPay]: '优惠兑换',
  [OrderPayWayType.GiftPay]: '礼品卡支付',
  [OrderPayWayType.MarkPay]: '标记支付',
  [OrderPayWayType.OfCash]: '现金支付',
  [OrderPayWayType.PeerPay]: '找人代付',
  [OrderPayWayType.PrepaidCard]: '储值余额支付',
  [OrderPayWayType.PresentPay]: '领取赠品',
  [OrderPayWayType.UmPay]: '银行卡支付',
  [OrderPayWayType.CombPay]: '组合支付',
  [OrderPayWayType.AbcEPay]: '农行商E付',
  [OrderPayWayType.OutPay]: '外部支付',
  [OrderPayWayType.CorporatePay]: '对公转账',
  [OrderPayWayType.YouzanYunPay]: '有赞云支付',
  [OrderPayWayType.QuickPay]: '云闪付支付',
  [OrderPayWayType.CcbPay]: '建行支付',
  [OrderPayWayType.EcnyPay]: '数币支付',
  [OrderPayWayType.MarkExchangePay]: '标记支付-换货'
};

/**
 * 支付方式文本与值映射
 */
export const orderPayWayTypeTextMap = new Map([
  [OrderPayWayType.All, '全部'],
  [OrderPayWayType.WxPayStore, '微信支付'],
  [OrderPayWayType.AlipayMerchant, '支付宝-商家扫'],
  [OrderPayWayType.AlipayCustomer, '支付宝-用户付'],
  [OrderPayWayType.AlipayHuabei, '花呗支付'],
  [OrderPayWayType.CodPay, '货到付款'],
  [OrderPayWayType.CouponPay, '优惠兑换'],
  [OrderPayWayType.GiftPay, '礼品卡付款'],
  [OrderPayWayType.MarkPay, '标记付款'],
  [OrderPayWayType.OfCash, '现金'],
  [OrderPayWayType.PeerPay, '找人代付'],
  [OrderPayWayType.PrepaidCard, '储值余额付款'],
  [OrderPayWayType.PresentPay, '领取赠品'],
  [OrderPayWayType.UmPay, '银行卡付款'],
  [OrderPayWayType.CombPay, '组合支付'],
  [OrderPayWayType.AbcEPay, '农行商E付'],
  [OrderPayWayType.OutPay, '外部支付'],
  [OrderPayWayType.CorporatePay, '对公转账'],
  [OrderPayWayType.YouzanYunPay, '有赞云支付'],
  [OrderPayWayType.QuickPay, '云闪付支付'],
  [OrderPayWayType.CcbPay, '建行支付'],
  [OrderPayWayType.EcnyPay, '数币支付']
]);

/**
 * 支付方式文本与值的数组对象
 */
export const orderPayWayTypeOptions = transformTextMapToOptions(orderPayWayTypeTextMap);

/**
 * 订单中的商品类型
 */
export enum OrderGoodsType {
  /** 全部 */
  All = -1,
  /** 普通类型商品 */
  Common = 0,
  /** 拍卖商品 */
  Auction = 1,
  /** 餐饮商品 */
  Food = 5,
  /** 分销商品 */
  FenXiao = 10,
  /** 会员卡商品 */
  MemberCard = 20,
  /** 礼品卡商品 */
  GiftCard = 21,
  /** 有赞会议商品 */
  Meetings = 23,
  /** 周期购 */
  PeriodBuy = 24,
  /** 知识付费商品 */
  Knowledge = 31,
  /** 酒店商品 */
  Hotel = 35,
  /** 普通服务类商品 */
  Service = 40,
  /** 普通虚拟商品 */
  NormalVirtual = 182,
  /** 电子卡券商品 */
  VirtualTicket = 183,
  /** 外部会员卡商品 */
  OutMemberCard = 201,
  /** 外部直接收款商品 */
  OutCash = 202,
  /** 外部普通商品 */
  OutCommon = 203,
  /** mock不存在商品 */
  Mock = 205,
  /** 小程序二维码 */
  WxQrCode = 206
}

/**
 * 订单中的商品类型文本与值的映射
 */
export const orderGoodsTypeTextMap = new Map([
  [OrderGoodsType.All, '全部'],
  [OrderGoodsType.Common, '普通类型商品'],
  [OrderGoodsType.Auction, '拍卖商品'],
  [OrderGoodsType.Food, '餐饮商品'],
  [OrderGoodsType.FenXiao, '分销商品'],
  [OrderGoodsType.MemberCard, '会员卡商品'],
  [OrderGoodsType.GiftCard, '礼品卡商品'],
  [OrderGoodsType.Meetings, '有赞会议商品'],
  [OrderGoodsType.PeriodBuy, '周期购'],
  [OrderGoodsType.Knowledge, '知识付费商品'],
  [OrderGoodsType.Hotel, '酒店商品'],
  [OrderGoodsType.Service, '普通服务类商品'],
  [OrderGoodsType.NormalVirtual, '普通虚拟商品'],
  [OrderGoodsType.VirtualTicket, '电子卡券商品'],
  [OrderGoodsType.OutMemberCard, '外部会员卡商品'],
  [OrderGoodsType.OutCash, '外部直接收款商品'],
  [OrderGoodsType.OutCommon, '外部普通商品'],
  [OrderGoodsType.Mock, 'mock不存在商品'],
  [OrderGoodsType.WxQrCode, '小程序二维码']
]);

/**
 * 订单中的商品类型文本与值的数组对象
 */

export const orderGoodsTypeOptions = transformTextMapToOptions(orderGoodsTypeTextMap);

/**
 * 营销活动类型
 */
export enum UmpActivityType {
  /** 全部 */
  All = -1,
  /** 团购返现 */
  Tuan = 2,
  /** 降价拍 */
  Auction = 3,
  /** 多人拼团 */
  GroupOn = 4,
  /** 积分抵扣 */
  PointsExample = 5,
  /** 秒杀 */
  SecKill = 6,
  /** 优惠套餐 */
  PackageBuy = 7,
  /** 赠品领取 */
  PresentExchange = 8,
  /** 商品扫码 */
  GoodsScan = 9,
  /** 会员折扣 */
  CustomerDiscount = 10,
  /** 限时折扣 */
  TimeLimitedDiscount = 11,
  /** 周期购 */
  PeriodBuy = 13,
  /** 满减送 */
  MeetReduce = 101,
  /** 我要送礼社群版 */
  GiftCommunity = 63,
  /** 订单返现 */
  CashBack = 102,
  /** 打包一口价 */
  Bale = 104,
  /** 优惠券 */
  CouponCard = 105,
  /** 优惠卡 */
  CouponCode = 105,
  /** 整单优惠 */
  EntireDiscount = 106,
  /** 抹零 */
  OddChange = 107,
  /** 团长优惠 */
  GroupOnHeaderDiscount = 16
}

/**
 * 营销活动类型文本与值的映射
 */
export const umpActivityTypeTextMap = new Map([
  [UmpActivityType.All, '全部'],
  [UmpActivityType.Tuan, '团购返现'],
  [UmpActivityType.Auction, '降价拍'],
  [UmpActivityType.GroupOn, '多人拼团'],
  [UmpActivityType.PointsExample, '积分折扣'],
  [UmpActivityType.SecKill, '秒杀'],
  [UmpActivityType.PackageBuy, '优惠套餐'],
  [UmpActivityType.PresentExchange, '赠品领取'],
  [UmpActivityType.GoodsScan, '商品扫码'],
  [UmpActivityType.CustomerDiscount, '会员折扣'],
  [UmpActivityType.TimeLimitedDiscount, '限时折扣'],
  [UmpActivityType.PeriodBuy, '周期购'],
  [UmpActivityType.MeetReduce, '满减送'],
  [UmpActivityType.GiftCommunity, '我要送礼社群版'],
  [UmpActivityType.CashBack, '订单返现'],
  [UmpActivityType.Bale, '打包一口价'],
  [UmpActivityType.CouponCard, '优惠券'],
  [UmpActivityType.CouponCode, '优惠卡'],
  [UmpActivityType.EntireDiscount, '整单优惠'],
  [UmpActivityType.OddChange, '抹零'],
  [UmpActivityType.GroupOnHeaderDiscount, '团长优惠']
]);

/**
 * 订单商品类型
 */
export enum ProductType {
  /** 普通商品 */
  GoodsTypeDefault = 1,
  /** 无码商品 */
  GoodsTypeNoSku = 2,
  /** 赠品 */
  GoodsTypeGift = 3,
  /** 购物袋 */
  ShoppingBag = 4,
  /** 电子卡券 */
  YzVirtualTicket = 5,
  /** 权益卡卡商品 */
  MemberCard = 6,
  /** 付费等级商品 */
  MemberLevel = 7
}

/**
 * 订单状态
 */
export enum OrderStateType {
  /** 全部 */
  All = -1,
  /** 待付款 */
  ToPay = 3,
  /** 待发货 */
  ToSend = 5,
  /** 待接单, 酒店订单类型，待接单即是待发货  */
  Paid = 5,
  /** 已接单, 酒店订单类型，已接单即是已发货 */
  Received = 6,
  /** 已发货 */
  Send = 6,
  /** 待支付 */
  WaitPay = 10,
  /** 待成团 */
  ToTuan = 50,
  /** 待接单（餐道） */
  ToAccept = 50,
  /** 交易成功 */
  Success = 100,
  /** 交易关闭 */
  Cancel = 99,
  /** 退款中 */
  Refunding = 600001,
  /** 退款结束 */
  RefundingClosed = 600002
}

/**
 * 订单状态文本与值的映射
 */
export const orderStateTypeTextMap = new Map([
  [OrderStateType.All, '全部'],
  [OrderStateType.ToPay, '待付款'],
  [OrderStateType.Paid, '待接单'],
  [OrderStateType.ToSend, '待发货'],
  [OrderStateType.Received, '已接单'],
  [OrderStateType.Send, '已发货'],
  [OrderStateType.ToTuan, '待成团'],
  [OrderStateType.Success, '已成功'],
  [OrderStateType.Cancel, '已关闭'],
  [OrderStateType.Refunding, '退款中'],
  [OrderStateType.RefundingClosed, '退款结束'],
  [OrderStateType.ToAccept, '待接单']
]);

/**
 * 订单状态类型文本与值的数组对象
 */
export const orderStateTypeOptions = transformTextMapToOptions(orderStateTypeTextMap);

/**
 * 退款里用到的支付方式code
 */

export enum PayCodeType {
  /** 微信支付 */
  WxPay = 1,
  /** 微信支付代销 */
  WxPayDaiXiao = 10,
  /** 现金 */
  OfCash = 201,
  /** 标记支付-微信 */
  MarkPayWxPay = 110,
  /** 标记支付-支付宝 */
  MarkPayAliPay = 111,
  /** 标记支付-pos */
  MarkPayPos = 112,
  /** 刷卡 */
  AllInSwipeCard = 113,
  /** 组合支付 */
  CombPay = 202
}

/**
 * 订单来源
 */
export enum OrderSourceType {
  /** 全部 */
  All = -1,
  /** 小程序 */
  Applets = 1,
  /** 微商城 */
  MicroMall = 2,
  /** 有赞精选 */
  YzFeatured = 3,
  /** 浏览器 */
  Browser = 4,
  /** 支付宝 */
  Alipay = 5,
  /** 腾讯QQ */
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  QQ = 6,
  /** 微博 */
  WeiBo = 7,
  /** 百度-商家小程序 */
  BaiduApplets = 8,
  /** 爱逛 */
  LoveShopping = 9,
  /** 快手 */
  QuickerWorker = 10,
  /** 自助收银 */
  SelfService = 11,
  /** 24小时货架 */
  AllDayShelf = 12,
  /** 农行掌银App */
  AbcBank = 13,
  /** 饿了么 */
  Eleme = 29,
  /** 美团 */
  Meituan = 28,
  /** 微信视频号 */
  WeChatVideoNumber = 41,
  /** 云闪付 APP */
  Yunshanfu = 44,
  /** 建行生活 APP */
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  CCB = 45,
  /** 其他 */
  Others = 100
}

/**
 * 订单来源文本与值的映射
 */
export const orderSourceTypeTextMap = new Map([
  [OrderSourceType.All, '全部'],
  [OrderSourceType.Applets, '小程序'],
  [OrderSourceType.MicroMall, '微商城'],
  [OrderSourceType.YzFeatured, '有赞精选'],
  [OrderSourceType.Browser, '浏览器'],
  [OrderSourceType.Alipay, '支付宝'],
  [OrderSourceType.YzFeatured, '有赞精选'],
  [OrderSourceType.QQ, '腾讯QQ'],
  [OrderSourceType.WeiBo, '微博'],
  [OrderSourceType.BaiduApplets, '百度-商家小程序'],
  [OrderSourceType.LoveShopping, '爱逛'],
  [OrderSourceType.QuickerWorker, '快手'],
  [OrderSourceType.SelfService, '自助收银'],
  [OrderSourceType.AllDayShelf, '小程序点单宝'],
  [OrderSourceType.AbcBank, '农行掌银App'],
  [OrderSourceType.Eleme, '饿了么'],
  [OrderSourceType.Meituan, '美团'],
  [OrderSourceType.WeChatVideoNumber, '微信视频号'],
  [OrderSourceType.Yunshanfu, '云闪付APP'],
  [OrderSourceType.CCB, '建行生活APP'],
  [OrderSourceType.Others, '其他']
]);

/**
 * 订单来源文本与值的数组对象
 */

export const orderSourceTypeOptions = transformTextMapToOptions(orderSourceTypeTextMap);

/**
 * 自提的发货状态
 */

export enum FulfillSelfFetchType {
  /** 全部 */
  All = -1,
  /** 待自提 */
  WaitSelfFetch = 0,
  /** 已自提 */
  AlreadySelfFetch = 1,
  /** 无需自提 */
  NoNeedSelfFetch = 2
}

/**
 * 自提的发货状态文本与值的对应
 */

export const fulfillSelfFetchTypeTextMap = new Map([
  [FulfillSelfFetchType.All, '全部'],
  [FulfillSelfFetchType.WaitSelfFetch, '待自提'],
  [FulfillSelfFetchType.AlreadySelfFetch, '已自提'],
  [FulfillSelfFetchType.NoNeedSelfFetch, ' 无需自提']
]);

/**
 * 自提的发货状态文本与值的数组对象
 */

export const fulfillSelfFetchTypeOptions = transformTextMapToOptions(fulfillSelfFetchTypeTextMap);

/**
 * 发货方式
 */

export enum ExpressType {
  /** 全部 */
  All = -1,
  /** 全部 */
  ExpressCity = 600001,
  /** 快递 */
  Express = 0,
  /** 同城配送 */
  City = 2,
  /** 自提 */
  SelfFetch = 1
}
export interface IGeoLocation {
  lon?: number;
  lat?: number;
  longitude?: number;
  latitude?: number;
}

export enum IRecommendType {
  /** 无推荐 */
  None = 1,
  /** 超出配送范围无推荐改派 */
  ExceedAndNoReassignment = 2,
  /** 未超出配送范围无推荐改派，无弹窗直接调用修改地址接口 */
  NoExceedAndNoReassignment = 3,
  /** 超出配送范围推荐改派 */
  ExceedAndReassignment = 4,
  /** 未超出配送范围推荐改派 */
  NoExceedAndReassignment = 5
}

/**
 * 发货方式文本与值的映射
 */
export const expressTypeTextMap = new Map([
  [ExpressType.All, '全部'],
  [ExpressType.ExpressCity, '全部'],
  [ExpressType.Express, '快递'],
  [ExpressType.City, '同城配送'],
  [ExpressType.SelfFetch, '自提']
]);

/**
 * 发货方式文本与值的数组对象
 */

export const expressTypeOptions = transformTextMapToOptions(expressTypeTextMap);

/**
 * 库存类型
 */
export enum StockOccupyType {
  /** 实物库存 */
  Physicals = 0,
  /** 计划库存 */
  Plan = 2
}

export const stockOccupyTypeTextMap = new Map([
  [StockOccupyType.Physicals, '实物库存'],
  [StockOccupyType.Plan, '计划库存']
]);

export const stockOccupyTypeOptions = transformTextMapToOptions(stockOccupyTypeTextMap);

export const SALES_TEXT = '分销员';

export const GUIDE_TEXT = '导购员';

export const hasLiteAbility =
  window._global?.shopAbilityInfo?.[ShopAbility.OnlineGoodsNewUiAbility]?.abilityStatus ===
  ShopAbilityStatus.Available;

/**
 * 推广方式
 */
export enum MarketingType {
  All = -1,
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  CPS = 1,
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  THIRD_PART = 2,
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  SALES = 3,
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  GUIDE = 4
}

const marketingTypeTextMap = new Map([
  [MarketingType.All, '全部'],
  [MarketingType.CPS, '分佣推广(有赞客)'],
  [MarketingType.THIRD_PART, '三方应用推广']
]);

if (!hasLiteAbility) {
  marketingTypeTextMap.set(MarketingType.SALES, SALES_TEXT);
  marketingTypeTextMap.set(MarketingType.GUIDE, GUIDE_TEXT);
}

export { marketingTypeTextMap };

export const marketingTypeOptions = transformTextMapToOptions(marketingTypeTextMap);

/**
 * 核销状态
 */
export enum VerifyState {
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  DEFAULT, // 默认
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  NOT_VERIFIED, // 未核销
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  VERIFIED, // 已核销
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  DISABLED, // 已失效
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  EXPIRED, // 已过期
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  REFUNDING // 退款中
}

/**
 * 核销状态 Map
 */
export const VerifyStateMap: { [key: number]: string } = {
  [VerifyState.DEFAULT]: '默认',
  [VerifyState.NOT_VERIFIED]: '未核销',
  [VerifyState.VERIFIED]: '已核销',
  [VerifyState.DISABLED]: '已失效',
  [VerifyState.EXPIRED]: '已过期',
  [VerifyState.REFUNDING]: '退款中'
};

/**
 * 配送渠道类型
 */

export enum DeliveryChannelType {
  /** YOUZAN */
  Youzan = 0,
  /** 饿了么 */
  Eleme = 120,
  /** 美团 */
  Meituan = 20,
  /** 美团闪购 */
  MeituanShangou = 130,
  /** 餐道 */
  Candao = 30,
  /** 管易云 */
  // eslint-disable-next-line @youzan/yz-retail/typescript/prefer-pascal-case-enums
  GYY = 40,
  /** 视屏号小店 */
  WxChannelShop = 51,
  /** 抖音随心团 */
  TiktokSxt = 140,
  /** 京东外卖 */
  JDWM = 150
}

/**
 * 共享库存模式
 */
export enum StockMode {
  /** 独立销售 */
  IndependentShared = 1,
  /** 共享总部 */
  SharedByHq,
  /** 共享门店/仓库 */
  SharedByOffline
}

/**
 * 对接平台渠道
 */
export enum ChannelAccountType {
  /** 视屏号小店 */
  WxChannelShop = 19
}
