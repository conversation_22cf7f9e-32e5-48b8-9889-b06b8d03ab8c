/**
 * 常量 - 订单相关
 */

// 订单类型
export const ORDER_TYPE = {
  ALL: {
    value: -1,
    text: '全部'
  },
  NORMAL: {
    value: 0,
    text: '普通订单'
  },
  TUAN: {
    value: 10,
    text: '多人拼团订单'
  },
  FENXIAO: {
    value: 3,
    text: '分销供货订单'
  },
  FX_BUYER: {
    value: 600007,
    text: '分销买家订单'
  },
  HOTEL: {
    value: 35,
    text: '酒店订单'
  },
  POINT_STORE: {
    value: 600001,
    text: '积分兑换订单'
  },
  PEER_PAY: {
    value: 600002,
    text: '代付订单'
  },
  GIFT: {
    value: 1,
    text: '送礼订单'
  },
  GIFT_COMMUNITY: {
    value: 600005,
    text: '送礼订单社群版'
  },
  WISH: {
    value: 5,
    text: '心愿订单'
  },
  FREE_GO: {
    value: 106,
    text: '扫码点单订单'
  },
  SCAN_GO: {
    value: 107,
    text: '自助结账订单'
  },
  FEEDBACK: {
    value: 600003,
    text: '维权订单'
  },
  PERIOD: {
    value: 600004,
    text: '周期购订单'
  },
  COMMUNITY_GROUPON: {
    value: 600009,
    text: '社区团购订单'
  },
  KNOWLEDGE: {
    value: 75,
    text: '知识付费订单'
  },
  QRCODE: {
    value: 6,
    text: '扫码收款订单'
  },
  OFFLINE_PRE_BUY: {
    value: 600006,
    text: '门店预定'
  },
  ZERO_DRAW: {
    value: 600008,
    text: '0元抽奖'
  },
  ENTITY_CARD: {
    value: 600010,
    text: '实体卡订单'
  },
  POINT_CARD: {
    value: 600012,
    text: '集点兑换订单'
  },
  PRE_SALE: {
    value: 27,
    text: '定金预售订单'
  },
  FULL_PAY_PRE_SALE: {
    value: 42,
    text: '全款预售订单'
  },
  DOUBLE_ORDER: {
    value: 43,
    text: '一品双开订单'
  },
  BEFORE_ORDER: {
    value: 44,
    text: '先囤订单'
  },
  AFTER_ORDER: {
    value: 45,
    text: '后约订单'
  },
  BLIND_BOX: {
    value: 600013,
    text: '盲盒订单'
  },
  BLIND_BOX_EXCHANGE: {
    value: 600014,
    text: '盲盒兑换订单'
  },
  MEDICAL_ORDER: {
    value: 600015,
    text: '处方药订单'
  },
  JOINT_ORDER: {
    value: 600016,
    text: '好友拼单'
  },
  ONLINE_SCAN_BUY: {
    value: 600017,
    text: '扫码购订单'
  },
  RECHARGE_ORDER_FREE: {
    value: 600018,
    text: '充值优惠订单'
  },
  PAY_COUPONS: {
    value: 600019,
    text: '付费优惠券订单'
  },
  ENJOY_BUY: {
    value: 32766,
    text: '随心购'
  }
};

// 订单状态
export const ORDER_STATE = {
  ALL: {
    value: -1,
    text: '全部'
  },
  TO_PAY: {
    value: 3,
    text: '待付款'
  },
  TO_SEND: {
    value: 5,
    text: '待发货'
  },
  // 酒店订单类型，待接单即是待发货
  PAID: {
    value: 5,
    text: '待接单'
  },
  // 酒店订单类型，已接单即是已发货
  RECEIVED: {
    value: 6,
    text: '已接单'
  },
  SEND: {
    value: 6,
    text: '已发货'
  },
  TO_TUAN: {
    value: 50,
    text: '待成团'
  },
  // 处方药订单新增
  TO_REVIEW: {
    value: 50,
    text: '审核中'
  },
  SUCCESS: {
    value: 100,
    text: '已成功'
  },
  CANCEL: {
    value: 99,
    text: '已关闭'
  },
  REFUNDING: {
    value: 600001,
    text: '售后中'
  },
  REFUNDING_CLOSED: {
    value: 600002,
    text: '退款结束'
  },
  TO_ACCEPT: {
    value: 50,
    text: '待接单'
  }
};

// 店铺查询列表增加收银相关的入参，(订单查询、售后维权、批量退款、订单评价这种订单纬度不展示crm门店)
export const AbilityCodesContainInvalid = ['pos_ability', 'online_store_base_ability'];

/** 订单列表标题适配 */
export const OrderNoTitleRecord = {
  normal: '订单编号',
  exchange: '换货单号'
};

/**
 * 订单搜索，时间类型
 */

export enum TimeType {
  BookTime = 'book_time',
  ShipTime = 'ship_time',
  PayTime = 'pay_time',
  SuccessTime = 'success_time'
}

export const TimeTypeTextMap = {
  [TimeType.BookTime]: '下单时间',
  [TimeType.ShipTime]: '发货时间',
  [TimeType.PayTime]: '支付时间',
  [TimeType.SuccessTime]: '交易完成时间'
};

/**
 * 销售渠道
 */
export enum SaleWay {
  /** 网店 */
  Online = 0,
  /** 门店 */
  Offline = 1
}

/**
 * 退款方式
 */
export enum ReturnType {
  /** 整单全部退款 */
  FullRefund = 1,
  /** 整单部分退款  */
  PartialRefund = 2
}

/**
 * 换货流程类型
 */
export enum ExchangeFlowType {
  /** 门店老换货 */
  OfflineOldExchangeFlow = 1,
  /** 门店换货1.0 */
  OfflineExchangeFlowV1 = 2,
  /** 网店换货 */
  OnlineExchangeFlow = 3,
  /** 门店换货2.0 */
  OfflineExchangeFlowV2 = 4
}
