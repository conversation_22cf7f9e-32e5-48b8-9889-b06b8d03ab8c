import type { Dictionary } from 'lodash';
import { mapValues, omit } from 'lodash';
import { ORDER_TYPE, ORDER_STATE } from './order';

interface OrderConstantItem {
  text: string;
  value: number;
}

const mapModelText = (data: Record<string, Pick<OrderConstantItem, 'text'>>): Dictionary<string> =>
  mapValues(data, item => item.text);

const mapModelValue = (
  data: Record<string, Pick<OrderConstantItem, 'value'>>
): Dictionary<number> => mapValues(data, item => item.value);

// 酒店订单的订单状态map
export const hotelOrderStateText = mapModelText(omit(ORDER_STATE, ['TO_SEND', 'SEND']));

export const rxOrderStateText = mapModelText(omit(ORDER_STATE, ['TO_TUAN', 'TO_ACCEPT']));

// 普通订单的订单状态map
export const commonOrderStateText = mapModelText(omit(ORDER_STATE, ['PAID', 'RECEIVED']));

export const orderTypeText = mapModelText(ORDER_TYPE);

// -----------------------------

export const orderStateValue = mapModelValue(ORDER_STATE);
// 酒店订单的订单状态map
export const hotelOrderStateValue = mapModelValue(omit(ORDER_STATE, ['TO_SEND', 'SEND']));
// 处方单
export const rxOrderStateValue = mapModelValue(omit(ORDER_STATE, ['TO_TUAN', 'TO_ACCEPT']));

// 普通订单的订单状态map
export const commonOrderStateValue = mapModelValue(omit(ORDER_STATE, ['PAID', 'RECEIVED']));
export const orderTypeValue = mapModelValue(ORDER_TYPE);
