import { global } from '@youzan/retail-utils';
import { FulFillType } from '@youzan/zan-hasaki';
import { ExpressType } from 'common/constants/common';
import { TimeType } from 'common/constants/order';

import { orderTypeValue } from './models';

const { SelfFetch, Express, City } = ExpressType;
const { KDT_ID } = global;

export const defaultPageInfo = {
  pageNo: 1,
  total: 0,
  pageSize: 20
};

export const defaultPageInfoKeys = Object.keys(defaultPageInfo);

export const defaultQueryFilter = {
  startTime: '',
  endTime: '',
  planExpressTime: '',
  deliveryStartTime: '',
  deliveryEndTime: '',
  expressType: -1,
  orderType: -1,
  searchLabel: 'order_no',
  searchField: '',
  goodsTitle: '',
  orderState: -1,
  isReminderStatus: -1,
  hasStar: -1,
  feedbackState: -1,
  cashierId: '',
  buyWay: -1,
  saleWay: -1,
  storeId: '',
  orderSource: -1
};

// retail-form版本
export const defaultQueryFilterRf = {
  orderTime: ['', ''],
  planExpressTime: '',
  deliveryTime: ['', ''],
  expressType: -1,
  timeType: TimeType.BookTime,
  orderType: -1,
  searchLabel: 'order_no',
  searchField: '',
  goodsTitle: '',
  orderState: -1,
  isReminder: -1,
  isReminderStatus: -1,
  hasStar: -1,
  feedbackState: -1,
  cashierId: '',
  buyWay: -1,
  saleWay: -1,
  storeId: '',
  salesId: '', // TODO 导购员 带验证
  orderSource: -1,
  brandId: 0,
  tableId: -1,
  brandIdList: [-1],
  classificationIdList: [-1]
};

/** 普惠版 Filter 默认值 */
export const defaultPHQueryFilterRf = {
  orderTime: ['', ''],
  expressType: -1,
  searchLabel: 'order_no',
  searchField: '',
  goodsTitle: '',
  buyWay: -1,
  saleWay: -1
};

export const defaultShipmentsFilter = {
  orderTime: ['', ''],
  planExpressTime: '',
  deliveryTime: ['', ''],
  expressType: ExpressType.All,
  orderType: orderTypeValue.ALL,
  searchLabel: 'order_no',
  searchField: ''
};

export const defaultChainShipmentsFilter = {
  orderTime: ['', ''],
  planExpressTime: '',
  deliveryTime: ['', ''],
  expressType: ExpressType.All,
  fulfillStatus: 0, // 默认选择待发货
  searchLabel: 'fulfill_no',
  searchField: '',
  orderType: -1,
  expressState: '',
  shopKdtId: -1,
  isReminderStatus: -1,
  deliveryTimeFetchSort: 1, // 按自提时间正序排列项 默认选中
  fulfillSaleWay: 'ALL'
};

export const defaultSelffetchFilter = {
  orderTime: ['', ''],
  expressType: SelfFetch,
  orderType: orderTypeValue.ALL,
  searchLabel: 'order_no',
  fulfillStatus: FulFillType.WaitDeliver, // 显示的是待自提的列表，因此应当默认筛选未发货的状态
  searchField: '',
  selfFetchId: -1,
  selfFetchTime: ['', '']
};

export const defaultSaleWayStore = [
  { text: '全部', value: -1 },
  { text: '网店', value: KDT_ID }
];
export const defaultPureSaleWayStore = [{ text: '全部', value: -1 }];

export const defaultRefundFilterRf = {
  applyTime: ['', ''],
  orderNo: '',
  refundId: '',
  csStatus: -1,
  type: -1,
  demand: -1,
  feedbackState: -1,
  shopKdtId: -1,
  saleWay: -1
};

export const defaultBatchExpressFilter = {
  orderType: orderTypeValue.ALL,
  expressType: Express
};

export const defaultBatchChooseShipmentsFilter = {
  orderTime: ['', ''],
  planExpressTime: '',
  deliveryTime: ['', ''],
  expressType: City,
  orderType: orderTypeValue.ALL,
  searchLabel: 'order_no',
  searchField: ''
};
