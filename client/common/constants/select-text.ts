import { values, mapValues, omit, pick, get } from 'lodash';
import {
  isRetailSingleStore,
  isRetailMinimalistShop,
  ShopAbility,
  isPHShop
} from '@youzan/utils-shop';
import { refundStatusTypeOptions, RefundStatusType } from '@youzan/zan-hasaki';
import {
  OrderPayWayType,
  orderPayWayTypeOptions,
  expressTypeOptions,
  ExpressType
} from 'common/constants/common';

import { ORDER_TYPE, ORDER_STATE } from './order';

// 店铺是否开通过群团团
const hasQttParty = get(window, '_global.hasQttParty');
const shopAbilityInfo = get(window, '_global.shopAbilityInfo');
const hasLiteAbility: boolean =
  (shopAbilityInfo[ShopAbility.OnlineGoodsNewUiAbility] || {}).abilityStatus === 1;
const hasProductMasterDataManageAbility: boolean =
  (shopAbilityInfo[ShopAbility.ProductMasterDataManageAbility] || {}).abilityStatus === 1;

const ALL: any = {
  order_no: '订单编号',
  outer_transaction_number: '外部单号',
  user_name: '收货人姓名',
  tel: '收货人手机号',
  offline_order_no: '离线订单编号',
  buyer_phone: '买家手机号',
  fulfill_no: '发货单号',
  express_no: '物流单号',
  sku_no: '商品编码',
  bar_code: '商品条码',
  buyer_phone_last_4: '买家手机号后四位',
  receiver_phone_last_4: '收货人手机号后四位',
  promoter_id: '视频号名称',
  out_pay_no: '支付流水号',
  participate_no: '群团团跟团号'
};

// 连锁d极简版、高级版、新专业版 商品条码还是要用 bar_code
if (!isRetailMinimalistShop && !hasLiteAbility && !hasProductMasterDataManageAbility && !isPHShop) {
  // eslint-disable-next-line dot-notation
  ALL.sku_no = '商品条码';
  delete ALL.bar_code;
}

const SINGLE = {
  order_no: '订单编号',
  outer_transaction_number: '外部单号',
  user_name: '收货人姓名',
  tel: '收货人手机号',
  offline_order_no: '离线订单编号',
  buyer_phone: '买家手机号',
  express_no: '物流单号',
  [hasProductMasterDataManageAbility ? 'bar_code' : 'sku_no']: '商品条码',
  buyer_phone_last_4: '买家手机号后四位',
  receiver_phone_last_4: '收货人手机号后四位',
  promoter_id: '视频号名称',
  out_pay_no: '支付流水号'
};

if (hasQttParty) {
  SINGLE.participate_no = '群团团跟团号';
}

// 订单搜索 字典
export const searchLabelMap = {
  ALL,
  SHIPMENTS: {
    order_no: '订单编号',
    outer_transaction_number: '外部单号',
    user_name: '收货人姓名',
    tel: '收货人手机号',
    buyer_phone: '买家手机号',
    buyer_phone_last_4: '买家手机号后四位',
    receiver_phone_last_4: '收货人手机号后四位'
  },
  FETCHLIST: {
    tel: '提货人手机号',
    user_name: '提货人姓名',
    order_no: '订单编号',
    buyer_phone_last_4: '买家手机号后四位',
    receiver_phone_last_4: '收货人手机号后四位'
  },
  CHAIN_FETCHLIST: {
    order_no: '订单编号',
    fulfill_no: '发货单号',
    tel: '提货人手机号',
    user_name: '提货人姓名'
  },
  CHAIN_SHIPMENTS: {
    order_no: '订单编号',
    fulfill_no: '发货单号',
    user_name: '收货人姓名',
    tel: '收货人手机号'
  },
  FEN_XIAO: {
    order_no: '订单编号',
    outer_transaction_number: '外部单号',
    user_name: '收货人姓名',
    tel: '收货人手机号',
    offline_order_no: '离线订单编号',
    sku_no: '商品条码'
  },
  SINGLE,
  PH: {
    order_no: '订单编号',
    outer_transaction_number: '外部单号',
    user_name: '收货人姓名',
    tel: '收货人手机号',
    offline_order_no: '离线订单编号',
    buyer_phone: '买家手机号',
    express_no: '物流单号',
    bar_code: '商品条码',
    buyer_phone_last_4: '买家手机号后四位',
    receiver_phone_last_4: '收货人手机号后四位',
    promoter_id: '视频号名称'
  }
};

// 对象格式化成select所需的格式
function formatToSelectData(objData: Record<string, string>): Array<{
  value: string;
  text: string;
}> {
  return Object.keys(objData).map(key => ({
    value: key,
    text: objData[key]
  }));
}

// 订单搜索关键字类型
export const searchLabel = mapValues(searchLabelMap, value => formatToSelectData(value));

// 根据店铺情况, 屏蔽订单类型 (老逻辑, 从 constants/models 挪过来)
const omitOrderTypes = isRetailSingleStore ? [] : ['FX_BUYER'];
const orderTypeMap = omit(ORDER_TYPE, omitOrderTypes);

const onlineOmitOrderTypes = ['QRCODE', 'FREE_GO', 'SCAN_GO', 'OFFLINE_PRE_BUY'];
let filteredOrderTypes = values(orderTypeMap);

if (!isRetailSingleStore) {
  onlineOmitOrderTypes.push('GIFT_COMMUNITY');
  filteredOrderTypes = values(omit(orderTypeMap, 'GIFT_COMMUNITY'));
}
const onlineOrderTypes = values(omit(orderTypeMap, onlineOmitOrderTypes));

// 订单类型
export const orderTypeLabel = {
  ALL: filteredOrderTypes,
  ONLINE: onlineOrderTypes,

  // 给网店发货用的
  ONLINE_EXPRESS: values(omit(orderTypeMap, ['QRCODE', 'FREE_GO', 'SCAN_GO', 'ZERO_DRAW'])),
  OFFLINE: values(
    pick(orderTypeMap, [
      'ALL',
      'NORMAL',
      'QRCODE',
      'POINT_STORE',
      'FREE_GO',
      'SCAN_GO',
      'OFFLINE_PRE_BUY'
    ])
  ),

  // 发货单使用
  FULFILLORDER: values(
    omit(orderTypeMap, [
      'HOTEL',
      'KNOWLEDGE',
      'FEEDBACK',
      'PEER_PAY',
      'SCAN_GO',
      'FREE_GO',
      'QRCODE',
      'ZERO_DRAW'
    ])
  )
};

// 订单状态, 根据订单类型联动
const orderStateAllLabel = values(
  pick(ORDER_STATE, ['ALL', 'TO_PAY', 'TO_SEND', 'SEND', 'SUCCESS', 'CANCEL', 'REFUNDING'])
);

export const orderStateLabel: { [key: string]: Array<{ text: string; value: number }> } = {
  ALL: orderStateAllLabel,
  NORMAL: orderStateAllLabel,
  TUAN: values(
    pick(ORDER_STATE, [
      'ALL',
      'TO_PAY',
      'TO_TUAN',
      'TO_SEND',
      'SEND',
      'SUCCESS',
      'CANCEL',
      'REFUNDING'
    ])
  ),
  FENXIAO: orderStateAllLabel,
  FX_BUYER: orderStateAllLabel,
  HOTEL: values(
    pick(ORDER_STATE, ['ALL', 'TO_PAY', 'PAID', 'RECEIVED', 'SUCCESS', 'CANCEL', 'REFUNDING'])
  ),
  POINT_STORE: orderStateAllLabel,
  PEER_PAY: orderStateAllLabel,
  // 集点兑换订单
  POINT_CARD: orderStateAllLabel,
  // 实体卡订单
  ENTITY_CARD: orderStateAllLabel,
  // 预售订单
  PRE_SALE: orderStateAllLabel,
  GIFT: orderStateAllLabel,
  GIFT_COMMUNITY: orderStateAllLabel,
  WISH: orderStateAllLabel,
  FEEDBACK: values(pick(ORDER_STATE, ['ALL', 'REFUNDING', 'REFUNDING_CLOSED'])),
  PERIOD: orderStateAllLabel,
  KNOWLEDGE: orderStateAllLabel,
  QRCODE: values(pick(ORDER_STATE, ['ALL', 'TO_PAY', 'SUCCESS', 'CANCEL'])),
  SCAN_GO: values(pick(ORDER_STATE, ['ALL', 'TO_PAY', 'SUCCESS', 'CANCEL'])),
  FREE_GO: values(pick(ORDER_STATE, ['ALL', 'TO_PAY', 'TO_SEND', 'SUCCESS', 'CANCEL'])),
  COMMUNITY_GROUPON: orderStateAllLabel,
  // TODO: 这个待确定  没找到订单类型
  OFFLINE: values(pick(ORDER_STATE, ['ALL', 'TO_PAY', 'SUCCESS', 'CANCEL', 'TO_SEND'])),
  // 门店预定
  OFFLINE_PRE_BUY: values(pick(ORDER_STATE, ['ALL', 'TO_PAY', 'SUCCESS', 'TO_SEND', 'CANCEL'])),
  ZERO_DRAW: orderStateAllLabel,
  BLIND_BOX: values(pick(ORDER_STATE, ['ALL', 'TO_PAY', 'SUCCESS', 'CANCEL'])),
  BLIND_BOX_EXCHANGE: values(pick(ORDER_STATE, ['ALL', 'SUCCESS'])),
  // 处方药订单
  MEDICAL_ORDER: values(
    pick(ORDER_STATE, [
      'ALL',
      'TO_PAY',
      'SEND',
      'TO_REVIEW',
      'SUCCESS',
      'CANCEL',
      'REFUNDING',
      'REFUNDING_CLOSED'
    ])
  ),
  ENJOY_BUY: values(pick(ORDER_STATE, ['TO_PAY', 'CANCEL', 'TO_SEND', 'SEND', 'SUCCESS']))
};

export const orderStateLabelWithSaleWay = {
  ALL: orderStateLabel,
  ONLINE: omit(orderStateLabel, ['OFFLINE', 'QRCODE']),
  ONLINE_EXPRESS: omit(orderStateLabel, ['OFFLINE', 'QRCODE']),
  OFFLINE: {
    ALL: orderStateLabel.FREE_GO,
    NORMAL: orderStateLabel.QRCODE,
    QRCODE: orderStateLabel.QRCODE,
    POINT_STORE: orderStateLabel.QRCODE,
    FREE_GO: orderStateLabel.FREE_GO,
    SCAN_GO: orderStateLabel.SCAN_GO,
    OFFLINE_PRE_BUY: orderStateLabel.OFFLINE_PRE_BUY
  }
};

// 发货状态
export const expressTypeLabel = {
  ALL: expressTypeOptions.filter(item => item.value !== ExpressType.ExpressCity),
  ONLINE: expressTypeOptions.filter(item => item.value !== ExpressType.ExpressCity),
  ONLINE_EXPRESS: expressTypeOptions.filter(item => item.value !== ExpressType.ExpressCity),
  OFFLINE: expressTypeOptions.filter(
    item => item.value === ExpressType.All || item.value === ExpressType.SelfFetch
  )
};

export const refundMapLabel = {
  ALL: refundStatusTypeOptions,
  ONLINE: refundStatusTypeOptions,
  OFFLINE: refundStatusTypeOptions.filter(item => item.value === RefundStatusType.All)
};

// 支付方式
export const buyWayLabel = {
  ALL: orderPayWayTypeOptions,
  ONLINE: orderPayWayTypeOptions.filter(
    item => item.value !== OrderPayWayType.MarkPay && item.value !== OrderPayWayType.OfCash
  ),
  OFFLINE: orderPayWayTypeOptions.filter(
    item =>
      item.value !== OrderPayWayType.CodPay &&
      item.value !== OrderPayWayType.PeerPay &&
      item.value !== OrderPayWayType.PresentPay &&
      item.value !== OrderPayWayType.CouponPay
  )
};

// 加星订单
export const starLabel = [
  { text: '全部', value: -1 },
  { text: '加星', value: true }
];

export const reminderLabel = [
  { text: '全部', value: -1 },
  { text: '催单', value: true }
];
