import { request } from '@youzan/retail-utils';
import { isUnifiedShop, branchStoreRole, isRetailMinimalistShop } from '@youzan/utils-shop';

interface ShopQueryParam {
  pageSize: number;
  shopRoleList?: number[];
}

// retail-utils 没有导出 request 方法的 interface, 不知道有没有办法复用, 暂时先复制过来
interface IRequest {
  /** 请求地址 */
  url: string;
  /** 请求方式 */
  method?: 'get' | 'post' | 'delete' | 'put' | 'options';
  rawResponse?: boolean;
  /** 参数 trim */
  needTrim?: boolean;
  /** 参数 */
  data?: Record<string, any>;
}

interface FullFetch extends IRequest {
  reducer?: Function;
}

interface ResponseShape {
  paginator: { totalCount: number; pageSize: number };
  items: any[];
}

const fullFetch = async ({ url, data }: FullFetch): Promise<ResponseShape> => {
  const response = request({ url, data });

  let totalData: ResponseShape;
  const requestQueue: Array<Promise<any>> = [];

  return response.then((res: ResponseShape) => {
    totalData = res;
    const {
      paginator: { totalCount, pageSize }
    } = res;
    const pages = totalCount / pageSize;

    if (pages > 1) {
      for (let i = 1; i < pages; i++) {
        const partialParam = { data: { ...data, pageNo: i + 1 }, url };
        requestQueue.push(request(partialParam));
      }

      return Promise.all(requestQueue).then(values => {
        values.forEach(res => totalData.items.push(...res.items));
        return totalData;
      });
    }
    return totalData;
  });
};

export async function fetchChainShopList(): Promise<ResponseShape> {
  const data: ShopQueryParam = {
    pageSize: 300
  };

  // 连锁 3.0 店铺, 总部不具备网店能力, 因此要排除在外
  (isUnifiedShop || isRetailMinimalistShop) && (data.shopRoleList = [branchStoreRole]);

  const url =
    isUnifiedShop || isRetailMinimalistShop
      ? 'youzan.retail.shop.hq.store.datapermission/1.0.0/page'
      : 'youzan.retail.shop.hq.store.manage/1.0.0/page';

  return fullFetch({
    url,
    data
  });
}

/** 订单筛选项下发配置接口 开始 */
export type ISaleWayMap = 'ALL' | 'ONLINE' | 'OFFLINE';

export interface OptionShape {
  text: string;
  value: number;
  enableChannels?: string[];
}

export type OptionShapeArr = OptionShape[];

export interface ConditionShape {
  display: string;
  paramValue: string;
  displayAtSaleWay: ISaleWayMap[];
}

export type KeyValueTextMap = ConditionShape[];

interface IFetchFilterOptions {
  shopRole?: number;
  shopTopic?: number;
  type: 'ordertype' | 'payway' | 'ordersource';
  saasSolution?: number;
  adminId?: number;
  shopType?: number;
  isFulfillOrderList?: boolean;
  saleWay?: ISaleWayMap;
  isFetchOrderList?: boolean;
}

/** 将后端下发的订单筛选项, 转换成前端组件需要的数据结构 */
export const formatter = ({
  display,
  paramValue: value,
  displayAtSaleWay
}: ConditionShape): OptionShape => ({
  text: display,
  value: +value,
  enableChannels: displayAtSaleWay
});

export const fetchFilterOptions = (data: IFetchFilterOptions): Promise<KeyValueTextMap> => {
  return request({
    url: 'youzan.retail.trade.manager.ordersearch.condition.fetch/1.0.0',
    data
  });
};
/** 订单筛选项下发配置接口 结束 */
