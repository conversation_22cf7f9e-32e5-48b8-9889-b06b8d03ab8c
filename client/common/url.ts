import { isPHShop } from '@youzan/utils-shop';
import { setUrlDomain } from '@youzan/retail-utils';
import args from '@youzan/utils/url/args';
import { ExchangeFlowType } from './constants/order';

export const customerDetailURL = isPHShop
  ? /** 旺小店需要保持三级域名, 不能用绝对路径 */ '/v4/scrm/ph/customer/manage#/detail?yzUid='
  : setUrlDomain('/v4/scrm/customer/manage#/detail?yzUid=', 'store');

/**
 * 获取网店换货售后单详情
 * @returns string
 */
export const getOnlineExchangeUrl = ({
  orderNo,
  refundId,
  itemId
}: {
  orderNo: string;
  refundId: string;
  itemId?: string;
}): string => {
  let result = `/v4/trade/refund/detail?orderNo=${orderNo}`;
  if (itemId) {
    result = args.add(result, { itemId });
  }
  if (refundId) {
    result = args.add(result, { refundId });
  }
  return result;
};

/**
 * 获取换货售后单详情
 * @returns string
 */
export const getExchangeDetailUrl = ({
  orderNo,
  refundId,
  exchangeOrderNo,
  itemId,
  /** 是否是网店换货 */
  isNewExchangeFlow,
  exchangeType
}: {
  orderNo: string;
  refundId: string;
  exchangeOrderNo: string;
  itemId?: string;
  isNewExchangeFlow?: boolean;
  exchangeType?: ExchangeFlowType;
}): string => {
  /** 新网店换货流程跳转到 /v4/trade/refund/detail */
  if (isNewExchangeFlow) {
    return getOnlineExchangeUrl({ orderNo, refundId, itemId });
  }
  return `/v2/order/exchange/detail#/?order_no=${orderNo}&exchange_no=${
    exchangeType === ExchangeFlowType.OfflineExchangeFlowV1 ? exchangeOrderNo : refundId
  }&exchange_type=${exchangeType}`;
};
