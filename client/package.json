{"dependencies": {"@types/blueimp-md5": "^2.18.0", "@types/js-cookie": "^3.0.0", "@youzan/biz-goods-selector": "3.6.2", "@youzan/biz-select-center": "3.7.0", "@youzan/captcha-verify-web": "1.0.2", "@youzan/content-loader-react": "1.0.2", "@youzan/export-center": "5.0.1", "@youzan/goods-domain-pc-components": "1.4.2", "@youzan/goods-params-select": "1.2.0", "@youzan/hooks": "1.0.0-beta.3", "@youzan/micro-transfer-dialog": "2.0.7", "@youzan/old-browser-modal": "1.5.0", "@youzan/order-domain-pc-components": "1.1.2-beta.20250610191513.0", "@youzan/payback-ads": "1.0.14", "@youzan/ranta-cloud-react": "2.12.3", "@youzan/react-amap": "6.1.0", "@youzan/react-components": "4.11.8-rc.12", "@youzan/react-hooks": "1.1.1", "@youzan/retail-armor": "2.1.1", "@youzan/retail-components": "4.10.0", "@youzan/retail-form": "2.0.0", "@youzan/retail-lazyload": "2.0.1", "@youzan/retail-utils": "5.10.6", "@youzan/runtime-sdk": "2.0.0", "@youzan/sam-components": "1.1.0", "@youzan/shop-ability": "1.0.0", "@youzan/subsidy-ads": "1.0.6", "@youzan/utils": "2.2.9", "@youzan/utils-shop": "2.7.10", "@youzan/zan-hasaki": "2.19.12", "@zent/compat": "2.0.0", "blueimp-md5": "^2.19.0", "classnames": "^2.2.5", "date-fns": "^1.28.5", "detect-browser": "^5.2.1", "downloadjs": "^1.4.7", "immer": "^9.0.6", "immutability-helper": "^2.4.0", "js-cookie": "^3.0.1", "lodash": "^4.17.0", "prop-types": "^15.6.0", "query-string": "^6.13.1", "react": "17.0.2", "react-amap": "^1.2.4", "react-calendar": "^3.6.0", "react-dnd": "7.4.5", "react-dnd-html5-backend": "^7.4.4", "react-dom": "17.0.2", "react-loadable": "^5.5.0", "react-router-dom": "5.1.0", "styled-components": "4.1.3", "use-immer": "^0.6.0", "wx-channel-shop-printer": "0.1.0", "zent": "9.7.2"}, "devDependencies": {"@babel/preset-typescript": "^7.3.3", "@kokojs/cli": "^1.5.1", "@types/classnames": "^2.2.7", "@types/downloadjs": "^1.4.2", "@types/enzyme": "^3.9.1", "@types/jest": "^24.0.12", "@types/lodash": "^4.14.123", "@types/react": "^16.8.16", "@types/react-calendar": "^3.5.0", "@types/react-dom": "^16.9.8", "@types/react-router-dom": "^4.3.3", "@types/store": "^2.0.2", "@types/styled-components": "^5.1.0", "@youzan-cloud/cloud-biz-types": "^1.1.74", "@youzan/eslint-config-biz-cloud": "^2.14.30", "@youzan/koko-plugin-biz-cloud": "^2.14.30", "@youzan/koko-plugin-inspector": "1.0.14", "@youzan/koko-preset-retail-pc": "3.32.12", "babel-jest": "^24.1.0", "enzyme": "^3.8.0", "enzyme-adapter-react-16": "^1.9.1", "eslint-import-resolver-webpack": "^0.11.0", "eslint-plugin-import": "^2.16.0", "jest": "^24.1.0", "jest-canvas-mock": "^2.1.0", "jest-css-modules": "^1.1.0", "jest-junit": "^6.4.0", "promise.prototype.finally": "^3.1.0", "raw-loader": "^0.5.1", "svg-sprite-loader": "^4.1.1", "ts-jest": "^24.0.2", "utility-types": "^3.10.0"}, "private": true, "scripts": {"init": "yarn --prod=false --registry=http://registry.npm.qima-inc.com", "prd": "koko build && koko cdn", "dev": "koko dev", "analyze": "superman2 -c superman.js analyze", "eslint-fix": "npx eslint --ext .js,.jsx  --fix ./ --ignore-pattern '/node_modules/*' --ignore-pattern '/__jest__/*'", "test": "jest __jest__/__tests__", "coverage": "npx jest __jest__/__tests__ --coverage --forceExit --detectOpenHandles", "precommit": "jest --bail --findRelatedTests --forceExit"}, "browserslist": ["> 1% in alt-as", "Chrome >= 49", "Safari >= 10", "Firefox >= 78"]}