const _global = require('../data/global.json');

require('promise.prototype.finally').shim();

Object.defineProperty(window, '_global', {
  writable: true,
  value: _global
});

Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(() => {})
});

window.matchMedia = jest.fn().mockImplementation(query => {
  return {
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
  };
});
