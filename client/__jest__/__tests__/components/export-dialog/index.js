import React from 'react';
import { mount } from 'enzyme';
import { RegisterForm } from '@youzan/retail-form';
import ExportDialog from 'components/export-dialog';
import ExportType from 'components/export-dialog/dialog-content-cpn/export-type';
import { Radio } from 'zent';
import * as api from 'route/query/api';

import fields from './fields.json';

describe('订单查询', () => {
  let wrapper;
  afterEach(() => {
    wrapper.unmount();
  });

  it('导出弹窗', () => {
    const props = {
      getDisplayInfo: () => ({
        startTime: 1552579200365,
        endTime: 1553157544365,
        deliveryStartTime: '',
        deliveryEndTime: '',
        planExpressTime: '',
        expressType: -1,
        orderType: -1,
        searchLabel: 'order_no',
        searchField: '',
        goodsTitle: '',
        orderState: -1,
        hasStar: -1,
        feedbackState: -1,
        cashierId: '',
        buyWay: -1,
        saleWay: -1,
        storeId: '',
        salesId: '',
        orderSource: -1,
        cashierName: '',
        salesName: '',
        storeName: ''
      }),
      showConfig: { isShowMultiStore: false, isShowCashier: false, isShowSales: true }
    };
    api.fetchReportFields = jest.fn(() => Promise.resolve(fields));
    wrapper = mount(
      <RegisterForm>
        <ExportDialog {...props} />
      </RegisterForm>
    );
    wrapper
      .find(ExportDialog)
      .at(0)
      .setState({ visible: true });
    expect(api.fetchReportFields).toHaveBeenCalled();
  });
});
