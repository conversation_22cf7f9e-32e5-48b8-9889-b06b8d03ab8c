import React from 'react';
import { mount } from 'enzyme';
import { <PERSON><PERSON>, Button } from 'zent';

import * as api from 'components/opt-components/buyer-refund/api';
import BuyerRefund from 'components/opt-components/buyer-refund';

import {
  AGREE_BUYER_REFUND,
  AGREE_RETURN_GOODS,
  CONFIRM_GOODS_REFUND,
  REFUSE_REFUND_APPLY,
  REFUSE_RECEIVE_GOODS
} from 'components/opt-components/constant';

import orderData from './orderData.json';
import addressListData from './addressListData.json';

const getWrapper = operation =>
  mount(
    <BuyerRefund
      key={operation.code}
      operation={operation}
      options={{
        ...orderData,
        orderInfo: {
          mainOrderInfo: {
            orderNo: orderData.simpleOrderInfo.orderNo
          }
        }
      }}
    />
  );

function flushPromises() {
  return new Promise(resolve => setImmediate(resolve));
}

api.fetchAddressList = jest.fn(() => Promise.resolve(addressListData));

const commonTest = wrapper => {
  wrapper
    .find('BuyerRefund')
    .instance()
    .openDialog();

  expect(wrapper.find(Dialog)).toHaveLength(2);
  expect(wrapper.find('BuyerRefund').instance().state.showDialog).toEqual(true);
  expect(
    wrapper
      .find(Dialog)
      .at(0)
      .instance().props.visible
  ).toEqual(true);

  /**
   * 通过 baseOpenDialog 方法调用 react 的 setState 没有让弹窗组件实例化，必须用这个方法；
   * 但这样对测试弹窗能否正常展示没有意义，所以必须有上面的过程
   */
  wrapper.find('BuyerRefund').setState({ showDialog: true });

  expect(
    wrapper
      .find(Dialog)
      .at(0)
      .props().visible
  ).toEqual(true);
};

describe('退款详情', () => {
  let wrapper;

  afterEach(() => {
    wrapper.unmount();
  });

  it('退货退款', () => {
    wrapper = getWrapper({
      code: AGREE_RETURN_GOODS,
      type: 'operation',
      text: '退货退款',
      disabled: false,
      displayAtOtherKdt: false,
      priority: 1
    });

    expect(api.fetchAddressList).toHaveBeenCalled();

    return flushPromises().then(() => {
      commonTest(wrapper);
      // 这里没有办法 mock api.acceptRefund，这个是业务组件写法带来的问题
      wrapper
        .find(Button)
        .at(0)
        .props()
        .onClick();

      expect(
        wrapper
          .find(Button)
          .at(1)
          .props().children
      ).toEqual('同意并发送退货地址');
    });
  });

  it('同意退款', () => {
    wrapper = getWrapper({
      code: AGREE_BUYER_REFUND,
      type: 'operation',
      text: '同意退款',
      disabled: false,
      displayAtOtherKdt: false,
      priority: 1
    });

    commonTest(wrapper);
    wrapper
      .find(Button)
      .at(0)
      .props()
      .onClick();

    expect(
      wrapper
        .find(Button)
        .at(0)
        .props().children
    ).toEqual('取消');

    expect(
      wrapper
        .find(Button)
        .at(1)
        .props().children
    ).toEqual('同意退款');
  });

  it('拒绝买家退款', () => {
    wrapper = getWrapper({
      code: REFUSE_REFUND_APPLY,
      type: 'operation',
      text: '拒绝买家退款',
      disabled: false,
      displayAtOtherKdt: false,
      priority: 1
    });

    commonTest(wrapper);
    wrapper
      .find(Button)
      .at(0)
      .props()
      .onClick();

    expect(
      wrapper
        .find(Button)
        .at(0)
        .props().children
    ).toEqual('取消');

    expect(
      wrapper
        .find(Button)
        .at(1)
        .props().children
    ).toEqual('确定拒绝');
  });

  it('确认收货并退款', () => {
    wrapper = getWrapper({
      code: CONFIRM_GOODS_REFUND,
      type: 'operation',
      text: '确认收货并退款',
      disabled: false,
      displayAtOtherKdt: false,
      priority: 1
    });

    commonTest(wrapper);
    wrapper
      .find(Button)
      .at(0)
      .props()
      .onClick();

    expect(
      wrapper
        .find(Button)
        .at(0)
        .props().children
    ).toEqual('取消');

    expect(
      wrapper
        .find(Button)
        .at(1)
        .props().children
    ).toEqual('确认收货并退款');
  });
});
