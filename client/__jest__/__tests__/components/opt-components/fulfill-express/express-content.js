import React from 'react';
import { mount } from 'enzyme';

import ExpressContent from 'components/opt-components/fulfill-express/express-content';

import * as api from 'components/opt-components/create-invoice/api';

const fulfillList = [{"channels":[],"consigneeInfo":{"consigneeAddress":"中国浙江省杭州市西湖区黄龙万科中心H座,黄龙万科","consigneeTelphone":"15996313950","consigneeName":"王婷","latitude":"30.272449633925277","longitude":"120.23717742198889"},"supportDeliveryTypes":[{"id":12,"text":"快递"},{"id":13,"text":"无需物流"}],"distInfo":{"distTypeDesc":"快递","distType":0},"deliveryNo":"201903201422590000090712","itemInfos":[{"pricingStrategy":0,"orderNo":"E20190320142257071200015","goodsInfo":"{\"alias\":\"366mvnntel58q\",\"buy_way\":1,\"categoryList\":[\"女人\"],\"class1\":1,\"class2\":\"0\",\"extra\":{\"deliveryTempleId\":0,\"weight\":0.0},\"extraMap\":{\"DELIVER_TIME\":\"0\",\"ABILITY_MARK_CODES\":\"[10002,40005,20001,30002]\"},\"goods_id\":457176877,\"goods_no\":\"P190320407002635\",\"img_url\":\"https://img.yzcdn.cn/public_files/2017/08/30/63a8d28bce4ca2e5d081e1e69926288e.jpg\",\"isFx\":false,\"is_virtual\":0,\"joinLevelDiscount\":1,\"mark\":0,\"needCustomsCheck\":false,\"needCustomsInfo\":false,\"needCustomsPicture\":false,\"points_price\":0,\"quota\":0,\"stock_deduct_mode\":0,\"title\":\"王大大320测试勿动\"}","itemIdStr":"1506305781245419331","realPay":9,"num":1000,"goodsNo":"P190320407002635","tagsInfo":"{\"STOCK_DEDUCTED\":true}","title":"王大大320测试勿动","canSelect":true,"feedback":0,"isPresent":false,"kdtId":40666106,"operations":[],"goodsRefund":0,"isSerialItem":false,"extra":"{\"DELIVER_TIME\":\"0\",\"STOCK_DEDUCT_SCENE\":\"0\",\"BIZ_TRACE_POINT\":\"{\\\"cartCreateTime\\\":1553062938,\\\"cartUpdateTime\\\":1553062938,\\\"extension\\\":{\\\"biz\\\":\\\"wsc\\\",\\\"uuid\\\":\\\"c456f0f2-7e9a-192c-d3b3-e496dd4db1a0\\\",\\\"platform\\\":\\\"h5\\\"},\\\"kdtSessionId\\\":\\\"YZ520285139293548544YZ90mHKlxJ\\\",\\\"pageSource\\\":\\\"\\\"}\",\"USED_PRO\":\"{\\\"activityAlias\\\":\\\"\\\",\\\"activityId\\\":0,\\\"activityType\\\":\\\"1\\\"}\",\"RETAIL_GOODS_CATEGORY_INFO\":\"{\\\"categoryEntities\\\":[{\\\"categoryId\\\":1036501,\\\"categoryName\\\":\\\"未分类\\\",\\\"level\\\":1}],\\\"categoryId\\\":1036501,\\\"categoryName\\\":\\\"未分类\\\",\\\"level\\\":1,\\\"name\\\":\\\"王大大320测试勿动\\\",\\\"skuId\\\":10328515,\\\"skuNo\\\":\\\"P190320407002635\\\",\\\"specifications\\\":\\\"\\\",\\\"unit\\\":\\\"件\\\",\\\"wareHouseSkuId\\\":10328515}\",\"FX_MODE\":\"1\",\"activityInfo\":\"\",\"POINTS_PRICE\":\"0\",\"WARE_HOUSE_ID\":\"40687714\"}","buyerMemo":"","sku":"[]","deliveryStatusDesc":"待发货","payPrice":9,"itemId":1506305781245419300,"buyerMemoDesc":"","goodsId":457176877,"postageRefund":0,"skuId":36284165,"unitPrice":9,"icons":[],"originUnitPrice":10,"tags":{"sTOCK_DEDUCTED":true},"unit":"件","isReduce":false,"imgUrl":"https://img.yzcdn.cn/public_files/2017/08/30/63a8d28bce4ca2e5d081e1e69926288e.jpg","refundNum":0,"goodsNum":1,"goodsType":0,"skuCode":"P190320407002635","goodsSnapUrl":"https://h5.youzan.com/v2/showcase/goodsSnapRedirect/urlByOrder?order_no=E20190320142257071200015&alias=366mvnntel58q&goods_id=457176877&snap_key=a645c0ee63fd6e8c4e52ac06b74fb188","deliveryStatus":0}],"warehouseId":40687714,"unDeliveryCount":1},{"channels":[],"consigneeInfo":{"consigneeAddress":"中国浙江省杭州市西湖区黄龙万科中心H座,黄龙万科","consigneeTelphone":"15996313950","consigneeName":"王婷","latitude":"30.272449633925277","longitude":"120.23717742198889"},"supportDeliveryTypes":[{"id":12,"text":"快递"},{"id":13,"text":"无需物流"}],"distInfo":{"distTypeDesc":"快递","distType":0},"deliveryNo":"201903201422590000100712","itemInfos":[{"pricingStrategy":0,"orderNo":"E20190320142257071200015","goodsInfo":"{\"alias\":\"3npas55vn88gq\",\"buy_way\":1,\"categoryList\":[\"其他\"],\"class1\":0,\"class2\":\"\",\"extra\":{\"deliveryTempleId\":0,\"weight\":0.0},\"extraMap\":{\"DELIVER_TIME\":\"0\",\"ABILITY_MARK_CODES\":\"[10002,40005,20001,30002]\"},\"goods_id\":457173698,\"goods_no\":\"P190320855690179\",\"img_url\":\"https://img.yzcdn.cn/public_files/2017/08/30/63a8d28bce4ca2e5d081e1e69926288e.jpg\",\"isFx\":false,\"is_virtual\":0,\"joinLevelDiscount\":1,\"mark\":0,\"needCustomsCheck\":false,\"needCustomsInfo\":false,\"needCustomsPicture\":false,\"points_price\":0,\"quota\":0,\"stock_deduct_mode\":0,\"title\":\"王大大测试22勿动\"}","itemIdStr":"1506305781245419332","realPay":18,"num":1000,"goodsNo":"P190320855690179","tagsInfo":"{\"STOCK_DEDUCTED\":true}","title":"王大大测试22勿动","canSelect":true,"feedback":0,"isPresent":false,"kdtId":40666106,"operations":[],"goodsRefund":0,"isSerialItem":false,"extra":"{\"DELIVER_TIME\":\"0\",\"STOCK_DEDUCT_SCENE\":\"0\",\"BIZ_TRACE_POINT\":\"{\\\"cartCreateTime\\\":1553062927,\\\"cartUpdateTime\\\":1553062927,\\\"extension\\\":{\\\"biz\\\":\\\"wsc\\\",\\\"uuid\\\":\\\"c456f0f2-7e9a-192c-d3b3-e496dd4db1a0\\\",\\\"platform\\\":\\\"h5\\\"},\\\"kdtSessionId\\\":\\\"YZ520285139293548544YZ90mHKlxJ\\\",\\\"pageSource\\\":\\\"\\\"}\",\"USED_PRO\":\"{\\\"activityAlias\\\":\\\"\\\",\\\"activityId\\\":0,\\\"activityType\\\":\\\"1\\\"}\",\"RETAIL_GOODS_CATEGORY_INFO\":\"{\\\"categoryEntities\\\":[{\\\"categoryId\\\":1036501,\\\"categoryName\\\":\\\"未分类\\\",\\\"level\\\":1}],\\\"categoryId\\\":1036501,\\\"categoryName\\\":\\\"未分类\\\",\\\"level\\\":1,\\\"name\\\":\\\"王大大测试22勿动\\\",\\\"skuId\\\":10328702,\\\"skuNo\\\":\\\"P190320855690179\\\",\\\"specifications\\\":\\\"\\\",\\\"unit\\\":\\\"件\\\",\\\"wareHouseSkuId\\\":10328702}\",\"FX_MODE\":\"1\",\"activityInfo\":\"\",\"POINTS_PRICE\":\"0\",\"WARE_HOUSE_ID\":\"40666239\"}","buyerMemo":"","sku":"[]","deliveryStatusDesc":"待发货","payPrice":18,"itemId":1506305781245419300,"buyerMemoDesc":"","goodsId":457173698,"postageRefund":0,"skuId":36282942,"unitPrice":18,"icons":[],"originUnitPrice":20,"tags":{"sTOCK_DEDUCTED":true},"unit":"件","isReduce":false,"imgUrl":"https://img.yzcdn.cn/public_files/2017/08/30/63a8d28bce4ca2e5d081e1e69926288e.jpg","refundNum":0,"goodsNum":1,"goodsType":0,"skuCode":"P190320855690179","goodsSnapUrl":"https://h5.youzan.com/v2/showcase/goodsSnapRedirect/urlByOrder?order_no=E20190320142257071200015&alias=3npas55vn88gq&goods_id=457173698&snap_key=7cbd735ce2a5ede75137bb7cccadabbf","deliveryStatus":0}],"warehouseId":40666239,"unDeliveryCount":1}]

describe('订单查询', () => {
    let wrapper;
    afterEach(() => {
        wrapper.unmount();
    });

    it('发货', () => {
        const props = {
            deliveryWindowTips: {},
            orderNo: "E20190320142257071200015",
            onSuccess: () => {},
            tabRef: () => {},
            onClose: () => {},
            data: fulfillList,
            storeId: 0,
            orderType: 0,
            supportMoreDistOrder: false,
            isFreeBuy: false
        }
        wrapper = mount(<ExpressContent {...props} />);
        wrapper.setProps(props);
    });
});