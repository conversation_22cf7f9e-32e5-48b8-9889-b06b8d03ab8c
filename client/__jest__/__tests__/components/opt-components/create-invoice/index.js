import React from 'react';
import { mount } from 'enzyme';

import Remark from 'components/opt-components/create-invoice';

import * as api from 'components/opt-components/create-invoice/api';

describe('订单查询', () => {
    let wrapper;
    afterEach(() => {
      wrapper.unmount();
    });

    it('发票', () => {
        api.checkInviceStatus = jest.fn(() => Promise.resolve(true));
        const operation = { type: 'operation' };
        wrapper = mount(<Remark operation={operation} />);
    });
});