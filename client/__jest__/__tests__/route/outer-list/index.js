import React from 'react';
import { mount } from 'enzyme';
import App from 'route/outer-list/app.jsx';
import * as api from 'route/outer-list/api.js';
import data from './data.json';

describe('外部订单单店', () => {
  let wrapper;
  afterEach(() => {
    wrapper.unmount();
  });

  it('渲染正常，api调用过', () => {
    api.fetchOutOrderList = jest.fn(() => Promise.resolve(data));
    wrapper = mount(<App />);
    wrapper.find('OrderList').first().find('OuterFilter').prop('onFilter')();
    expect(api.fetchOutOrderList).toHaveBeenCalled();
    api.fetchOutOrderList.mockReset();
  });
});
