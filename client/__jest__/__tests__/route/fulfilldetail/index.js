import React from 'react';
import { mount } from 'enzyme';
import App from 'route/fulfilldetail/app.jsx';
import * as api from 'route/fulfilldetail/api';
import data from './data.json';

api.fetchFulfillDetail = jest.fn(() => Promise.resolve(data));

describe('发货单详情渲染', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(<App />);
  });

  afterEach(() => {
    api.fetchFulfillDetail.mockReset();
    wrapper.unmount();
  });

  it('渲染一个发货单 预发测试连锁大网店 201901301153240000020000', () => {
    expect(api.fetchFulfillDetail).toHaveBeenCalled();
  });
});
