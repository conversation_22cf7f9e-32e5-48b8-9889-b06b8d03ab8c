import React from 'react';
import { mount } from 'enzyme';
import { HashRouter as Router } from 'react-router-dom';
import { RegisterForm } from '@youzan/retail-form';
import SingleList from 'route/onlineshipments/single/index.jsx';
import ChainList from 'route/onlineshipments/chain/index.jsx';
import api from 'route/onlineshipments/api.js';
import data from './data.json';
import storeData from './stores.json';
import chainData from './chainData.json';

describe('网店发货', () => {
  let wrapper;
  afterEach(() => {
    wrapper.unmount();
  });

  it('单店列表', () => {
    api.fetchOrderList = jest.fn(() => Promise.resolve(data));
    wrapper = mount(
      <RegisterForm>
        <Router>
          <SingleList />
        </Router>
      </RegisterForm>
    );
    expect(api.fetchOrderList).toHaveBeenCalled();
    api.fetchOrderList.mockReset();
  });

  it('连锁列表', () => {
    api.fetchFulfillOrderList = jest.fn(() => Promise.resolve(chainData));
    api.fetchWarehouseList = jest.fn(() => Promise.resolve(storeData));
    wrapper = mount(
      <RegisterForm>
        <Router>
          <ChainList />
        </Router>
      </RegisterForm>
    );
    expect(api.fetchFulfillOrderList).toHaveBeenCalled();
    expect(api.fetchWarehouseList).toHaveBeenCalled();
    api.fetchOrderList.mockReset();
  });
});
