[{"area": "河北区", "contactName": "fff", "address": "浙江省杭州市下城区朝晖街道沙县小吃(浙江协作大厦东北)稻香园北区", "lng": "120.154539", "contactPhone": "+86-13136133603", "city": "天津市", "warehouseCode": "4069190412", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": false, "province": "天津市", "groupId": 40666106, "hasSupplyRelation": false, "name": "warehouse", "self": false, "lat": "30.286654", "warehouseId": 40691904, "status": 1}, {"area": "宝山区", "contactName": "异星", "address": "上海市宝山区", "lng": "120.126268", "contactPhone": "+86-13136103604", "city": "上海市", "warehouseCode": "test1", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": true, "province": "上海市", "groupId": 40666106, "hasSupplyRelation": true, "name": "异星的仓库", "self": false, "lat": "30.275923", "warehouseId": 40689301, "status": 0}, {"area": "滨江区", "contactName": "张三", "address": "杭州市拱墅区8号俱乐部(文一路)5号", "lng": "120.148765", "contactPhone": "+86-13136103603", "city": "杭州市", "warehouseCode": "406893343", "express": false, "remark": "test", "type": 1, "local": false, "supplyHq": true, "province": "浙江省", "groupId": 40666106, "hasSupplyRelation": true, "name": "乐宜仓库", "self": false, "lat": "30.289951", "warehouseId": 40689204, "status": 0}, {"area": "江干区", "contactName": "wppp", "address": "浙江省杭州市下城区朝晖街道青园小区3楼", "lng": "120.163223", "contactPhone": "+86-15757907781", "city": "杭州市", "warehouseCode": "40689202", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": true, "province": "浙江省", "groupId": 40666106, "hasSupplyRelation": true, "name": "独立仓C", "self": false, "lat": "30.278548", "warehouseId": 40689202, "status": 0}, {"area": "东城区", "contactName": "陈肃", "address": "浙江省杭州市下城区朝晖街道稻香园(北区)稻香园北区", "lng": "120.153141", "contactPhone": "+86-18500679539", "city": "北京市", "warehouseCode": "40688802", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": true, "province": "北京市", "groupId": 40666106, "hasSupplyRelation": true, "name": "陈肃测试独立仓", "self": false, "lat": "30.287357", "warehouseId": 40688802, "status": 0}, {"area": "西湖区", "contactName": "项辉", "address": "杭州市西湖区杭州西湖风景名胜区", "lng": "120.108478", "contactPhone": "+86-18557536057", "city": "杭州市", "warehouseCode": "40685304", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": true, "province": "浙江省", "groupId": 40666106, "hasSupplyRelation": true, "name": "独立仓D", "self": false, "lat": "30.220671", "warehouseId": 40685304, "status": 0}, {"area": "天心区", "contactName": "步步高", "address": "长沙市天心区铁道学院(地铁站)", "lng": "112.987321", "contactPhone": "+86-15636039212", "city": "长沙市", "warehouseCode": "40678511", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": true, "province": "湖南省", "groupId": 40666106, "hasSupplyRelation": true, "name": "独立仓E", "self": false, "lat": "28.136157", "warehouseId": 40678511, "status": 0}, {"area": "滨江区", "contactName": "wpp", "address": "长沙市天心区铁道学院(地铁站)", "lng": "112.987321", "contactPhone": "+86-15636039213", "city": "杭州市", "warehouseCode": "1234567890", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": true, "province": "浙江省", "groupId": 40666106, "hasSupplyRelation": true, "name": "独立仓B", "self": false, "lat": "28.136157", "warehouseId": 40666131, "status": 0}, {"area": "和平区", "contactName": "武汉老徐", "address": "浙江省杭州市西湖区西湖街道杭州西湖", "lng": "120.133128", "contactPhone": "+86-12345678921", "city": "天津市", "warehouseCode": "40666231", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": false, "province": "天津市", "groupId": 40666106, "hasSupplyRelation": false, "name": "徐孟帆的独立仓(别改)", "self": false, "lat": "30.246669", "warehouseId": 40666231, "status": 0}, {"area": "西湖区", "contactName": "杨s", "address": "杭州市西湖区浙商财富中心3号楼", "lng": "120.100928", "contactPhone": "+86-18877778888", "city": "杭州市", "warehouseCode": "40666122", "express": false, "remark": "", "type": 1, "local": false, "supplyHq": false, "province": "浙江省", "groupId": 40666106, "hasSupplyRelation": false, "name": "已禁用仓库1", "self": false, "lat": "30.271037", "warehouseId": 40666122, "status": 1}, {"area": "西城区", "contactName": "赵娜娜", "address": "北京市北京市西城区复兴门外大街15号长安商场", "lng": "116.35159685029", "contactPhone": "+86-17722442068", "city": "北京市", "warehouseCode": "MD00006", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": false, "province": "北京市", "groupId": 40666106, "hasSupplyRelation": true, "name": "店铺开关优化测试", "self": false, "lat": "39.913946064162", "warehouseId": 42382111, "status": 0}, {"area": "东城区", "contactName": "bob", "address": "北京市北京市东城区东长安街天安门", "lng": "116.40393378531", "contactPhone": "+86-13554011045", "city": "北京市", "warehouseCode": "MD00005", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": false, "province": "北京市", "groupId": 40666106, "hasSupplyRelation": false, "name": "店铺能力测试", "self": false, "lat": "39.914800511407", "warehouseId": 42382109, "status": 0}, {"area": "库伦旗", "contactName": "技术 ", "address": "内蒙古自治区通辽市库伦旗文化街南150米库伦旗(京东服务中心)", "lng": "121.78167225166", "contactPhone": "+86-15504667780", "city": "通辽市", "warehouseCode": "MD00004", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": true, "province": "内蒙古自治区", "groupId": 40666106, "hasSupplyRelation": true, "name": "每周重复", "self": false, "lat": "42.744845835055", "warehouseId": 42316407, "status": 0}, {"area": "东城区", "contactName": "技术", "address": "北京市北京市东城区干面胡同10号中共北京市东城区委党校", "lng": "116.43188232462", "contactPhone": "+86-15504667780", "city": "北京市", "warehouseCode": "MD00003", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": true, "province": "北京市", "groupId": 40666106, "hasSupplyRelation": true, "name": "营业时间线上验证创建", "self": false, "lat": "39.923047384709", "warehouseId": 42317803, "status": 0}, {"area": "东城区", "contactName": "bob", "address": "北京市北京市东城区惠河南街1092号北京美迪", "lng": "116.50519203172", "contactPhone": "+86-13554011045", "city": "北京市", "warehouseCode": "MD00002", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": true, "province": "北京市", "groupId": 40666106, "hasSupplyRelation": true, "name": "营业时间测试店铺22", "self": false, "lat": "39.911157308146", "warehouseId": 42312607, "status": 0}, {"area": "西湖区", "contactName": "bob", "address": "浙江省杭州市西湖区西湖天目山路278号黄龙时代广场", "lng": "120.13185573785", "contactPhone": "+86-13554011045", "city": "杭州市", "warehouseCode": "MD00001", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": true, "province": "浙江省", "groupId": 40666106, "hasSupplyRelation": true, "name": "门店激活展示延迟测试门店", "self": false, "lat": "30.279182086683", "warehouseId": 42276920, "status": 0}, {"area": "和平区", "contactName": "12345", "address": "天津市天津市和平区于家堡街道和平路339号天津银行(和平路支行)", "lng": "117.67260923200", "contactPhone": "+86-13967190774", "city": "天津市", "warehouseCode": "444555", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": true, "province": "天津市", "groupId": 40666106, "hasSupplyRelation": true, "name": "创建联营门店1", "self": false, "lat": "39.024786714847", "warehouseId": 41383309, "status": 0}, {"area": "和平区", "contactName": "22234", "address": "天津市天津市和平区新华路209号天津市政协", "lng": "117.21286557597", "contactPhone": "+86-13967190774", "city": "天津市", "warehouseCode": "12345", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": true, "province": "天津市", "groupId": 40666106, "hasSupplyRelation": true, "name": "23创3年门33店333444", "self": false, "lat": "39.126871370068", "warehouseId": 41349301, "status": 0}, {"area": "东城区", "contactName": "煎蛋专用测试", "address": "北京市北京市东城区幸福大街32号北京市东城区政协", "lng": "116.43787193196", "contactPhone": "+86-15504667780", "city": "北京市", "warehouseCode": "1234", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": true, "province": "北京市", "groupId": 40666106, "hasSupplyRelation": true, "name": "820上线3213545", "self": false, "lat": "39.894260318283", "warehouseId": 41345830, "status": 0}, {"area": "松山湖", "contactName": "zly", "address": "广东省东莞市松山湖工业西路东100米威士丁(松山湖店)", "lng": "113.89618986021", "contactPhone": "+86-17764577896", "city": "东莞市", "warehouseCode": "123456", "express": false, "remark": "", "type": 2, "local": false, "supplyHq": false, "province": "广东省", "groupId": 40666106, "hasSupplyRelation": false, "name": "新鲜生活0711", "self": false, "lat": "22.970615652877", "warehouseId": 40747096, "status": 0}]