import React from 'react';
import { mount } from 'enzyme';
import { HashRouter as Router } from 'react-router-dom';
import List from 'route/exportList/app.jsx';
import * as api from 'route/exportList/api.js';
import data from './data.json';
import cashierData from './cashierData.json';

api.getExportList = jest.fn(() => Promise.resolve(data));
api.getCashierList = jest.fn(() => Promise.resolve(cashierData));

describe('报表页', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(
      <Router>
        <List />
      </Router>);
  });

  afterEach(() => {
    api.getExportList.mockReset();
    api.getCashierList.mockReset();
    wrapper.unmount();
  });

  it('渲染正确', () => {
    expect(api.getExportList).toHaveBeenCalled();
    expect(api.getCashierList).toHaveBeenCalled();
  });
});
