import React from 'react';
import { mount } from 'enzyme';
import App from 'route/order-detail/app.jsx';
import * as api from 'route/order-detail/api';
import orderDetailData from './orderDetailHq.json';

api.fetchOrderDetail = jest.fn(() => Promise.resolve(orderDetailData));
api.fetchRefundInfo = jest.fn(() =>
  Promise.resolve({
    data: [],
    total: 0
  })
);

describe('连锁订单详情渲染', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(<App />);
  });

  afterEach(() => {
    api.fetchOrderDetail.mockReset();
    api.fetchRefundInfo.mockReset();
    wrapper.unmount();
  });

  it('渲染一个网店订单 预发测试连锁大网店 E20190214145232098600015', () => {
    expect(api.fetchOrderDetail).toHaveBeenCalled();
    expect(api.fetchRefundInfo).toHaveBeenCalled();
  });
});
