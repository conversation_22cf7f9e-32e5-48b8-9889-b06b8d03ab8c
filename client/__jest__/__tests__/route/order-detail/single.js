import React from 'react';
import { mount } from 'enzyme';
import App from 'route/order-detail/app.jsx';
import * as api from 'route/order-detail/api';
import orderDetailData from './orderDetail.json';

api.fetchOrderDetail = jest.fn(() => Promise.resolve(orderDetailData));
api.fetchRefundInfo = jest.fn(() =>
  Promise.resolve({
    data: [],
    total: 0
  })
);

describe('单店订单详情渲染', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(<App />);
  });

  afterEach(() => {
    api.fetchOrderDetail.mockReset();
    api.fetchRefundInfo.mockReset();
    wrapper.unmount();
  });

  it('渲染一个门店订单 预发测试单店 E20190213111137099200036', () => {
    expect(api.fetchOrderDetail).toHaveBeenCalled();
    expect(api.fetchRefundInfo).toHaveBeenCalled();
  });
});
