{"configs": [{"automock": false, "browser": false, "cache": true, "cacheDirectory": "/private/var/folders/rh/bbk2xhts48100rqjmrlv8jhm0000gn/T/jest_dx", "clearMocks": false, "coveragePathIgnorePatterns": ["/node_modules/", "__jest__"], "cwd": "/Users/<USER>/proj/retail-node-order/client", "dependencyExtractor": null, "detectLeaks": false, "detectOpenHandles": false, "errorOnDeprecated": false, "filter": null, "forceCoverageMatch": [], "globalSetup": null, "globalTeardown": null, "globals": {}, "haste": {"computeSha1": false, "providesModuleNodeModules": []}, "moduleDirectories": ["node_modules"], "moduleFileExtensions": ["jsx", "js"], "moduleNameMapper": [["^constants(.*)$", "/Users/<USER>/proj/retail-node-order/client/constants$1"], ["^common(.*)$", "/Users/<USER>/proj/retail-node-order/client/common$1"], ["^cpn(.*)$", "/Users/<USER>/proj/retail-node-order/client/components$1"], ["^components(.*)$", "/Users/<USER>/proj/retail-node-order/client/components$1"], ["^route(.*)$", "/Users/<USER>/proj/retail-node-order/client/route$1"], ["\\.(css|scss|pcss|less)$", "/Users/<USER>/proj/retail-node-order/client/__jest__/__mocks__/styleMock.js"], ["\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$", "/Users/<USER>/proj/retail-node-order/client/__jest__/__mocks__/fileMock.js"], ["^testroot(.*)$", "/Users/<USER>/proj/retail-node-order/client/__jest__$1"]], "modulePathIgnorePatterns": [], "name": "4fd7bc4129417b011e18b973bc12e476", "prettierPath": "prettier", "resetMocks": false, "resetModules": false, "resolver": null, "restoreMocks": false, "rootDir": "/Users/<USER>/proj/retail-node-order/client", "roots": ["/Users/<USER>/proj/retail-node-order/client"], "runner": "jest-runner", "setupFiles": ["/Users/<USER>/proj/retail-node-order/client/node_modules/jest-canvas-mock/lib/index.js"], "setupFilesAfterEnv": ["/Users/<USER>/proj/retail-node-order/client/__jest__/testlib/setupTest.js"], "skipFilter": false, "snapshotSerializers": [], "testEnvironment": "/Users/<USER>/proj/retail-node-order/client/node_modules/jest-environment-jsdom/build/index.js", "testEnvironmentOptions": {}, "testLocationInResults": false, "testMatch": ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[tj]s?(x)"], "testPathIgnorePatterns": ["/node_modules/"], "testRegex": [], "testRunner": "/Users/<USER>/proj/retail-node-order/client/node_modules/jest-jasmine2/build/index.js", "testURL": "http://localhost", "timers": "real", "transform": [["^.+\\.[jt]sx?$", "/Users/<USER>/proj/retail-node-order/client/node_modules/babel-jest/build/index.js"]], "transformIgnorePatterns": ["/node_modules/"], "watchPathIgnorePatterns": []}], "globalConfig": {"bail": 0, "changedFilesWithAncestor": false, "collectCoverage": false, "collectCoverageFrom": ["route/**/*.{js,jsx}", "common/**/*.{js,jsx}", "components/**/*.{js,jsx}", "!route/**/api.js"], "coverageDirectory": "/Users/<USER>/proj/retail-node-order/client/coverage", "coverageReporters": ["json", "html", "text-summary"], "coverageThreshold": null, "detectLeaks": false, "detectOpenHandles": false, "errorOnDeprecated": false, "expand": false, "filter": null, "globalSetup": null, "globalTeardown": null, "listTests": false, "maxConcurrency": 5, "maxWorkers": 3, "noStackTrace": false, "nonFlagArgs": [], "notify": false, "notifyMode": "failure-change", "passWithNoTests": false, "projects": null, "rootDir": "/Users/<USER>/proj/retail-node-order/client", "runTestsByPath": false, "skipFilter": false, "testFailureExitCode": 1, "testPathPattern": "", "testResultsProcessor": null, "updateSnapshot": "new", "useStderr": false, "verbose": null, "watch": false, "watchman": true}, "version": "24.1.0"}