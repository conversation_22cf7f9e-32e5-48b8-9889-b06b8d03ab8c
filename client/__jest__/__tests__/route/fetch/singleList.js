import React from 'react';
import { mount } from 'enzyme';
import { HashRouter as Router } from 'react-router-dom';
import { RegisterForm } from '@youzan/retail-form';
import * as api from 'route/fetch/api';
import * as filterApi from 'components/order-filter/api';
import singleData from './singleData.json';
import SingleList from 'route/fetch/pages/unfetchList/index';

filterApi.fetchSelfFetchAddress = jest.fn(() => Promise.resolve([]));
api.fetchOrderList = jest.fn(() => Promise.resolve(singleData));

describe('单店列表页', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(
      <Router>
        <RegisterForm>
          <SingleList />
        </RegisterForm>
      </Router>
    );
  });

  afterEach(() => {
    filterApi.fetchSelfFetchAddress.mockReset();
    api.fetchOrderList.mockReset();
    wrapper.unmount();
  });

  it('自提总揽页面数量要正确', () => {
    expect(api.fetchOrderList).toHaveBeenCalled();
    expect(filterApi.fetchSelfFetchAddress).toHaveBeenCalled();
    expect(wrapper.find('UnfetchList').instance().state.data.length).toBe(20);
    // Filter.find('.zent-date-range-picker__btn').first().simulate('click');
    // 后面应该找到input元素并检查内容
  });
});
