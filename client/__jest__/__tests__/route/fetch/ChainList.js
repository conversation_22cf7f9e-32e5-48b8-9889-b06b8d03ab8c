import React from 'react';
import { mount } from 'enzyme';
import { HashRouter as Router } from 'react-router-dom';
import { RegisterForm } from '@youzan/retail-form';
import * as api from 'route/fetch/api';
import chainData from './chainData.json';
import ChainList from 'route/fetch/pages/unfetchList/chain';

api.fetchSelfFetchStoreList = jest.fn(() => Promise.resolve([]));
api.fetchFulfillOrderList = jest.fn(() => Promise.resolve(chainData));

describe('连锁列表页', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(
      <Router>
        <RegisterForm>
          <ChainList />
        </RegisterForm>
      </Router>);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('渲染正确', () => {
    expect(api.fetchFulfillOrderList).toHaveBeenCalled();
    expect(api.fetchSelfFetchStoreList).toHaveBeenCalled();
  });

});
