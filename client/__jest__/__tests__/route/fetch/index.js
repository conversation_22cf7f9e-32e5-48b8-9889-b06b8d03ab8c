import React from 'react';
import { mount } from 'enzyme';
import Routers from 'route/fetch/routers';
import * as api from 'route/fetch/api';

api.getSelfFetchTotalNum = jest.fn(() => Promise.resolve(666));

describe('单店自提总揽页面', () => {
  let wrapper;

  beforeEach(() => {
    wrapper = mount(<Routers />);
  });

  afterEach(() => {
    api.getSelfFetchTotalNum.mockReset();
    wrapper.unmount();
  });

  it('自提总揽页面数量要正确', () => {
    expect(api.getSelfFetchTotalNum).toHaveBeenCalled();
    expect(
      wrapper
        .find('.oneself__link-to-list__desc')
        .first()
        .text()
    ).toBe('666个订单待自提');
  });

});
