import React from 'react';
import { mount } from 'enzyme';
import App from 'route/returnorder/app.jsx';
import * as api from 'route/returnorder/components/api.js';
import data from './data.json';

// api.fetchFulfillDetail = jest.fn(() => Promise.resolve(data));

describe('退款维权单测', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = mount(<App />);
  });

  afterEach(() => {
    wrapper.unmount();
  });

  it('页面基础渲染无错，调用api', () => {
    api.fetchList = jest.fn(() => Promise.resolve(data));
    const RefundFilter = wrapper.find('RefundFilter').first();
    RefundFilter.prop('onSearch')();
    expect(api.fetchList).toHaveBeenCalled();
    wrapper.find('Tabs').first().prop('onChange')(1);
    expect(api.fetchList.mock.calls.length).toBe(2);
  });
});
