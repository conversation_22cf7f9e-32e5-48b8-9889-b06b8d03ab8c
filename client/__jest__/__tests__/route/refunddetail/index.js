import React from 'react';
import { mount } from 'enzyme';
import App from 'route/refunddetail/app.js';
import * as api from 'route/refunddetail/api.js';
import data from './data.json';

describe('发货单详情渲染', () => {
  let wrapper;
  afterEach(() => {
    wrapper.unmount();
  });

  it('渲染一个发货单 预发测试连锁大网店 201901301153240000020000', () => {
    api.newFetchSafeguardInfo = jest.fn(() => Promise.resolve(data));
    wrapper = mount(<App />);
    expect(api.newFetchSafeguardInfo).toHaveBeenCalled();
  });
});
