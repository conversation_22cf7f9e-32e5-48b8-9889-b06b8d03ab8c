import React from 'react';
import { mount } from 'enzyme';
import { HashRouter as Router } from 'react-router-dom';
import { RegisterForm } from '@youzan/retail-form';
import resetGlobal from '../../../../testlib/setChain';
import ChainList from 'route/query/list/chain';
import * as api from 'route/query/api';
import * as filterApi from 'components/order-filter/api';
import extraData from './extraData.json';
import data from './data.json';

describe('订单查询', () => {
  let wrapper;
  afterEach(() => {
    wrapper.unmount();
  });

  it('连锁列表', () => {
    api.fetchAllChainShopList = jest.fn(() => Promise.resolve(extraData.allStoreData));
    api.newFetchOrderList = jest.fn(() => Promise.resolve(data));
    filterApi.fetchStoreList = jest.fn(() => Promise.resolve(extraData.storeList));
    api.fetchSalesman = jest.fn(() => Promise.resolve(extraData.salersData));
    wrapper = mount(
      <RegisterForm>
        <Router>
          <ChainList />
        </Router>
      </RegisterForm>
    );
    expect(api.fetchAllChainShopList).toHaveBeenCalled();
    // expect(filterApi.fetchStoreList).toHaveBeenCalled();
    const Filter = wrapper.find('OrderFilter').first();
    Filter.find('Form').prop('onSubmit')();
    expect(api.newFetchOrderList).toHaveBeenCalled();
    api.fetchAllChainShopList.mockReset();
    api.newFetchOrderList.mockReset();
    filterApi.fetchStoreList.mockReset();
    api.fetchSalesman.mockReset();
    resetGlobal();
  });
});
