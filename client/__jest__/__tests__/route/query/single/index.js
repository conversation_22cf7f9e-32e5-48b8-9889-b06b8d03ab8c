import React from 'react';
import { mount } from 'enzyme';
import { HashRouter as Router } from 'react-router-dom';
import { RegisterForm } from '@youzan/retail-form';
import SingleList from 'route/query/list/single/index.js';
import * as api from 'route/query/api';
import extraData from './extraData.json';
import data from './data.json';

describe('订单查询', () => {
  let wrapper;
  afterEach(() => {
    wrapper.unmount();
  });

  it('单店列表', () => {
    api.fetchSalesman = jest.fn(() => Promise.resolve(extraData.salersData));
    api.newFetchOrderList = jest.fn(() => Promise.resolve(data));
    wrapper = mount(
      <RegisterForm>
        <Router>
          <SingleList />
        </Router>
      </RegisterForm>
    );
    expect(api.fetchSalesman).toHaveBeenCalled();
    //这种方式模拟点击
    const searchCb = wrapper
      .find('List')
      .first()
      .find('Filter')
      .first()
      .prop('onSearch');
    searchCb();
    expect(api.newFetchOrderList).toHaveBeenCalled();
  });
});
