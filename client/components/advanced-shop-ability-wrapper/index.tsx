import * as React from 'react';
import { ShopAbilityWrapper } from '@youzan/shop-ability';
import { ShopAbility } from '@youzan/utils-shop';
import { IsAdvancedVersion } from 'common/utils';

export interface IAdvancedShopAbilityWrapper {
  shopAbilityKey: ShopAbility;
}

const AdvancedShopAbilityWrapper: React.FC<IAdvancedShopAbilityWrapper> = ({
  children,
  ...restProps
}) => {
  return IsAdvancedVersion ? (
    <ShopAbilityWrapper {...restProps}>{children}</ShopAbilityWrapper>
  ) : (
    <>{children}</>
  );
};

export default AdvancedShopAbilityWrapper;
