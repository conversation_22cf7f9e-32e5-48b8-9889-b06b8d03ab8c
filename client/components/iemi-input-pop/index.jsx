import React from 'react';
import { isBoolean } from 'lodash';
import { Pop } from 'zent';

import Content from './content';
import style from './style.scss';

export default class IemiInputPop extends React.Component {
  state = { popVisable: false };

  triggerPop = value => {
    const { popVisable } = this.state;
    if (this.props.disabled) {
      return;
    }
    this.setState({ popVisable: isBoolean(value) ? value : !popVisable });
  };

  render() {
    const { onChange, children, value } = this.props;
    const content = <Content onConfirm={onChange} value={value} triggerPop={this.triggerPop} />;

    return (
      <Pop
        content={content}
        className={style.pop}
        visible={this.state.popVisable}
        position="auto-bottom-center"
      >
        <div className={style.trigger} onClick={this.triggerPop}>
          {children}
        </div>
      </Pop>
    );
  }
}
