import React from 'react';
import { Grid } from 'zent';
import './style.scss';

export default function CodeList({ onDelete, list = [] }) {
  const data = list.map(code => ({ code }));
  const columns = [
    {
      title: '序号',
      width: '20%',
      bodyRender: (_, index) => <div className="code-number">{list.length - index.row}</div>
    },
    {
      title: '唯一码',
      name: 'code',
      width: '60%'
    },
    {
      title: '操作',
      width: '20%',
      bodyRender: ({ code }) => (
        <a className="delete" onClick={() => onDelete(code)}>
          删除
        </a>
      )
    }
  ];

  return (
    <div className="code-list-content">
      <Grid
        columns={columns}
        datasets={data}
        scroll={{ y: 112 }}
        rowKey="code"
        className="code-list-table"
      />
    </div>
  );
}
