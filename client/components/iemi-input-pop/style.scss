@import '~shared/style';

:local(.iemi-list) {
  .iemi-list__input {
    display: flex;
    margin-bottom: 20px;

    .zent-input-wrapper {
      width: 260px;
      flex: 1;
      margin-right: 10px;
    }
  }

  .iemi-list__submit {
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
  }

  .code-list-content {
    .code-no {
      margin-right: 15px;
    }

    .code-err {
      color: $color-alert;
    }

    .delete {
      font-size: 12px;
      color: $color-link;
    }

    .code-number {
      text-align: center;
    }

    .zent-grid-body {
      overflow-x: hidden;
    }

    .zent-grid-td {
      word-break: break-all;
      word-wrap: break-word;
    }
  }
}

:local(.pop) {
  width: 400px;
  padding: 10px 0;
}

:local(.trigger) {
  cursor: pointer;
}
