import type { Contains } from 'definition/common';
import type { IGridColumn } from 'zent';

import * as React from 'react';
import { pick } from 'lodash';
import { Grid } from 'zent';
import withOrderInfo from 'components/with-order-info';
import createColumns from './create-columns';

import style from './style.scss';

const defaultProps = {
  orderInfo: {},
  data: [],
  mode: 'simple',
  showPrice: true,
  showNumber: true,
  showSubtotal: true
};
function GoodsList(
  props: Contains<{
    columns?: IGridColumn[];
    mode: 'simple' | 'intact';
    showNumber: boolean;
    columnsAdapter: Function;
    operations: Array<Contains<{ code: string }>>;
    itemInfo: Array<Contains<{}>>;
    mainOrderInfo: Contains<{}>;
    paymentInfo: Contains<{}>;
    orderActivities: unknown[];
    fetchOrderInfo: Function;
  }>
) {
  const orderInfo: any = pick(props, [
    'itemInfo',
    'mainOrderInfo',
    'paymentInfo',
    'operations',
    'orderActivities'
  ]);

  const columns = createColumns({
    ...props,
    orderInfo,
    reload: props.fetchOrderInfo
  });

  return (
    <Grid
      columns={columns}
      className={style['goods-list']}
      datasets={props.itemInfo}
      rowKey="itemIdStr"
    />
  );
}

GoodsList.defaultProps = defaultProps;

export default withOrderInfo(GoodsList);
