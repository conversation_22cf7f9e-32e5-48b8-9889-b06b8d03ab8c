import type { Contains } from 'definition/common';
import type { IGoodsInfoProps } from 'components/goods-info';
import type { IGridColumn } from 'zent';

import * as React from 'react';
import GoodsInfo from 'components/goods-info';
import OrderUmpInfo from 'components/order-ump-info';
import { OptComponent } from 'components/opt-components';

import { DETAIL_REFUND_OPERATION_CODES } from 'components/opt-components/constant';

import { convertFenToYen } from 'common/fns/format';
import { times } from '@youzan/retail-utils';
import { divGoodsNum } from 'common/helper';
import { camelCase, omit } from 'lodash';

// 这期的操作按钮，只有买家退款
const getOptOptions = (_: never, orderInfo: Contains<any>, reload: Function) => {
  const { mainOrderInfo = {}, itemInfo = [], paymentInfo = {} } = orderInfo;

  const options = {
    mainOrderInfo,
    reload,
    isOnline: true,
    realPay: paymentInfo.realPay,
    itemInfo
  };
  return options;
};

export default (
  options: Contains<{
    columns?: IGridColumn[];
    mode: 'simple' | 'intact';
    showPrice?: boolean;
    showNumber?: boolean;
    showSubtotal?: boolean;
    showDeliveryStatus?: boolean;
    showRefundStatus?: boolean;
    showUmpInfo?: boolean;
    columnsAdapter: Function;
    orderInfo: Contains<{
      operations: Array<Contains<{ code: string }>>;
      itemInfo: Array<Contains<{}>>;
    }>;
    reload: Function;
    data?: unknown[];
  }>
): IGridColumn[] => {
  if (options.columns) {
    return options.columns;
  }

  const { orderInfo, reload } = options;

  const { operations = [], itemInfo = [] } = orderInfo;

  const goodsCount = itemInfo.length;

  const getRowSpanElement = (children: JSX.Element | JSX.Element[]) => ({
    props: {
      rowSpan: goodsCount,
      className: 'with-border'
    },
    children
  });

  const defaultColumns = [
    {
      title: '商品',
      key: 'product',
      width: '35%',
      bodyRender: (goodsInfo: IGoodsInfoProps) => {
        if (options.mode === 'simple') {
          return goodsInfo.title;
        }

        // eslint-disable-next-line react/jsx-props-no-spreading
        return <GoodsInfo {...goodsInfo} />;
      }
    },
    {
      title: `单价(元)${options.showNumber ? '/数量' : ''}`,
      key: 'price',
      bodyRender: ({
        num,
        unit,
        originUnitPrice
      }: Contains<{ num: number; unit: string; originUnitPrice: number }>) => (
        <>
          <p>￥{convertFenToYen(originUnitPrice)}</p>
          {options.showNumber && <p>{`${divGoodsNum(num)}${unit}`}</p>}
        </>
      )
    },
    {
      title: '小计(元)',
      key: 'subtotal',
      bodyRender: ({
        payPrice,
        originUnitPrice,
        num
      }: Contains<{ payPrice: number; originUnitPrice: number; num: number }>) => {
        const originPrice = times(originUnitPrice, divGoodsNum(num));
        return (
          <>
            <p>￥{convertFenToYen(payPrice)}</p>
            {payPrice < originPrice && (
              <p className="price-line grey">￥{convertFenToYen(originPrice)}</p>
            )}
          </>
        );
      }
    },
    {
      title: '发货状态',
      key: 'deliveryStatus',
      name: 'deliveryStatusDesc'
    },
    {
      // 暂时支持三方订单， 整单级别
      title: '退款状态',
      key: 'refundStatus',
      textAlign: 'center',
      bodyRender: (goodsInfo: never, { row }: Contains<{ row: number }>) => {
        const refundOpts = operations.filter(({ code }) =>
          DETAIL_REFUND_OPERATION_CODES.includes(code)
        );

        if (row !== 0) {
          return {
            props: {
              rowSpan: 0
            }
          };
        }

        return getRowSpanElement(
          refundOpts.map(opt => (
            <p key={opt.code} className="opt-line">
              <OptComponent operation={opt} options={getOptOptions(goodsInfo, orderInfo, reload)} />
            </p>
          ))
        );
      }
    },
    {
      // 整单级别
      title: '优惠',
      key: 'umpInfo',
      textAlign: 'center',
      bodyRender: (_: unknown, { row }: Contains<{ row: number }>) => {
        if (row !== 0) {
          return {
            props: {
              rowSpan: 0
            }
          };
        }
        return getRowSpanElement(<OrderUmpInfo orderInfo={orderInfo} />);
      }
    }
  ];

  if (options.columnsAdapter) {
    return options.columnsAdapter(defaultColumns);
  }

  const columns = defaultColumns.reduce((cols, col) => {
    const showKey = camelCase(`show_${col.key}`);
    if (col.key && !options[showKey]) {
      return cols;
    }

    return [...cols, omit(col, 'key')];
  }, []) as IGridColumn[];

  return columns;
};
