import React, { Component, PureComponent } from 'react';
import cx from 'classnames';
import update from 'immutability-helper';
import { previewImage } from 'zent';
import { get, isArray } from 'lodash';
import { Upload } from '@youzan/react-components';
import { transformToHttps, global } from '@youzan/retail-utils';

import { captureMessage } from 'common/shared';
import { TOKEN_URL, FETCH_URL } from 'common/constant';

import PicItem from './pic-item';

const { KDT_ID } = global;

class UploadField extends (PureComponent || Component) {
  static defaultProps = {
    value: [],
    maxLen: 15
  };

  uploadSuccess = data => {
    if (!isArray(data)) {
      return;
    }

    const { value, maxLen, onChange, onlyNeedUrl } = this.props;

    const mappedData = data.map(item =>
      onlyNeedUrl
        ? {
            url: transformToHttps(item.attachmentUrl || item.attachment_url)
          }
        : {
            id: item.attachmentId || item.attachment_id,
            url: transformToHttps(item.attachmentUrl || item.attachment_url),
            width: item.width,
            height: item.height
          }
    );

    try {
      const res = update(value, {
        $push: mappedData
      });
      onChange(res.slice(0, maxLen));
    } catch (err) {
      captureMessage(`update: $push: ${err}`, {
        level: 'warning',
        extra: {
          data,
          mappedData
        },
        tags: {
          type: 'pic-field'
        }
      });
    }
  };

  handleItemDelete = index => {
    const { value, onChange } = this.props;

    const res = update(value, {
      $splice: [[index, 1]]
    });

    onChange(res);
  };

  handleItemPreview = index => {
    const { value } = this.props;

    previewImage({
      images: value.map(item => item.url),
      showRotateBtn: false,
      index
    });
  };

  handleItemMove = (fromIndex, toIndex) => {
    const { value, onChange } = this.props;
    const fromItem = value[fromIndex];
    const toItem = value[toIndex];

    const res = update(this.props.value, {
      $splice: [
        [fromIndex, 1, toItem],
        [toIndex, 1, fromItem]
      ]
    });

    onChange(res);
  };

  getTrigger = props => {
    if (this.props.renderTrigger) {
      return this.props.renderTrigger({
        ...props,
        className: cx(['add-goods', props.className])
      });
    }

    return (
      // eslint-disable-next-line react/jsx-props-no-spreading
      <a className="add-goods" href="javascript:void(0)" {...props}>
        +加图
      </a>
    );
  };

  render() {
    const { value, disabled } = this.props;
    const qnPublic = get(window, '_global.js.qn_public', 'kdt_img');

    return (
      <div>
        <ul className="app-image-list clearfix">
          {value.map((item, index) => (
            <PicItem
              key={index}
              index={index}
              url={item.url}
              isDragable
              disabled={disabled}
              onMove={this.handleItemMove}
              onPreview={this.handleItemPreview}
              onDelete={this.handleItemDelete}
            />
          ))}
          <li
            className={cx('image-item', {
              hide: value.length >= this.props.maxLen || disabled
            })}
          >
            <Upload
              disabled={disabled}
              materials
              maxAmount={this.props.maxLen - value.length}
              maxSize={1024 * 1024 * 3}
              scope={qnPublic}
              tokenUrl={TOKEN_URL}
              fetchUrl={FETCH_URL}
              kdtId={KDT_ID}
              trigger={this.getTrigger}
              onSuccess={this.uploadSuccess}
              triggerClassName=""
            />
          </li>
        </ul>
      </div>
    );
  }
}

export { UploadField };
