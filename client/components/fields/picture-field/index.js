import React from 'react';
import { Form } from '@zent/compat';
import cx from 'classnames';

import { UploadField } from './pic-cpn';
import css from './index.scss';

const { Field, getControlGroup } = Form;
const PicCpn = getControlGroup(UploadField);

export default function PictureField(props) {
  /* 为了兼容 shop 里使用这个组件的方式 */
  const val = props.picture || props.value;

  return (
    <Field
      name={props.name || 'picture'}
      label={props.label}
      maxLen={props.maxLen}
      value={val}
      disabled={props.disabled}
      component={PicCpn}
      onChange={props.onChange}
      className={cx(css.pic, props.className)}
      helpDesc={props.disabled ? '' : props.helpDesc}
      onlyNeedUrl={props.onlyNeedUrl}
      validations={{
        maxLength: 15
      }}
      validationErrors={{
        maxLength: '商品图片最多支持 15 张。'
      }}
    />
  );
}

PictureField.defaultProps = {
  maxLen: 15,
  helpDesc: '建议尺寸：640 x 640 像素；你可以拖拽图片调整图片顺序。',
  onlyNeedUrl: false
};
