@import '~shared/style';

:local(.pic) {
  .app-image-list {
    .image-item {
      float: left;
      margin: 0 10px 10px 0;
      width: 50px;
      height: 50px;
      border: 1px solid $border-color-dark-base;
      background-color: $color-white;
      position: relative;

      a {
        display: block;
        height: 100%;

        &.close-modal {
          display: none;
          position: absolute;
          z-index: 2;
          top: -9px;
          right: -9px;
          width: 20px;
          height: 20px;
          font-size: 16px;
          line-height: 18px;
          color: $color-white;
          text-align: center;
          cursor: pointer;
          background: rgba(153, 153, 153, 0.6);
          border-radius: 10px;

          &:hover {
            color: $color-white;
            background: $color-black;
          }
        }
      }

      img {
        height: 100%;
        width: 100%;
      }

      .add-goods,
      .add {
        display: inline-block;
        width: 100%;
        height: 100%;
        line-height: 50px;
        text-align: center;
        cursor: pointer;
      }
    }

    li:hover .close-modal {
      display: block;
    }
  }
}
