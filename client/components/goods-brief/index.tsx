import * as React from 'react';
import { Pop, Tag, ClampLines } from 'zent';

import './index.scss';
import { Contains } from 'definition/common';

export default function GoodsBrief({
  data = []
}: Contains<{ data: Array<Contains<{ name: string }>> }>): JSX.Element {
  const head10 = data.slice(0, 10);
  data.length > 10 && head10.push({ name: '更多请查看商品库详情...' });

  return (
    <Pop
      trigger="hover"
      position="bottom-left"
      className="multiple-goods-brief"
      wrapperClassName="goods-brief-wrapper"
      content={
        <div className="multiple-goods-brief__content">
          {head10.map(({ name }, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <ClampLines key={index} lines={1} showPop={false} text={name} />
          ))}
        </div>
      }
    >
      {/** div 是为了解决 Tag 为纯函数组件导致 Pop 拿不到 ref 而失效的问题 */}
      <div>
        <Tag theme="blue" outline className="goods-brief-tag">
          商品明细
        </Tag>
      </div>
    </Pop>
  );
}
