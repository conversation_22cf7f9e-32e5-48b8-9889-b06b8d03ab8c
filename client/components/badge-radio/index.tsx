/* eslint-disable react/jsx-props-no-spreading */
import * as React from 'react';
import { Badge } from '@youzan/retail-components';
import { isNil, isNumber } from 'lodash';

const colors = {
  disabled: '#333333',
  active: '#3388FF',
  border: '#E0E0E0'
};
export default class BadgeRadio extends React.Component<{
  dataSource: Array<{ text: string; value: number }>;
  selectorArr: boolean[];
  onChange: (value: number) => void;
  badgeItemProps: {};
  value: number;
}> {
  lastIndex: number;

  static defaultProps = {
    dataSource: [],
    selectorArr: [],
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onChange: () => {},
    badgeItemProps: {}
  };

  state = {
    selectorArr: this.props.dataSource.map(() => false)
  };

  clickIndex =
    (i: number): (() => void) =>
    () => {
      const { selectorArr: selectorArrOrigin } = this.state;
      const selectorArr = [...selectorArrOrigin];
      if (this.lastIndex === i) {
        return;
      }
      const { onChange, dataSource } = this.props;
      onChange(dataSource[i].value);
      if (isNumber(this.lastIndex)) selectorArr[this.lastIndex] = false;

      selectorArr[i] = true;
      this.lastIndex = i;
      this.setState({ selectorArr });
    };

  checkActive = (i: number): boolean => {
    const { value, dataSource } = this.props;
    const { selectorArr } = this.state;
    if (!isNil(value)) {
      return dataSource[i].value === value;
    }
    return selectorArr[i];
  };

  render() {
    return (
      <div>
        {this.props.dataSource.map((data, index) => (
          <Badge
            key={index}
            className="badge-radio-item"
            radius={3}
            badgeSize={{ width: 16, height: 16 }}
            iconSize={{ long: 6, short: 2 }}
            iconWeight={2}
            iconPosition={{ x: 1, y: 2 }}
            // retail-components 的一个 bug, 无法导入 Positions, 暂时用 any
            position={'bottomRight' as any}
            {...this.props.badgeItemProps}
            showIcon={this.checkActive(index)}
            color={this.checkActive(index) ? colors.active : colors.border}
            // eslint-disable-next-line
            // @ts-ignore
            onClick={this.clickIndex(index)}
          >
            <span
              style={{
                color: this.checkActive(index) ? colors.active : colors.disabled
              }}
            >
              {data.text || ''}
            </span>
          </Badge>
        ))}
      </div>
    );
  }
}
