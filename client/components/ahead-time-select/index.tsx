import * as React from 'react';
import { NumberInput } from 'zent';
import { Select } from '@zent/compat';
import { get } from 'lodash';
import './style.scss';

const { useCallback } = React;
const isWestcake = !!get(window, '_global.business.isWestcake');

const AheadTimeRangeMap = {
  day: [1, isWestcake ? 6 : 31],
  hour: [1, 48],
  minute: [1, 1440]
};

export enum AheadMinType {
  Day = 'day',
  Hour = 'hour',
  Minute = 'minute'
}

const aheadTypeOptions = [
  {
    value: AheadMinType.Day,
    text: '天'
  },
  {
    value: AheadMinType.Hour,
    text: '小时'
  },
  {
    value: AheadMinType.Minute,
    text: '分钟'
  }
];

export interface IAheadTimeSelectData {
  aheadMin: number;
  aheadMinType: AheadMinType;
  aheadDay: number;
}

interface IAheadTimeSelectProps {
  data: IAheadTimeSelectData;
  onChange: (data: IAheadTimeSelectData) => void;
  disabled?: boolean;
  showDayOption?: boolean;
  timeRangeMap?: { [key: string]: any } | null;
}

export default function AheadTimeSelect(props: IAheadTimeSelectProps) {
  const { data, onChange, disabled, showDayOption = true, timeRangeMap = null } = props;

  let options = [...aheadTypeOptions];

  if (!showDayOption) {
    options = aheadTypeOptions.filter(item => item.value !== AheadMinType.Day);
  }

  const onValChange = useCallback(
    (val: string) => {
      onChange({
        ...data,
        aheadMin: parseInt(val, 10)
      });
    },
    [data, onChange]
  );

  const onTypeChange = useCallback(
    evt => {
      const type = evt.target.value;
      if (data.aheadMinType !== type) {
        onChange({
          ...data,
          aheadMin: type === AheadMinType.Minute ? 30 : 1, // 默认 1(天/小时) 或 30(分钟)
          aheadMinType: type
        });
      }
    },
    [data, onChange]
  );

  const { aheadMin, aheadMinType } = data;

  let range = AheadTimeRangeMap[aheadMinType];
  if (timeRangeMap && timeRangeMap[aheadMinType]) {
    range = timeRangeMap[aheadMinType];
  }

  return (
    <div className="ahead-time-select">
      <NumberInput
        disabled={disabled}
        value={String(aheadMin)}
        showStepper
        min={range[0]}
        max={range[1]}
        onChange={onValChange}
      />
      <Select
        className="ahead-time-select-type"
        disabled={disabled}
        autoWidth
        data={options}
        value={aheadMinType}
        onChange={onTypeChange}
      />
    </div>
  );
}
