import type { Contains } from 'definition/common';

import * as React from 'react';
import { Button, Notify } from 'zent';
import { isRetailSingleStore } from '@youzan/utils-shop';

import { baiduToGaode } from 'common/helper';
import AMap from './map';
import './style.scss';
import * as api from './api';

// 标识地图来源
const BAIDU_SOURCE = 2;

// const KDT_ID = get(window, '_global.business.mp_data.id');

class CityDeliveryMap extends React.Component<
  Contains<{
    buyerPos: unknown;
    packId?: string;
    orderNo: string;
    storeId?: number;
    warehouseId?: number;
  }>
> {
  state = {
    horsemanPos: {},
    buyerPos: this.props.buyerPos || {},
    sellerPos: {},
    packId: this.props.packId || '',
    loading: false,
    transporterTrackDownGradeTips: ''
  };

  componentDidMount() {
    this.fetchPos();
  }

  componentWillReceiveProps(nextProps: {
    estimateArriveTimeTip?: string;
    buyerPos: unknown;
    packId?: string;
  }) {
    const newState: typeof nextProps & {
      horsemanPos: {
        estimateArriveTimeTip?: string;
      };
    } = {
      buyerPos: nextProps.buyerPos,
      horsemanPos: {
        ...(this.state.horsemanPos || {})
      }
    };
    if (nextProps.packId !== undefined) {
      newState.packId = nextProps.packId;
      this.fetchPos();
    }

    if (nextProps.estimateArriveTimeTip) {
      newState.horsemanPos.estimateArriveTimeTip = nextProps.estimateArriveTimeTip;
    }
    this.setState(newState);
  }

  fetchPos = () => {
    const { orderNo, storeId, warehouseId } = this.props;
    const { packId } = this.state;
    this.setState({ loading: true });

    const fetchPackPosParams = { packId, orderNo };

    // 连锁版获取店铺数据参数不一样
    const fetchStoreInfoParams = isRetailSingleStore
      ? { storeId: storeId! }
      : { warehouseId: warehouseId! };

    Promise.all([api.fetchStoreInfo(fetchStoreInfoParams), api.fetchPackPos(fetchPackPosParams)])
      .then(res => {
        const sellerPos = res[0] || {};
        const { positionSource } = sellerPos;

        // 如果是百度  需要转换下
        if (positionSource === BAIDU_SOURCE) {
          const { lat, lng } = baiduToGaode(sellerPos.lat, sellerPos.lng);
          sellerPos.lat = lat;
          sellerPos.lng = lng;
        }
        const horsemanPos = res[1] || {};
        horsemanPos.estimateArriveTimeTip = this.props.estimateArriveTimeTip;

        const { transporterTrackDownGradeTips } = horsemanPos;

        this.setState({
          sellerPos,
          horsemanPos,
          transporterTrackDownGradeTips
        });

        setTimeout(() => {
          this.setState({
            transporterTrackDownGradeTips: ''
          });
        }, 2000);
      })
      .catch(err => {
        Notify.error(err.msg || '位置信息获取失败!');
      })
      .finally(() => {
        this.setState({
          loading: false
        });
      });
  };

  render() {
    const { transporterTrackDownGradeTips, loading, ...posInfo } = this.state;
    return (
      <div className="city-delivery-map">
        <Button onClick={this.fetchPos} className="flush-btn" loading={loading}>
          刷新
        </Button>
        {/*
         // @ts-ignore */}
        <AMap
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...posInfo}
        />
        {transporterTrackDownGradeTips && (
          <div className="toast">{transporterTrackDownGradeTips}</div>
        )}
      </div>
    );
  }
}

export default CityDeliveryMap;
