import * as React from 'react';
import { Map, Marker } from '@youzan/react-amap';
import { div } from '@youzan/retail-utils';

import { baiduToGaode } from 'common/helper';

const DEFAULT_MAP_ZOOM = 11;

class AMap extends React.Component<{
  sellerPos: { lat: number; lng: number };
  buyerPos: { lat: number; lon: number };
  horsemanPos: { transporterLat: number; transporterLng: number; estimateArriveTimeTip?: string };
}> {
  static defaultProps = {
    sellerPos: {},
    buyerPos: {},
    horsemanPos: {}
  };

  componentDidMount() {
    window._AMapSecurityConfig = {
      securityJsCode: '6c3bcdda746e99e0ca83f2e1bcdcfb6f'
    };
  }

  renderHorsemanMarker = () => {
    const { transporterLat, transporterLng } = this.props.horsemanPos;

    return transporterLat && transporterLng ? (
      <Marker position={{ lat: transporterLat, lng: transporterLng }}>
        <div className="horseman-box">
          <span className="horseman-icon" />
          {this.props.horsemanPos?.estimateArriveTimeTip && (
            <span className="horseman-arrive-time">
              {this.props.horsemanPos?.estimateArriveTimeTip}
            </span>
          )}
        </div>
      </Marker>
    ) : null;
  };

  // 送货地址
  renderBuyerMarker = () => {
    const { lat: originLat, lon: originLng } = this.props.buyerPos;

    const { lat, lng } = baiduToGaode(originLat, originLng);
    return lat && lng ? (
      <Marker
        label={{
          content: '买家',
          offset: {
            x: -5,
            y: 35
          }
        }}
        position={{
          lat,
          lng
        }}
      />
    ) : null;
  };

  // 取货地址
  renderSellerMarker = () => {
    const { lat, lng } = this.props.sellerPos;
    return lat && lng ? (
      <Marker
        label={{
          content: '商家',
          offset: {
            x: -5,
            y: 35
          }
        }}
        position={{ lat, lng }}
      />
    ) : null;
  };

  calcCenterPos = () => {
    const { horsemanPos, sellerPos, buyerPos } = this.props;

    const getMiddleValue = (...args: number[]) =>
      div(
        args.reduce((pre, item) => pre + (item || 0), 0),
        args.length
      );

    const formatNumer = (val: number): number => +val || 0;

    const midLng =
      (formatNumer(horsemanPos.transporterLng),
      formatNumer(sellerPos.lng),
      formatNumer(buyerPos.lon));
    const midLat = getMiddleValue(
      formatNumer(horsemanPos.transporterLat),
      formatNumer(sellerPos.lat),
      formatNumer(buyerPos.lat)
    );

    return midLng && midLat
      ? {
          lat: midLat,
          lng: midLng
        }
      : null;
  };

  render() {
    this.calcCenterPos();
    return (
      <Map
        zoom={DEFAULT_MAP_ZOOM}
        center={this.calcCenterPos()!}
        amapkey="decef01a1628430d5159a8cd10dec3da"
        // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
        proxyHost="lbsmap.youzan.com"
      >
        {this.renderHorsemanMarker()}
        {this.renderBuyerMarker()}
        {this.renderSellerMarker()}
      </Map>
    );
  }
}

export default AMap;
