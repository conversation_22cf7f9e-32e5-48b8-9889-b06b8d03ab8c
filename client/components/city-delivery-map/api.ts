import { request } from '@youzan/retail-utils';

// 获取门店位置
export const fetchStoreInfo = (
  data:
    | {
        warehouseId: number;
        storeId?: number;
      }
    | { warehouseId?: number; storeId: number }
): Promise<any> =>
  request({
    url: '/youzan.retail.trademanager.order.delivery/1.0.0/findshop',
    data
  });

// 获取包裹位置(骑手位置)
export const fetchPackPos = (data: { packId: string; orderNo: string }): Promise<any> =>
  request({
    url: '/youzan.retail.trademanager.order.delivery/1.0.0/packinfo',
    data
  });
