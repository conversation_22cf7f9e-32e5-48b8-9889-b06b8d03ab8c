@import '~shared/style';

.city-delivery-map {
  width: 100%;
  height: 200px;
  position: relative;

  .flush-btn {
    position: absolute;
    font-size: $font-size-small;
    right: 10px;
    top: 10px;
    z-index: 10;
    color: $color-link;
    border-radius: 25%;
    border-radius: 20px;
    width: 60px;
  }
}

.amap-marker-label {
  border: none;
  background-color: inherit;
  color: $color-link;
}
.horseman-box {
  display: flex;
}
.horseman-icon {
  width: 40px;
  height: 40px;
  display: inline-block;
  background-image: image-cdn('retail/img/order/horseman.png');
  background-size: contain;
}
.horseman-arrive-time {
  line-height: 24px;
  white-space: nowrap;
  background-color: white;
  padding: 4px 10px;
  font-size: 12px;
  position: relative;
  border-radius: 5px;
  margin-top: -36px;
  height: 24px;
  margin-left: -20px;
}
.toast {
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  position: absolute;
  color: #fff;
  padding: 8px 12px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
}
