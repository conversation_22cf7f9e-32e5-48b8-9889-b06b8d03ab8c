import * as React from 'react';
import { get } from 'lodash';

export default function BeforeAfterHoc(
  WrappedCpn: (props: Record<string, unknown>) => JSX.Element
) {
  return class BeforeAfterAction extends React.Component<{
    actionName: string;
    onClick: (...args: unknown[]) => void;
  }> {
    static defaultProps = {
      actionName: 'submit',
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      onClick: () => {}
    };

    handle = (...args: unknown[]) => {
      const { actionName } = this.props;
      const beforeAction = get(this.props, `before${actionName}`, null);
      if (beforeAction) {
        beforeAction(this.do.bind(this, ...args));
        return;
      }
      this.do(...args);
    };

    do(...args: unknown[]) {
      const { actionName, onClick } = this.props;
      const afterAction = get(this.props, `after${actionName}`, null);
      if (afterAction) {
        onClick(...args, afterAction);
        return;
      }
      onClick(...args);
    }

    render() {
      // eslint-disable-next-line react/jsx-props-no-spreading
      return <WrappedCpn {...this.props} onClick={this.handle} />;
    }
  };
}
