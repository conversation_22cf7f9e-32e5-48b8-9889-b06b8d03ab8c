import React from 'react';
import { refundOrderStatusTypeOptions } from '@youzan/zan-hasaki';
import { Filter } from '@youzan/retail-components';
import RetailSelect from '../components/retail-select';

const { FilterField } = Filter;

const RefundStateField = ({ refundStateData, ...props }) => (
  <div className="filter-item__field refund-state-select">
    <FilterField
      label="退款状态："
      name="status"
      className="retail-select"
      component={RetailSelect}
      data={refundStateData || refundOrderStatusTypeOptions}
      {...props}
    />
  </div>
);

export default RefundStateField;
