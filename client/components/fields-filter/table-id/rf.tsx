import * as React from 'react';
import { Field } from '@youzan/retail-form';
import { BrandSelect } from '@youzan/biz-select-center';

interface SelectFieldProps {
  data: { [key: string]: string | number }[];
}
const TableId = (props: SelectFieldProps) => {
  return (
    <div className="filter-item__field">
      <Field
        label="桌号："
        name="tableId"
        component={BrandSelect}
        isRF
        totalValue={-1}
        totalText="全部"
        optionText="text"
        optionValue="value"
        props={{ data: props.data }}
      />
    </div>
  );
};

export default TableId;
