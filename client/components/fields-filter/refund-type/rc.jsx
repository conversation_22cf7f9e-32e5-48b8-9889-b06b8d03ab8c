import React from 'react';
import { refundTypeOptions } from '@youzan/zan-hasaki';

import { Filter } from '@youzan/retail-components';
import { formatArrayByAddAll } from 'common/common';

const { FilterField } = Filter;

const RefundTypeField = props => (
  <div className="filter-item__field">
    <FilterField
      label="退款类型："
      name="type"
      component="SelectField"
      data={formatArrayByAddAll(refundTypeOptions)}
      {...props}
    />
  </div>
);

export default RefundTypeField;
