import { IBaseProps } from '@youzan/biz-select-center/types/base-select';
import { IFieldProps, IWithControlFieldProps } from '@youzan/retail-form';
import { SalesChannel } from './constant';

export type ISelectFieldProps = IBaseProps & Partial<IFieldProps>;

export type ISelectControlledFieldProps = ISelectFieldProps & IWithControlFieldProps;

export interface ISalesChannelData {
  /** 渠道归属 */
  belongType: number;
  /** 渠道名称 */
  channelName: string;
  /** 渠道logo */
  channelLogo: string;
  /** 渠道id */
  channelId: SalesChannel;
}
