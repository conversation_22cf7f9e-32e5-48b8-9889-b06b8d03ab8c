import { request } from '@youzan/retail-utils';
import { ISalesChannelData } from './types';

/**
 * 查询 是否展示外部渠道
 */
export function queryShowExternalSalesChannel() {
  return request<boolean>({
    url: 'youzan.retail.trademanager.query.checkShowSalesChannelQuery/1.0.0'
  });
}

/**
 * 查询 销售渠道
 */
export function queryOpenedSalesChannel(data: { isLiteOnlineStoreManager?: boolean }) {
  return request<ISalesChannelData[]>({
    url: '/v2/order/query/queryOpenedSalesChannel',
    data
  });
}
