import * as React from 'react';
import { useEffect, useState, useMemo } from 'react';
import { Notify } from 'zent';
import { BaseSelectField } from '@youzan/biz-select-center';
import { IsLiteOnlineStoreManager } from 'common/utils';
import { ISelectControlledFieldProps, ISalesChannelData } from './types';
import { queryShowExternalSalesChannel, queryOpenedSalesChannel } from './api';
import { SalesChannel } from './constant';

const defaultSalesChannelData = [
  {
    channelName: '全部',
    channelId: -1
  }
];

function getParams() {
  if (IsLiteOnlineStoreManager) {
    return { isLiteOnlineStoreManager: IsLiteOnlineStoreManager };
  }
  return {};
}

export function OpenedSalesChannelField(props: ISelectControlledFieldProps) {
  const [showExternalSalesChannel, setShowExternalSalesChannel] = useState<boolean>(false);
  const [salesChannelData, setSalesChannelData] = useState<ISalesChannelData[]>([]);

  useEffect(() => {
    Promise.all([queryShowExternalSalesChannel(), queryOpenedSalesChannel(getParams())])
      .then(([showSalesChannel, salesChannelData = []]) => {
        setShowExternalSalesChannel(showSalesChannel);
        setSalesChannelData(salesChannelData);
      })
      .catch(err => {
        Notify.error(err?.msg || '数据请求失败');
      });
  }, []);

  const data = useMemo(() => {
    if (showExternalSalesChannel) {
      return [...defaultSalesChannelData, ...salesChannelData];
    }
    const data = salesChannelData.filter(item => {
      return [SalesChannel.Online, SalesChannel.Offline].includes(item.channelId);
    });
    return [...defaultSalesChannelData, ...data];
  }, [salesChannelData, showExternalSalesChannel]);

  return (
    <BaseSelectField
      label="销售渠道："
      name="saleWay"
      optionText="channelName"
      optionValue="channelId"
      className="filter-item__field"
      data={data}
      {...props}
    />
  );
}
