/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';

import { Field, IFieldProps } from '@youzan/retail-form';
import { ICascaderIdType } from '@youzan/goods-domain-definitions';
import {
  GoodsClassificationSelector,
  IGoodsClassificationSelectorProps
} from '@youzan/goods-domain-pc-components';

type IOnChange = IGoodsClassificationSelectorProps['onChange'];

const AllOptionValue = -1;
const GoodsClassificationSelect: React.FC<
  Omit<IGoodsClassificationSelectorProps, 'value' | 'onChange'> & {
    input: {
      value: ICascaderIdType | ICascaderIdType[];
      onChange: IOnChange;
    };
    propOnChange: IOnChange;
  }
> = props => {
  const DefaultValue = props.leafInValue ? [AllOptionValue] : AllOptionValue;
  const {
    leafInValue = true,
    input: { value = DefaultValue, onChange: inputOnChange },
    propOnChange
  } = props;

  const onChange: IOnChange = data => {
    propOnChange && propOnChange(data);
    inputOnChange(data);
  };

  const val = leafInValue && Array.isArray(value) ? value[0] : (value as ICascaderIdType);

  return (
    <GoodsClassificationSelector
      leafInValue={leafInValue}
      maxChildCount={200}
      value={val}
      onChange={onChange}
      showAll
      allOptionLabel="全部"
      showActions={false}
      expandTrigger="hover"
      popupOptionContainWidth={160}
      addPopEllipsisTextEffect
      {...props}
    />
  );
};

const GoodsClassificationSelectFieldName = 'classificationIdList';
const GoodsClassificationSelectField: React.FC<IFieldProps> = fieldProps => {
  const {
    name = GoodsClassificationSelectFieldName,
    label = '商品分类：',
    component = GoodsClassificationSelect,
    props = {},
    ...rest
  } = fieldProps;

  return (
    <div className="filter-item__field">
      <Field name={name} label={label} props={props} component={component} {...rest} />
    </div>
  );
};

export { GoodsClassificationSelectFieldName };
export default GoodsClassificationSelectField;
