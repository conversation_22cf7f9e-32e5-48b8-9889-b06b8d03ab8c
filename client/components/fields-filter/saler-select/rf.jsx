/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { Field } from '@youzan/retail-form';
import { global } from '@youzan/retail-utils';

import SalersSelect from './saler-select';
import withValueOnChange from '../components/with-value-onchange';

const SalersSel = withValueOnChange(SalersSelect);

const SalersSelectField = props => (
  <div className="filter-item__field">
    <Field
      name="salesId"
      label={`${global.BUSINESS.isSalesmanUpgrade ? '内部销售员' : '导购员'}：`}
      component={SalersSel}
      {...props}
    />
  </div>
);

export default SalersSelectField;
