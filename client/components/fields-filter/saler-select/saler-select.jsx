import React from 'react';
import { Select } from '@youzan/biz-select-center';
import { Notify } from 'zent';
import { debounce } from '@youzan/retail-armor';
import * as api from 'route/query/api';
import { uniqBy } from 'lodash';
import { global } from '@youzan/retail-utils';
import { isHqStore, isPartnerStore } from '@youzan/utils-shop';

const NO_KEYWORD = '';
const { KDT_ID } = global;
const urlData = []; // url链接上的参数

class SalesmanSel extends React.PureComponent {
  state = {
    data: [],
    isFiltering: false,
    prevData: []
  };

  initData = () => {
    // 非总店
    if (this.props.subKdtId) {
      this.fetch();
    }
  };

  componentDidMount() {
    this.initData();
  }

  // 切换销售渠道触发新查询
  componentDidUpdate(prevProps) {
    const { subKdtId, onChange, staffName } = this.props;

    if (subKdtId && subKdtId !== prevProps.subKdtId) {
      this.initData();
      if (prevProps.subKdtId) {
        // 初始化subKdtId值不重置value，value值可能来自url
        onChange(null); // 切换销售渠道，重置导购员value
      }
      if (!prevProps.subKdtId && staffName) {
        // 初始化subKdtId值,使用url链接参数
        this.setUrlData();
      }
    }
  }

  setUrlData = () => {
    const { value, staffName } = this.props;
    urlData.push({
      staffName,
      adminId: value
    });
    this.setState({
      data: urlData,
      prevData: urlData
    });
  };

  // 关键字查询
  @debounce(1000) // 防止短时间请求过多
  fetchKeyword(keyword) {
    if (keyword === '') {
      this.setState(prevState => ({ data: prevState.prevData }));
      return;
    }
    this.fetchSalesman(keyword)
      .then(({ items }) => {
        this.setState(prevState => ({
          data: [...prevState.data, ...items],
          prevData: uniqBy([...prevState.prevData, ...items], 'adminId') // 合并新数据和旧数据
        }));
      })
      .finally(() => this.setState({ isFiltering: false }));
  }

  // 查询
  fetch = () => {
    this.fetchSalesman().then(({ items }) => {
      let prevData = [];
      let { shopKdtId } = this.props;
      if (!this.props.shopKdtId || this.props.shopKdtId === -1) {
        shopKdtId = KDT_ID;
      }
      if (shopKdtId === this.props.subKdtId) {
        // url链接的shopId和销售渠道相同时，使用url链接数据
        prevData = urlData;
      }
      this.setState(
        () => ({
          data: uniqBy([...items, ...prevData], 'adminId'), // 合并新数据和url链接数据
          prevData: uniqBy([...items, ...prevData], 'adminId') // 合并新数据和url链接数据
        }),
        () => {
          this.props.setSalers(this.state.prevData);
        }
      );
    });
  };

  // 查询导购员列表
  fetchSalesman = (keyword = NO_KEYWORD) => {
    const { subKdtId } = this.props;
    const param = {
      pageSize: 100,
      roleIds: JSON.stringify([18]),
      biz: 'retail',
      keyword,
      pageNo: 1,
      subKdtId
    };

    // 连锁版下导购员接口, 前端对 staffName -> name 字段进行了映射
    return api.fetchSalesman(param).catch(err => {
      Notify.error(err.msg || '获取导购员列表数据失败！');
    });
  };

  render() {
    const { onChange, value, subKdtId, setSalers } = this.props;
    const { data, isFiltering } = this.state;
    return (
      <Select
        isFiltering={isFiltering}
        value={value}
        data={data}
        optionText="staffName"
        optionValue="adminId"
        generateKey={item => `${item.kdtId}/${item.adminId}`}
        onChange={id => {
          onChange(id);
          this.setState(
            prevState => ({ data: prevState.prevData }),
            () => {
              setSalers(this.state.prevData);
            }
          );
        }}
        onAsyncFilter={keyword => {
          if (!(isHqStore || isPartnerStore) || subKdtId !== KDT_ID) {
            // 店铺角色非零售总部 且 非合伙人 或 销售渠道非全部、网店，则不用异步搜索
            return;
          }
          this.setState(() => ({
            isFiltering: true, // 转圈给用户及时相应的感觉
            data: []
          }));
          this.fetchKeyword(keyword);
        }}
        filter={(item, keyword) => (item.name || item.staffName).indexOf(keyword) > -1}
      />
    );
  }
}

export default SalesmanSel;
