/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { Form } from '@zent/compat';

import SalersSelect from './saler-select';

const { getControlGroup, Field } = Form;

const SalersSel = getControlGroup(SalersSelect);

const SalersSelectField = props => (
  <div className="filter-item__field">
    <Field name="salesId" label="内部销售员：" component={SalersSel} {...props} />
  </div>
);

export default SalersSelectField;
