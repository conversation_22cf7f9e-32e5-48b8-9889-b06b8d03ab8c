import React, { Component } from 'react';
import { DateRangeQuickPicker, Notify } from 'zent';

import { dateRangePickerPreset } from 'common/constants';

class DateRangeQuickPickerWrapper extends Component {
  state = { chooseDays: undefined };

  handleChange = (value, chooseDays) => {
    const { onChange, checkOrderDateRange, filterRange } = this.props;
    const [startTime, endTime] = value;
    // 如果当前是在 订单查询页面 下进行下单时间筛选，才走此check逻辑
    if (filterRange === 'query' && checkOrderDateRange && checkOrderDateRange(startTime, endTime)) {
      Notify.error('每次可搜索一年订单记录，请重新选择起止时间');
    }
    const resolveValues = value.map(v => {
      // 需要处理下组件中获得的值，不然直接传到后端筛选条件有问题
      if (v === 0) return '';
      return v;
    });
    onChange && onChange(resolveValues, chooseDays);
    this.setState({ chooseDays });
  };

  render() {
    const { value, ...others } = this.props;
    let dateValue = value;
    let chooseDays;

    if (!Array.isArray(value)) {
      chooseDays = undefined;
      dateValue = [];
    } else {
      chooseDays = value[1] && value[0] ? this.state.chooseDays : undefined;
    }
    return (
      <DateRangeQuickPicker
        format="YYYY-MM-DD HH:mm:ss"
        preset={dateRangePickerPreset}
        {...others}
        value={dateValue}
        onChange={this.handleChange}
        chooseDays={chooseDays}
      />
    );
  }
}

export { DateRangeQuickPickerWrapper };
