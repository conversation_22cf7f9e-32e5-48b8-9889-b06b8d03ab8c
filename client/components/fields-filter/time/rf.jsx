import React from 'react';
import { Pop, Icon } from 'zent';
import { addMonths, endOfDay } from 'date-fns';
import { Field } from '@youzan/retail-form';
import { isRetailShop } from '@youzan/utils-shop';
import parseDate from '@youzan/utils/date/parseDate';

import {
  dateRangePickerProps,
  futureDateRangePickerProps,
  datePickerDisableSevenDays,
  YearlyDateRangePickerPreset
} from 'common/constants';
import withValueOnChange from '../components/with-value-onchange';
import { DateRangeQuickPickerWrapper } from './date-range-picker';

const DateRangeQuickPickerCpn = withValueOnChange(DateRangeQuickPickerWrapper);

const DateQuickPickerField = props => {
  return <Field component={DateRangeQuickPickerCpn} {...props} />;
};

function getDateRangePickerProps() {
  if (isRetailShop) {
    return {
      format: dateRangePickerProps.format,
      preset: YearlyDateRangePickerPreset
    };
  }
  return dateRangePickerProps;
}

const TimeField = props => {
  const { label, name, component, disableSevenDays, suffixField, isFuturePreset, ...others } =
    props;
  const disableSevenProps = disableSevenDays ? datePickerDisableSevenDays : {};
  return (
    <div className="filter-item">
      <div className="filter-item__field">
        <Field
          label={label}
          name={name}
          component={component}
          valueType="number"
          props={{
            ...(isFuturePreset ? futureDateRangePickerProps : getDateRangePickerProps()),
            ...disableSevenProps
          }}
          {...others}
        />
      </div>
      {suffixField}
    </div>
  );
};

// 判断订单下单时间间隔是否大于一年
const checkOrderDateRange = (startTime, endTime) => {
  // 起止日期其一为空，跳过校验
  if (!startTime || !endTime) {
    return false;
  }
  const startDate = parseDate(startTime);
  const endDate = parseDate(endTime);
  const limitedEndDate = endOfDay(addMonths(startDate, 12));
  return endDate > limitedEndDate;
};

// 订单查询，下单时间field
const OrderTimeField = props => {
  return (
    <TimeField
      name="orderTime"
      component={DateRangeQuickPickerCpn}
      checkOrderDateRange={checkOrderDateRange}
      disableSevenDays
      filterRange={props.filterRange}
    />
  );
};

// 退款维权，申请时间field
const ApplyTimeField = () => (
  <TimeField
    label="申请时间："
    name="applyTime"
    component={DateRangeQuickPickerCpn}
    validationErrors={{
      required: '请填写完整申请时间',
      timeSection: '时间查询区间不能超过90天'
    }}
    disableSevenDays
  />
);

// 送达时间，支持自定义preset，支持自定义label
const DeliveryTimeField = ({
  isFuturePreset = false,
  label = '送达时间',
  sortDisabled = true,
  isNeedSort = false
}) => (
  <TimeField
    label={`${label}：`}
    name="deliveryTime"
    component={DateRangeQuickPickerCpn}
    isFuturePreset={isFuturePreset}
    suffixField={
      isNeedSort && (
        <div style={{ display: 'inline-block', marginLeft: 12, verticalAlign: 'middle' }}>
          <Field
            label=""
            name="deliveryTimeFetchSort"
            component="CheckboxField"
            disabled={sortDisabled}
          >
            <Pop
              trigger="hover"
              content={<div>仅支持“发货状态”选择“待发货”时，对订单进行排序。</div>}
            >
              按预约送达时间正序排列
            </Pop>
            <Pop
              trigger="hover"
              position="top-right"
              content={<div>仅支持“发货状态”选择“待发货”时，对订单进行排序。</div>}
            >
              <Icon
                style={{
                  color: '#c8c9cc',
                  marginLeft: 5,
                  fontSize: 16
                }}
                type="help-circle"
              />
            </Pop>
          </Field>
        </div>
      )
    }
  />
);

const PlanExpressTimeField = () => (
  <TimeField label="送达时间：" name="planExpressTime" component="DatePickerField" />
);

const SelfFetchTimeField = ({ sortDisabled = true }) => (
  <TimeField
    label="自提时间："
    name="selfFetchTime"
    component={DateRangeQuickPickerCpn}
    suffixField={
      <div style={{ display: 'inline-block', marginLeft: 12, verticalAlign: 'middle' }}>
        <Field label="" name="selfFetchSort" component="CheckboxField" disabled={sortDisabled}>
          <Pop trigger="hover" content={<div>仅支持“待自提”状态时，对订单进行排序。</div>}>
            按自提时间正序排列
          </Pop>
          <Pop
            trigger="hover"
            position="top-right"
            content={
              <div style={{ width: 280 }}>
                <h5>排序规则：</h5>
                <p>1. 有自提时间的订单，则按自提开始时间先后排序；</p>
                <p>2. “请尽快到自提点提货”的订单，排在最前面。</p>
              </div>
            }
          >
            <Icon
              style={{
                color: '#c8c9cc',
                marginLeft: 5,
                fontSize: 16
              }}
              type="help-circle"
            />
          </Pop>
        </Field>
      </div>
    }
  />
);

export {
  DateQuickPickerField,
  OrderTimeField,
  ApplyTimeField,
  DeliveryTimeField,
  PlanExpressTimeField,
  SelfFetchTimeField
};
export default TimeField;
