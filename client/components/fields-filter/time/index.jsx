import React from 'react';
import { isArray, omit } from 'lodash';
import { DatePicker } from 'zent';
import { Form } from '@zent/compat';

import {
  datePickerDisableSevenDays,
  dateRangePickerPreset,
  datePickerDefaultTime
} from 'common/constants';
import { DateRangeQuickPickerWrapper } from './date-range-picker';

const { getControlGroup, unknownProps, Field } = Form;

const widthDateFormat = DateComponent => props => {
  const parsedProps = omit(props, unknownProps);
  const { dateFormat, ...others } = parsedProps;
  return <DateComponent {...others} format={dateFormat} />;
};

const DatePickerField = getControlGroup(widthDateFormat(DatePicker));
const DateRangeQuickPickerField = getControlGroup(widthDateFormat(DateRangeQuickPickerWrapper));

export const FormDatePickerField = props => (
  <Field {...props} component={DatePickerField} preset={dateRangePickerPreset} />
);

export const FormDateRangeQuickPickerField = props => (
  <Field
    {...props}
    component={DateRangeQuickPickerField}
    preset={dateRangePickerPreset}
    defaultTime={datePickerDefaultTime}
  />
);

// 时间选择不能为空, 要么两个都填，要么两个都不填, 不能一个有值一个没值
const validateTime = (_, times) => {
  if (!isArray(times)) {
    return true;
  }
  const emptyCount = times.reduce((pre, time) => pre + (time ? 1 : 0), 0);
  return emptyCount !== 1;
};

// 检查时间选择区间不能超过90天
const validateTimeSection = (formData, times) => {
  if (validateTime(formData, times) && times.indexOf('') === -1) {
    return times[1] - times[0] <= 90 * 24 * 60 * 60 * 1000;
  }
  return true;
};

const OrderTimeField = props => (
  <div className="filter-item__field">
    <FormDateRangeQuickPickerField
      label="下单时间："
      name="orderTime"
      dateFormat="YYYY-MM-DD HH:mm:ss"
      valueType="number"
      validations={{
        required: validateTime,
        timeSection: validateTimeSection
      }}
      validationErrors={{
        required: '请填写完整下单时间',
        timeSection: '时间查询区间不能超过90天'
      }}
      {...datePickerDisableSevenDays}
      {...props}
    />
  </div>
);

export default OrderTimeField;
