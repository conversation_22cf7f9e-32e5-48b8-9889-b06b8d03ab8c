import React from 'react';
import { Field } from '@youzan/retail-form';

import { TimeType, TimeTypeTextMap } from 'common/constants/order';

const TimeSelectOptionsData = [
  {
    value: TimeType.BookTime,
    text: TimeTypeTextMap[TimeType.BookTime]
  },
  {
    value: TimeType.ShipTime,
    text: TimeTypeTextMap[TimeType.ShipTime]
  },
  {
    value: TimeType.PayTime,
    text: TimeTypeTextMap[TimeType.PayTime]
  },
  {
    value: TimeType.SuccessTime,
    text: TimeTypeTextMap[TimeType.SuccessTime]
  }
];
/** 订单搜索 */
export const TimeTypeSelect = () => {
  return (
    <Field
      label="时间搜索："
      className="search-label"
      name="timeType"
      component="SelectField"
      type="rc"
      data={TimeSelectOptionsData}
    />
  );
};
