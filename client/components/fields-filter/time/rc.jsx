import React from 'react';
import { Filter } from '@youzan/retail-components';
import { dateRangePickerProps } from 'common/constants';

const { FilterField } = Filter;

export default function TimeField({ label = '申请时间：', name = 'applyTime' }) {
  return (
    <FilterField
      label={label}
      name={name}
      component="DateRangeQuickPickerField"
      valueType="number"
      props={dateRangePickerProps}
    />
  );
}
