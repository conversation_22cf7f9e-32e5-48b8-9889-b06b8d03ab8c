/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';

import { Field, IFieldProps } from '@youzan/retail-form';
import { IGoodsBrandSelectorProps, ICascaderIdType } from '@youzan/goods-domain-definitions';
import { GoodsBrandSelector } from '@youzan/goods-domain-pc-components';

type IOnChange = IGoodsBrandSelectorProps['onChange'];

const AllOptionValue = -1;
const GoodsBrandSelect: React.FC<
  Omit<IGoodsBrandSelectorProps, 'value' | 'onChange'> & {
    input: {
      value: ICascaderIdType | ICascaderIdType[];
      onChange: IOnChange;
    };
    propOnChange: IOnChange;
  }
> = props => {
  const DefaultValue = props.leafInValue ? [AllOptionValue] : AllOptionValue;
  const {
    leafInValue = true,
    input: { value = DefaultValue, onChange: inputOnChange },
    propOnChange
  } = props;

  const onChange: IOnChange = data => {
    inputOnChange(data);
    propOnChange && propOnChange(data);
  };

  const val = leafInValue && Array.isArray(value) ? value[0] : (value as ICascaderIdType);

  return (
    <GoodsBrandSelector
      leafInValue={leafInValue}
      maxChildCount={200}
      value={val}
      onChange={onChange}
      showAll
      allOptionLabel="全部"
      showNoBrand
      showActions={false}
      expandTrigger="hover"
      popupOptionContainWidth={160}
      addPopEllipsisTextEffect
      {...props}
    />
  );
};

const GoodsBrandSelectFieldName = 'brandIdList';
const GoodsBrandSelectField: React.FC<IFieldProps> = fieldProps => {
  const {
    name = GoodsBrandSelectFieldName,
    label = '商品品牌：',
    component = GoodsBrandSelect,
    props = {},
    ...rest
  } = fieldProps;

  return (
    <div className="filter-item__field">
      <Field name={name} label={label} props={props} component={component} {...rest} />
    </div>
  );
};

export { GoodsBrandSelectFieldName };
export default GoodsBrandSelectField;
