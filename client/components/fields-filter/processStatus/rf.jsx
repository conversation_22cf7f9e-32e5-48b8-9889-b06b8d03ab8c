import React from 'react';
import { Field } from '@youzan/retail-form';

const ProcessStatusList = [
  {
    text: '全部',
    value: -1
  },
  {
    text: '无需作业',
    value: 0
  },
  {
    text: '待加工',
    value: 10
  },
  {
    text: '加工中',
    value: 11
  },
  {
    text: '待调拨',
    value: 20
  },
  {
    text: '调拨中',
    value: 21
  },
  {
    text: '作业完成',
    value: 60
  }
];

const ProcessStatusField = props => (
  <div className="filter-item__field">
    <Field
      label="加工状态："
      name="processStatus"
      component="SelectField"
      type="rc"
      data={ProcessStatusList}
      {...props}
    />
  </div>
);

export default ProcessStatusField;
