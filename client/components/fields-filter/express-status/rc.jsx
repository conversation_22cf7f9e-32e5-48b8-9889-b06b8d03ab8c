import React from 'react';
import { Filter } from '@youzan/retail-components';

const { FilterField } = Filter;

const ExpressState = [
  {
    text: '全部',
    value: -1
  },
  {
    text: '已发货',
    value: 2
  },
  {
    text: '待发货',
    value: 1
  }
];

const ExpressType = props => (
  <div className="filter-item__field">
    <FilterField
      label="发货状态："
      name="deliveryStatus"
      component="SelectField"
      data={ExpressState}
      {...props}
    />
  </div>
);

export default ExpressType;
