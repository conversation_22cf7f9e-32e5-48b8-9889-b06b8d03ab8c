import * as React from 'react';
import { Field } from '@youzan/retail-form';

import { useEffect, useState } from 'react';
import { Notify } from 'zent';
import { fetchFilterOptions, OptionShapeArr } from 'common/api';

const OrderSourceField = () => {
  const [data, setData] = useState<OptionShapeArr>([]);

  useEffect(() => {
    fetchFilterOptions({ type: 'ordersource' })
      .then(res => {
        const t = res.map(o => ({
          text: o.display,
          value: +o.paramValue
        }));
        t.unshift({ text: '全部', value: -1 });
        setData(t);
      })
      .catch(e => Notify.error(e?.msg));
  }, []);

  return (
    <div className="filter-item__field">
      <Field
        label="订单来源："
        name="orderSource"
        component="SelectField"
        type="rc"
        filter
        data={data}
      />
    </div>
  );
};

export default React.memo(OrderSourceField);
