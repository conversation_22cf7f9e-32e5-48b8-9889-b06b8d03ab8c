import React from 'react';
import { Field } from '@youzan/retail-form';

const SelffetchPointField = props => (
  <Field
    label="自提点："
    className="fetch-store"
    name="selfFetchId"
    component="SelectField"
    type="rc"
    {...props}
  />
);

const ChainSelffetchPointField = props => (
  <div className="filter-item__field">
    <Field
      label="自提点："
      className="fetch-store"
      name="selfFetchId"
      component="SelectField"
      // type="rc"
      onAsyncFilter={props.onAsyncFilter}
      onChange={props.onChange}
      {...props}
    />
  </div>
);

export { ChainSelffetchPointField };

export default SelffetchPointField;
