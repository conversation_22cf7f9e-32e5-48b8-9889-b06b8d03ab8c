import React, { Component } from 'react';
import { isHqStore, isPartnerStore, isRetailMinimalistShop } from '@youzan/utils-shop';
import { fetchChainShopList } from 'common/api';

import TargetField from '../sale-way/rc';

export default class SaleWayShopField extends Component {
  state = {
    storeList: [
      {
        text: '全部',
        value: -1
      }
    ]
  };

  componentDidMount() {
    (isHqStore || isPartnerStore) &&
      !isRetailMinimalistShop &&
      fetchChainShopList().then(rst => {
        const ds = rst.items.map(ele => ({
          text: ele.storeName,
          value: ele.storeKdtId
        }));
        const arr = [...this.state.storeList];
        this.setState({ storeList: [...arr, ...ds] });
      });
  }

  renderField() {
    if ((isHqStore || isPartnerStore) && !isRetailMinimalistShop) {
      return <TargetField name="shopKdtId" ds={this.state.storeList} />;
    }
    return <TargetField />;
  }

  render() {
    return this.renderField();
  }
}
