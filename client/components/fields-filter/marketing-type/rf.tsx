import * as React from 'react';
import { Field } from '@youzan/retail-form';

interface SelectFieldProps {
  data: { [key: string]: string | number }[];
}

const MarketingField = (props: SelectFieldProps) => (
  <div className="filter-item__field">
    <Field
      label="推广方式："
      name="marketingType"
      component="SelectField"
      type="rc"
      data={props.data}
    />
  </div>
);

export default MarketingField;
