import * as React from 'react';
import { Field } from '@youzan/retail-form';
import { useEffect, useState } from 'react';

import { Notify } from 'zent';
import {
  ConditionShape,
  fetchFilterOptions,
  ISaleWayMap,
  OptionShapeArr,
  formatter
} from 'common/api';

interface IBuyWayFieldProps {
  saleWay: ISaleWayMap;
}

const BuyWayField = (props: IBuyWayFieldProps) => {
  const { saleWay } = props;
  const [originData, setOriginData] = useState<OptionShapeArr>([]);
  const [data, setData] = useState<OptionShapeArr>([]);

  useEffect(() => {
    fetchFilterOptions({ type: 'payway' })
      .then(res => {
        const data = res.map(formatter);
        data.unshift({ text: '全部', value: -1, enableChannels: ['ALL', 'OFFLINE', 'ONLINE'] });
        setData(
          data.filter(o =>
            (o.enableChannels as ConditionShape['displayAtSaleWay']).includes(saleWay)
          )
        );
        setOriginData(data);
      })
      .catch(e => Notify.error(e.msg));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setData(
      originData.filter(o =>
        (o.enableChannels as ConditionShape['displayAtSaleWay']).includes(saleWay)
      )
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [saleWay]);

  return (
    <div className="filter-item__field">
      <Field
        label="支付方式："
        name="buyWay"
        component="SelectField"
        type="rc"
        filter
        data={data}
      />
    </div>
  );
};

function areEqual(prev: IBuyWayFieldProps, curr: IBuyWayFieldProps) {
  return prev.saleWay === curr.saleWay;
}

export default React.memo(BuyWayField, areEqual);
