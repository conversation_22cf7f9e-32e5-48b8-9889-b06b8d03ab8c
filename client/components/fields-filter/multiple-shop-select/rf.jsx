import React from 'react';
import { Field } from '@youzan/retail-form';
import { ShopSelect } from '@youzan/react-components';
import queryHistory from 'common/query-history';

/**
 * 总部版使用销售渠道
 * @param {any} props
 * @returns
 */
const MultipleShopSelect = props => {
  // 获取query中defaultShopKeyword字段使店铺选择组件能获取到query中的店铺
  const currentQuery = queryHistory.getQuery();
  let defaultKeyword = '';
  if (currentQuery.defaultShopKeyword) {
    defaultKeyword = currentQuery.defaultShopKeyword;
  }
  return (
    <div className="filter-item__field">
      <Field
        label={props.label || '销售渠道：'}
        name="shopKdtIds"
        component={ShopSelect}
        type="rc"
        defaultKeyword={defaultKeyword}
        {...props}
      />
    </div>
  );
};

export default MultipleShopSelect;
