// 弹窗上的商品列表(暂时就只有接单拒单有)
import * as React from 'react';
import { Grid } from 'zent';
import cx from 'classnames';
import { divGoodsNum } from 'common/helper';
import style from './style.scss';

const columns = [
  {
    title: '商品',
    name: 'title'
  },
  {
    title: '数量',
    bodyRender: ({ num, unit }: { num: number; unit: string }) =>
      num ? `${divGoodsNum(num)}${unit}` : '-'
  }
];

interface IGoodsList {
  className: string;
  data: unknown[];
}

function DialogGoodsList(props: IGoodsList) {
  const { data, className } = props;

  return (
    <Grid
      className={cx(style['goods-list'], className)}
      columns={columns}
      rowKey="itemIdStr"
      datasets={data}
    />
  );
}

export default DialogGoodsList;
