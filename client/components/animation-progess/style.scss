@import '~shared/style';

:local(.progess-wrapper) {
  height: 5px;
  width: 100%;
  background: $background-color-base;

  .process-base {
    height: 5px;
    background: $color-link;
  }

  .progess {
    width: 0%;
    animation-name: progess;
    animation-duration: 5s;
    animation-fill-mode: forwards;
    animation-timing-function: ease;
  }

  .progess-full {
    width: 100%;
  }
}

@keyframes progess {
  from {
    width: 0%;
  }

  to {
    width: 100%;
  }
}
