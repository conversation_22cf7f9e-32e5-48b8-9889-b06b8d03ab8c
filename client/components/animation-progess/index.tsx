import * as React from 'react';
import cx from 'classnames';
import style from './style.scss';

export default class AnimProgess extends React.Component<{ onProgess: (percent: number) => void }> {
  static defaultProps = {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onProgess: () => {}
  };

  wrapperIns = React.createRef<HTMLDivElement>();

  barIns = React.createRef<HTMLDivElement>();

  interval: number;

  componentDidMount() {
    const computedBarStyle = getComputedStyle(this.barIns.current!);
    const computedWrapperStyle = getComputedStyle(this.wrapperIns.current!);
    this.interval = setInterval(() => {
      if (this.barIns && this.wrapperIns) {
        const [total, current] = [
          Number.parseInt(computedWrapperStyle.width, 10),
          Number.parseInt(computedBarStyle.width, 10)
        ];
        const percent = Math.trunc((100 * current) / total);
        if (percent >= 100) {
          clearInterval(this.interval);
        }
        this.props.onProgess(percent);
      }
    }, 200);
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  render() {
    return (
      <div ref={this.wrapperIns} className={style['progess-wrapper']}>
        <div
          className={cx({
            'process-base': true,
            progess: true
          })}
          ref={this.barIns}
        />
      </div>
    );
  }
}
