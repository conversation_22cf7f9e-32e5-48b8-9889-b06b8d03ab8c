import React, { useEffect, useState } from 'react';
import { isRetailChainStore, isWscChainStore } from '@youzan/utils-shop';
import ajax from 'zan-pc-ajax';

const LOCALSTORAGE_KEY = 'electron-way-bill-cache';
const maxShowCount = 4;
const isNotChainStore = !isRetailChainStore && !isWscChainStore;

import './index.scss';

function getFacilitatorList() {
  return ajax(`/v4/ump/api/logisticsService/facilitators`);
}

const ElectronWayBanner = ({ isControl = false }) => {
  const [isShow, setIsShow] = useState(true);
  const [disCount, setDisCount] = useState(0);
  const [disCountAmount, setDisCountAmount] = useState(0);
  const [currentExpressId, setCurrentExpressId] = useState(0);

  useEffect(() => {
    async function getLocalStorage() {
      // 前三次进入显示
      const key = window._global?.kdtId || '';
      if (key) {
        const cache = JSON.parse(window.localStorage.getItem(LOCALSTORAGE_KEY) || '{}');
        let { count = 0, isShow = true } = cache[key] || {};
        if (isShow && count < maxShowCount) {
          count += 1;
        }
        if (count === maxShowCount) {
          isShow = false;
        }
        if (isShow) {
          try {
            const res = await getFacilitatorList();
            // 筛选出有设置折扣的物流渠道,说明是官方推荐
            const data = res.filter(
              item => Object.keys(item.logisticsRecommendInfo || {}).length > 0
            );
            const latticePointDetailModelList = data
              .map(item => item.latticePointDetailModel)
              .filter(item => item?.length > 0);
            const logisticsRecommendInfoList = data.map(item => item.logisticsRecommendInfo);
            const discountList = logisticsRecommendInfoList
              .map(item => +item.discount)
              .sort((pre, next) => pre - next);
            const discountAmountList = logisticsRecommendInfoList
              .map(item => +item.discountAmount)
              .sort((pre, next) => next - pre);
            setDisCount(discountList[0]);
            setDisCountAmount(discountAmountList[0]);
            // 外部受控, 需要判断
            if (isControl) {
              // 任一一个有值，说明已经开通服务了，那就可以消失了
              isShow = !(latticePointDetailModelList.length > 0);
              setCurrentExpressId(data[0]?.expressId);
            }
          } catch (err) {}
        }
        setIsShow(isShow);
        window.localStorage.setItem(
          LOCALSTORAGE_KEY,
          JSON.stringify({ ...cache, [key]: { count, isShow } })
        );
      }
    }
    getLocalStorage();
  }, []);

  const handleToDetail = url => {
    window.open(url);
    const key = window._global?.kdtId || '';
    const cache = JSON.parse(window.localStorage.getItem(LOCALSTORAGE_KEY) || '{}');
    if (key) {
      window.localStorage.setItem(
        LOCALSTORAGE_KEY,
        JSON.stringify({ ...cache, [key]: { isShow: false, count: maxShowCount } })
      );
    }
  };

  return (
    <>
      {isNotChainStore && isShow && disCount && disCountAmount && (
        <div className="electron-way-bill__tips">
          <div>
            使用官方寄件发货，享受有赞平台专享折扣，最低可享
            <span className="electron-way-bill__tips-price">{disCount}</span>折，每单最高可省
            <span className="electron-way-bill__tips-price">{disCountAmount}</span>元。
            <span
              className="electron-way-bill__tips-to-detail"
              onClick={() =>
                handleToDetail('https://help.youzan.com/displaylist/detail_4_4-2-13117')
              }
            >
              查看详情
            </span>
          </div>
          {isControl && (
            <span
              className="electron-way-bill__tips-action"
              onClick={() => handleToDetail(`/v4/ump/logisticsService/serviceManage?code=${currentExpressId}`)}
            >
              去发货
            </span>
          )}
        </div>
      )}
    </>
  );
};

export default ElectronWayBanner;
