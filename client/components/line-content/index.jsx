import React from 'react';
import cx from 'classnames';
import style from './style.scss';

function LineContent({ text, label, right, className }) {
  if (!text && !right) {
    return null;
  }

  return (
    <div className={cx(style['line-content'], className)}>
      {text && (
        <div className="left">
          <span className="blacker">{label}：</span>
          <span className="gray">{text}</span>
        </div>
      )}
      {right}
    </div>
  );
}

export default LineContent;
