import React, { Component, Fragment } from 'react';
import { Di<PERSON>, Button } from 'zent';
import PropTypes from 'prop-types';
import { Button as SamButton } from '@youzan/sam-components';

import DialogContent from './dialog-content';
import style from './style.scss';

export default class ExportDialog extends Component {
  state = { visible: false };

  changeHandle = visible => {
    this.setState({ visible });
  };

  clickButton = () => {
    if (this.props.getDisplayInfo()) {
      this.changeHandle(true);
    }
  };

  render() {
    const { visible } = this.state;
    return (
      <>
        <SamButton className={style['export-button']} name="导出" onClick={this.clickButton} />
        {visible && (
          <Dialog
            visible={visible}
            className={style['export-order-dialog']}
            closeBtn={false}
            style={{ width: '830px' }}
            maskClosable
            onClose={() => {
              // 点击遮罩关闭弹窗
              this.changeHandle(false);
            }}
          >
            <DialogContent
              closeHandle={this.changeHandle}
              displayInfo={this.props.getDisplayInfo()}
              {...this.props}
            />
          </Dialog>
        )}
      </>
    );
  }
}

ExportDialog.propTypes = { getDisplayInfo: PropTypes.func.isRequired };
