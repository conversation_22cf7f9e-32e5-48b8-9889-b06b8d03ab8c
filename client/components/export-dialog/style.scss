@import '~shared/style';

%export-item {
  display: flex;

  .export-label {
    display: inline-block;
    width: 80px;
    font-size: 14px;

    &.label-type {
      vertical-align: middle;
      line-height: 30px;
    }
  }
}

:local(.export-button) {
  margin-left: 16px !important;
}

:local(.export-order-dialog) {
  .zent-dialog-r-body {
    padding-top: 0;
    color: $color-text-primary;
  }

  .title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px solid $border-color-base;
    margin-bottom: 20px;

    .close {
      display: inline-block;
      color: $color-text-secondary;
      font-size: 25px;
      line-height: 20px;
      text-align: right;
      vertical-align: middle;
      cursor: pointer;
      border: none;
      background-color: $color-white;
      outline: none;
      -webkit-user-select: none;
      -moz-user-select: none;
      user-select: none;
      padding: 0 10px;
      margin-right: -10px;
      margin-bottom: -10px;
      padding-bottom: 10px;
    }
  }

  .export-alert {
    span {
      color: $color-alert;
    }

    margin-bottom: 20px;
  }

  .query-item {
    @extend %export-item;

    font-size: 12px;
    margin-bottom: 20px;

    label {
      padding-top: 15px;
    }

    .content {
      background-color: $background-color-base;
      border-radius: 2px;
      flex: 1;
      padding: 15px 15px 0;

      label {
        width: 60px;
      }

      .content-item {
        margin-bottom: 15px;
      }

      &__col-group {
        position: relative;
        overflow: hidden;

        .zent-col {
          float: left;
        }
      }
    }
  }

  .export-type {
    @extend %export-item;

    font-size: 14px;
    margin-bottom: 20px;

    .type-text {
      margin-right: 5px;
      font-size: 14px;

      span {
        color: $color-text-secondary;
      }
    }

    .explain {
      margin-left: 75px;
      font-size: 12px;
      margin-top: 12px;
      color: $color-text-secondary;
    }
  }

  .export-field {
    @extend %export-item;

    font-size: 12px;
    margin-bottom: 50px;

    .config-content {
      flex: 1;

      .config {
        color: $color-link;
        display: inline-block;
        cursor: pointer;
        font-size: 14px;
        margin-bottom: 15px;
      }

      .disable {
        color: $color-text-secondary;
        cursor: wait;
        pointer-events: none;
      }

      .content {
        border-radius: 2px;
        background-color: $background-color-base;
        padding: 15px 0 0 15px;
      }

      .hidden {
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .transExpand {
        transition: height 200ms;
        overflow: hidden;
      }

      li {
        display: inline-block;
        line-height: 20px;
        border: 1px solid $color-b1;
        border-radius: 2px;
        color: $color-link;
        background-color: $color-b1;
        margin: 0 15px 15px 0;
        padding: 4px 12px;
      }

      .place-holder {
        .zent-placeholder-shape {
          height: 30px;
          margin: 0 15px 0 0;
        }
      }

      .expand {
        padding-bottom: 15px;
        padding-top: 15px;

        .expand-span {
          cursor: pointer;
        }
      }

      .isExpand {
        padding-top: 0;
      }

      .triangleDown {
        transform: rotate(180deg);
      }

      .triangleUp {
        display: inline-block;
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 6px solid;
        margin-left: 8px;
        transition: transform 250ms 150ms;
      }
    }
  }

  .sel-field-checkbox {
    position: relative;

    @extend %export-item;

    font-size: 14px;
    padding-top: 5px;
    margin-bottom: 15px;

    .text {
      font-size: 14px;
      padding-bottom: 3px;
    }

    .search-input {
      color: $color-text-secondary;
      width: 130px;
      height: 30px;
      border-radius: 2px;
      position: absolute;
      right: 0;
      top: -5px;
    }
  }

  .field-list {
    margin-top: 15px;

    .field-box {
      border-radius: 2px;
      border: 1px solid $border-color-base;
      padding: 0 15px;
      margin-left: 80px;
      margin-bottom: 30px;
      height: 298px;
      overflow-y: auto;

      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: $border-color-dark-base;
      }

      span {
        font-weight: 500;
        padding-top: 15px;
        padding-bottom: 15px;
        display: inline-block;
      }

      .bb {
        border-bottom: 1px solid $border-color-base;
      }

      ul {
        color: $color-text-light-primary;
        padding-bottom: 15px;
        margin-top: -20px;

        li {
          display: inline-block;
          margin-right: 20px;
          margin-top: 20px;
        }

        a {
          cursor: pointer;
          color: $color-text-light-primary;
        }

        .sel {
          color: $color-text-disable;
        }

        .disable-del {
          color: $color-text-disable;
          pointer-events: none;
          cursor: text;
        }
      }
    }
  }

  .selected-field {
    @extend %export-item;

    margin-bottom: 50px;

    ul {
      flex: 1;

      li {
        display: inline-block;
        line-height: 20px;
        border: 1px solid $color-b1;
        border-radius: 2px;
        color: $color-link;
        background-color: $color-b1;
        margin: 0 15px 15px 0;
        padding: 4px 0 5px 5px;

        span {
          padding-left: 5px;
          padding-right: 5px;
          font-size: 16px;
          font-weight: 550;
          cursor: pointer;
        }
      }

      .disable-del {
        opacity: 0.6;
        padding-right: 5px;
      }
    }
  }

  .export-button {
    display: flex;
    justify-content: flex-end;

    .zent-btn-primary {
      width: 94px;
    }
  }
}
