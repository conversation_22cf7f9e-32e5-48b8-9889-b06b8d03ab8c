import React, { Component } from 'react';
import { intersectionWith, findIndex, map, filter } from 'lodash';
import { Field, getFormValues, connect } from '@youzan/retail-form';
import { Button, Notify } from 'zent';
import * as api from 'route/query/api';

import SearchInputField from './search-input';
import { FieldListField } from './field-list';
import { SelectedField } from './selected-field';
import './style.scss';

@connect(state => ({ allValues: getFormValues('order-export')(state) }))
class CustomField extends Component {
  state = { loading: false };

  // 获取自定义字段和标准字段的交集
  getIntersectionFields = () => {
    const { customFields, dimension } = this.props;
    const { order, goods } = customFields[dimension];
    const { normalFields } = this.props;
    return {
      order: intersectionWith(order, normalFields, (x, y) => x.name === y.name),
      goods: intersectionWith(goods, normalFields, (x, y) => x.name === y.name)
    };
  };

  intersectionFields = this.getIntersectionFields(); // 全部字段和标准字段 按类型取的交集

  componentDidMount = () => {
    this.initFieldList();
  };

  uniqWithName = (fieldListFieldObj, type, flag) => {
    const newFieldList = {};
    type.forEach(item => {
      const intersectionFields = this.intersectionFields[item];
      newFieldList[item] = fieldListFieldObj[item].map(val => {
        const value = val;
        if (
          value.category !== 'required' &&
          findIndex(intersectionFields, ['name', value.name]) !== -1
        ) {
          value.selectedFlag = flag;
        }
        return value;
      });
    });
    return newFieldList;
  };

  initFieldList = () => {
    const { customFields, dimension, change } = this.props;
    const { order, goods, biz } = customFields[dimension];
    change(
      'fieldListField',
      JSON.stringify({
        // 数据较为复杂，序列化一下，防止后面change失败
        order,
        goods,
        biz
      })
    );
  };

  compareField = () => {
    const {
      allValues: { fieldListField }
    } = this.props;
    const fieldListFieldObj = JSON.parse(fieldListField);
    const interField = this.intersectionFields;

    const intersectionOrderVal = intersectionWith(
      fieldListFieldObj.order,
      interField.order,
      (x, y) => x.name === y.name && x.selectedFlag !== false // selectedFlag为true或undefined 选中或必须字段
    );
    const intersectionGoodsVal = intersectionWith(
      fieldListFieldObj.goods,
      interField.goods,
      (x, y) => x.name === y.name && x.selectedFlag !== false
    );
    if (
      [...intersectionOrderVal, ...intersectionGoodsVal].length ===
      [...interField.order, ...interField.goods].length
    ) {
      return true;
    }
    return false;
  };

  changeCheckBox = ({ target: { checked } }) => {
    const {
      allValues: { fieldListField },
      change
    } = this.props;
    let newFieldList;
    const fieldListFieldObj = JSON.parse(fieldListField);
    if (checked) {
      newFieldList = this.uniqWithName(fieldListFieldObj, ['order', 'goods'], true);
    } else {
      newFieldList = this.uniqWithName(fieldListFieldObj, ['order', 'goods'], false);
    }
    this.changeMake = true;
    change('fieldListField', JSON.stringify({ ...fieldListFieldObj, ...newFieldList }));
  };

  // 选择字段
  selField = () => (
    <div className="sel-field-checkbox">
      <label className="export-label">选择字段：</label>
      {/* TODO:逻辑有问题，暂时不上 */}
      {/* {fieldListField && (
          <Checkbox
            onChange={this.changeCheckBox}
            value="normal"
            checked={this.compareField()}
            className="text"
          >
            {`一键选中标准报表中的${dimension === 'order'?'订单':'商品'}报表字段`}
          </Checkbox>
        )} */}
      {this.searchField()}
    </div>
    // </Field>
  );

  searchField = () => (
    <Field name="searchField" component={SearchInputField} className="search-input" prefix="" />
  );

  fieldsList = () => (
    <Field
      name="fieldListField"
      component={FieldListField}
      className="field-list"
      searchField={this.props.allValues.searchField}
      dimension={this.props.dimension}
      prefix=""
    />
  );

  selectedField = () => (
    <SelectedField
      val={this.props.allValues.fieldListField}
      change={this.props.change}
      changeKey="fieldListField"
      prefix=""
    />
  );

  saveFetchReportFields = () => {
    const {
      dimension,
      allValues: { fieldListField },
      change,
      handleClose
    } = this.props;
    const data = JSON.parse(fieldListField);
    const filterData = filter(
      [...data.order, ...data.goods, ...data.biz],
      item => item.selectedFlag || item.category === 'required'
    );
    const selectedFields = map(filterData, 'name');
    this.setState({ loading: true });
    api
      .saveFetchReportFields({
        dimension,
        selectedFields
      })
      .then(() => {
        Notify.success('保存成功');
        change('saveFieldList', {
          [dimension]: { ...data, selFields: filterData }
        });
        // hank unmount cpn can't setSate
        setTimeout(() => {
          handleClose();
        }, 0);
      })
      .catch(err => {
        Notify.error(err.msg || '保存失败');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  renderActions = () => (
    <Field prefix="" name="saveFieldList">
      <div className="export-button">
        <Button type="primary" loading={this.state.loading} onClick={this.saveFetchReportFields}>
          保存
        </Button>
      </div>
    </Field>
  );

  render() {
    return (
      <>
        {this.selField()}
        {this.fieldsList()}
        {this.selectedField()}
        {this.renderActions()}
      </>
    );
  }
}

export default CustomField;
