// 报表类型
import React from 'react';
import { Field } from '@youzan/retail-form';
import { Radio, Tag } from 'zent';
import '../style.scss';

export default props => (
  <Field className="export-type" name="type" component="RadioGroupField">
    <label className="export-label label-type">报表类型：</label>
    <Radio value="normal">
      <span className="type-text">标准报表</span>
      <Tag outline style={{ color: '#09BB07', borderColor: '#09BB07' }}>
        推荐
      </Tag>
    </Radio>
    <Radio value="custom">
      <span className="type-text">自定义报表</span>
      <Tag outline style={{ color: '#FF0000', borderColor: '#FF0000' }}>
        新
      </Tag>
    </Radio>
    {props.type === 'normal' && (
      <div className="explain">包含两个CSV格式的报表：订单报表、商品报表</div>
    )}
  </Field>
);
