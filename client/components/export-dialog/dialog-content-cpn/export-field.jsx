// 报表字段
import React from 'react';
import { get } from 'lodash';
import cx from 'classnames';
import { Placeholder } from 'zent';
import '../style.scss';

// const fieldUlRef = React.createRef();

const ExportField = ({ state, allValues, goCustomPage, showExpandFuc }, ref) => {
  const { normalFields, customFields, showExpand, isNormalExpand, isCustomExpand } = state;
  const dashSegments = [
    [20, 20, 20, 20, 20],
    [20, 20, 20, 20, 20]
  ];
  let queryFields;
  let isExpand;
  let expandHeight;

  if (allValues.type === 'normal') {
    queryFields = normalFields;
    isExpand = isNormalExpand;
  } else {
    queryFields = get(customFields, `${allValues.dimension}.selFields`);
    isExpand = isCustomExpand;
  }

  if (showExpand) {
    if (isExpand) {
      expandHeight = get(ref, 'current.scrollHeight') + 15 || 'auto';
    } else {
      expandHeight = 75;
    }
  }

  return (
    <div className="export-field">
      <label className="export-label">报表字段：</label>
      <div className="config-content">
        {allValues.type === 'custom' && (
          <span
            className={cx('config', {
              disable: !get(customFields, allValues.dimension) || !normalFields
            })}
            onClick={goCustomPage}
          >
            配置报表字段
          </span>
        )}
        <div className="content">
          {queryFields ? (
            <>
              <ul
                ref={ref}
                className={cx({ hidden: !isExpand, transExpand: showExpand })}
                style={{ height: expandHeight }}
              >
                {queryFields.map(item => (
                  <li key={item.name || item.title}>{item.title}</li>
                ))}
              </ul>
              {showExpand && (
                <div className={cx('expand', { isExpand })}>
                  <span className="expand-span" onClick={showExpandFuc}>
                    {`${isExpand ? '收起' : '展开'}(共${queryFields.length}个)`}
                    <span className={cx('triangleUp', { triangleDown: isExpand })} />
                  </span>
                </div>
              )}
            </>
          ) : (
            <Placeholder.TextBlock
              rows={2}
              className="place-holder"
              dashSegments={dashSegments}
              style={{ paddingBottom: '15px' }}
              lineSpacing="15px"
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default React.forwardRef(ExportField);
