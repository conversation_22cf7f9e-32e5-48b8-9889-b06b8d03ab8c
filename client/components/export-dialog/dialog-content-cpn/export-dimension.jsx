// 导出维度
import React from 'react';
import { Field } from '@youzan/retail-form';
import { Radio } from 'zent';
import '../style.scss';

export default () => (
  <Field className="export-type" name="dimension" component="RadioGroupField">
    <label className="export-label label-type">导出维度：</label>
    <Radio value="order">
      <span className="type-text">
        以订单维度导出
        <span> (报表中每个订单编号一行)</span>
      </span>
    </Radio>
    <Radio value="goods">
      <span className="type-text">
        以商品维度导出
        <span> (报表中每个商品名称一行)</span>
      </span>
    </Radio>
  </Field>
);
