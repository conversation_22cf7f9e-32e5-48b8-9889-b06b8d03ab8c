import React from 'react';
import { LayoutRow as Row, LayoutGrid as Grid } from 'zent';
import { find, get, invert } from 'lodash';
import { formatDatetime } from '@youzan/retail-utils';
import { refundStatusTypeTextMap } from '@youzan/zan-hasaki';
import {
  channelTypeTextMap,
  orderPayWayTypeTextMap,
  orderSourceTypeTextMap,
  expressTypeTextMap,
  marketingTypeTextMap
} from 'common/constants/common';

import {
  isBranchStore,
  isRetailOfflineBranchStore,
  isRetailSingleStore,
  isPHShop
} from '@youzan/utils-shop';

import {
  orderTypeText,
  orderTypeValue,
  hotelOrderStateText,
  commonOrderStateText,
  commonOrderStateValue,
  hotelOrderStateValue,
  rxOrderStateText,
  rxOrderStateValue
} from 'common/constants/models';
import { ORDER_TYPE } from 'common/constants/order';
import { starLabel, searchLabelMap } from 'common/constants/select-text';
import { orderTypeHelper } from 'common/model-helper';

import { getShopBaseInfo } from '../api/index';
import { DisplayCol } from '../display-filed';
import '../style.scss';

const ShowQueryItem = props => {
  const {
    displayInfo: {
      startTime,
      endTime,
      planExpressTime,
      deliveryStartTime,
      deliveryEndTime,
      expressType,
      orderType,
      searchLabel,
      searchField,
      orderState,
      hasStar,
      feedbackState,
      buyWay,
      saleWay,
      goodsTitle,
      cashierName,
      storeName,
      saleWayShopName,
      salesName,
      orderSource,
      marketingType,
      shopKdtIds,
      tableIdName
    },
    showConfig: {
      isShowMultiStore,
      isShowCashier,
      isShowSales,
      saleStoreLabel,
      isShowTableId,
      hasOrderState,
      hasOrderType
    }
  } = props;
  // 同城配送达时间
  const deliveryTimeStr =
    deliveryStartTime && deliveryEndTime
      ? `${formatDatetime(deliveryStartTime)} 至 ${formatDatetime(deliveryEndTime)}`
      : '-';
  // 对于订单状态的文案 需要对酒店订单和普通订单做区分判断

  let orderStateText = commonOrderStateText[invert(commonOrderStateValue)[orderState]];

  if (orderTypeHelper.isHotel(orderType)) {
    orderStateText = hotelOrderStateText[invert(hotelOrderStateValue)[orderState]];
  }

  // 处方单类型订单
  if (orderType === ORDER_TYPE.MEDICAL_ORDER.value) {
    orderStateText = rxOrderStateText[invert(rxOrderStateValue)[orderState]];
  }

  const displaySaleWay =
    isRetailSingleStore || isPHShop ? channelTypeTextMap.get(saleWay) : saleWayShopName;

  const [realStoreName, setRealStoreName] = React.useState('');
  React.useEffect(() => {
    if (shopKdtIds && shopKdtIds.length === 1) {
      if (shopKdtIds[0] === -1) {
        setRealStoreName('全部');
      } else {
        getShopBaseInfo(shopKdtIds[0]).then(res => {
          setRealStoreName(res.shopName);
        });
      }
    } else if (shopKdtIds && shopKdtIds.length > 1) {
      setRealStoreName(`已选择${shopKdtIds.length}家店铺`);
    }
  }, [shopKdtIds]);

  return (
    <Grid className="content">
      <Row>
        <DisplayCol
          span={12}
          label="下单时间"
          value={`${formatDatetime(startTime)} 至 ${formatDatetime(endTime)}`}
        />
        <DisplayCol span={12} label={searchLabelMap.ALL[searchLabel]} value={searchField} />
      </Row>
      <div className="content__col-group">
        <DisplayCol
          show={shopKdtIds && shopKdtIds.length}
          span={6}
          label="所属店铺"
          value={realStoreName || storeName}
        />
        <DisplayCol
          show={!isBranchStore}
          span={6}
          label={saleStoreLabel || '销售渠道'}
          value={displaySaleWay}
        />
        {hasOrderType ? (
          <DisplayCol
            span={6}
            label="订单类型"
            value={orderTypeText[invert(orderTypeValue)[orderType]]}
          />
        ) : null}
        {hasOrderState ? <DisplayCol span={6} label="订单状态" value={orderStateText} /> : null}
        <DisplayCol span={6} label="退款状态" value={refundStatusTypeTextMap.get(feedbackState)} />
        <DisplayCol span={6} label="配送方式" value={expressTypeTextMap.get(expressType)} />
        <DisplayCol span={6} label="支付方式" value={orderPayWayTypeTextMap.get(buyWay)} />
        <DisplayCol
          span={6}
          label="是否加星"
          value={get(find(starLabel, { value: hasStar }), 'text')}
        />
        <DisplayCol
          show={!isRetailOfflineBranchStore}
          span={6}
          label="推广方式"
          value={marketingTypeTextMap.get(marketingType)}
        />
        <DisplayCol span={6} label="订单来源" value={orderSourceTypeTextMap.get(orderSource)} />
        <DisplayCol span={6} label="商品名称" value={goodsTitle} defaultValue="-" />
        <DisplayCol show={isShowTableId} span={6} label="桌号" value={tableIdName} />
        <DisplayCol show={isShowCashier} span={6} label="收银员" value={cashierName} />
        <DisplayCol show={isShowSales} span={6} label="导购员" value={salesName} />
        <DisplayCol show={isShowMultiStore} span={6} label="归属网点" value={storeName} />
        <DisplayCol span={12} label="周期购送达时间" value={formatDatetime(planExpressTime)} />
        <DisplayCol span={12} label="同城配送达时间" value={deliveryTimeStr} />
      </div>
    </Grid>
  );
};

export default ShowQueryItem;
