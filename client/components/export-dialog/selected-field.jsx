import React from 'react';
import cx from 'classnames';

export const SelectedField = ({ val = '', change, changeKey }) => {
  if (!val) return null;
  const data = JSON.parse(val);
  const requiredFields = [...data.order, ...data.goods].filter(
    item => item.category === 'required'
  );
  const selData = requiredFields.concat(
    [...data.order, ...data.goods, ...data.biz].filter(item => item.category !== 'required')
  );

  const changeField = param => {
    const type = param.category;
    const newData = data[type].map(item => {
      const newItem = item;
      if (newItem.name === param.name) {
        newItem.selectedFlag = false;
      }
      return newItem;
    });
    change(changeKey, JSON.stringify({ ...data, ...newData }));
  };

  return (
    <div className="selected-field">
      <label className="export-label">已选字段：</label>
      <ul>
        {selData.map(item => {
          if (item.selectedFlag !== false) {
            return (
              <li key={item.name} className={cx({ 'disable-del': item.category === 'required' })}>
                {item.title}
                {item.category !== 'required' && (
                  <span
                    onClick={() => {
                      changeField(item);
                    }}
                  >
                    ×
                  </span>
                )}
              </li>
            );
          }
          return null;
        })}
      </ul>
    </div>
  );
};
