import * as React from 'react';
import { LayoutCol } from 'zent';

interface IDisplayField {
  label: string;
  value: string | number;
  defaultValue?: string;
}

interface IDisplayCol extends IDisplayField {
  span: number;
  show?: boolean;
}

export default function DisplayField(props: IDisplayField) {
  const { label, value, defaultValue } = props;

  if (!value || value === '-') return defaultValue || null;

  return (
    <div className="content-item">
      <label>{`${label}：`}</label>
      <span>{value}</span>
    </div>
  );
}

export const DisplayCol = ({ label, value, span, show = true, defaultValue }: IDisplayCol) => {
  if (!show) return null;

  if ((value && value !== '-') || defaultValue) {
    return (
      <LayoutCol span={span}>
        <div className="content-item">
          <label>{`${label}：`}</label>
          <span>{value || defaultValue}</span>
        </div>
      </LayoutCol>
    );
  }

  return null;
};
