import React from 'react';
import cx from 'classnames';
import { get } from 'lodash';

export const FieldListField = ({ input, searchField, dimension }) => {
  const { value: val, onChange } = input;

  let props = null;
  if (typeof val !== 'string') return null; // 输入val值必须是JSON字符串
  if (val) props = JSON.parse(val);

  const changeItem = (item, index, key) => {
    const newProps = [...props[key]];
    newProps[index].selectedFlag = !newProps[index].selectedFlag;
    onChange(JSON.stringify({ ...props, [key]: newProps })); // 数据较为复杂，序列化一下，防止后面change失败
  };

  const handleLi = function(item, index) {
    if (item.title.toString().includes(searchField)) {
      return (
        <li key={item.name}>
          <a
            className={cx(
              { sel: item.selectedFlag },
              { 'disable-del': item.category === 'required' }
            )}
            onClick={() => {
              changeItem(item, index, this.key); // eslint-disable-line
            }}
          >
            {item.title}
          </a>
        </li>
      );
    }
    return null;
  };

  return (
    <div className="field-box">
      <div>
        <span>订单相关字段</span>
        <ul className="bb">{get(props, 'order', []).map(handleLi, { key: 'order' })}</ul>
      </div>
      {dimension !== 'order' && (
        <div>
          <span>商品相关字段</span>
          <ul className="bb">{get(props, 'goods', []).map(handleLi, { key: 'goods' })}</ul>
        </div>
      )}
      <div>
        <span>业务相关字段</span>
        <ul>{get(props, 'biz', []).map(handleLi, { key: 'biz' })}</ul>
      </div>
    </div>
  );
};
