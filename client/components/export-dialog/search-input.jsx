import React, { Component } from 'react';
import { Input } from 'zent';
import { trim } from 'lodash';

export default class SearchInputField extends Component {
  state = { value: '' };

  valideData = val => {
    if (val.length === 0) {
      this.props.input.onChange('');
    }
    if (val.length > 12) {
      this.setState({ value: val.slice(0, 12) });
      return;
    }
    this.setState({ value: val });
  };

  render() {
    const { input } = this.props;
    return (
      <Input
        icon="search"
        placeholder="搜索字段"
        value={this.state.value}
        onChange={evt => this.valideData(trim(evt.target.value))}
        onPressEnter={evt => input.onChange(evt.target.value)}
      />
    );
  }
}
