import React, { Component } from 'react';
import { <PERSON><PERSON>, Button, Notify, Sweetalert } from 'zent';
import Form, { Actions, createForm, connect, getFormValues } from '@youzan/retail-form';
import * as api from 'route/query/api';
import { get, pick } from 'lodash';
import { global } from '@youzan/retail-utils';

import CustomFieldPage from './custom-field';
import Title from './dialog-content-cpn/export-title';
import QueryItem from './dialog-content-cpn/query-item';
import ExportType from './dialog-content-cpn/export-type';
import ExportField from './dialog-content-cpn/export-field';
import ExportDimension from './dialog-content-cpn/export-dimension';
import './style.scss';

const { USER_INFO } = global;
const DIALOG_CONTENT = 1;
const DIALOG_CUSTOM = 2;
const TWO_LINE_HEIGHT = 90;
const fieldUlRef = React.createRef();

@connect(state => ({ allValues: getFormValues('order-export')(state) }))
@createForm({ form: 'order-export' })
class ExportDialogContent extends Component {
  static defaultProps = {
    exportLink: '/v2/order/exportList',
    allValues: {}
  };

  state = {
    showDialogPage: DIALOG_CONTENT,
    normalFields: null,
    customFields: undefined,
    showExpand: false,
    isNormalExpand: false, // 标准报表字段是否展开
    isCustomExpand: false, // 自定义报表字段是否展开
    loading: false
  };

  componentDidMount = () => {
    this.init();
  };

  init = () => {
    const { initialize } = this.props;
    initialize({
      type: 'normal',
      dimension: 'order',
      searchField: ''
    });
  };

  fetchReportFields = callback => {
    const { normalFields, customFields = {} } = this.state;
    const { type, dimension: ds } = this.props.allValues;
    let queryType;
    let dimension = ds;
    if (type === 'normal') {
      if (normalFields) {
        callback();
        return;
      }
      dimension = null;
      queryType = 0;
    }
    if (type === 'custom') {
      if (customFields[ds]) {
        callback();
        return;
      }
      queryType = 1;
    }
    api
      .fetchReportFields({ queryType, dimension })
      .then(data => {
        const {
          requiredOrderFields,
          requiredGoodsFields,
          orderFields,
          goodsFields,
          bizFields,
          standardFields
        } = data;
        const cf = {};
        if (type === 'normal') {
          this.setState({ normalFields: standardFields }, callback);
        }
        if (type === 'custom') {
          const selData = [
            ...requiredOrderFields,
            ...requiredGoodsFields,
            ...orderFields,
            ...goodsFields,
            ...bizFields
          ];
          const newData = {
            order: [...requiredOrderFields, ...orderFields],
            goods: [...requiredGoodsFields, ...goodsFields],
            biz: bizFields,
            selFields: this.getSelFields(selData)
          };
          cf[dimension] = newData;
          this.setState({ customFields: { ...customFields, ...cf } }, callback);
        }
      })
      .catch(err => {
        Notify.error(err.msg || '查询配置字段数据失败！');
      });
  };

  getSelFields = allFields => {
    if (!allFields) {
      return;
    }
    return allFields.filter(item => item.selectedFlag || item.category === 'required');
  };

  componentDidUpdate = (preProps, prevState) => {
    const { type, dimension } = this.props.allValues;
    const { showDialogPage } = this.state;

    if (
      preProps.allValues.type !== type ||
      preProps.allValues.dimension !== dimension ||
      (prevState.showDialogPage !== showDialogPage && showDialogPage === DIALOG_CONTENT)
    ) {
      this.setState(
        {
          isNormalExpand: false,
          isCustomExpand: false,
          showExpand: false
        },
        () => {
          this.fetchReportFields(() => {
            const { showExpand } = this.state;
            const domClientHeight = get(fieldUlRef, 'current.clientHeight');
            const domScrollHeight = get(fieldUlRef, 'current.scrollHeight');
            if (domClientHeight) {
              // 报表字段超出两行显示‘展开’
              if (
                !showExpand &&
                (domClientHeight < domScrollHeight || domClientHeight > TWO_LINE_HEIGHT)
              ) {
                this.setState({ showExpand: true });
              }
              if (
                showExpand &&
                domClientHeight === domScrollHeight &&
                domClientHeight <= TWO_LINE_HEIGHT
              ) {
                this.setState({ showExpand: false });
              }
            }
          });
        }
      );
    }
  };

  handleClose = () => {
    const { closeHandle } = this.props;
    const { showDialogPage, customFields } = this.state;
    const { dimension, saveFieldList } = this.props.allValues;
    if (showDialogPage === DIALOG_CONTENT) {
      closeHandle(false);
      return;
    }
    if (showDialogPage === DIALOG_CUSTOM) {
      let dimensionData = customFields[dimension];
      if (saveFieldList && saveFieldList[dimension]) {
        dimensionData = saveFieldList[dimension];
      }
      this.setState(prevState => ({
        showDialogPage: prevState.showDialogPage - 1,
        customFields: {
          ...customFields,
          [dimension]: dimensionData
        }
      }));
    }
  };

  showExpandFuc = () => {
    const { isNormalExpand, isCustomExpand } = this.state;
    const { type } = this.props.allValues;

    if (type === 'normal') {
      this.setState({ isNormalExpand: !isNormalExpand });
    } else if (type === 'custom') {
      this.setState({ isCustomExpand: !isCustomExpand });
    }
  };

  goCustomPage = () => {
    this.state.customFields &&
      this.setState(prevState => ({
        showDialogPage: prevState.showDialogPage + 1
      }));
  };

  renderFields() {
    const { showDialogPage, customFields, normalFields } = this.state;
    const {
      displayInfo,
      change,
      allValues: { type, dimension },
      showConfig
    } = this.props;
    let content, exportTitle;
    if (showDialogPage === DIALOG_CONTENT) {
      content = (
        <>
          <Alert
            description="为保证报表导出性能，只能导出距当前时间5分钟前的订单，已生成的报表将保留30天。"
            className="export-alert"
          />
          <QueryItem displayInfo={displayInfo} showConfig={showConfig} />
          {!showConfig.isCustomizedExportDisabled ? <ExportType type={type} /> : null}
          {type === 'custom' && <ExportDimension />}
          <ExportField
            goCustomPage={this.goCustomPage}
            showExpandFuc={this.showExpandFuc}
            state={this.state}
            allValues={this.props.allValues}
            ref={fieldUlRef}
          />
          {this.renderActions()}
        </>
      );
      exportTitle = '批量导出订单';
    }
    if (showDialogPage === DIALOG_CUSTOM) {
      content = (
        <CustomFieldPage
          change={change}
          dimension={dimension}
          customFields={customFields}
          normalFields={normalFields}
          handleClose={this.handleClose}
        />
      );
      exportTitle = '配置自定义字段';
    }
    return (
      <>
        <Title handleClose={this.handleClose}>{exportTitle}</Title>
        {content}
      </>
    );
  }

  renderActions = () => {
    const { handleSubmit } = this.props;
    return (
      <Actions className="export-button">
        <Button type="primary" outline href={this.props.exportLink} target="_blank">
          查看已生成报表
        </Button>
        <Button
          type="primary"
          htmlType="submit"
          loading={this.state.loading}
          onClick={handleSubmit(this.handleSearch)}
        >
          确定导出
        </Button>
      </Actions>
    );
  };

  customizedOrderExport = () => {
    const { account, nickName: nickname } = USER_INFO || {};
    const {
      allValues: { type, dimension },
      defaultValue,
      displayInfo
    } = this.props;
    let pickParams;
    if (defaultValue) {
      pickParams = pick(displayInfo, Object.keys(defaultValue));
    } else {
      pickParams = displayInfo;
    }
    if (displayInfo.saleWayShopName) {
      pickParams.shopName = displayInfo.saleWayShopName;
    }
    const params = {
      ...pickParams,
      account,
      nickname: nickname || account,
      exportType: type === 'custom' ? 1 : 0,
      dimension: type === 'custom' ? dimension : ''
    };

    this.setState({ loading: true });
    api
      .newExportList(params)
      .then(() => {
        this.viewExportModal();
      })
      .catch(err => {
        Notify.error(err.msg || '导出失败！');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  // 查看已生成订单
  viewExport = () => {
    window.open(this.props.exportLink, '_blank');
  };

  // 导出成功后弹窗
  viewExportModal = () => {
    Sweetalert.confirm({
      type: 'success',
      content: '导出报表成功。',
      title: '提示',
      confirmText: '查看报表',
      cancelText: '关闭',
      onConfirm: this.viewExport
    });
  };

  handleSearch = () => {
    this.customizedOrderExport();
  };

  render() {
    return <Form prefix="">{this.renderFields()}</Form>;
  }
}
export default ExportDialogContent;
