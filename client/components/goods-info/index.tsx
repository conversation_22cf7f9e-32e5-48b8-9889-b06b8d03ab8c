/* eslint-disable no-useless-concat */
/* eslint-disable @youzan/yz-retail/no-direct-arithmetic */
import * as React from 'react';
import { useCallback, useState } from 'react';
import { GoodsCell, BlankLink } from '@youzan/react-components';
import { ITagProps, Pop } from 'zent';
import { div, global } from '@youzan/retail-utils';
import { isPHShop } from '@youzan/utils-shop';
import cx from 'classnames';

import type { Contains } from 'definition/common';
import type { IOrderItemInfo, IOrderSourceInfo } from 'definition/order-info';

import { formatMoney } from 'common/fns/format';

import EllipsisTag from 'components/ellipsis-tag';

import './style.scss';

export interface IGoodsInfoProps extends Partial<IOrderItemInfo> {
  alias?: string;
  title: string;
  skuDesc?: string;
  skuCode?: string;
  icons?: IGoodsInfoIcon[];
  imgUrl?: string;

  className?: string;

  goodsSnapUrl?: string;
  /** 是否为组合商品 */
  isPackagedGoods?: boolean;
  /** 商品明细数据 */
  goodsBriefData?: Array<Contains<{ name: string }>>;
  /** 差价总额 */
  totalDiffAmount?: number;
  /** 差价详情 */
  priceDiffInfo?: any[];
  /** 规格条码 */
  goodsNo?: string;
  /** 是否展示规格条码 */
  isShowGoodsNo?: boolean;
  /** 是否为酒店订单 */
  isHotelOrder?: boolean;
  /** 酒店订单显示入住时间 */
  checkInTime?: string;
  /** 品牌 */
  brandName?: string;
  /** 显示商品品牌 */
  withBrand?: boolean;
  /** 订单物流状态 */
  orderExpressDesc?: string;
  /** 是否展示快照 */
  showSnapshot?: boolean;
  /** 是否使用网店商品 h5 链接 */
  withOnlineAliasLink?: boolean;

  comboDetail?: {
    comboGroupInfos: any[];
  };
  comboType?: number;
  // 订单来源信息
  sourceInfo?: IOrderSourceInfo;

  customizeExtraInfo?: any;
}

enum EnumComboType {
  'Free' = 1,
  'Stable' = 0
}

interface ICombo {
  addPrice: number;
  goodsId: number;
  goodsName: string;
  id: string;
  num: number;
  skuId: number;
  skuName: string;
  pros: { [propName: string]: any }[];
}

/** 套餐商品详情 - 最多显示行数 */
const ComboDetailShowCount = 1;

/** 格式化加价 */
export const formatAddPrice = (price: number) => {
  if (!price) {
    return '';
  }
  // 支持负数
  return price > 0 ? `¥${formatMoney(div(price, 100))}` : `-¥${formatMoney(div(-price, 100))}`;
};

/*
 * goodsNo / skuCode 分别代表 规格条码 / 商品条码
 * 两者统计纬度不一样, 并且只要展示其中一个就可以了
 * 但是数据可能同时存在, 并且没有优先级的概念
 * 所以用 isShowGoodsNo 进行互斥
 */

/**
 * 显示商品信息, 不包括单位、数量信息
 */
const GoodsInfo = (props: IGoodsInfoProps) => {
  const {
    className,
    title,
    alias,
    // sku,
    skuDesc,
    skuCode,
    goodsNo,
    imgUrl,
    icons = [],
    goodsSnapUrl,
    brandName,
    deliveryStatusDesc,
    orderExpressDesc,
    showSnapshot = false,
    /** 直接修改 GoodsInfo, 不用每个地方都针对小店传 false */
    withOnlineAliasLink = !isPHShop,
    comboDetail,
    comboType,
    sourceInfo = {},
    customizeExtraInfo
  } = props;

  const [comboStr, setComboStr] = useState([]);

  const comboDetails = comboSubItemInfosArr => {
    const strArr = comboSubItemInfosArr.map((combo: ICombo) => {
      const propDesc = combo?.pros
        ?.map(x => {
          if (x.price) {
            return `${x.valName}${formatAddPrice(x.price)}`;
          }
          return x.valName;
        })
        .flat()
        .filter(x => x)
        .join(',');
      const newAddPrice = formatAddPrice(combo?.addPrice);
      if (combo?.pros?.length) {
        return {
          detailStr:
            `${combo.goodsName || ''} ${
              combo.skuName ? `(${combo.skuName}${newAddPrice},${propDesc})` : `${propDesc}`
            }   ` + ` x${combo.num || ''}`
        };
      }
      return {
        detailStr:
          `${combo?.goodsName || ''} ${
            combo?.skuName ? `(${combo?.skuName}${newAddPrice})` : ''
          }   ` + ` x${combo?.num || ''}`
      };
    });
    return strArr;
  };

  React.useEffect(() => {
    if (comboDetail) {
      const { comboGroupInfos } = comboDetail;
      // 自选套餐
      if (comboType === EnumComboType.Free) {
        const comboSubItemInfosArr = comboGroupInfos.map(c => c.comboSubItemInfos).flat();
        setComboStr(comboDetails(comboSubItemInfosArr) as any);
      }
      // 固定套餐
      if (comboType === EnumComboType.Stable && comboGroupInfos?.length) {
        const { comboSubItemInfos } = comboGroupInfos[0];
        setComboStr(comboDetails(comboSubItemInfos));
      }
    }
  }, [comboDetail, comboType]);

  const tagPropList = (() => {
    let tags: ITagProps[] = [];
    tags = icons.map(({ text, code }) => {
      if (code === 'item_over') {
        return {
          children: (
            <Pop
              trigger="hover"
              position="top-left"
              content="商品开启了付款减库存，购买人数过多造成超卖，需商家与买家协商补货或退款。"
              className="oversold"
            >
              {text}
            </Pop>
          ),
          theme: 'yellow',
          outline: false
        };
      }
      return {
        children: text,
        theme: 'yellow',
        outline: false
      };
    });
    if (deliveryStatusDesc === '已发货' && orderExpressDesc !== '已发货') {
      tags.push({
        children: deliveryStatusDesc,
        theme: 'yellow',
        outline: false
      });
    }

    return tags;
  })();

  const renderComboDetail = useCallback(() => {
    return comboStr?.length ? (
      <div className="goods-info__combo_detail_str">
        {comboStr.length < ComboDetailShowCount + 1
          ? comboStr.map((str: { detailStr: string }) => {
              return <div className="goods-info__combo_detail_str__info">{str.detailStr}</div>;
            })
          : comboStr.slice(0, 1).map((str: { detailStr: string }) => {
              return <div className="goods-info__combo_detail_str__info">{str.detailStr}</div>;
            })}
        {comboStr.length > ComboDetailShowCount && (
          <Pop
            className="combo-detail-pop"
            trigger="hover"
            position="bottom-left"
            content={comboStr.map((str: { detailStr: string }) => {
              return <div>{str.detailStr}</div>;
            })}
          >
            <span className="info-link">全部</span>
          </Pop>
        )}
      </div>
    ) : null;
  }, [comboStr]);

  return (
    <div className={cx('goods-info', className)}>
      <GoodsCell
        name={
          <>
            {withOnlineAliasLink && !sourceInfo.isOfflineOrder && alias ? (
              <BlankLink
                href={`//shop${192168 + global.KDT_ID}.youzan.com/v2/showcase/goods?alias=${alias}`}
              >
                {title}
              </BlankLink>
            ) : (
              title
            )}
            {showSnapshot && goodsSnapUrl && (
              <BlankLink href={goodsSnapUrl}>[商品交易快照]</BlankLink>
            )}
          </>
        }
        photoUrl={imgUrl}
        skuNo={skuCode || goodsNo}
        brandName={brandName}
        specifications={skuDesc}
        tags={tagPropList}
        renderTag={props => {
          return <EllipsisTag {...props} />;
        }}
        extraInfo={[renderComboDetail(), customizeExtraInfo]}
      />
    </div>
  );
};

export interface IGoodsInfoIcon extends Record<string, any> {
  text: string;
}

export default GoodsInfo;
