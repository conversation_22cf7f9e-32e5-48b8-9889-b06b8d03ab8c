import React from 'react';
import ReactDOM from 'react-dom';

import DelayReceive from './delay-receive';
import HotelAccept from './hotel/accept-modal';
import HotelReject from './hotel/reject-modal';
import Reprint from './reprint';

const modalMap = {
  'new-delay-receive': DelayReceive, // 顺延发货
  'new-hotel-accept': HotelAccept, // 酒店接单
  'new-hotel-reject': HotelReject, // 酒店拒单
  reprint: Reprint
};

export const openModal = (type, options = {}) => {
  if (Object.keys(modalMap).indexOf(type) === -1) {
    return;
  }

  const ModalComponent = modalMap[type];

  let container = document.createElement('div');
  options.onComponentClose = () => {
    if (!container) {
      return;
    }
    ReactDOM.unmountComponentAtNode(container);
    container = undefined;
  };
  ReactDOM.render(React.createElement(ModalComponent, options), container);
};
