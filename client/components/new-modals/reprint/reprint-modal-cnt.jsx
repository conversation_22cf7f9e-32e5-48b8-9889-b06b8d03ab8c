import React, { Component, PureComponent } from 'react';
import { <PERSON>Loa<PERSON>, But<PERSON>, Dialog, Notify } from 'zent';
import { Form } from '@zent/compat';
import { isArray, values, pick, filter } from 'lodash';
import { BlankLink } from '@youzan/react-components';
import { setUrlDomain } from '@youzan/retail-utils';

import { safeJsonParse } from 'common/utils';
import { StorageKeyJdExpressLastEWaybillTemplateId } from 'common/constants/fulfillment';

import { JD_EXPRESS_CODE } from '@youzan/order-domain-pc-components/es/delivery-dialog/constants';

import * as api from './api.js';
import style from './style.scss';

const { FormSelectField, createForm } = Form;

@createForm()
class ReprintModalCnt extends (Component || PureComponent) {
  state = {
    loading: false, // 请求打印机列表标识
    printers: [], // 打印机初始列表数据
    printerOptList: [] // 选择打印机下拉框option 列表数据
    // printerErr: '' // 打印机错误
  };

  componentDidMount() {
    this.fetch();
    this.fetchEWaybillTemplates();
  }

  fetch = () => {
    this.setState({
      loading: true
    });

    return Promise.all([this.fetchPrinterList(), this.fetchEWaybillTemplates()])
      .catch(err => {
        Notify.error(err.msg || '数据请求失败');
        throw err;
      })
      .finally(() =>
        this.setState({
          loading: false
        })
      );
  };

  fetchPrinterList = () => {
    return api.fetchPrinterList().then(res => {
      const onLinePrinterList = res.items || [];
      this.setPrinterListData(onLinePrinterList);
    });
  };

  fetchEWaybillTemplates = () => {
    const { expressId } = this.props;
    if (expressId === JD_EXPRESS_CODE) {
      api.fetchExpressConfig({ expressId, deliveryWay: 1 /** 快递 */ }).then(data => {
        const lastId = safeJsonParse(
          localStorage.getItem(StorageKeyJdExpressLastEWaybillTemplateId)
        );
        const newState = {};
        if (lastId) {
          this.props.zentForm.setFieldsValue({
            eWaybillTemplateId: lastId
          });
        }
        newState.eWaybillTemplateOptions =
          data.expressWaybillSizeTypes?.map(item => {
            return {
              value: item.templateUrl,
              text: item.specification
            };
          }) || [];
        this.setState(newState);
      });
    }
  };

  createOptionsFromList(list = [], valueKeysOrigin, textKeysOrigin) {
    let valueKeys = valueKeysOrigin;
    let textKeys = textKeysOrigin;
    if (!valueKeys || !textKeys) return [];
    if (isArray(valueKeys) && valueKeys.length === 0) return [];
    if (isArray(textKeys) && textKeys.length === 0) return [];
    if (typeof valueKeys === 'string') {
      valueKeys = [valueKeys];
    }
    if (typeof textKeys === 'string') {
      textKeys = [textKeys];
    }
    const optionsDataSets = list.map(itm => ({
      value: values(pick(itm, valueKeys)).join(''),
      text: values(pick(itm, textKeys)).join('')
    }));
    return optionsDataSets;
  }

  setPrinterListData(resData) {
    const printerOptList = this.createOptionsFromList(resData, 'id', 'name');
    this.setState({
      printers: resData,
      printerOptList
    });
  }

  handleReprint = vals => {
    const { dialogId, orderNo, packId, expressId } = this.props;
    const { printers } = this.state;
    if (!vals.printerId) {
      Dialog.closeDialog(dialogId);
      return;
    }
    // const { zentForm, onSearch } = this.props;
    const [printer] = filter(printers, { id: +vals.printerId });

    const printerParams = {
      sourceId: 1002, // 微商城和零售是1002，餐饮1001
      printerKey: printer.equipmentKey,
      printerDeviceNo: printer.equipmentNumber,
      printerChannel: printer.equipmentTypeId
    };

    switch (expressId) {
      case JD_EXPRESS_CODE:
        printerParams.templateUrl = vals.eWaybillTemplateId;
        break;
      default:
        break;
    }

    api
      .reprint({
        orderId: orderNo,
        packId,
        ...printerParams
      })
      .then(res => {
        /*
          kdt_id: 55
          is_accept: true
          delivery_no: "YZ201805187nho76550"
        */
        if (res.isAccept) {
          Notify.success('打印成功');
        }
      })
      .catch(err => {
        // TODO(zhb): request 库有问题, 这里先快速修复, 插个眼
        Notify.error(err?.msg || '打印失败');
        throw err;
      })
      .finally(() => {
        Dialog.closeDialog(dialogId);
      });
  };

  render() {
    const { expressId, handleSubmit } = this.props;
    const { loading, printerOptList } = this.state;

    const printerHelpElements = [
      !loading && printerOptList.length === 0 ? (
        <p style={{ color: '#df4545' }}>当前打印机未连接</p>
      ) : null,
      expressId !== JD_EXPRESS_CODE ? (
        <p>请确认打印机里使用的是“100*180mm”规格的电子面单纸</p>
      ) : null
    ];

    return (
      <BlockLoading loading={loading}>
        <Form
          className={style['reprint-form']}
          onSubmit={handleSubmit(this.handleReprint)}
          horizontal
        >
          {expressId === JD_EXPRESS_CODE ? (
            <FormSelectField
              label="面单模板："
              name="eWaybillTemplateId"
              placeholder="请选择"
              data={this.state.eWaybillTemplateOptions}
              required
              validations={{ required: true }}
              validationErrors={{ required: '请选择面单模板' }}
            />
          ) : null}

          <div className="printer-wrapper">
            <FormSelectField
              label="打印机："
              name="printerId"
              placeholder="请选择"
              helpDesc={
                printerHelpElements.filter(Boolean).length > 0 ? (
                  <div className="printer-help-container">{printerHelpElements}</div>
                ) : null
              }
              className="printer-field"
              data={printerOptList}
              required
              validations={{ required: true }}
              validationErrors={{ required: '请选择打印机' }}
            />
            <BlankLink href={setUrlDomain('/setting/common/device#/', 'store')}>新增</BlankLink>
          </div>

          <footer className="btn-group">
            <Button className="reprint-btn" type="primary" htmlType="submit" loading={loading}>
              确定
            </Button>
            {!printerOptList.length && (
              <Button
                type="primary"
                outline
                href={setUrlDomain('/thread-667366-1-1.html', 'bbs')}
                target="_blank"
              >
                查看打印机连接指南
              </Button>
            )}
          </footer>
        </Form>
      </BlockLoading>
    );
  }
}

export default ReprintModalCnt;
