/**
 * 模块内部ajax请求
 */
import { request } from '@youzan/retail-utils';

const urlMap = {
  fetchPrinterListUrl: '/youzan.retail.peripheral.equipment/1.0.0/querypage',
  wayBillReprintUrl: 'youzan.logistics.waybill/1.0.0/print'
};

/**
 * 获取打印机列表(这里写死参数)
 * @param {Object} params
 */

const defaultParams = {
  peripheralTypeId: 1001, // 表示设备类型是打印机
  pageSize: 20,
  pageNo: 1,
  /**
   * 电子面单目前只支持：
   * 1001001: 365 云打印-M2
   * 1001003: 映美云 CLP-180
   */
  equipmentTypeIds: [1001001, 1001003]
};

/**
 * 获取打印机列表
 * @param {Object} params 请求参数
 */
export function fetchPrinterList(params) {
  return request({
    url: urlMap.fetchPrinterListUrl,
    data: params || defaultParams
  });
}

/**
 * 重新打印面单
 * @param {Object} params
 */
export function reprint(params) {
  return request({
    url: urlMap.wayBillReprintUrl,
    method: 'post',
    data: params || defaultParams
  });
}

// 获取发货配置
export const fetchExpressConfig = data =>
  request({
    url: '/youzan.retail.trade.manager.express.config/1.0.0/get',
    data
  });
