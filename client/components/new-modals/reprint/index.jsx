import React, { Component, PureComponent } from 'react';
import { Dialog } from 'zent';
import ReprintModalCnt from './reprint-modal-cnt';

import style from './style.scss';

const dialogId = 1;
class ReprintModal extends (PureComponent || Component) {
  static defaultProps = {
    title: '打印面单',
    onHideBtn: () => null
  };

  componentDidMount() {
    this.open();
  }

  open = () => {
    Dialog.openDialog({
      dialogId,
      maskClosable: false,
      className: style['reprint-dialog'],
      title: this.props.title,
      children: <ReprintModalCnt {...this.props} dialogId={dialogId} />,
      onClose: () => {
        this.props.onComponentClose();
        this.props.callback && this.props.callback();
      }
    });
  };

  render() {
    return <span />;
  }
}

export default ReprintModal;
