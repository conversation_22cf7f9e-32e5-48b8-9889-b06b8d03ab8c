import React, { Component, PureComponent } from 'react';
import { Button, Dialog, Notify } from 'zent';
import Content from './content';
import { receiveOrder } from './api';

class AcceptModal extends (PureComponent || Component) {
  componentDidMount() {
    this.open();
  }

  open = () => {
    const dialogId = 1;
    Dialog.openDialog({
      dialogId,
      title: '商品接单',
      children: <Content orderNo={this.props.orderNo} />,
      onClose: () => {
        this.props.onComponentClose();
        this.props.callback();
      },
      footer: (
        <footer>
          <Button
            type="primary"
            onClick={() => {
              receiveOrder({
                order_no: this.props.orderNo
              })
                .then(() => {
                  Notify.success('接单成功！');
                })
                .catch(err => Notify.error(err.msg || '接单失败！'))
                .finally(() => {
                  Dialog.closeDialog(dialogId);
                });
            }}
          >
            确认接单
          </Button>
        </footer>
      )
    });
  };

  render() {
    return <span />;
  }
}

export default AcceptModal;
