import { request } from '@youzan/retail-utils';

const preUrl = `${window._global.url.www}/trade/order/`;

export const fetchOrderItems = data =>
  request({
    url: `${preUrl}orderItems.json`,
    data
  });

export const receiveOrder = data =>
  request({
    url: `${preUrl}pickOrder.json`,
    method: 'post',
    data
  });

export const refuseOrder = data =>
  request({
    url: `${preUrl}refusePickOrder.json`,
    method: 'post',
    data
  });
