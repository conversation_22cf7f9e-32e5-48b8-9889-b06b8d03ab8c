import React, { Component, PureComponent } from 'react';
import { Grid, Notify } from 'zent';
import { fetchOrderItems } from './api';

class Content extends (PureComponent || Component) {
  constructor(props) {
    super(props);
    this.state = {
      items: [],
      loading: true
    };
  }

  componentDidMount() {
    fetchOrderItems({
      order_no: this.props.orderNo
    })
      .then(data => {
        this.setState({
          loading: false,
          items: data.items
        });
      })
      .catch(err => Notify.error(err.msg));
  }

  render() {
    return (
      <div>
        {this.props.isReject && (
          <div style={{ paddingBottom: 20 }}>
            确认拒单后订单将关闭，钱款原路退回给买家。你可以在订单详情里查看退款信息。
          </div>
        )}
        <Grid
          columns={[
            {
              title: '商品',
              bodyRender: data => (
                <div>
                  <a href={data.url} target="_blank" rel="noopener noreferrer">
                    {data.title}
                  </a>
                  {data.sku &&
                    data.sku.map((item, index) => (
                      <span key={index} className="grey">
                        {`${item.k} ${item.v}  `}
                      </span>
                    ))}
                </div>
              )
            },
            {
              title: '数量',
              name: 'num'
            }
          ]}
          loading={this.state.loading}
          datasets={this.state.items}
        />
      </div>
    );
  }
}

export default Content;
