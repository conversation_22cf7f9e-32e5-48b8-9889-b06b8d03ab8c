import React, { Component, PureComponent } from 'react';
import { Button, Dialog, Notify } from 'zent';
import Content from './content';
import { refuseOrder } from './api';

class ChangePriceModal extends (PureComponent || Component) {
  componentDidMount() {
    this.open();
  }

  open = () => {
    const dialogId = 'hotel-reject';
    Dialog.openDialog({
      dialogId,
      title: '确认拒单？',
      children: <Content isReject orderNo={this.props.orderNo} />,
      onClose: () => {
        this.props.onComponentClose();
        this.props.callback();
      },
      footer: (
        <footer>
          <Button
            type="primary"
            onClick={() => {
              refuseOrder({
                order_no: this.props.orderNo
              })
                .then(() => {
                  Notify.success('拒单成功！');
                })
                .catch(err => Notify.error(err.msg || '拒单失败！'))
                .finally(() => {
                  Dialog.closeDialog(dialogId);
                });
            }}
          >
            确认拒单
          </Button>
          <Button onClick={() => Dialog.closeDialog(dialogId)}>下次再说</Button>
        </footer>
      )
    });
  };

  render() {
    return <span />;
  }
}

export default ChangePriceModal;
