import React, { Component, PureComponent } from 'react';
import { Sweetalert, Notify } from 'zent';
import * as api from './api';

class DelayReceiveModal extends (PureComponent || Component) {
  static defaultProps = {
    onSubmit: () => null
  };

  componentDidMount() {
    this.open();
  }

  open = () => {
    Sweetalert.confirm({
      content: '确认后订单自动确认时间将会延长3天',
      title: '确定延长收货吗',
      onConfirm: () => {
        api
          .delayReceive({
            order_no: this.props.orderNo
          })
          .then(() => {
            Notify.success('延长收货成功！');
          })
          .catch(err => Notify.error(err.msg || '延长收货失败'));
        this.props.onComponentClose();
      },
      onCancel: this.props.onComponentClose
    });
  };

  render() {
    return <span />;
  }
}

export default DelayReceiveModal;
