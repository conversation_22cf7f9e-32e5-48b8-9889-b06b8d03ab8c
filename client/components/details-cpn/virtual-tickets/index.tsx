// 电子卡券核销记录
// 2020-06-10

import * as React from 'react';
import { Grid, IGridColumn } from 'zent';
import { VerifyStateMap } from 'common/constants/common';
import { format } from 'date-fns';
import { style } from './style.scss';

interface IVirtualTicketData {
  state: number;
  validity: string;
  ticketItemNo: string;
  operatorName: string;
  verifyTime: number;
}

const COLUMNS: IGridColumn<IVirtualTicketData>[] = [
  {
    title: '商品券码',
    name: 'ticketItemNo'
  },
  {
    title: '有效期',
    name: 'validity'
  },
  {
    title: '核销时间',
    name: 'verifyTime',
    bodyRender({ verifyTime }) {
      if (!verifyTime) return '-';
      return format(verifyTime * 1000, 'YYYY-MM-DD HH:mm:ss');
    }
  },
  {
    title: '核销人',
    name: 'operatorName',
    bodyRender({ operatorName }) {
      return operatorName || '-';
    }
  },
  {
    title: '状态',
    name: 'state',
    bodyRender({ state }) {
      return VerifyStateMap[state];
    }
  }
];

export default function VirtualTickets({ data }: { data: Array<IVirtualTicketData> }) {
  if (data.length === 0) return null;

  return <Grid className={style} datasets={data} bordered={false} columns={COLUMNS} />;
}
