@import '~shared/style';

.verify-records {
  margin-top: 4px;

  .verify-content {
    display: flex;
    border: 1px solid $background-color-base;
    padding: 20px 15px 0;

    .verify-info {
      padding: 20px 0 0 15px;
      max-width: 300px;
      flex: 1;
      line-height: 20px;

      &__item .label {
        display: inline-block;
        width: 60px;
      }
    }

    .verify-goods-info {
      padding: 20px 15px 0 0;
      width: 360px;
      height: 150px;

      &__text {
        clear: both;
        padding-top: 10px;

        p {
          width: 60px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          color: $color-text-secondary;
        }
      }
    }
  }
}
