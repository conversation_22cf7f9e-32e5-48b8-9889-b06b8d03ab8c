import React from 'react';
import { formatDatetime } from '@youzan/retail-utils';

import CenteredImage from 'components/centered-image';

const VerifyInfo = props => {
  const { currentTab = 1, records = [] } = props;
  const currentIndex = currentTab - 1;
  if (currentIndex >= records.length) {
    return null;
  }
  const item = records[currentIndex];
  return (
    <div className="verify-content">
      <div className="verify-info">
        <div>
          <div className="verify-info__item">
            <span className="label">核销人：</span>
            <span className="value">{item.verifyPerson}</span>
          </div>
          <div className="verify-info__item">
            <span className="label">核销时间：</span>
            <span className="value">
              {+item.verifyTime // 兼容string形式和 number
                ? formatDatetime(item.verifyTime * 1000)
                : item.verifyTime}
            </span>
          </div>
        </div>
        <div className="verify-goods-info">
          <CenteredImage src={item.imageUrl} />
          <div className="verify-goods-info__text">
            <p>{item.title}</p>
            <p>{`数量：${item.verifyCount}`}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyInfo;
