import { request } from '@youzan/retail-utils';

const urlMap = {
  fetchTicketsVerifyRecords: 'youzan.trade.virtualticket.record.list/3.0.0/get',
  fetchVirtualGoodsVerifyRecords: 'youzan.trade.virtualcode/3.0.0/get'
};

// 电子卡券核销记录
export function fetchTicketsVerifyRecords(data) {
  return request({
    url: urlMap.fetchTicketsVerifyRecords,
    data: Object.assign(data, { pageNo: 1 })
  });
}

// 虚拟商品核销记录
export function fetchVirtualGoodsVerifyRecords(data) {
  return request({
    url: urlMap.fetchVirtualGoodsVerifyRecords,
    data
  });
}
