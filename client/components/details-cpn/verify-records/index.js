import React from 'react';
import { Tabs, Notify } from 'zent';
import { get } from 'lodash';

import * as api from 'components/details-cpn/verify-records/api';
import VerifyInfo from './verify-info';
import './style.scss';

class VerifyRecords extends React.Component {
  state = {
    currentTab: 1,
    records: [],
    isVirtualTicket: false
  };

  componentWillMount() {
    const { items } = this.props;
    const isVirtualTicket = +get(items, '[0].goodsType') === 183;
    this.setState({
      isVirtualTicket
    });
  }

  componentDidMount() {
    this.fetch();
  }

  transformToRecords(originalData) {
    const records = [];
    // originalData.status 核销状态 ： 1未核销，2已核销
    // 只展示已核销的记录
    if (originalData && +originalData.status === 2) {
      const {
        verifyPerson,
        useTime: verifyTime,
        trade: { picPath: imageUrl = '', title = '', num: verifyCount = '' }
      } = originalData;

      records.push({
        verifyPerson,
        verifyTime,
        imageUrl,
        title,
        verifyCount
      });
    }
    return records;
  }

  handleTabChange = currentTab => {
    this.setState({ currentTab });
  };

  fetch() {
    const { orderNo } = this.props;
    const { isVirtualTicket } = this.state;

    let apiFn = 'fetchVirtualGoodsVerifyRecords';
    let paramName = 'code';

    if (isVirtualTicket) {
      apiFn = 'fetchTicketsVerifyRecords';
      paramName = 'orderNo';
    }
    api[apiFn]({ [paramName]: orderNo })
      .then(data => {
        if (!data) return;
        const records = isVirtualTicket ? get(data, 'list', []) : this.transformToRecords(data);
        this.setState({ records });
      })
      .catch(err => Notify.error(err.msg || '核销记录获取失败'));
  }

  render() {
    const { currentTab, records } = this.state;
    return (
      <div className="verify-records">
        <Tabs activeId={currentTab} onChange={this.handleTabChange} type="card">
          {records.map((item, index) => (
            <Tabs.TabPanel key={index} id={index + 1} tab={`记录${index + 1}`} />
          ))}
        </Tabs>
        <VerifyInfo {...this.state} />
      </div>
    );
  }
}

export default VerifyRecords;
