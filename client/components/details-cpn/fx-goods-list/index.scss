@import '~shared/style';

.item-basic-info {
  padding: 10px;
  text-align: left;
  display: flex;

  .retail-lazyimage-container > img {
    width: 60px;
    height: 60px;
  }

  &__content {
    flex: 1;
    padding: 5px;
  }
}

.zent-grid-td {
  border-right: 1px solid $border-color-base;

  &:last-of-type {
    border-right: none;
  }
}

.rebate-brief {
  width: 368px;
  line-height: 18px;
}

.zent-grid-tr {
  &:hover,
  &__mouseover {
    background-color: $color-white;
  }

  .zenticon-help-circle {
    margin-left: 5px;

    &::before {
      font-size: $font-size-base;
      color: $color-text-disable;
    }
  }
}

.order-fold-label {
  padding: 0;
}

.order-goods-fold {
  padding: 15px 10px 15px 0;
  line-height: 16px;
  color: $color-text-secondary;

  .order-fold-bar {
    width: 20px;
    padding: 10px 0;
    border-radius: 0 3px 3px 0;
    background-color: $background-color-base;
    text-align: center;
    cursor: pointer;
  }

  .fold-icon {
    color: $color-text-disable;
    transform: scale(0.7);
  }
}

.refund-history {
  display: flex;
  margin-top: 12px;

  .pop-content {
    padding: 16px 10px;
  }

  &-item {
    display: flex;
    flex-shrink: 0;
    margin-bottom: 6px;

    &:last-of-type {
      margin-bottom: 0;
    }

    span {
      margin-right: 12px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .color-text {
    color: $color-b4;
    cursor: pointer;
  }
}
