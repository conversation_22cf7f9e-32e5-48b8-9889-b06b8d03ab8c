/**
 * 之前的订单详情 GoodsList 没有用表格, 完全是自己写的 DOM 结构, 不便于维护
 * 这里用 Grid 先重构掉分销供货单的部分
 * 后续有机会会把订单详情全迁移到这个组件上
 *
 * <AUTHOR>
 * @date 2019.05.28
 */
import type { Contains } from 'definition/common';

import * as React from 'react';
import { BlockLoading, Grid } from 'zent';
import { isEmpty, sortBy } from 'lodash';
import type { IOrderExpressInfo } from 'definition/order-info';
import { isHqStore, isPartnerStore, isUnifiedOnlineBranchStore } from '@youzan/utils-shop';
import './index.scss';

import isFxStore from 'common/is-fx-store';
import getColumns from './columns';

const { useState } = React;

// 根据发货单号排序
const orderByDispatchBillNo = (itemInfo: Array<Contains<{ fulfillNo: number }>> = []) =>
  sortBy(itemInfo, 'fulfillNo');

const scroll = { x: 1000 };

export default function FxGoodsList(
  props: Contains<{
    orderInfo: Contains<{ itemInfo: any[] }>;
    orderExpressInfo?: IOrderExpressInfo;
    reload: () => void;
    refundInfo: {
      data: Contains<{
        refundFund: {
          operator: string;
          createTime: number;
          refundFee: number;
          refundId: string;
        };
      }>;
    };
  }>
): JSX.Element {
  const [isFullColumns, setColumns] = useState(false);
  const [loading, setLoading] = useState(true);
  const toggleColumns = () => setColumns(true);

  const { orderInfo, orderExpressInfo = {}, reload, refundInfo } = props;
  if (!isEmpty(orderInfo) && loading) {
    setLoading(false);
  }
  if (loading) {
    return <BlockLoading loading />;
  }

  const { itemInfo } = orderInfo;
  const sortItems =
    isHqStore || isPartnerStore || isUnifiedOnlineBranchStore || isFxStore
      ? orderByDispatchBillNo(itemInfo)
      : itemInfo;

  const columns = getColumns({
    isFullColumns,
    orderInfo,
    toggleColumns,
    orderExpressInfo,
    reload,
    refundInfo
  });

  return <Grid datasets={sortItems} columns={columns} scroll={scroll} />;
}
