/**
 * 这个文件里, 很多代码是从原来的 GoodsList 里拆出来的
 * 后面为了方便完整的迁移成 Pure Function, 在维护的时候, 请优先对比一下 GoodsList
 * 看看是不是已经做好了
 */
import type { Contains } from 'definition/common';
import type { IGridCellPos, IGridColumn } from 'zent';
import type { IItemInfo, IOrderExpressInfo, IPaymentInfo } from 'definition/order-info';
import type { IGoodsInfo } from 'definition/goods-info';
import * as React from 'react';
import { Icon, Pop, ClampLines } from 'zent';
import { isNil, get, find } from 'lodash';
import { isRetailSingleStore } from '@youzan/utils-shop';
import { LazyImage } from '@youzan/retail-lazyload';
import { BlankLink } from '@youzan/react-components';
import { formatDate } from '@youzan/retail-utils';
import { getCorrectImgUrl, divGoodsNum } from 'common/helper';
import { convertFenToYen } from 'common/fns/format';
import { isOnlineOrder } from 'common/helper';
import { OptComponent } from 'components/opt-components';

const CITY_EXPRESS_CODE = 2;
const EXPRESS_STATUS_MAP = {
  0: '未发货',
  1: '已发货',
  [CITY_EXPRESS_CODE]: '同城配送',
  3: '部分已发货'
};

// 处理跨行
function getRowSpan<T extends JSX.Element | string>({
  pos,
  rowSpan,
  children
}: {
  pos: { row: number };
  rowSpan: number;
  children: T;
}): { children?: T; props: { rowSpan: number } } {
  if (pos.row === 0) {
    return {
      props: {
        rowSpan
      },
      children
    };
  }

  return {
    props: {
      rowSpan: 0
    }
  };
}

// 发货状态
const renderExpressState = (
  orderExpressInfo: IOrderExpressInfo,
  goodsInfo: Pick<
    IGoodsInfo,
    | 'hasExpressed'
    | 'itemIdStr'
    | 'giftToBeReceivedNum'
    | 'latestDeliveryTermDesc'
    | 'latestDeliveryTimeDesc'
  >
) => {
  const { packs = [] } = orderExpressInfo;
  const {
    hasExpressed,
    itemIdStr,
    giftToBeReceivedNum,
    latestDeliveryTermDesc: termDesc,
    latestDeliveryTimeDesc: timeDesc
  } = goodsInfo;

  const goodsPack = find(packs, ({ itemIds = [] }) => itemIds.indexOf(itemIdStr) > -1);

  const expressDesc =
    hasExpressed === CITY_EXPRESS_CODE
      ? get(goodsPack, 'takeoutExpressDetail.statusDesc', '')
      : EXPRESS_STATUS_MAP[hasExpressed as keyof typeof EXPRESS_STATUS_MAP];

  return (
    <>
      <p>{!isNil(hasExpressed) && expressDesc}</p>
      {!!giftToBeReceivedNum && (
        <p className="orange">
          还有
          {giftToBeReceivedNum}
          个礼单待领取
        </p>
      )}
      {termDesc && <p style={{ color: 'grey' }}>{termDesc}</p>}
      {timeDesc && <p style={{ color: 'grey' }}>{timeDesc}</p>}
    </>
  );
};

// 推广佣金(元)
const renderRebate = (
  { paymentInfo }: { paymentInfo: IPaymentInfo },
  itemInfo: IItemInfo,
  pos: { row: number }
) => {
  return getRowSpan({
    pos,
    rowSpan: itemInfo.length,
    children: `${convertFenToYen(paymentInfo.purchaseOrderPaymentInfo.fxRebateFee)}`
  });
};

// 供货运费(发货单详情有)
const renderPostage = (
  { paymentInfo }: { paymentInfo: IPaymentInfo },
  itemInfo: IItemInfo,
  pos: { row: number }
) => {
  return getRowSpan({
    pos,
    rowSpan: itemInfo.length,
    children: `${convertFenToYen(paymentInfo.purchaseOrderPaymentInfo.supPostage)}`
  });
};

// 优惠信息
const renderPromotionInfo = (goodsInfo: IGoodsInfo) => {
  return (goodsInfo.purchaseOrderItemPriceInfo.supUmpInfoList || []).map(
    ({ umpName, umpAmount }, index) => {
      return (
        <div className="ump-info__item" key={index}>
          <p>{`${umpName}${convertFenToYen(umpAmount)}`}</p>
        </div>
      );
    }
  );
};

// 供货价(小计)
const renderAmountPrice = (goodsInfo: IGoodsInfo): JSX.Element => (
  <div className="item--subtotalled">
    <p>￥{convertFenToYen(goodsInfo.purchaseOrderItemPriceInfo.payPrice)}</p>
  </div>
);

// 分销推广补差价
const renderPatchPay = (
  { paymentInfo }: { paymentInfo: IPaymentInfo },
  itemInfo: IItemInfo,
  pos: { row: number }
): { children?: string; props: { rowSpan: number } } =>
  getRowSpan({
    pos,
    rowSpan: itemInfo.length,
    children: `${convertFenToYen(paymentInfo.purchaseOrderPaymentInfo.orderFxPatchPay)}`
  });

// 运费
const renderFreight = (
  { paymentInfo }: { paymentInfo: IPaymentInfo },
  itemInfo: IItemInfo,
  pos: { row: number }
): { children?: string; props: { rowSpan: number } } =>
  getRowSpan({
    pos,
    rowSpan: itemInfo.length,
    children: `${convertFenToYen(paymentInfo.purchaseOrderPaymentInfo.fxPostage)}`
  });

// 推广商品优惠
const renderDiscountAmount = (goodsInfo: IGoodsInfo): string => {
  return `${convertFenToYen(goodsInfo.purchaseOrderItemPriceInfo.fxUmpAmount)}`;
};

// 推广价格
const renderPayPrice = (goodsInfo: IGoodsInfo) => {
  return `${convertFenToYen(goodsInfo.purchaseOrderItemPriceInfo.fxPayPrice)}`;
};

const showNum = ({ refundNum, num, unit }: IGoodsInfo) => {
  const goodsNum = divGoodsNum(num);
  if (!refundNum) {
    return `${goodsNum}${unit}`;
  }
  return (
    <>
      ({goodsNum}
      {unit}/
      <span style={{ color: '#ff4444' }}>
        已退{divGoodsNum(refundNum)}
        {unit}
      </span>
      )
    </>
  );
};

// 数量
const renderGoodsNum = (goodsInfo: IGoodsInfo): JSX.Element => {
  return (
    <div className="item--goods-num">
      <p>{showNum(goodsInfo)}</p>
    </div>
  );
};

// 商品信息
const renderGoodsInfo = (goodsInfo: IGoodsInfo): JSX.Element => {
  const clampedTitle = <ClampLines popWidth={280} lines={1} text={goodsInfo.title} />;

  return (
    <>
      <div className="item-basic-info">
        <LazyImage
          alt="商品图片"
          height={60}
          width={60}
          offset="200"
          src={getCorrectImgUrl(goodsInfo.imgUrl)}
          fullfill={{ rule: '!100x100.jpg' }}
          placeholder={<div className="retail-image-placeholder" />}
        />
        <div className="item-basic-info__content">
          <h4 className="goods-title">
            {goodsInfo.goodsSnapUrl && isRetailSingleStore ? (
              <BlankLink href={`${goodsInfo.goodsSnapUrl}`}>{clampedTitle}</BlankLink>
            ) : (
              <span>{clampedTitle}</span>
            )}
          </h4>
          <p className="goods-content grey">{goodsInfo.skuDesc}</p>
          {goodsInfo.skuCode && (
            <p className="goods-content grey">{`商品条码：${goodsInfo.skuCode}`}</p>
          )}
          <p>
            {goodsInfo.icons?.map(({ code, text }: { code: string; text: string }) => (
              <span className="tag present-tag" key={code}>
                {text}
              </span>
            ))}
          </p>
        </div>
      </div>
      {goodsInfo.buyerMemoDesc && (
        <p className="grey goods-remark">
          商品留言：
          {goodsInfo.buyerMemoDesc}
        </p>
      )}
    </>
  );
};

/**
 *
 * @memberof GoodsList
 *  门店和网店返回不一样
 *  并且对operation.code === ('goods_refund' || 'refund') 即主动退款的进行特殊处理
 *  因为主动退款重构了一波
 *  目前这段代码 中间态。
 */
const getOptOptions = (
  operation: Contains<{ code: string }>,
  goodsInfo: Contains<{ operations: Array<Contains<{ code: string }>>; itemIdStr: string }>,
  orderInfo: Contains<{
    itemInfo: IItemInfo[];
    paymentInfo: Contains<{ realPay: number; payDeduction: number }>;
  }>,
  reload: () => void
) => {
  const { mainOrderInfo, paymentInfo, itemInfo = [] } = orderInfo;
  const isOnline = isOnlineOrder(orderInfo);
  let ret = null;
  if (~['goods_refund', 'refund'].indexOf(operation.code)) {
    ret = {
      mainOrderInfo,
      reload,
      isOnline,
      isFxOrder: true,
      realPay: paymentInfo?.realPay,
      payDeduction: paymentInfo?.payDeduction,
      itemInfo: [goodsInfo],
      allItems: itemInfo
    };
  }
  return ret;
};

const renderOperations = (
  operations: Array<Contains<{ code: string }>>,
  goodsInfo: Contains<{ operations: Array<Contains<{ code: string }>>; itemIdStr: string }>,
  orderInfo: Contains<{
    itemInfo: IItemInfo[];
    paymentInfo: Contains<{ realPay: number; payDeduction: number }>;
  }>,
  reload: () => void
) => {
  return operations.map(operation => (
    <p key={operation.code}>
      <OptComponent
        operation={operation}
        options={getOptOptions(operation, goodsInfo, orderInfo, reload)}
      />
    </p>
  ));
};

// 退款状态
const renderRefundState = (
  goodsInfo: Contains<{ operations: Array<Contains<{ code: string }>>; itemIdStr: string }>,
  orderInfo: Contains<{
    itemInfo: IItemInfo[];
    paymentInfo: Contains<{ realPay: number; payDeduction: number }>;
  }>,
  reload: () => void,
  refundInfo: Array<{
    refundFund: {
      operator: string;
      createTime: number;
      refundFee: number;
      refundId: string;
    };
    refundOrderItemInfos: Array<Contains<{ itemIdStr: string }>>;
  }>
) => {
  const { operations: itemOperations = [], itemIdStr } = goodsInfo;

  const refundOpts = itemOperations.filter(
    operation => ~['goods_refund', 'refund'].indexOf(operation.code)
  );
  // 当前item对应的退款记录
  let refundHistory: Array<{
    refundFund: {
      operator: string;
      createTime: number;
      refundFee: number;
      refundId: string;
    };
    refundOrderItemInfos: Array<Contains<{ itemIdStr: string }>>;
  }> = [];
  // 当前item对应的退款金额
  let itemRefundMoney = 0;

  // refundInfo是订单纬度的  根据itemid过滤一把， 顺便计算出来总和
  if (refundInfo?.length) {
    refundHistory = refundInfo.filter(({ refundOrderItemInfos, refundFund }) => {
      if (itemIdStr === refundOrderItemInfos[0]?.itemIdStr) {
        itemRefundMoney += +refundFund?.refundFee;
        return true;
      }
      return false;
    });
  }

  return (
    <div>
      <div>{renderOperations(refundOpts, goodsInfo, orderInfo, reload)}</div>
      {refundHistory?.length ? (
        <div className="refund-history">
          <span>主动退款（-{convertFenToYen(itemRefundMoney)}）</span>
          <Pop
            trigger="click"
            style={{
              maxWidth: 600
            }}
            position="left-center"
            content={
              <div className="pop-content">
                {refundHistory.map(refundItem => {
                  return refundItem?.refundFund ? (
                    <div className="refund-history-item" key={refundItem.refundFund.refundId}>
                      {/* 操作时间 */}
                      <span>
                        {formatDate(refundItem.refundFund.createTime, 'YYYY-MM-DD HH:mm:ss')}
                      </span>
                      {/* 操作人姓名 */}
                      <span
                        style={{
                          maxWidth: 90
                        }}
                      >
                        {refundItem.refundFund.operator}
                      </span>
                      <span>主动退款</span>
                      {/* 退款金额 供货商承担金额 */}
                      <span>-{convertFenToYen(refundItem.refundFund.refundFee)}</span>
                      {/* 退款编号 */}
                      <span>{refundItem.refundFund.refundId}</span>
                    </div>
                  ) : null;
                })}
              </div>
            }
          >
            <span className="color-text">记录</span>
          </Pop>
        </div>
      ) : null}
    </div>
  );
};
export default ({
  isFullColumns,
  orderInfo,
  toggleColumns,
  orderExpressInfo,
  reload,
  refundInfo
}: Pick<
  IGoodsInfo,
  'isFullColumns' | 'orderInfo' | 'toggleColumns' | 'orderExpressInfo' | 'reload' | 'refundInfo'
>): IGridColumn[] => {
  const { itemInfo } = orderInfo;
  const columns: IGridColumn[] = [
    {
      title: '商品',
      bodyRender: renderGoodsInfo,
      width: 260,
      fixed: true,
      textAlign: 'left'
    },
    {
      title: '数量',
      bodyRender: renderGoodsNum
    },
    {
      title: '推广价格(元)',
      bodyRender: renderPayPrice,
      textAlign: 'right'
    },
    {
      title: '推广商品优惠(元)',
      bodyRender: renderDiscountAmount,
      textAlign: 'right'
    },
    {
      title: '运费(元)',
      // 因为 数据结构不适用于 Table 的 dataSets, 所以 运费需要单独处理
      bodyRender: (_item: never, pos: { row: number }) => renderFreight(orderInfo, itemInfo, pos),
      textAlign: 'right'
    },
    {
      title: '分销推广补差(元)',
      bodyRender: (_item: never, pos: { row: number }) => renderPatchPay(orderInfo, itemInfo, pos),
      textAlign: 'right'
    }
  ];

  if (!isFullColumns) {
    return columns.concat([
      {
        className: 'order-fold-label',
        title: '',
        bodyRender: (_item: unknown, pos: IGridCellPos): React.ReactNode =>
          getRowSpan({
            pos,
            children: (
              <div className="order-goods-fold">
                <div className="order-fold-bar" onClick={toggleColumns}>
                  <div className="fold-icon">
                    <Icon type="right" />
                    <Icon type="right" />
                  </div>
                  查看供货信息
                </div>
              </div>
            ),
            rowSpan: itemInfo.length
          }),
        width: 30
      },
      {
        title: '售后状态',
        textAlign: 'left',
        width: 180,
        // fixed: 'right',
        bodyRender: (
          goodsInfo: Contains<{
            operations: Array<Contains<{ code: string }>>;
            itemIdStr: string;
          }>
        ) => renderRefundState(goodsInfo, orderInfo, reload, refundInfo)
      },
      {
        title: '状态',
        bodyRender: (
          goodsInfo: Pick<
            IGoodsInfo,
            | 'hasExpressed'
            | 'itemIdStr'
            | 'giftToBeReceivedNum'
            | 'latestDeliveryTermDesc'
            | 'latestDeliveryTimeDesc'
          >
        ): React.ReactNode => renderExpressState(orderExpressInfo, goodsInfo),
        width: 75,
        textAlign: 'right',
        fixed: 'right'
      }
    ]);
  }

  return columns.concat([
    {
      title: '供货价(元)',
      bodyRender: renderAmountPrice
    },
    {
      title: '供货商品优惠(元)',
      bodyRender: renderPromotionInfo,
      textAlign: 'right'
    },
    {
      title: '供货运费(元)',
      bodyRender: (_item: unknown, pos: IGridCellPos): React.ReactNode =>
        renderPostage(orderInfo, itemInfo, pos),
      textAlign: 'right'
    },
    {
      title: (
        <span>
          推广佣金(元)
          <Pop
            trigger="hover"
            position="top-right"
            content={
              <div className="rebate-brief">
                <p>
                  推广佣金: 订单结算时，系统自动触发供货商返给分销商的一笔佣金。
                  单商品推广佣金=单商品买家实付-单商品实际供货价
                </p>
                <p>其中，</p>
                <p>
                  单商品买家实付=推广价格-推广优惠-均摊到商品上的推广订单优惠
                  单商品实际供货价=供货价-供货商优惠
                </p>
              </div>
            }
          >
            <Icon type="help-circle" />
          </Pop>
        </span>
      ),
      bodyRender: (_item: unknown, pos: IGridCellPos): React.ReactNode =>
        renderRebate(orderInfo, itemInfo, pos),
      textAlign: 'right'
    },
    {
      title: '售后状态',
      width: 180,
      textAlign: 'left',
      // fixed: 'right',
      bodyRender: (
        goodsInfo: Contains<{ operations: Array<Contains<{ code: string }>>; itemIdStr: string }>
      ) => renderRefundState(goodsInfo, orderInfo, reload, refundInfo)
    },
    {
      title: '状态',
      bodyRender: (
        goodsInfo: Pick<
          IGoodsInfo,
          | 'hasExpressed'
          | 'itemIdStr'
          | 'giftToBeReceivedNum'
          | 'latestDeliveryTermDesc'
          | 'latestDeliveryTimeDesc'
        >
      ): React.ReactNode => renderExpressState(orderExpressInfo, goodsInfo),
      width: 75,
      textAlign: 'right',
      fixed: 'right'
    }
  ]);
};
