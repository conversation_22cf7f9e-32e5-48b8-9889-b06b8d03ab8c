@import '~shared/style';

$border: 1px solid $border-color-base;

.child-list {
  border: $border;

  > table {
    width: 100%;
  }

  .child-order-item {
    border-bottom: $border;

    &:last-child {
      border-bottom: none;
    }
  }

  td {
    padding: 10px 15px;
    text-align: center;

    &.receiver-name {
      text-align: left;
    }
  }

  &__head {
    background-color: $background-color-base;
    line-height: 40px;
  }

  .row-alert {
    padding: 10px;
  }

  .row-alert-empty {
    margin-bottom: 20px;
    margin-top: 25px;
    color: $color-text-secondary;
    text-align: center;
  }

  .zent-alert-content {
    line-height: 1.5;
  }

  .child-table-orange {
    color: $color-warn;
    font-weight: bold;
  }
}

.col-10 {
  width: 10%;
}

.col-15 {
  width: 15%;
}

.col-20 {
  width: 20%;
}

.col-40 {
  width: 40%;
}

.send-pop {
  padding: 10px;

  .send-pop-group {
    margin-bottom: 10px;

    .group-label {
      display: inline-block;
      float: left;
      padding-top: 5px;
      width: 100px;
      line-height: 18px;
      text-align: right;
      font-size: 14px;
    }

    .group-control {
      margin-left: 110px;
      word-break: break-all;
    }

    p {
      margin-top: 10px;
      line-height: 14px;
      font-size: 12px;
      color: $color-text-light-primary;
      opacity: 0.6;
      cursor: pointer;

      &:hover {
        opacity: 1;
      }
    }

    .zent-radio-group {
      line-height: 26px;
    }

    .zent-select {
      width: 220px;
    }

    .zent-input-wrapper {
      width: 220px;
    }
  }

  & + .zent-pop-buttons {
    margin-bottom: 10px;
    text-align: center;

    .zent-btn-small {
      padding: 4px 12px;
      height: 32px;
    }
  }
}
