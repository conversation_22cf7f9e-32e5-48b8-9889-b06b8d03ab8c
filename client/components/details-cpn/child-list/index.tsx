import type { Contains } from 'definition/common';

import * as React from 'react';
import { <PERSON><PERSON>, Button } from 'zent';
import { get, assign, find } from 'lodash';
import { formatDatetime, setUrlDomain } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';

import createOrderCpm from 'components/create-order-cpm';
import { orderStateValue } from 'common/constants/models';
import openGiftExpressDialog from 'components/modals/gift-express';

import './style.scss';

const { CANCEL } = orderStateValue;
@createOrderCpm()
class ChildList extends React.Component<
  Contains<{
    reload: Function;
    itemInfo?: Array<Contains<{ itemIdStr: string; title: string }>>;
    mainOrderInfo?: Contains<{}>;
    childInfo?: Contains<{
      childInfoListDetail: Array<
        Contains<{
          orderNo?: string;
          createTime?: number;
          itemList: Array<Contains<{ itemId: string }>>;
        }>
      >;
    }>;
  }>
> {
  static defaultProps = {
    childInfo: []
  };

  get isGiftGroup() {
    return get(this.props, 'mainOrderInfo.activityType') === 63;
  }

  get bbsGiftUrl() {
    const url = this.isGiftGroup
      ? '/thread-670401-1-1.html'
      : '/forum.php?mod=viewthread&tid=198040&extra=page%3D3';
    return setUrlDomain(url, 'bbs');
  }

  handleClick = (childOrderInfo: Contains<{ itemList: Array<Contains<{ itemId: string }>> }>) => {
    const { reload, itemInfo = [], mainOrderInfo = {} } = this.props;

    const itemId = get(childOrderInfo, 'itemList[0].itemId');
    const giftItemInfo = find(itemInfo, { itemIdStr: itemId });
    openGiftExpressDialog({
      orderInfo: assign(
        {
          items: this.isGiftGroup ? [giftItemInfo] : itemInfo,
          parentOrderNo: mainOrderInfo.orderNo,
          isNewOrder: true
        },
        childOrderInfo
      ),
      callback: reload
    });
  };

  renderChildOrderBtn = (
    childOrderInfo: Contains<{
      haveExpressed?: boolean;
      itemList: Array<Contains<{ itemId: string }>>;
    }>
  ) => (
    <td>
      {!childOrderInfo.haveExpressed && (
        <Button onClick={() => this.handleClick(childOrderInfo)}>发货</Button>
      )}
    </td>
  );

  render() {
    const { childInfo, mainOrderInfo = {}, itemInfo = [] } = this.props;
    const childOrderList = childInfo?.childInfoListDetail ?? [];
    const isClose = mainOrderInfo.state === CANCEL;

    if (isClose && childOrderList.length === 0) {
      return null;
    }

    return (
      <div className="child-list info-block">
        <table>
          <thead className="child-list__head">
            <tr>
              <th className="col-20">子订单编号</th>
              {this.isGiftGroup && <th className="col-10">商品</th>}
              <th className="col-30">领取人</th>
              <th className="col-15">领取时间</th>
              <th className="col-10">订单状态</th>
              <th className="col-10">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={this.isGiftGroup ? 6 : 5} className="row-alert">
                <Alert type="warning">
                  该订单为送礼订单，需由买家将该礼单通过朋友圈、微信群、微信好友，分享给自己的小伙伴领取并填写收货信息。领取后生成子订单，可进行发货。请勿使用订单留言和备注收货地址的方式，私下将货物直接发给买家，以免钱货两空。
                  <BlankLink href={this.bbsGiftUrl}>查看“我要送礼”介绍</BlankLink>
                </Alert>
                {childOrderList.length === 0 && (
                  <p className="row-alert-empty">
                    暂无人领取礼单，超过15天未领取礼单需进行在线退款关闭订单
                  </p>
                )}
              </td>
            </tr>
            {childOrderList.length > 0 &&
              childOrderList.map(childOrderInfo => {
                const itemId = get(childOrderInfo, 'itemList[0].itemId');
                const giftItemInfo = (find(itemInfo, { itemIdStr: itemId }) || {}) as Contains<{
                  title?: string;
                }>;
                return (
                  <tr key={childOrderInfo.orderNo} className="child-order-item">
                    <td>{childOrderInfo.orderNo}</td>
                    {this.isGiftGroup && (
                      <td>
                        <span>{giftItemInfo.title}</span>
                      </td>
                    )}
                    <td className="receiver-name">
                      <div>
                        {[
                          childOrderInfo.province,
                          childOrderInfo.city,
                          childOrderInfo.county,
                          childOrderInfo.addressDetail,
                          childOrderInfo.userName,
                          childOrderInfo.tel,
                          childOrderInfo.postalCode
                        ].join(' ')}
                      </div>
                    </td>
                    <td>{formatDatetime((childOrderInfo.createTime ?? NaN) * 1000)}</td>
                    <td className="child-table-orange">
                      {childOrderInfo.haveExpressed ? '已发货' : '商品等待发货'}
                    </td>
                    {this.renderChildOrderBtn(childOrderInfo)}
                  </tr>
                );
              })}
          </tbody>
        </table>
      </div>
    );
  }
}

export default ChildList;
