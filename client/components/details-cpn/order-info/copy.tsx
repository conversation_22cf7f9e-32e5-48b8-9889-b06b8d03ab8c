import * as React from 'react';
import { Notify, Pop } from 'zent';
import { LinkButton } from '@youzan/retail-components';
import { onShowRiskWarningDialog } from '@youzan/order-domain-pc-components';

const { useCallback, useMemo, memo, useEffect } = React;

interface ICopy {
  text: string;
  children?: React.ReactNode;
  className?: string;
  orderInfo: any;
}

const isSupportCopy = document.queryCommandSupported('copy');

const Copy = (props: ICopy): JSX.Element => {
  const handleCopy = useCallback(
    ({ isShowSuccess = true, text = props.text }: { isShowSuccess?: boolean; text?: string }) => {
      const el = document.createElement('textarea');
      el.value = text;
      document.body.appendChild(el);
      el.select();
      document.execCommand('copy');
      document.body.removeChild(el);
      isShowSuccess && Notify.success('复制成功');
    },
    [props.text]
  );

  const handleListenCopy = useCallback(() => {
    const selectedText = window.getSelection()?.toString() || '';
    // 如果复制的内容包含省、市、区，则显示风险提示弹窗
    if (
      selectedText &&
      (selectedText.includes('省') || selectedText.includes('市') || selectedText.includes('区'))
    ) {
      beforeCopy({ isShowSuccess: false, text: selectedText });
    }
  }, [beforeCopy]);

  const initCopyListener = useCallback(() => {
    document.addEventListener('copy', handleListenCopy);
  }, [handleListenCopy]);

  const removeCopyListener = useCallback(() => {
    document.removeEventListener('copy', handleListenCopy);
  }, [handleListenCopy]);

  const beforeCopy = useCallback(
    data => {
      const toCopy = (isCopy = true) => {
        removeCopyListener();
        if (isCopy) {
          setTimeout(() => {
            handleCopy(data);
          }, 100);
        }
      };
      try {
        onShowRiskWarningDialog({
          orderInfo: props.orderInfo,
          onContinue: toCopy
        });
      } catch (err) {
        toCopy();
      }
    },
    [handleCopy, props.orderInfo]
  );

  useEffect(() => {
    initCopyListener();
    return () => {
      removeCopyListener();
    };
  }, [handleListenCopy, initCopyListener, removeCopyListener]);

  return useMemo(() => {
    if (isSupportCopy) {
      return (
        <LinkButton className={props.className} onClick={() => beforeCopy({ isShowSuccess: true })}>
          {props.children}
        </LinkButton>
      );
    }

    return (
      <Pop content="当前浏览器版本不支持复制，请升级浏览器" position="top-center">
        <LinkButton className={props.className} disabled>
          {props.children}
        </LinkButton>
      </Pop>
    );
  }, [beforeCopy, props.children, props.className]);
};

const areEqual = (prev: ICopy, next: ICopy) => {
  return (
    prev.children === next.children && prev.text === next.text && prev.className === next.className
  );
};

export default memo(Copy, areEqual);
