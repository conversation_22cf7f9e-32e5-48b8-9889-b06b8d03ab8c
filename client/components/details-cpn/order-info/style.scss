@import '~shared/style';

:local(.order-infos) {
  background-color: $background-color-base;
  border: 1px solid $background-color-base;
  display: flex;

  .info-item {
    flex: 1;
    padding: 12px;
    font-size: $font-size-small;
    color: $color-text-light-primary;

    &__title {
      font-size: $font-size-base;
      margin-bottom: 14px;
      color: $color-text-primary;
      font-weight: 700;
    }
  }

  .action {
    margin-left: 10px;
    font-size: $font-size-small;

    &__item,
    &__item[data-zv] {
      margin-left: 8px !important;
    }
  }

  .id-card-info {
    display: inline-block;
    vertical-align: top;

    &__show-btn {
      margin-left: 5px;
    }
  }

  .copy-button {
    font-size: $font-size-small;
  }
}

:local(.receiver-info-tip) {
  margin-bottom: 16px;
}

:local(.receiver-info-dialog) {
  .zent-form__control-group {
    margin-bottom: 15px;
  }

  // stylelint-disable-next-line
  &.disabled .zent-form__control-group {
    margin-bottom: 5px;
  }

  .zent-form__control-label {
    font-size: $font-size-base;
    color: $color-text-light-primary;
  }

  .zent-form__form-actions {
    text-align: right;
  }

  .receiver-info__region-select .zent-region-select__text[ignore] {
    vertical-align: top;

    &.is-placeholder {
      color: $color-text-light-primary;
    }
  }

  .receiver-info__raw-value {
    line-height: 30px;

    &.warn {
      color: $color-warn;
    }
  }

  .receiver-info__receiver-name,
  .receiver-info__receiver-tel {
    width: 200px;
  }

  .receiver-info__delivery-street {
    align-items: start;
  }
}

.store-shopping-address {
  width: 150px;
  display: inline-block;
  vertical-align: middle;
  line-height: 14px;
  height: 14px;
}

.receiverInfo-map {
  height: 400px;
}

.invoice-more-btn {
  font-size: 14px;
  color: #155bd4;
  cursor: pointer;
}

.amap-sug-result {
  z-index: 1060;
}
