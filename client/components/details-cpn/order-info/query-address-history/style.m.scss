@import '~shared/style';

.info {
  padding: 0 10px 12px 10px;
}
.title {
  font-weight: normal;
  color: $color-text-secondary;
  margin-bottom: 5px;
  display: flex;
  font-size: $font-size-small;
  padding: 0 12px;
  justify-content: space-between;
}

.infoDetail {
  padding: 12px 15px;
  border-radius: 5px;
  background-color: $color-white;
}

.item {
  margin-bottom: 12px;
}

.modifyAddress {
  display: inline-block;
}

:local(.addressHistoryButton) {
  font-size: $font-size-small !important;
}

:local(.queryAddressHistory) {
  :global(.zent-dialog-r-body) {
    background-color: $background-color-base;
  }

  height: auto;
  margin-top: 0;
  padding-bottom: 0 !important;
}
