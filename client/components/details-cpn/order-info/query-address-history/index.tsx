import React, { useState } from 'react';
import { Dialog, Notify } from 'zent';
import cx from 'classnames';
import formatDate from '@youzan/utils/date/formatDate';
import { LinkButton } from '@youzan/retail-components';
import { IHistroyAddress } from 'definition/order-info';
import { getBlurredInfoRequest } from './api';
import s from './style.m.scss';

interface IProps {
  className?: string;
  orderHistoryAddress?: IHistroyAddress[];
  orderNo: string;
}

const ROLE_NAME_MAP: Record<string, any> = {
  seller: '店铺员工',
  buyer: '买家'
};

// 修改收货地址
const QueryAddressHistory = ({ className, orderNo, orderHistoryAddress = [] }: IProps) => {
  const [visible, setVisible] = useState<boolean>(false);
  const triggerDialog = (visible: boolean) => {
    setVisible(visible);
  };
  // 排列顺序按照修改时间，越晚修改，顺序越前
  const historyList = [...orderHistoryAddress].sort((a, b) => {
    return b.addressModifyTime - a.addressModifyTime;
  });
  // 点击查看历史 需要借助脱敏上报日志
  const loggerBlurred = () => {
    const searchKey = [
      {
        path: 'orderAddressInfo',
        key: 'modifyAddressDetail'
      }
    ];
    getBlurredInfoRequest({
      orderNo,
      searchKey
    }).catch(err => Notify.error(err));
  };

  // render
  const renderForm = (historyInfo: Record<string, any>) => {
    const { address, addressModifyTime, role, receiverName, receiverTel } = historyInfo;
    const { district, city, province, detail } = address;
    const addressText = `${province}/${city}/${district}`;
    return (
      <div className={s.info} key={historyInfo.addressModifyTime}>
        <div className={s.title}>
          <span>{formatDate(addressModifyTime, 'YYYY-MM-DD HH:mm:ss')}</span>
          <span>操作人：{ROLE_NAME_MAP[role]}</span>
        </div>
        <div className={s.infoDetail}>
          <div className={s.item}>收货人：{receiverName}</div>
          <div className={s.item}>手机号：{receiverTel}</div>
          <div className={s.item}>所在地区：{addressText}</div>
          <div className={s.item}>详细地址：{detail}</div>
        </div>
      </div>
    );
  };

  return (
    <>
      <LinkButton
        className={cx(s.addressHistoryButton, className)}
        onClick={() => {
          loggerBlurred();
          setVisible(true);
        }}
      >
        变更记录
      </LinkButton>
      <Dialog
        visible={visible}
        className={s.queryAddressHistory}
        onClose={() => triggerDialog(false)}
        title="收货人信息变更记录"
      >
        {historyList.map(historyInfo => {
          return renderForm(historyInfo);
        })}
      </Dialog>
    </>
  );
};

export default QueryAddressHistory;
