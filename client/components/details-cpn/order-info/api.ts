import type { IOrderAddressInfo, IApplyChangeDispatchParams } from 'definition/order-info';

import { request } from '@youzan/retail-utils';

export const fetchLogistics = (data: Pick<IOrderAddressInfo, 'orderNo'>): Promise<any> => {
  return request({
    url: '/youzan.retail.trademanager.delivery.logistics/1.0.0/get',
    data
  });
};

export const updateLogistics = (
  data: Pick<
    IOrderAddressInfo,
    | 'orderNo'
    | 'deliveryCity'
    | 'deliveryProvince'
    | 'deliveryDistrict'
    | 'deliveryStreet'
    | 'receiverName'
    | 'receiverTel'
  > &
    Pick<Partial<IOrderAddressInfo>, 'deliveryCountry' | 'deliveryPostalCode'>
): Promise<any> => {
  return request({
    url: '/youzan.retail.trademanager.delivery.logistics/1.0.0/update',
    method: 'post',
    data
  });
};

// 查询自提点列表
export const getSelfFetchPointList = (data: Pick<IOrderAddressInfo, 'orderNo'>): Promise<any> => {
  return request({
    url: 'youzan.retail.supply.selffetchpoint.query/1.0.0',
    data
  });
};

// 查询可选自提时间
export const getShopBusinessHours = (data: Pick<IOrderAddressInfo, 'orderNo'>): Promise<any> => {
  return request({
    url: 'youzan.retail.business.time.query/1.0.1',
    data
  });
};

// 到店自提查看订单信息
export const getSelffetchLogistics = (data: Pick<IOrderAddressInfo, 'orderNo'>): Promise<any> => {
  return request({
    url: 'youzan.retail.selffetch.get.logistics/1.0.0',
    data
  });
};

// 到店自提修改订单信息
export const updateSelffetchLogistics = (
  data: Pick<IOrderAddressInfo, 'orderNo' | 'receiverName' | 'receiverTel' | 'selfFetchId'> &
    Pick<Partial<IOrderAddressInfo>, 'deliveryCountry' | 'deliveryPostalCode'>
): Promise<any> => {
  return request({
    url: 'youzan.retail.selffetch.update.logistics/1.0.0',
    method: 'post',
    data
  });
};

// 同城配送查看订单信息
export const getLocaldeliveryLogistics = (
  data: Pick<IOrderAddressInfo, 'orderNo'>
): Promise<any> => {
  return request({
    url: 'youzan.retail.localdelivery.get.logistics/1.0.0',
    data
  });
};

// 同城配送修改订单信息
export const updateLocaldeliveryLogistics = (
  data: Pick<
    IOrderAddressInfo,
    | 'orderNo'
    | 'deliveryCity'
    | 'deliveryProvince'
    | 'deliveryDistrict'
    | 'deliveryStreet'
    | 'receiverName'
    | 'receiverTel'
  > &
    Pick<Partial<IOrderAddressInfo>, 'deliveryCountry' | 'deliveryPostalCode'>
): Promise<any> => {
  return request({
    url: 'youzan.retail.localdelivery.update.logistics/1.0.0',
    method: 'post',
    data
  });
};

/**
 *
 * 修改收货地址推荐改派门店/仓库
 */
export function recommendWarehouse(data: IApplyChangeDispatchParams): Promise<any> {
  return request({
    url: 'youzan.retail.modifyAddress.get.recommendWarehouse/1.0.0',
    method: 'POST',
    data
  });
}
