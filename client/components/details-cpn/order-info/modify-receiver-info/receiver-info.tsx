import * as React from 'react';
import { Dialog, Notify, BlockLoading, <PERSON><PERSON>, But<PERSON> } from 'zent';
import { Select, Form } from '@zent/compat';
import cx from 'classnames';
import { omit } from 'lodash';
import { RegionSelect, SuperButton, ReactAmap, Space } from '@youzan/react-components';
import { formatDate, setUrlDomain } from '@youzan/retail-utils';
import type { IApplyChangeDispatchParams, IOrderAddressInfo } from 'definition/order-info';
import { ExpressType, IGeoLocation, IRecommendType } from 'common/constants/common';

import { isRetailSingleStore } from '@youzan/utils-shop';
import {
  applyChangeDispatch,
  queryGoodsStockInWarehouse
} from 'components/opt-components/order-redispatch/api';
import {
  fetchLogistics,
  updateLogistics,
  getSelfFetchPointList,
  getShopBusinessHours,
  getSelffetchLogistics,
  updateSelffetchLogistics,
  getLocaldeliveryLogistics,
  updateLocaldeliveryLogistics,
  recommendWarehouse
} from '../api';
import {
  addAreaRegionsToMap,
  isPointInAreaRegions,
  formatAddressObject,
  gaodeToBaidu,
  baiduToGaode,
  initMapAutoComplete
} from './helper';
import style from '../style.scss';

const { useState, useEffect, useRef, useCallback } = React;
const { createForm, Field, InputField, getControlGroup, FormSelectField } = Form;
const { closeDialog } = Dialog;

const DialogId = 'receiverInfoDialogId';
const ModifyHistoryUrl = setUrlDomain('/v4/setting/newoperate', 'store');
const FieldWidth = 260;

const isPhone = (_: never, value: string) => {
  // 大陆电话号码
  const isCNPhone = /^((\+86)|(86))?(1)\d{10}$/.test(value);
  // 大陆手机号
  const isCNMobile = /^0[0-9\-]{10,13}$/.test(value);
  // 香港手机号，5、6、8、9 开头的八位数字(包含澳门)，国号 852
  const isHKMobile = /^((\+852)|(852))?(5|6|8|9)\d{7}$/.test(value);
  // 台湾手机号，09 开头的 10 位数字，若加上 886 国号则不加 0
  const isTWMobile = /^((\+886)|(886)|(0))?9\d{2}-?\d{3}-?\d{3}$/.test(value);
  return isCNPhone || isCNMobile || isHKMobile || isTWMobile;
};

function isGeoEqual(reg1: IGeoLocation, reg2: IGeoLocation) {
  return reg1.lon === reg2.lon && reg1.lat === reg2.lat;
}
function getDayText(date: Date) {
  return `${(date.getMonth() + 1).toString().padStart(2, '0')} - ${date
    .getDate()
    .toString()
    .padStart(2, '0')}`;
}

function getTimeText(startDate: Date, endDate: Date) {
  return `${startDate.getHours().toString().padStart(2, '0')}:${startDate
    .getMinutes()
    .toString()
    .padStart(2, '0')} - ${endDate.getHours().toString().padStart(2, '0')}:${endDate
    .getMinutes()
    .toString()
    .padStart(2, '0')}`;
}

const RawValue = getControlGroup(({ value }) => (
  <div className="receiver-info__raw-value">{value}</div>
));

function AppointmentTimeFieldCpn(props: {
  appointmentTimeLabel: string;
  appointmentTimeData: Record<string, any>;
  onChange: Function;
  displayError: boolean;
  isDirty: boolean;
  error: string;
}) {
  const { appointmentTimeLabel, appointmentTimeData, onChange, displayError, isDirty, error } =
    props;
  const dayList = Object.keys(appointmentTimeData).map(key => appointmentTimeData[key]);
  const [timeList, setTimeList] = useState([]);

  function onDayChange(
    e: any,
    selectedItem: { value: any; timeList: React.SetStateAction<never[]> }
  ) {
    const newValue = {
      appointmentDay: selectedItem.value
    };
    setTimeList(selectedItem.timeList);
    onChange(newValue, { merge: true });
  }

  function onTimeChange(e: any, selectedItem: { value: any }) {
    const newValue = {
      appointmentTime: selectedItem.value
    };
    onChange(newValue, { merge: true });
  }

  const showError = displayError === undefined ? isDirty && error !== null : displayError;

  return (
    <div className={cx('zent-form__control-group', { 'has-error': showError })}>
      <label className="zent-form__control-label">修改{appointmentTimeLabel}时间：</label>
      <div className="zent-form__controls">
        <div style={{ display: 'flex' }}>
          <Select
            width={120}
            className="appointmentDay"
            data={dayList}
            optionValue="value"
            optionText="text"
            onChange={onDayChange}
          />
          <Select
            width={200}
            className="appointmentTime"
            data={timeList}
            optionValue="value"
            optionText="text"
            onChange={onTimeChange}
          />
        </div>
        {showError && <p className="zent-form__error-desc">{props.error}</p>}
        <p className="zent-form__error-desc">仅需要修改{appointmentTimeLabel}时间时填写</p>
      </div>
    </div>
  );
}

let mapCheckInterval: number;

export const ReceiverInfo = createForm()(
  React.forwardRef(
    (props: {
      handleSubmit: Function;
      orderNo: string;
      reload: () => void;
      expressType: ExpressType;
      fulfillNo: string;
    }) => {
      const deliveryMapRef = useRef();
      const { handleSubmit, orderNo, reload, expressType, fulfillNo } = props;

      const [logisticsInfo, setLogisticsInfo] = useState<
        IOrderAddressInfo & {
          allowModify: boolean;
          forbidModifyReason: string;
          isFirstModify: boolean;
          lastModifyTime: number;
          lat: number;
          lon: number;
          appointmentTime: string;
        }
      >();
      const {
        allowModify = false,
        forbidModifyReason = '',
        isFirstModify = true,
        lastModifyTime,
        receiverName = '-',
        receiverTel = '-',
        deliveryProvince = '-',
        deliveryCity = '-',
        deliveryDistrict = '-',
        deliveryStreet = '-',
        selfFetchId,
        lat,
        lon
      } = logisticsInfo || {};
      const [deliveryRegions, setDeliveryRegions] = useState({
        regions: {
          province: deliveryProvince,
          city: deliveryCity,
          area: deliveryDistrict
        },
        value: ''
      });
      const [submitting, setSubmitting] = useState(false);
      const [loading, setLoading] = useState(false);

      const [selfFetchPointList, setSelfFetchPointList] = useState([]);
      const [selectedSelfFetchPoint, setSelectedSelfFetchPoint] = useState(null);

      const [originalAppointmentTimeText, setOriginalAppointmentTimeText] = useState('-');

      const [nlat, setNlat] = useState();
      const [nlon, setNlon] = useState();
      const [geoLocation, setGeoLocation] = useState<{ lon: number; lat: number }>();
      const [originAddress, setOriginAddress] = useState('');

      const [deliveryDistanceError, setDeliveryDistanceError] = useState(false);

      const onReactAmapChange = useCallback(
        (lnglat, address, data, logisticsData = logisticsInfo) => {
          // 校验距离
          const { areaModelList = [] } = logisticsData;
          const inRegion = isPointInAreaRegions([lnglat.lng, lnglat.lat], areaModelList);
          setDeliveryDistanceError(!inRegion);

          setLogisticsInfo({
            ...logisticsData,
            lng: lnglat.lng,
            lat: lnglat.lat,
            deliveryStreet: data.address
          });
          setDeliveryRegions({
            regions: {
              province: data.province,
              city: data.city,
              area: data.district
            },
            value: data.adcode
          });
          setNlat(lnglat.lat);
          setNlon(lnglat.lng);
        },
        [logisticsInfo]
      );

      useEffect(() => {
        if (orderNo) {
          setLoading(true);

          let getApi;
          if (expressType === ExpressType.City) {
            getApi = getLocaldeliveryLogistics;
          } else if (expressType === ExpressType.SelfFetch) {
            getApi = getSelffetchLogistics;
          } else {
            getApi = fetchLogistics;
          }

          getApi({ orderNo })
            .then(res => {
              if (expressType === ExpressType.City) {
                // 初始化送达时间
                const {
                  appointmentTime,
                  deliveryProvince,
                  deliveryCity,
                  deliveryDistrict,
                  deliveryStreet
                } = res;
                if (!appointmentTime) {
                  setOriginalAppointmentTimeText('尽快送达');
                } else {
                  const [startTimeStr, endTimeStr] = appointmentTime.split(',');
                  const startTime = Number(startTimeStr);
                  const endTime = Number(endTimeStr);
                  if (startTime && endTime) {
                    const startDate = new Date(startTime);
                    const endDate = new Date(endTime);
                    setOriginalAppointmentTimeText(
                      `${getDayText(startDate)} ${getTimeText(startDate, endDate)}`
                    );
                  }
                }
                // 初始化地图信息
                clearInterval(mapCheckInterval);
                mapCheckInterval = setInterval(() => {
                  const { areaModelList = [] } = res;
                  if (
                    deliveryMapRef &&
                    deliveryMapRef.current &&
                    deliveryMapRef.current.map &&
                    window.AMap
                  ) {
                    clearInterval(mapCheckInterval);
                    const {
                      current: { map }
                    } = deliveryMapRef;
                    initMapAutoComplete(map, (lnglat, addressData) => {
                      const { addressComponent, formattedAddress } = addressData;
                      onReactAmapChange(
                        lnglat,
                        formattedAddress,
                        formatAddressObject(addressComponent),
                        res
                      );
                    });
                    addAreaRegionsToMap(map, areaModelList, (lnglat, addressData) => {
                      const $map = deliveryMapRef.current.map;
                      const coord = [lnglat.lng, lnglat.lat];
                      const { addressComponent, formattedAddress } = addressData;
                      $map.marker.setPosition(coord);
                      $map.infoWindow.setPosition(coord);
                      $map.infoWindow.setContent(formattedAddress);
                      onReactAmapChange(
                        lnglat,
                        formattedAddress,
                        formatAddressObject(addressComponent),
                        res
                      );
                    });
                  }
                }, 200);
                setDeliveryRegions({
                  regions: {
                    province: deliveryProvince,
                    city: deliveryCity,
                    area: deliveryDistrict
                  },
                  value: ''
                });
                setOriginAddress(
                  `${deliveryProvince}${deliveryCity}${deliveryDistrict}${deliveryStreet}`
                );
              } else if (expressType === ExpressType.SelfFetch) {
                // 初始化自提时间
                const { appointmentTime } = res;
                if (!appointmentTime) {
                  setOriginalAppointmentTimeText('尽快送达');
                } else {
                  const [startTimeStr, endTimeStr] = appointmentTime.split(',');
                  const startTime = Number(startTimeStr);
                  const endTime = Number(endTimeStr);
                  if (startTime && endTime) {
                    const startDate = new Date(startTime);
                    const endDate = new Date(endTime);
                    setOriginalAppointmentTimeText(
                      `${getDayText(startDate)} ${getTimeText(startDate, endDate)}`
                    );
                  }
                }
              } else {
                setDeliveryRegions({
                  regions: {
                    province: res.deliveryProvince,
                    city: res.deliveryCity,
                    area: res.deliveryDistrict
                  },
                  value: ''
                });
              }
              setLogisticsInfo(res);
              setGeoLocation({ lat: res.lat, lon: res.lon });
              setLoading(false);
            })
            .catch(error => {
              Notify.error(error.msg || '获取收货信息失败');
            });

          if (expressType === ExpressType.SelfFetch) {
            getSelfFetchPointList({ orderNo }).then(data => {
              const { selfFetchPointList: pointList = [] } = data;
              setSelfFetchPointList(pointList);
            });
          }
        }
      }, [orderNo, expressType]);

      const [appointmentTimeData, setAppointmentTimeData] = useState([]);
      useEffect(() => {
        getShopBusinessHours({ orderNo }).then(data => {
          const { businessTimes = [] } = data;
          const timeList = businessTimes
            .filter((v: { startTime: any; endTime: any }) => v.startTime && v.endTime)
            .map((v: { startTime: any; endTime: any }) => {
              const { startTime, endTime } = v;
              const startDate = new Date(startTime);
              const endDate = new Date(endTime);

              return {
                day: {
                  text: getDayText(startDate),
                  value: startTime
                },
                time: {
                  text: getTimeText(startDate, endDate),
                  value: `${startTime},${endTime}`
                }
              };
            });
          // setAppointmentTimeList(appointmentTimeList);
          const timeData = {};
          timeList.forEach((v: { day: { text: any }; time: any }) => {
            const key = v.day.text;
            if (!timeData[key]) {
              timeData[key] = { ...v.day, timeList: [v.time] };
            } else {
              timeData[key].timeList.push(v.time);
            }
          });
          setAppointmentTimeData(timeData);
        });
      }, [orderNo]);

      const disabled = !allowModify || !isFirstModify;
      const initDeliveryRegions = `${deliveryProvince} / ${deliveryCity} / ${deliveryDistrict}`;

      const onRegionsChange = (data: {
        province: string;
        city: string;
        area: string;
        county_id: string;
      }): void => {
        setDeliveryRegions({
          regions: data,
          value: data.county_id
        });
      };

      // eslint-disable-next-line react-hooks/exhaustive-deps
      const beforeUpdateLocalDelivery = async (
        data: IApplyChangeDispatchParams,
        callback: (recommendWarehouseId?: number) => Promise<any>
      ) => {
        const dialogId = 'recommend-delivery_dialog';
        recommendWarehouse(data)
          .then(res => {
            // res.recommendType = 2;
            // res.recommendWarehouse = {
            //   recommendWarehouseId: 1,
            //   recommendWarehouseName: '推荐门店',
            //   recommendWarehouseDistance: '1.11'
            // };

            // res.currentWarehouse = {
            //   recommendWarehouseId: 2,
            //   currentWarehouseName: '当前门店',
            //   currentWarehouseDistance: '22.11'
            // };

            const { recommendType, recommendWarehouse = {}, currentWarehouse = {} } = res;
            const recommendWarehouseName = recommendWarehouse.warehouseName;
            const recommendWarehouseDistance = recommendWarehouse.warehouseDistance;
            const recommendWarehouseId = recommendWarehouse.warehouseId;

            const currentWarehouseName = currentWarehouse.warehouseName;
            const currentWarehouseDistance = currentWarehouse.warehouseDistance;

            if (
              [IRecommendType.None, IRecommendType.NoExceedAndNoReassignment].includes(
                recommendType
              )
            ) {
              return callback();
            }

            if ([IRecommendType.ExceedAndNoReassignment].includes(recommendType)) {
              Dialog.openDialog({
                dialogId,
                maskClosable: false,
                style: { width: '100px' },
                title: '提示信息',
                children: (
                  <pre style={{ whiteSpace: 'pre-wrap' }}>
                    {`修改后的收货地址，已超出"${
                      currentWarehouseName || '门店'
                    }"的配送范围，是否继续修改?`}
                  </pre>
                ),
                onClose: () => {
                  Dialog.closeDialog(dialogId);
                },
                footer: (
                  <footer>
                    <Button
                      onClick={() => {
                        Dialog.closeDialog(dialogId);
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        callback();
                        Dialog.closeDialog(dialogId);
                      }}
                    >
                      确定
                    </Button>
                  </footer>
                )
              });
            }

            if (
              [
                IRecommendType.ExceedAndReassignment,
                IRecommendType.NoExceedAndReassignment
              ].includes(recommendType)
            ) {
              let info = '';
              if (IRecommendType.ExceedAndReassignment === recommendType) {
                info = `修改后的收货地址,已超出了“${
                  currentWarehouseName || '门店'
                }”的配送范围,距离“${
                  recommendWarehouseName || '门店'
                }”更近,是否确定改派到更近的门店?`;
              }
              if (IRecommendType.NoExceedAndReassignment === recommendType) {
                info = `修改后的收货地址,距离“${
                  recommendWarehouseName || '门店'
                }”更近,是否要改派到更近的门店?
      直线距离“${currentWarehouseName || '门店'}”: ${currentWarehouseDistance}km
      直线距离“${recommendWarehouseName || '门店'}”: ${recommendWarehouseDistance}km
                `;
              }
              Dialog.openDialog({
                dialogId,
                maskClosable: false,
                title: '提示信息',
                style: { width: '100px' },
                children: <pre style={{ whiteSpace: 'pre-wrap' }}> {info}</pre>,
                onClose: () => {
                  Dialog.closeDialog(dialogId);
                },
                footer: (
                  <footer>
                    <Button
                      onClick={() => {
                        callback();
                        Dialog.closeDialog(dialogId);
                      }}
                    >
                      直接保存
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        callback(recommendWarehouseId).then(() => {
                          Dialog.closeDialog(dialogId);
                        });
                      }}
                    >
                      修改并改派
                    </Button>
                  </footer>
                )
              });
            }
          })
          .catch((err: { message: any }) => {
            Notify.error((err && err.message) || '收货信息修改失败，请重新操作');
          })
          .finally(() => {
            setSubmitting(false);
          });
      };

      const checkChangedPoi = useCallback(async () => {
        // 修改订单改自提点（即自提订单的改派）需要校验库存：https://xiaolv.qima-inc.com/#/demand/search?show=true&ids=160415
        const preSelfPointKdtId = selfFetchPointList.find(
          item => item.selfFetchId === selfFetchId
        )?.selfPointKdtId;
        const warehouseId = selectedSelfFetchPoint?.selfPointKdtId;
        if (warehouseId && preSelfPointKdtId !== warehouseId) {
          const result = await queryGoodsStockInWarehouse({
            orderNo,
            warehouseId,
            fulfillNo
          }).catch(err => {
            Notify.error((err && err.message) || '查询改派门店/仓信息失败');
            setSubmitting(false);
          });
          const { warnTips = '' } = result || {};
          if (warnTips) {
            const dialogId = 'check_changed_poi_dialog';
            const resetDialog = () => {
              Dialog.closeDialog(dialogId);
              setSubmitting(false);
            };
            return new Promise(resolve => {
              Dialog.openDialog({
                dialogId,
                title: '提示信息',
                children: <div>{warnTips}</div>,
                footer: (
                  <>
                    <Button
                      onClick={() => {
                        resetDialog();
                        resolve(false);
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        resolve(true);
                        Dialog.closeDialog(dialogId);
                      }}
                    >
                      确定
                    </Button>
                  </>
                ),
                onClose() {
                  resetDialog();
                  resolve(false);
                }
              });
            });
          }
        }
        return Promise.resolve(true);
      }, [fulfillNo, orderNo, selectedSelfFetchPoint, selfFetchId, selfFetchPointList]);

      const update = useCallback(
        async ({ updateApi, data }) => {
          const status = await checkChangedPoi();
          return status
            ? updateApi(data)
                .then(() => {
                  Notify.success('收货信息修改成功');
                  closeDialog(DialogId);
                  reload && reload();
                })
                .catch((err: { message: any }) => {
                  Notify.error((err && err.message) || '收货信息修改失败，请重新操作');
                })
                .finally(() => {
                  setSubmitting(false);
                })
            : Promise.resolve();
        },
        [reload, checkChangedPoi]
      );

      const submit = useCallback(
        async (
          values: Pick<IOrderAddressInfo, 'deliveryStreet' | 'receiverName' | 'receiverTel'> &
            Record<string, unknown>
        ) => {
          const { province, city, area } = deliveryRegions.regions;
          if (!province || !city || !area) {
            Notify.error('请完整填写省/市/区联系地址');
            return;
          }

          setSubmitting(true);

          let updateApi = updateLogistics; // 默认快递
          let data = {
            orderNo,
            deliveryProvince: province,
            deliveryCity: city,
            deliveryDistrict: area,
            ...values
          };

          if (expressType === ExpressType.City) {
            // 同城配送
            updateApi = updateLocaldeliveryLogistics;
            const { appointment = {}, deliveryStreet = '', deliveryStreetNo = '' } = values;
            delete values.appointment;
            delete values.originalAppointmentTimeText;
            data = {
              orderNo,
              expressType,
              deliveryProvince: province,
              deliveryCity: city,
              deliveryDistrict: area,
              appointmentTime: appointment.appointmentTime || logisticsInfo.appointmentTime,
              deliveryStreet: `${deliveryStreet}${deliveryStreetNo}`, // 交易只有详细地址字段，所以将门牌号拼接在详细地址后面。
              ...omit(values, ['deliveryStreet', 'deliveryStreetNo'])
            };

            const { lat, lon } = logisticsInfo;
            const [defaultLon, defaultLat] = baiduToGaode([+lon, +lat]);
            const newLat = nlat || defaultLat;
            const newLon = nlon || defaultLon;

            const newAddress = `${province}${city}${area}${deliveryStreet}${deliveryStreetNo}`;
            // 修改了地址，但是经纬度为空
            const isAddressChangedWithoutPoi =
              !newAddress.includes(originAddress) && !(nlat && nlon);

            if (!(newLat && newLon) || isAddressChangedWithoutPoi) {
              Notify.error('请在详细地址地图下选点');
              setSubmitting(false);
              return;
            }
            const [baiDuLon, baiduLat] = gaodeToBaidu([+newLon, +newLat]);
            data.lat = baiduLat;
            data.lon = baiDuLon;
            // 如果经经纬度发生改变,触发推荐最近门店
            if (geoLocation && !isGeoEqual(geoLocation, logisticsInfo) && !isRetailSingleStore) {
              beforeUpdateLocalDelivery(
                {
                  ...data,
                  orderNo,
                  areaCode: deliveryRegions.value
                },
                (reDispatchWarehouseId?: number) =>
                  update({ updateApi, data: { ...data, reDispatchWarehouseId } })
              );
            } else {
              update({ updateApi, data });
            }
          } else if (expressType === ExpressType.SelfFetch) {
            // 上门自提
            updateApi = updateSelffetchLogistics;
            const { appointment = {}, selfFetchId } = values;
            delete values.appointment;
            delete values.originalAppointmentTimeText;
            data = {
              orderNo,
              expressType,
              appointmentTime: appointment.appointmentTime || logisticsInfo.appointmentTime,
              ...values
            };

            const selfFetchPoint = selfFetchPointList.find(v => v.selfFetchId === selfFetchId);
            if (selfFetchPoint) {
              data.selfPointKdtId = selfFetchPoint.selfPointKdtId;
              data.selfFetchName = selfFetchPoint.name;
            }
            update({ updateApi, data });
          } else {
            update({ updateApi, data });
          }
        },
        [
          deliveryRegions.regions,
          deliveryRegions.value,
          orderNo,
          expressType,
          logisticsInfo,
          originAddress,
          nlat,
          nlon,
          geoLocation,
          beforeUpdateLocalDelivery,
          update,
          selfFetchPointList
        ]
      );

      const receiverNameLabel = expressType === ExpressType.SelfFetch ? '提货人' : '收货人';
      const appointmentTimeLabel = expressType === ExpressType.SelfFetch ? '提货' : '送达';
      const [mapQuery, setMapQuery] = useState('');

      return (
        <BlockLoading loading={loading}>
          {logisticsInfo && (
            <>
              <Alert className={style['receiver-info-tip']}>
                <span>请务必在买家知情且同意下修改。</span>
                <SuperButton mode="link" href={ModifyHistoryUrl} isBlank>
                  查看修改记录
                </SuperButton>
              </Alert>
              <Form
                horizontal
                onSubmit={handleSubmit(submit)}
                className={cx(style['receiver-info-dialog'], { disabled })}
              >
                <Field
                  required
                  name="receiverName"
                  type="text"
                  value={receiverName}
                  label={receiverNameLabel}
                  placeholder={`请填写${receiverNameLabel}`}
                  width={FieldWidth}
                  component={disabled ? RawValue : InputField}
                  validations={{ required: true }}
                  validationErrors={{ required: '收货人姓名不能为空' }}
                />
                <Field
                  required
                  name="receiverTel"
                  type="text"
                  label="联系电话："
                  placeholder={`请填写${receiverNameLabel}联系电话`}
                  width={FieldWidth}
                  value={receiverTel}
                  component={disabled ? RawValue : InputField}
                  validations={{ required: true, isPhone }}
                  validationErrors={{
                    required: `${receiverNameLabel}联系电话不能为空`,
                    isPhone: '请填写正确的座机号或手机号'
                  }}
                />
                {expressType !== ExpressType.SelfFetch && (
                  <div className="zent-form__control-group">
                    <label className="zent-form__control-label">联系地址：</label>
                    <div className="zent-form__controls">
                      {!disabled ? (
                        <RegionSelect
                          className="receiver-info__region-select"
                          placeholder={initDeliveryRegions}
                          value={deliveryRegions.value}
                          onChange={onRegionsChange}
                        />
                      ) : (
                        <div className="receiver-info__raw-value">{initDeliveryRegions}</div>
                      )}
                    </div>
                  </div>
                )}

                <Space className="receiver-info__delivery-street">
                  {expressType !== ExpressType.SelfFetch && (
                    <Field
                      required
                      name="deliveryStreet"
                      type="text"
                      label="详细地址："
                      placeholder="请填写详细地址"
                      id="amap-address-input"
                      width={FieldWidth}
                      value={deliveryStreet}
                      component={disabled ? RawValue : InputField}
                      validations={{
                        required: true,
                        distance: () => !deliveryDistanceError || !isRetailSingleStore
                      }}
                      validationErrors={{
                        required: '详细地址不能为空',
                        distance: '选点需在图示配送范围以内'
                      }}
                      onChange={e => {
                        setLogisticsInfo({
                          ...logisticsInfo,
                          deliveryStreet: e.target.value
                        });
                      }}
                    />
                  )}

                  {!disabled && expressType === ExpressType.City && (
                    <SuperButton
                      onClick={() => {
                        if (logisticsInfo) {
                          const { deliveryStreet } = logisticsInfo;

                          /**
                           * 搜索地图时, 拼上"联系地址"的省市区
                           * @see https://jira.qima-inc.com/browse/ONLINE-587691
                           */
                          const regionsStr = deliveryRegions.regions
                            ? [
                                deliveryRegions.regions.province,
                                deliveryRegions.regions.city,
                                deliveryRegions.regions.area
                              ]
                                .filter(Boolean)
                                .join('')
                            : '';

                          setMapQuery(`${regionsStr}${deliveryStreet}`);
                        }
                      }}
                    >
                      搜索地图
                    </SuperButton>
                  )}
                </Space>

                {!disabled && expressType === ExpressType.City && (
                  <Field
                    name="deliveryStreetNo"
                    type="text"
                    placeholder="请填写门牌号"
                    width={FieldWidth}
                    component={InputField}
                    helpDesc={
                      !deliveryDistanceError && isRetailSingleStore && '选点需在图示配送范围以内'
                    }
                  />
                )}

                {!disabled && expressType === ExpressType.City && (
                  <div className="zent-form__control-group">
                    <label className="zent-form__control-label" />
                    <div className="zent-form__controls">
                      <div style={{ display: 'flex' }}>
                        <ReactAmap
                          ref={deliveryMapRef}
                          className="receiverInfo-map"
                          width="500px"
                          height="400px"
                          defaultValue={baiduToGaode([+lon, +lat])}
                          query={mapQuery}
                          onChange={onReactAmapChange}
                          mapConfig={{
                            zoom: 12
                          }}
                          version="1.4.15"
                          apiKey="decef01a1628430d5159a8cd10dec3da"
                          // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
                          proxyHost="lbsmap.youzan.com"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {expressType === ExpressType.SelfFetch && (
                  <FormSelectField
                    name="selfFetchId"
                    label="自提点："
                    value={selfFetchId}
                    width={FieldWidth}
                    data={selfFetchPointList.map(v => ({ value: v.selfFetchId, text: v.name }))}
                    required
                    onChange={(id: any) => {
                      setSelectedSelfFetchPoint(selfFetchPointList.find(v => v.selfFetchId === id));
                    }}
                    validations={{ required: true }}
                    validationErrors={{ required: '请选择类型' }}
                  />
                )}
                {expressType === ExpressType.SelfFetch && (
                  <Field
                    required
                    name="selfFetchIdAddress"
                    type="text"
                    value={(() => {
                      const selfFetchPoint =
                        selectedSelfFetchPoint ||
                        selfFetchPointList.find(v => v.selfFetchId === selfFetchId);
                      if (selfFetchPoint) {
                        const { province, city, district, address } = selfFetchPoint;
                        return `${province}-${city}-${district}-${address}`;
                      }
                      return '-';
                    })()}
                    label="自提点地址："
                    component={RawValue}
                    validations={{ required: true }}
                  />
                )}
                {(expressType === ExpressType.City || expressType === ExpressType.SelfFetch) && (
                  <Field
                    required
                    name="originalAppointmentTimeText"
                    type="text"
                    value={originalAppointmentTimeText}
                    label={`${appointmentTimeLabel}时间：`}
                    component={RawValue}
                  />
                )}
                {!disabled &&
                  (expressType === ExpressType.City || expressType === ExpressType.SelfFetch) &&
                  logisticsInfo.appointmentTime && (
                    <Field
                      name="appointment"
                      value={{}}
                      appointmentTimeData={appointmentTimeData}
                      component={AppointmentTimeFieldCpn}
                      appointmentTimeLabel={appointmentTimeLabel}
                      validationErrors={{ validTime: `请选择${appointmentTimeLabel}时间` }}
                    />
                  )}
                {disabled && (
                  <div className="zent-form__control-group">
                    {/* eslint-disable-next-line jsx-a11y/control-has-associated-label */}
                    <label className="zent-form__control-label" />
                    <div className="zent-form__controls">
                      <p className="receiver-info__raw-value warn">
                        {!isFirstModify && !forbidModifyReason && (
                          <span>
                            {formatDate(lastModifyTime!, 'YYYY-MM-DD')} 已修改，暂不支持二次修改
                          </span>
                        )}
                        {!!forbidModifyReason && <span>{forbidModifyReason}</span>}
                      </p>
                    </div>
                  </div>
                )}
                {!disabled && (
                  <div className="zent-form__form-actions">
                    <SuperButton onClick={() => closeDialog(DialogId)}>取消</SuperButton>
                    <SuperButton loading={submitting} type="primary" htmlType="submit">
                      确定
                    </SuperButton>
                  </div>
                )}
              </Form>
            </>
          )}
        </BlockLoading>
      );
    }
  )
);
