import * as React from 'react';
import { Dialog } from 'zent';
import cx from 'classnames';
import { SuperButton } from '@youzan/react-components';
import { ExpressType, OrderStateType } from 'common/constants/common';
import { ReceiverInfo } from './receiver-info';

const { openDialog } = Dialog;
const DialogId = 'receiverInfoDialogId';

export interface ModifyReceiverInfoProps {
  className?: string;
  orderNo?: string;
  reload?: () => void;
  expressType?: ExpressType;
  fulfillNo?: string;
  orderState?: OrderStateType;
  style?: React.CSSProperties;
}

export default function ModifyReceiverInfo({
  className,
  orderNo,
  reload,
  expressType,
  fulfillNo,
  orderState,
  style = {}
}: ModifyReceiverInfoProps) {
  if (
    ![
      OrderStateType.ToPay,
      OrderStateType.ToSend,
      /**
       * 同城配送，已发货支持修改地址
       * @see https://xiaolv.qima-inc.com/#/newDailyDemand/dailyDemand#145865
       */
      ...(expressType === ExpressType.City ? [OrderStateType.Send] : [])
    ].includes(orderState!)
  ) {
    // 仅待付款和待发货状态允许修改
    return null;
  }

  return (
    <SuperButton
      className={cx('action', className)}
      type="link"
      style={{ padding: 0, ...style }}
      onClick={() => {
        openDialog({
          dialogId: DialogId,
          title: '修改订单信息',
          children: (
            <ReceiverInfo
              fulfillNo={fulfillNo}
              orderNo={orderNo}
              reload={reload}
              expressType={expressType}
            />
          ),
          style: {
            width: '700px'
          }
        });
      }}
    >
      修改
    </SuperButton>
  );
}
