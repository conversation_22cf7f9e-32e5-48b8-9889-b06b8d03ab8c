import { has } from 'lodash';
import {
  IPoint,
  TCoordinate,
  EAreaType,
  IAreaModel,
  ICircle,
  IPolygon,
  TAreaRegion,
  IAreaRegionInstance,
  IGaodeMapInstance
} from './type';
import { Colors } from './constant';

/**
 * 高德 => 百度
 * @param {Array} node
 */
export function gaodeToBaidu(node: number[]) {
  const lng = +node[0] + 0.0065;
  const lat = +node[1] + 0.006;

  return [parseFloat(lng, 10).toFixed(6), parseFloat(lat, 10).toFixed(6)];
}

/**
 * 百度 => 高德
 * @param {Array} node
 */
export function baiduToGaode(node: number[]) {
  const lng = +node[0] - 0.0065;
  const lat = +node[1] - 0.006;

  return [parseFloat(lng, 10).toFixed(6), parseFloat(lat, 10).toFixed(6)];
}

/** 获取地图区域列表 */
export function getAreaRegions(areaModelList: IAreaModel[]): TAreaRegion[] {
  const areaRegions: TAreaRegion[] = [];

  areaModelList.forEach(v => {
    const { areaType, circleAreaModel, polygonAreaModel } = v;
    if (areaType === EAreaType.Circle) {
      if (circleAreaModel) {
        const circle: ICircle = {
          areaType: EAreaType.Circle,
          center: baiduToGaode([+circleAreaModel.lng, +circleAreaModel.lat]),
          radius: circleAreaModel.scope
        };
        areaRegions.push(circle);
      }
    } else if (areaType === EAreaType.Polygon && polygonAreaModel) {
      const { pointModelList } = polygonAreaModel;
      if (pointModelList && pointModelList.length !== 0) {
        const path = pointModelList.map(
          point => baiduToGaode([+point.lng, +point.lat]) as TCoordinate
        );
        const polygon: IPolygon = {
          areaType: EAreaType.Polygon,
          path
        };
        areaRegions.push(polygon);
      }
    }
  });
  return areaRegions;
}

/** 绘制区域浮层到高德地图 */
export function addAreaRegionsToMap(
  map: IGaodeMapInstance,
  areaModelList: IAreaModel[],
  onClick?: (lnglat: IPoint, regeocode) => void
): IAreaRegionInstance[] {
  const areaRegions = getAreaRegions(areaModelList);
  const regionInstanceList: IAreaRegionInstance[] = [];

  areaRegions.forEach((v, idx) => {
    const { areaType, ...rest } = v;
    const regionParams = {
      ...rest,
      strokeColor: Colors[idx], // 线颜色
      strokeOpacity: 1, // 线透明度
      strokeWeight: 2, // 线粗细度
      fillColor: Colors[idx], // 填充颜色
      fillOpacity: 0.15 // 填充透明度
    };

    let regionInstance: IAreaRegionInstance;
    if (areaType === EAreaType.Circle) {
      regionInstance = new window.AMap.Circle(regionParams);
    } else {
      regionInstance = new window.AMap.Polygon(regionParams);
    }
    if (onClick) {
      regionInstance.on('click', ({ lnglat }) => {
        const geocoder = new window.AMap.Geocoder();
        geocoder.getAddress([lnglat.lng, lnglat.lat], function (status, res) {
          if (status === 'complete' && res.regeocode) {
            onClick(lnglat, res.regeocode);
          }
        });
      });
    }
    regionInstanceList.push(regionInstance);
  });
  map.add(regionInstanceList);
  return regionInstanceList;
}

/** 输入详细地址自动提示 */
export function initMapAutoComplete(
  map: IGaodeMapInstance,
  callback: (lnglat: IPoint, regeocode: any) => void
) {
  window.AMap.plugin(['AMap.Autocomplete', 'AMap.Geocoder'], () => {
    const autocomplete = new window.AMap.Autocomplete({
      input: 'amap-address-input',
      datatype: 'poi'
    });
    const geocoder = new window.AMap.Geocoder();
    const getInfoContent = (address: string) => {
      return `<span style="font-size: 12px">${address}</span>`;
    };
    window.AMap.event.addListener(autocomplete, 'select', (data: any) => {
      const {
        location: { lng, lat }
      } = data.poi;
      const position = [lng, lat];
      geocoder.getAddress([lng, lat], (status: string, result: any) => {
        if (status === 'complete' && result?.regeocode) {
          callback(data.poi.location, result.regeocode);
          const { formattedAddress } = result.regeocode;
          map.setCenter(position);
          map.infoWindow.setContent(getInfoContent(formattedAddress));
          map.infoWindow.open(map, position);
          map.marker.setPosition(position);
          map.marker.show();
        }
      });
    });
  });
}

/** 地图点是否在区域内 */
export function isPointInAreaRegions(coord: TCoordinate, areaModelList: IAreaModel[]): boolean {
  const m1 = new window.AMap.Marker({ position: coord });
  const p1 = m1.getPosition();
  const areaRegions = getAreaRegions(areaModelList);

  if (areaRegions.length === 0) {
    return true;
  }

  let inRegion = false;
  areaRegions.forEach(v => {
    const { areaType, ...rest } = v;
    if (areaType === EAreaType.Circle) {
      const m2 = new window.AMap.Marker({
        position: rest.center
      });
      const p2 = m2.getPosition();
      const isPointInCircle = p1.distance(p2) <= rest.radius;
      if (isPointInCircle) {
        inRegion = true;
      }
    } else {
      const isPointInPolygon = window.AMap.GeometryUtil.isPointInRing(p1, rest.path);
      if (isPointInPolygon) {
        inRegion = true;
      }
    }
  });
  return inRegion;
}

export function formatAddressObject(data) {
  const { adcode } = data;
  if (has(data, 'province') && has(data, 'city')) {
    const { province, city, district, street, streetNumber, building } = data;
    return {
      adcode,
      province,
      city: city || province,
      district,
      address: `${street}${streetNumber}`,
      name: building
    };
  }
  if (has(data, 'pname') && has(data, 'cityname')) {
    const { pname, cityname, address, adname, name } = data;
    return {
      adcode,
      province: pname,
      city: cityname,
      district: adname,
      address,
      name
    };
  }
  return {};
}
