/** 地图相关 */

/** 地图点 */
export interface IPoint<T = number | string> {
  lng: T;
  lat: T;
}

/** 地图坐标 [lng, lat] */
export type TCoordinate<T = number | string> = [T, T];

/** 地图区域类型 */
export enum EAreaType {
  Circle = 1, // 圆形区域
  Polygon = 2 // 多边形区域
}

/** 地图圆形区域 */
export interface ICircle {
  areaType: EAreaType.Circle;
  /** 圆心位置 */
  center: TCoordinate;
  /** 半径 */
  radius: number;
}

/** 地图多变形区域 */
export interface IPolygon {
  areaType: EAreaType.Polygon;
  /** 路径 */
  path: TCoordinate[];
}

/** 地图区域 */
export type TAreaRegion = ICircle | IPolygon;

/** 地图圆形区域数据模型 */
export type ICircleAreaModel = IPoint & {
  scope: number;
};

/** 地图区域接口数据模型 */
export interface IAreaModel {
  areaName: string;
  areaType: EAreaType;
  circleAreaModel?: IPoint & {
    scope: number;
  };
  polygonAreaModel?: {
    pointModelList: IPoint[];
  };
}

/** 地图圆形区域实例 */
export interface ICircleInstance {
  [propName: string]: any;
}
/** 地图圆形区域实例 */
export interface IPolygonInstance {
  [propName: string]: any;
}
/** 地图区域实例 */
export interface IAreaRegionInstance {
  [propName: string]: any;
}

/** 高德地图实例 */
export interface IGaodeMapInstance {
  add: (ins: any) => void;
  remove: (ins: any) => void;
  [propName: string]: any;
}
