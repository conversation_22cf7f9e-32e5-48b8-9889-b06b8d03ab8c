import React, { useEffect, useState } from 'react';
import { Dialog, Button } from 'zent';

import { queryWxAccountInfo } from './api';

import './style.scss';

// 去开通弹窗Id
const EnableOrderShipmentDialogId = 'enable_order_shipment_dialog';
// 去授权弹窗Id
const AuthOrderShipmentDialogId = 'auth_order_shipment_dialog';

/**
 * 微信管控通知组件
 * 根据微信侧的两个状态，订单发货管理是否开通以及小程序是否被管控来展示不同文案
 */
const WxControlNotice: React.FC = () => {
  const [isOrderShipmentEnabled, setIsOrderShipmentEnabled] = useState(false);

  useEffect(() => {
    queryWxAccountInfo().then(info => {
      const { funcInfo = [] } = info || {};
      // 订单发货管理是否开通
      const isOrderShipmentEnabled = funcInfo.find(
        (item: any) => item.funcId === 142 && item.funcState === 1
      );
      setIsOrderShipmentEnabled(!!isOrderShipmentEnabled);
    });
  }, []);

  // 处理"重试"按钮点击
  const handleRetryClick = () => {
    // 重新打开新的tab进行授权流程
    const authUrl = '/v4/shop/wx/auth?reauthFlag=reauth';
    window.open(authUrl, '_blank');
  };

  // 查看详情点击
  const handleViewDetailsClick = () => {
    // 这里可以添加查看详情的逻辑，例如打开一个新的页面或弹窗
    window.open(
      'https://developers.weixin.qq.com/miniprogram/product/jiaoyilei/yunyingguifan.html#_1-5-%E8%B5%84%E9%87%91%E7%BB%93%E7%AE%97',
      '_blank'
    );
  };

  // 处理"已完成授权"按钮点击
  const handleAuthCompleteClick = () => {
    // 关闭弹窗
    Dialog.closeDialog(AuthOrderShipmentDialogId);

    // 刷新页面
    window.location.reload();
  };

  // 展示授权状态弹窗
  const showAuthStatusDialog = () => {
    Dialog.openDialog({
      dialogId: AuthOrderShipmentDialogId,
      title: '授权状态',
      children: (
        <p className="dialog-content">
          请在新打开的页面完成微信授权，授权成功后即可开通小程序订单管理功能。
        </p>
      ),
      footer: (
        <>
          <Button onClick={handleRetryClick}>遇到问题，点击重试</Button>
          <Button type="primary" onClick={handleAuthCompleteClick}>
            已完成授权
          </Button>
        </>
      ),
      onClose: () => handleAuthCompleteClick(),
      className: 'wx-control-notice-auth-dialog-wrapper'
    });
  };

  // 处理"去授权"按钮点击
  const handleAuthClick = () => {
    // 关闭当前弹窗
    Dialog.closeDialog(EnableOrderShipmentDialogId);

    handleRetryClick();

    // 在当前页面展示新弹窗
    showAuthStatusDialog();
  };

  // 处理"去开通"按钮点击
  const handleEnableClick = () => {
    Dialog.openDialog({
      dialogId: EnableOrderShipmentDialogId,
      title: '开通小程序订单管理',
      children: (
        <p className="dialog-content">
          开通小程序订单管理需要重新授权微信小程序，以获取微信小程序最新权限。
        </p>
      ),
      footer: (
        <>
          <Button onClick={() => Dialog.closeDialog(EnableOrderShipmentDialogId)}>取消</Button>
          <Button type="primary" onClick={handleAuthClick}>
            去授权
          </Button>
        </>
      ),
      className: 'wx-control-notice-dialog-wrapper'
    });
  };

  return (
    <div className="wx-control-notice">
      {isOrderShipmentEnabled ? (
        // 订单发货管理开通 + 管控
        <div className="notice-content">
          <span>由于你的小程序已被微信管控,资金结算规则已更改为微信官方结算规则。</span>
          <a className="link" onClick={handleViewDetailsClick}>
            查看详情
          </a>
        </div>
      ) : (
        // 订单发货管理未开通 + 管控
        <div className="notice-content">
          <span>
            由于你的小程序已被微信管控，订单需要在微信小程序后台操作发货，开通《小程序订单管理》功能后，有赞系统可自动将物流同步至微信。
          </span>
          <a className="link" onClick={handleEnableClick}>
            去开通
          </a>
        </div>
      )}
    </div>
  );
};

export default WxControlNotice;
