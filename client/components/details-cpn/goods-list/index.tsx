import type { Contains } from 'definition/common';
import type {
  IRetailSkuList,
  IPaymentInfo,
  IFulfillOrder,
  IOrderExchangeInfo,
  IMultiOrderInfo
} from 'definition/order-info';

import * as React from 'react';
import cx from 'classnames';
import { get, isNil, filter, sortBy, find, omit, pick } from 'lodash';
import {
  Notify,
  Icon,
  Pop,
  previewImage,
  Dialog,
  Timeline,
  Checkbox,
  Sweetalert,
  Grid
} from 'zent';
import { IGridCellPos } from 'zent/es/grid';
import { deepJsonParse, setUrlDomain } from '@youzan/retail-utils';
import {
  isHqStore,
  isPartnerStore,
  isUnifiedOnlineBranchStore,
  isRetailMinimalistShop,
  isPHShop
} from '@youzan/utils-shop';
import type { IOrderSourceInfo } from 'definition/order-info';
import { BlankLink, SuperButton } from '@youzan/react-components';
import GoodsInfo from 'components/goods-info';
import { IMainOrderInfo, ExchangeStatus, IOrderItemInfo } from 'definition/order-info';
import { ISupplyMode } from 'definition/main-order-info';
import { LotCodeItem } from 'definition/lot-code';
import { OrderPayWayType } from 'common/constants/common';
import { OptComponent } from 'components/opt-components';
import { OFFLINE_REFUND_CODES } from 'components/opt-components/code-map';
import createOrderCpm from 'components/create-order-cpm'; // eslint-disable-line
import Redispatch from 'components/redispatch';
import TextInSafeArea from 'components/details-cpn/text-in-safe-area';
import { getOnlineExchangeUrl } from 'common/url';

import {
  withOrderHelper,
  getItemUmpInfo,
  combineUmpInfo,
  isFalseValue,
  divGoodsNum,
  getRelatedSkuList
} from 'common/helper';
import { noExpressReasonDesc } from 'common/constants';
import { orderTypeValue } from 'common/constants/models';
import { ORDER_TYPE } from 'common/constants/order';
import { convertFenToYen } from 'common/fns/format';
import isFxStore from 'common/is-fx-store';
import {
  getGoodsShowPrice,
  isCandaoOrder,
  checkIsCouponPayOrder,
  checkIsMeituanOrder,
  getCurrentPrice
} from 'common/biz-helper';
import api from 'route/onlineshipments/api';
import { fetchRefundData, checkIsDeposited } from 'components/opt-components/seller-refund-new/api';
import { EProcessStatus } from 'route/process-setting/types';
import Content from 'components/opt-components/seller-refund-new/content';
import sellerRefundStyle from 'components/opt-components/seller-refund-new/style.scss';

import { LotCodePopButton } from './lot-code-pop-button';
import IEMIPopList from './iemi-pop-list';
import style from './style.scss';
import { renderTariff, hasTariffGood } from './helper';

const { openDialog } = Dialog;
const { Fragment } = React;

const { GIFT } = orderTypeValue;
const COD_PAY = OrderPayWayType.CodPay;

const CITY_EXPRESS_CODE = 2;

// 货单详情
const isFullFillOrder = /fulfilldetail/.test(window.location.pathname);

const EXPRESS_STATUS_MAP = {
  0: '未发货',
  1: '已发货',
  [CITY_EXPRESS_CODE]: '同城配送',
  3: '部分已发货'
};

// 需要展示的优惠类型
const DISPLAY_UMP_TYPES = [
  'packageBuy', // 优惠套餐
  'meetReduce', // 满减送
  'bale', // 打包一口价
  'couponCard', // 优惠券
  'couponCode', // 优惠码
  'coupon', // 优惠券码
  'entireDiscount', // 整单优惠
  'ignoreOddChange', // 抹零
  'pointDeduction', // 积分抵扣
  'secondHalfDiscount', // 第二件半价
  'combine', // 组合套餐
  'storedCustomerDiscount' // 储值专享折扣
];

// 改派的 operations code
const DISPATCH_CODES = ['redispatch', 'redispatch_record', 'unable_change_dispatcher'];

interface OrderColumn {
  title: string;
  judgement?: boolean; // 判断条件，如果判断条件不满足就不显示这列
}

/**
 * 买家诉求类型 1：仅退款 2：退货退款 3：换货
 */
enum DisputeType {
  // 仅退款
  OnlyRefund = 1,
  // 退货退款
  RefundAndReturn = 2,
  // 换货
  Exchange = 3
}

const handleColumnFilter = (columns: OrderColumn[]) => {
  return columns.filter(col => {
    if (col.judgement === undefined || col.judgement === true) return true;
    return false;
  });
};

@createOrderCpm()
@withOrderHelper
class GoodsList extends React.Component<
  Contains<{
    itemInfo?: Array<
      Contains<{
        title: string;
        goodsInfo: Contains<any>;
        fulfillNo: string;
        fulfillNos: string[];
        itemIdStr: string;
        relatedRetailSkuInfos: IRetailSkuList;
        retailSkuList: IRetailSkuList;
        statusAndUniqueCodeMap: Contains<Record<number, string[]>>;
        isReduce: boolean;
        reducePrice: number;
        unitPrice: number;
        originUnitPrice: number;
        standardPrice: number;
        refundNum: number;
        num: number;
        unit: string;
        distStatusDesc: string;
        deliveryStatusDesc: string;
        noNeedDeliveryReason: keyof typeof noExpressReasonDesc;
        collectPointsPrice: number;
        payPrice: number;
        giftToBeReceivedNum: number;
        isPresent?: boolean;
        latestDeliveryTermDesc: string;
        latestDeliveryTimeDesc: string;
        operations: Array<
          Contains<{
            code: string;
            text: string;
          }>
        >;
        hasExpressed: keyof typeof EXPRESS_STATUS_MAP;
        disputeType: DisputeType;
      }>
    >;
    orderExpressInfo?: Contains<{ packs: Array<Contains<{ itemIds: string[] }>> }>;
    fulfillOrder?: IFulfillOrder;
    mainOrderInfo: IMainOrderInfo;
    multiOrderInfo?: IMultiOrderInfo;
    isHotelOrder(): boolean;
    isOnlineOrder(): boolean;
    reload: () => void;
    paymentInfo?: IPaymentInfo;
    operations?: Array<Contains<{ code: string }>>;
    orderActivities?: Array<Contains<{ decrease: number; displayDecrease: number }>>;
    isFulfillOrder: boolean;
    sourceInfo: IOrderSourceInfo;
    exchangeInfo?: IOrderExchangeInfo;
  }>
> {
  static defaultProps = {
    itemInfo: [],
    orderExpressInfo: {},
    fulfillOrder: {},
    mainOrderInfo: {}
  };

  state = {
    checkedList: [],
    itemIdStrs: [],
    loading: false,
    fetchData: null,
    checkedItem: [],
    isRefund: false,
    wrapperWidth: 0
  };

  wrapperIns = React.createRef<HTMLDivElement>();

  componentDidMount() {
    const { itemInfo, operations } = this.props;
    const itemIdStrs = itemInfo
      ?.filter(item => {
        return item?.operations[0]?.code === 'goods_refund' && !item?.operations[0]?.disabled;
      })
      .map(item => item.itemIdStr);
    this.setState({
      itemIdStrs,
      isRefund: !!operations?.filter(item => item.code === 'refund' && !item.disabled).length,
      wrapperWidth: this.wrapperIns.current?.offsetWidth
    });
    // 由于商品列宽度要求整体自适应30%，最小268px。grid组件固定col不支持设定百分比。决定监听width，手动计算
    window.addEventListener('resize', this.handleWindowResize.bind(this));
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.handleWindowResize.bind(this));
  }

  handleWindowResize() {
    this.setState({
      wrapperWidth: this.wrapperIns.current?.offsetWidth
    });
  }

  checkIsOnlineAndNotMeituan() {
    const { isOnlineOrder, mainOrderInfo } = this.props;
    const isMeituanOrder = checkIsMeituanOrder(mainOrderInfo.channelType);
    return isOnlineOrder() && !isMeituanOrder;
  }

  /**
   * 卡券支付订单 和 换货单只能整单退
   */
  get isCanOnlyRefundAll(): boolean {
    const { paymentInfo, exchangeInfo } = this.props;
    return checkIsCouponPayOrder(paymentInfo) || !!exchangeInfo?.sourceOrderNo;
  }

  /**
   * 检查是否使用网店式（每个订单商品分开退款）的主动退款方式
   *
   * @returns
   */
  useOnlineOrderRefund() {
    const { isRefund } = this.state;

    return this.checkIsOnlineAndNotMeituan() && isRefund;
  }

  /**
   * 判断是否是积分兑换订单
   * 这个判断比较恶心，因为后端orderType不能告知是否是积分兑换
   *只能通过判断是否有积分价格来判断
   */
  isUglyPoints = () => {
    const { itemInfo = [] } = this.props;
    // 这里很明显是不需要初始化的，itemInfo 怎么可能是 undefined[]
    return itemInfo.reduce((preOrigin, goodsInfo) => {
      let pre = preOrigin;
      const goodsInfoExtra = deepJsonParse(goodsInfo?.goodsInfo);
      if (goodsInfoExtra?.points_price) {
        pre = true;
      }
      return pre;
    }, false);
  };

  /**
   * 判断是否需要展示发货方 只有总部展示(待发货之前状态的订单都不应显示)
   */
  showFulfillInfo = () => {
    const { payState, isPeriodBuy, orderType, tagsInfo = '{}', buyWay } = this.props.mainOrderInfo!;
    const tags = deepJsonParse(tagsInfo);

    const canShow = !isFalseValue(
      (payState || buyWay === COD_PAY) && // 待付款且不是货单付款不显示
        (isHqStore ||
          isPartnerStore ||
          isUnifiedOnlineBranchStore ||
          isRetailMinimalistShop ||
          isFxStore) && // 不是总店不显示
        !isPeriodBuy && // 周期购不显示
        orderType !== GIFT && // 送礼订单不显示
        !tags.IS_VIRTUAL && // 虚拟商品不显示
        !tags.IS_FENXIAO_ORDER
    );

    return canShow;
  };

  /**
   * 是否是集点兑换订单
   */
  showCollectCardInfo = () => {
    const { orderType } = this.props.mainOrderInfo!;
    return orderType === ORDER_TYPE.POINT_CARD.value;
  };

  handleCheckedAll = e => {
    const { itemIdStrs } = this.state;
    this.setState({
      checkedList: e.target.checked ? itemIdStrs.slice() : []
    });
  };

  handleChange = (val: Array<string>) => {
    this.setState({
      checkedList: val
    });
  };

  onClose = () => {
    this.setState({ fetchData: null });
  };

  allRefund = () => {
    const { checkedList } = this.state;
    const {
      sourceInfo: { orderNo },
      itemInfo
    } = this.props;
    const checkedItem = itemInfo?.filter(item => checkedList.includes(item.itemIdStr));
    const params = {
      itemIds: checkedList,
      orderNo
    };

    this.setState({ loading: true, checkedItem });

    Promise.all([fetchRefundData(params), checkIsDeposited(params.orderNo)])
      .then(([refundData, depositInfo]) => {
        const failReason = get(refundData, 'orderInfo.refuseRefundReason');

        if (failReason) {
          Sweetalert.alert({
            content: <p>{failReason}</p>,
            parentComponent: this,
            closeBtn: true,
            maskClosable: true
          });
          return;
        }

        if (depositInfo.isDeposited) {
          Sweetalert.confirm({
            content: <p>{depositInfo.remindInfo}</p>,
            onConfirm: () => {
              this.setState({ fetchData: refundData });
            },
            parentComponent: this
          });
          return;
        }
        this.setState({ fetchData: refundData });
      })
      .catch(err => {
        Notify.error(err.msg || '获取退款信息失败');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  /** 商品信息 */
  renderGoodsInfo = (
    goodsInfo: Contains<{
      itemIdStr: string;
      title: string;
      relatedRetailSkuInfos?: IRetailSkuList;
      retailSkuList?: IRetailSkuList;
      buyerMemo: string;
      isPresent?: boolean;
    }>
  ) => {
    // 货单详情页面, 组合商品 展示「商品明细」
    const relatedSkuList = getRelatedSkuList(goodsInfo);
    const isShowGoodsBrief = relatedSkuList.length > 1 && isFullFillOrder;
    const addressExtra = get(this.props, 'orderAddressInfo.addressExtra', '');
    const { checkInTime } = deepJsonParse(addressExtra);
    let buyerMemo;
    let isBuyerMemoJSONify;
    try {
      buyerMemo = JSON.parse(goodsInfo.buyerMemo);
      isBuyerMemoJSONify = true;
    } catch (err) {
      isBuyerMemoJSONify = false;
      buyerMemo = {};
    }
    const buyerMemoValues = Object.values(buyerMemo);
    const isImage = (str: string) => {
      const reg = /\.(png|jpg|gif|jpeg|webp|svg)$/;
      return reg.test(str);
    };
    const isText = (str: string) => {
      const reg = /\.(png|jpg|gif|jpeg|webp|svg)$/;
      return !reg.test(str);
    };
    const imgArr = buyerMemoValues.filter(isImage);
    const textObj = {};
    const imgObj = {};
    Object.keys(buyerMemo).forEach(k => {
      if (isImage(buyerMemo[k])) {
        imgObj[k] = buyerMemo[k];
      }
      if (isText(buyerMemo[k])) {
        textObj[k] = buyerMemo[k];
      }
    });
    const textKey = Object.keys(textObj);
    const imgKey = Object.keys(imgObj);

    const handlePreview = e => {
      e.persist();
      previewImage({
        images: imgArr,
        index: imgArr.indexOf(e.target.src),
        parentComponent: this,
        scaleRatio: 3,
        showRotateBtn: false
      });
    };

    const { operations = [], itemIdStr } = goodsInfo;
    const { sourceInfo } = this.props;

    return (
      <>
        {this.useOnlineOrderRefund() && !this.isCanOnlyRefundAll ? (
          <Checkbox.Group value={this.state.checkedList} onChange={this.handleChange}>
            <Checkbox
              key={itemIdStr}
              value={itemIdStr}
              disabled={
                operations[0]?.code !== 'goods_refund' ||
                (operations[0]?.code === 'goods_refund' && operations[0]?.disabled)
              }
            >
              <GoodsInfo
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...goodsInfo}
                className="goods-item__info"
                sourceInfo={sourceInfo}
                isPackagedGoods={isShowGoodsBrief}
                isHotelOrder={this.props.isHotelOrder()}
                checkInTime={checkInTime}
                withBrand
                showSnapshot={!isPHShop}
              />
            </Checkbox>
          </Checkbox.Group>
        ) : (
          <GoodsInfo
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...goodsInfo}
            className="goods-item__info"
            sourceInfo={sourceInfo}
            isPackagedGoods={isShowGoodsBrief}
            isHotelOrder={this.props.isHotelOrder()}
            checkInTime={checkInTime}
            withBrand
            showSnapshot={!isPHShop}
          />
        )}
        {!isBuyerMemoJSONify && goodsInfo.buyerMemo && (
          <div className="grey goods-remark">商品留言：{goodsInfo.buyerMemo}</div>
        )}
        {buyerMemoValues.length > 0 && (
          <div className="grey goods-remark">
            商品留言：
            {textKey.length > 0 &&
              textKey.map((k, index) => {
                return (
                  <p key={index} style={{ lineHeight: '16px' }}>
                    {k}：{textObj[k]}
                  </p>
                );
              })}
            {imgKey.length > 0 && (
              <div className="grey goods-img">
                {imgKey.map((k, index) => {
                  return (
                    <div className="goods-img-box">
                      <span>{k}：</span>
                      {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
                      <img src={imgObj[k]} alt={k} key={index} onClick={handlePreview} />
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        )}
      </>
    );
  };

  showNum = ({
    refundNum,
    num,
    unit,
    isExchangeItem,
    exchangeGoodsNum,
    exchangeStatus
  }: Contains<{
    refundNum: number;
    num: number;
    unit: string;
    isExchangeItem?: boolean;
    exchangeGoodsNum?: number;
    exchangeStatus?: ExchangeStatus;
  }>) => {
    const goodsNum = divGoodsNum(num);
    const text = `${isExchangeItem ? '-' : ''}${goodsNum}  ${unit}`;
    if (typeof exchangeGoodsNum !== 'undefined' && exchangeStatus !== undefined) {
      return `${goodsNum} ${unit}(已换${divGoodsNum(exchangeGoodsNum)}${unit})`;
    }
    if (!refundNum) {
      return text;
    }
    return (
      <>
        {text}/
        <span style={{ color: '#ff4444' }}>
          已退{divGoodsNum(refundNum)}
          {unit}
        </span>
      </>
    );
  };

  renderDiscountDetails = (
    goodsInfo: Contains<{
      goodsActivities: [];
    }>
  ) => {
    const { goodsActivities = [] } = goodsInfo;
    return goodsActivities.length > 0 ? (
      <ul>
        {goodsActivities.map((discount, idx) => {
          const { name, decrease } = discount;
          // [name decrease] name超出几个字省略并hover可以看全
          return (
            <li className="item-goods_discount-item" key={idx}>
              <TextInSafeArea val={name} />
              <span className="item-goods_discount-item-value">
                {`-¥${convertFenToYen(decrease)}`}
              </span>
            </li>
          );
        })}
      </ul>
    ) : (
      '-'
    );
  };

  // 价格(发货单里只显示数量)
  renderPrice = (
    goodsInfo: Contains<{
      isLotCodeItem: boolean;
      lotCodeInfos: LotCodeItem[];
      statusAndUniqueCodeMap: Contains<Record<number, string[]>>;
      isReduce: boolean;
      reducePrice: number;
      unitPrice: number;
      payPrice: number;
      originUnitPrice: number;
      standardPrice: number;
      refundNum: number;
      num: number;
      unit: string;
      title: string;
      isExchangeItem: boolean;
      icons: { code: string }[];
    }>
  ) => {
    const serialNoMap = get(goodsInfo, 'statusAndUniqueCodeMap', {});

    return (
      <>
        <p>{getCurrentPrice(goodsInfo)}</p>
        {!this.props.isFulfillOrder ? getGoodsShowPrice(goodsInfo) : null}
        <p className="item-goods__num">{this.showNum(goodsInfo)}</p>
        {Object.keys(serialNoMap).length > 0 ? (
          <IEMIPopList data={serialNoMap} goods={goodsInfo} />
        ) : null}
        {goodsInfo.isLotCodeItem ? <LotCodePopButton datasets={goodsInfo.lotCodeInfos} /> : null}
      </>
    );
  };

  // 积分价
  renderPoints = ({
    unitPrice,
    goodsInfo
  }: Contains<{ unitPrice: number; goodsInfo: unknown }>) => {
    const goodsInfoExtra = deepJsonParse(goodsInfo);
    return (
      <>
        {goodsInfoExtra.points_price
          ? `积分${goodsInfoExtra.points_price}+￥${convertFenToYen(unitPrice)}`
          : '--'}
      </>
    );
  };

  // 集点兑换
  renderPointCar = ({
    unitPrice,
    collectPointsPrice
  }: {
    unitPrice: number;
    collectPointsPrice: number;
  }) => {
    return (
      <>
        {collectPointsPrice ? `集点兑换${collectPointsPrice}+￥${convertFenToYen(unitPrice)}` : ''}
      </>
    );
  };

  // 交易额，原小计（小计逻辑：商品单价*数量；交易额逻辑：优惠价*数量）
  renderSubtotalled({
    tradePrice = 0,
    isExchangeItem
  }: Contains<{ tradePrice?: number; isExchangeItem: boolean }>) {
    return (
      <>
        <div className="item--subtotalled">
          <p>
            {isExchangeItem ? '-' : ''}
            {convertFenToYen(tradePrice)}
          </p>
        </div>
      </>
    );
  }

  /**
   * 渲染改派按钮
   *
   * @params {Object} goodsInfo
   */
  renderReDispatchButton = (
    goodsInfo: Contains<{
      operations: Array<Contains<{ code: string; text: string }>>;
      orderNo?: string;
      fulfillNos?: string[];
      warehouseNames?: string[];
      itemIdStr?: string;
      imgUrl?: string;
      title?: string;
      sku_desc?: string;
      goodsNo?: string;
      num?: number;
      unit?: string;
      pricingStrategy?: number;
    }>
  ) => {
    const dispatchOperations = goodsInfo.operations.filter(operation =>
      DISPATCH_CODES.includes(operation.code)
    );

    if (dispatchOperations.length === 0) return null;

    return dispatchOperations.map(operation => {
      if (operation.code === 'unable_change_dispatcher') {
        return (
          <Pop
            trigger="click"
            position="top-left"
            // 先 any 了, lodash 的类型推断看不懂
            content={get(operation, 'attributes.tip', '') as any}
          >
            <a className="disabled">{operation.text}</a>
          </Pop>
        );
      }

      return (
        <Redispatch
          type={operation.code === 'redispatch' ? 'create' : 'view'}
          onClose={() => this.props.reload()}
          goods={pick(goodsInfo, [
            'orderNo',
            'fulfillNos',
            'warehouseNames',
            'itemIdStr',
            'imgUrl',
            'title',
            'sku_desc',
            'goodsNo',
            'num',
            'unit',
            'pricingStrategy',
            'processStatus'
          ])}
        />
      );
    });
  };

  // 发货状态
  renderExpressState = (
    goodsInfo: Contains<{
      operations: Array<Contains<{ code: string; text: string }>>;
      hasExpressed: keyof typeof EXPRESS_STATUS_MAP;
      deliveryStatusDesc: string;
      itemIdStr: string;
      giftToBeReceivedNum: number;
      latestDeliveryTermDesc: string;
      latestDeliveryTimeDesc: string;
    }>
  ) => {
    const { packs = [] } = this.props.orderExpressInfo!;
    const {
      hasExpressed,
      deliveryStatusDesc,
      itemIdStr,
      giftToBeReceivedNum,
      latestDeliveryTermDesc,
      latestDeliveryTimeDesc
    } = goodsInfo;

    const goodsPack = find(packs, ({ itemIds = [] }) => itemIds.indexOf(itemIdStr) > -1);

    let expressDesc =
      hasExpressed === CITY_EXPRESS_CODE
        ? (get(goodsPack, 'takeoutExpressDetail.statusDesc', '') as string)
        : deliveryStatusDesc;
    if (isNil(expressDesc)) {
      expressDesc = EXPRESS_STATUS_MAP[hasExpressed];
    }

    return (
      <>
        <p>{expressDesc}</p>
        {!!giftToBeReceivedNum && (
          <p className="orange">
            还有
            {giftToBeReceivedNum}
            个礼单待领取
          </p>
        )}
        {latestDeliveryTermDesc && <p style={{ color: 'grey' }}>{latestDeliveryTermDesc}</p>}
        {latestDeliveryTimeDesc && <p style={{ color: 'grey' }}>{latestDeliveryTimeDesc}</p>}
        {/* 根据下发的 operations 渲染改派按钮 */}
        {this.renderReDispatchButton(goodsInfo)}
      </>
    );
  };

  renderExchangeStatus = (goodsInfo: IOrderItemInfo) => {
    const { exchangeNo, refundId, exchangeStatus, orderNo, itemIdStr } = goodsInfo || {};
    if (![ExchangeStatus.ExchangeSuccess, ExchangeStatus.Exchanging].includes(+exchangeStatus)) {
      return null;
    }
    return (
      <div>
        {refundId && (
          <p>
            <BlankLink href={getOnlineExchangeUrl({ orderNo, refundId, itemId: itemIdStr })}>
              换货记录
            </BlankLink>
          </p>
        )}
        <span>换货订单：</span>
        {exchangeNo ? (
          <BlankLink href={`/v2/order/orderdetail#/?order_no=${exchangeNo}`}>
            {exchangeNo}
          </BlankLink>
        ) : (
          '-'
        )}
      </div>
    );
  };

  // 发货单的发货状态
  renderFulfillStatus = ({
    distStatusDesc,
    deliveryStatusDesc,
    noNeedDeliveryReason
  }: {
    distStatusDesc: string;
    deliveryStatusDesc: string;
    noNeedDeliveryReason: keyof typeof noExpressReasonDesc;
  }) => (
    <>
      <p>{distStatusDesc || deliveryStatusDesc}</p>
      {noNeedDeliveryReason && <p>({noExpressReasonDesc[noNeedDeliveryReason]})</p>}
    </>
  );

  // 商品加工状态
  renderProcessStatus = ({
    processStatus,
    processStatusDesc,
    produceShopName,
    itemIdStr,
    orderNo,
    fulfillNo
  }: {
    processStatus?: number;
    processStatusDesc?: string;
    produceShopName: string;
    itemIdStr: string;
    orderNo: string;
    fulfillNo: string;
  }) => {
    return (
      <>
        {processStatusDesc ? (
          <div>
            <div>{processStatusDesc}</div>
            {processStatus !== EProcessStatus.Default && (
              <SuperButton
                type="link"
                onClick={() => {
                  api
                    .listProcesslog({
                      orderItemId: itemIdStr,
                      orderNo,
                      fulfillNo
                    })
                    .then(data => {
                      const list = data || [];
                      openDialog({
                        title: '作业状态详情',
                        children: (
                          <div>
                            <div style={{ marginBottom: 16 }}>制作店铺：{produceShopName}</div>
                            作业状态：
                            <div style={{ border: '1px solid #dcdee0', padding: 8, marginTop: 8 }}>
                              {' '}
                              <Timeline type="vertical">
                                {list.map(
                                  (
                                    v: {
                                      processStatusDesc: string;
                                      operatorName: string;
                                      createdAt: number;
                                    },
                                    idx: number
                                  ) => {
                                    let dotColor;
                                    let size;
                                    if (idx !== 0) dotColor = '#dcdee0';
                                    if (idx === list.length - 1) size = 0;
                                    // eslint-disable-next-line @youzan/yz-retail/no-new-date
                                    const d = new Date(v.createdAt);
                                    return (
                                      <Timeline.Item
                                        label={
                                          <div>
                                            <span style={{ display: 'inline-block', width: 240 }}>
                                              {v.operatorName && `${v.operatorName}：`}
                                              {v.processStatusDesc}
                                            </span>
                                            <span>
                                              {v.createdAt &&
                                                `${d.getFullYear()}-${String(
                                                  d.getMonth() + 1
                                                ).padStart(2, '0')}-${String(d.getDate()).padStart(
                                                  2,
                                                  '0'
                                                )} ${String(d.getHours()).padStart(
                                                  2,
                                                  '0'
                                                )}:${String(d.getMinutes()).padStart(
                                                  2,
                                                  '0'
                                                )}:${String(d.getSeconds()).padStart(2, '0')}`}
                                            </span>
                                          </div>
                                        }
                                        dotColor={dotColor}
                                        size={size}
                                      />
                                    );
                                  }
                                )}
                              </Timeline>
                            </div>
                          </div>
                        )
                      }).catch((err: any) => {
                        Notify.error((err && err.msg) || '获取详情失败');
                      });
                    });
                }}
              >
                查看详情
              </SuperButton>
            )}
          </div>
        ) : (
          '-'
        )}
      </>
    );
  };

  /**
   *
   * @memberof GoodsList
   *  门店和网店返回不一样
   *  并且对operation.code === ('goods_refund' || 'refund') 即主动退款的进行特殊处理
   *  因为主动退款重构了一波
   *  目前这段代码 中间态。
   */
  getOptOptions = (
    operation: Contains<{ code: string }>,
    goodsInfo: unknown,
    isMeituanOrder: boolean
  ) => {
    const {
      isOnlineOrder,
      mainOrderInfo,
      paymentInfo,
      reload,
      itemInfo = [],
      refundInfo,
      exchangeInfo,
      multiOrderInfo
    } = this.props;
    const isOnline = isOnlineOrder();

    if (['goods_refund', 'refund'].indexOf(operation.code) > -1) {
      return {
        mainOrderInfo,
        multiOrderInfo,
        reload,
        isOnline,
        isMeituanOrder,
        realPay: paymentInfo?.realPay,
        payDeduction: paymentInfo?.payDeduction,
        itemInfo: this.checkIsOnlineAndNotMeituan() ? [goodsInfo] : itemInfo,
        allItems: itemInfo,
        exchangeInfo
      };
    }

    const options = {
      orderInfo: omit(this.props, ['reload', 'refundInfo']),
      reload,
      refundInfo,
      allItems: itemInfo,
      item: this.checkIsOnlineAndNotMeituan() ? goodsInfo : undefined
    };

    return options;
  };

  // 操作按钮
  renderOperations = (
    operations: Array<Contains<{ code: string }>>,
    goodsInfo: unknown,
    isMeituanOrder?: boolean
  ) => {
    if (!operations?.length) {
      return '-';
    }
    return operations.map(operation => (
      <p key={operation.code}>
        <OptComponent
          operation={operation}
          options={this.getOptOptions(operation, goodsInfo, isMeituanOrder)}
        />
      </p>
    ));
  };

  // 退款状态
  renderRefundState = (
    goodsInfo: Contains<{ operations: Array<Contains<{ code: string }>>; itemIdStr: string }>,
    pos: number,
    len: number
  ) => {
    const { operations = [], mainOrderInfo } = this.props;
    const isMeituanOrder = checkIsMeituanOrder(mainOrderInfo.channelType);
    // 门店订单退款操作并行显示
    // 美团的网店订单也需要合并
    if ((!this.props.isOnlineOrder() && !isCandaoOrder(this.props)) || isMeituanOrder) {
      const refundOpts = operations.filter(operation =>
        OFFLINE_REFUND_CODES.includes(operation.code)
      );
      if (pos === 0) {
        return {
          props: {
            rowSpan: len
          },
          children: this.renderOperations(refundOpts, goodsInfo, isMeituanOrder)
        };
      }
      return {
        props: {
          rowSpan: 0
        }
      };
    }

    const { operations: itemOperations = [] } = goodsInfo;

    return <>{this.renderOperations(itemOperations, goodsInfo)}</>;
  };

  // 优惠信息
  renderUmpInfo = () => {
    const { itemInfo, orderActivities, mainOrderInfo } = this.props;

    // 相同活动的价格进行聚合
    const itemUmpInfo = getItemUmpInfo(itemInfo, mainOrderInfo);
    const combineOrderActivities = combineUmpInfo(orderActivities);

    // 过滤掉不需要显示的优惠信息
    const displayActivities = filter(
      [...combineOrderActivities, ...itemUmpInfo],
      umpInfo => DISPLAY_UMP_TYPES.indexOf(umpInfo.type) > -1
    );

    return (
      <>
        <div className="item--ump-info">
          {displayActivities.map((umpInfo, index) => {
            const { displayDecrease, type, extraInfo, name } = umpInfo;
            const sumDecrease = isNil(displayDecrease)
              ? ''
              : `：-${convertFenToYen(displayDecrease)}`;

            // 满减送的优惠信息需要处理
            const meetReduceExtraInfo = [];
            if (type === 'meetReduce' && extraInfo) {
              // 满减送包邮
              +extraInfo.postage === 1 &&
                meetReduceExtraInfo.push(<p key="meet-reduce-postage">满减送：包邮</p>);
              const actualPoints = get(
                deepJsonParse(extraInfo.activityResult),
                'send_points_result.actual_points',
                null
              );

              // 满减送积分
              actualPoints !== null &&
                meetReduceExtraInfo.push(
                  <p key="meet-reduce-score">
                    满减送：
                    {actualPoints}
                    积分
                  </p>
                );

              // 满减送优惠券
              extraInfo.couponDefault &&
                meetReduceExtraInfo.push(
                  <p key="meet-reduce-coupon">
                    满减送：
                    {extraInfo.couponDefault}
                  </p>
                );
            }

            return (
              // eslint-disable-next-line react/no-array-index-key
              <div className="ump-info__item" key={index}>
                <p>
                  {name}
                  <span>{sumDecrease}</span>
                </p>
                {extraInfo && meetReduceExtraInfo}
              </div>
            );
          })}
        </div>
      </>
    );
  };

  // 商品库商品名称(只有发货单详情有)
  renderRealGoodsName = ({ retailSkuList = [] }: Contains<{ retailSkuList?: IRetailSkuList }>) => {
    // 目前只显示一个
    const { name, retailSkuId } = retailSkuList[0] || {};
    return (
      <>
        <p key="sku-name">
          <strong>{name}</strong>
        </p>
        <p key="sku-code" className="grey">
          商品编码: {retailSkuId}
        </p>
      </>
    );
  };

  // 发货单运费和实付运费(发货单详情有)
  renderPostage = () => {
    const { postFee } = this.props.fulfillOrder!;
    return <>{convertFenToYen(postFee)}</>;
  };

  // 发货单运费和实付运费(发货单详情有)
  renderActualPostage = () => {
    const { realDeliveryFee } = this.props.fulfillOrder!;
    return <>{convertFenToYen(realDeliveryFee)}</>;
  };

  // 根据发货单号排序
  sortItemByFulfillNo<T extends Contains<{ fulfillNo: string }>>(itemInfo: T[]): T[] {
    return sortBy(itemInfo, 'fulfillNo');
  }

  // 发货方信息
  renderFulfillInfo = (
    fulfillGroups: Array<
      Array<Contains<{ warehouseId: number; warehouseName: string; fulfillNo: string }>>
    > = [],
    index: number,
    mainOrderInfo: Contains<{ supplyMode: ISupplyMode; shopName: string }>
  ) => {
    const fulfillGroup = fulfillGroups[index] ?? [];
    const { supplyMode, shopName } = mainOrderInfo;
    const isVirtualDelivery = supplyMode === ISupplyMode.VIRTUAL;

    return (
      <>
        {fulfillGroup.slice(0, 1).map(({ warehouseId, warehouseName, fulfillNo }) => {
          const url = setUrlDomain(`/v2/order/fulfilldetail#/?fulfillNo=${fulfillNo}`, 'store');

          return (
            <Fragment key={warehouseId}>
              <p>{isVirtualDelivery ? shopName : warehouseName}</p>
              {fulfillNo ? (
                <p className="shipment_num">
                  发货单号：
                  {isFxStore || isVirtualDelivery ? (
                    <span>{fulfillNo}</span>
                  ) : (
                    <BlankLink href={url}>{fulfillNo}</BlankLink>
                  )}
                </p>
              ) : null}
            </Fragment>
          );
        })}
        {fulfillGroup.length > 1 ? (
          <Pop
            trigger="click"
            position="bottom-left"
            content={fulfillGroup.map(({ warehouseId, warehouseName, fulfillNo }) => {
              const url = setUrlDomain(`/v2/order/fulfilldetail#/?fulfillNo=${fulfillNo}`, 'store');
              return (
                <p key={warehouseId}>
                  {`${warehouseName} 发货单号：`}
                  <BlankLink href={url}>{fulfillNo}</BlankLink>
                </p>
              );
            })}
          >
            <a>查看全部</a>
          </Pop>
        ) : null}
      </>
    );
  };

  // 获取货单分组(用来显示发货方栏)
  getFulfillGroups = (
    sortItems: Array<
      Contains<{ warehouseIds?: number[]; warehouseNames?: string[]; fulfillNos: string[] }>
    > = []
  ) => {
    return sortItems.map(item => {
      const { warehouseIds = [], warehouseNames = [], fulfillNos } = item;
      return warehouseIds.map((warehouseId, index) => {
        return {
          warehouseId,
          warehouseName: warehouseNames[index],
          fulfillNo: fulfillNos[index]
        };
      });
    });
  };

  onAllRefund = (isCanOnlyRefundAll: boolean) => {
    // 三方券支付的订单需要先执行勾选操作
    if (isCanOnlyRefundAll) {
      this.setState({
        checkedList: this.props.itemInfo?.map(item => item.itemIdStr)
      });
    }
    // Into next event loop
    setTimeout(() => {
      this.allRefund();
    }, 0);
  };

  render() {
    const { checkedList, itemIdStrs, loading, fetchData, checkedItem, isRefund, wrapperWidth } =
      this.state;
    const {
      itemInfo = [],
      mainOrderInfo,
      reload,
      isFulfillOrder,
      fulfillOrder,
      sourceInfo,
      multiOrderInfo
    } = this.props;
    const checkedAll = !!checkedList.length && checkedList.length === itemIdStrs.length;
    const indeterminate = !!checkedList.length && checkedList.length !== itemIdStrs.length;
    const isMeituanOrder = checkIsMeituanOrder(mainOrderInfo.channelType);
    const sortItems =
      isHqStore ||
      isPartnerStore ||
      isUnifiedOnlineBranchStore ||
      isRetailMinimalistShop ||
      isFxStore
        ? this.sortItemByFulfillNo(itemInfo)
        : itemInfo;

    /** 是否存在新的网店换货流程 */
    const isIncludesExchangeOrder = !!sortItems?.find(i => i.isNewExchangeFlow);

    const goodsInfoColWidth = Math.max(Math.ceil(wrapperWidth * 0.3), 268);
    const FulfillOrderColumns: any = [
      {
        title: '商品',
        textAlign: 'left',
        width: goodsInfoColWidth,
        bodyRender: (goodsInfo: any) => this.renderGoodsInfo(goodsInfo)
      },
      {
        title: '商品库商品名称',
        textAlign: 'left',
        width: 150,
        className: 'goods-name',
        bodyRender: (goodsInfo: any) => this.renderRealGoodsName(goodsInfo)
      },
      {
        title: '数量',
        textAlign: 'center',
        bodyRender: (goodsInfo: any) => this.renderPrice(goodsInfo)
      },
      {
        title: '发货状态',
        textAlign: 'center',
        bodyRender: (goodsInfo: any) => this.renderFulfillStatus(goodsInfo)
      },
      {
        judgement: !!this.props.isProcessSettingOn,
        title: '作业状态',
        textAlign: 'center',
        bodyRender: (goodsInfo: any) => this.renderProcessStatus(goodsInfo)
      },
      {
        title: '运费(元)',
        textAlign: 'center',
        className: 'with-border-left',
        judgement: /** 多仓包裹运费不对, 需要隐藏 */ !fulfillOrder?.multiWarehouse,
        bodyRender: (_, pos: IGridCellPos) => {
          const { row } = pos;
          if (row === 0) {
            return {
              props: {
                rowSpan: sortItems.length
              },
              children: this.renderPostage()
            };
          }
          return {
            props: {
              rowSpan: 0
            }
          };
        }
      },
      {
        title: '实付运费(元)',
        textAlign: 'center',
        judgement: /** 多仓包裹运费不对, 需要隐藏 */ !fulfillOrder?.multiWarehouse,
        bodyRender: (_, pos: IGridCellPos) => {
          const { row } = pos;
          if (row === 0) {
            return {
              props: {
                rowSpan: sortItems.length
              },
              children: this.renderActualPostage()
            };
          }
          return {
            props: {
              rowSpan: 0
            }
          };
        }
      }
    ];

    const DefaultColumns: any = [
      {
        title: (() => {
          const { checkedList, itemIdStrs } = this.state;
          const checkedAll = !!checkedList.length && checkedList.length === itemIdStrs.length;
          const indeterminate = !!checkedList.length && checkedList.length !== itemIdStrs.length;

          return this.useOnlineOrderRefund() && !this.isCanOnlyRefundAll ? (
            <Checkbox
              checked={checkedAll}
              indeterminate={indeterminate}
              onChange={this.handleCheckedAll}
              disabled={!itemIdStrs.length}
            >
              商品
            </Checkbox>
          ) : (
            '商品'
          );
        })(),
        width: goodsInfoColWidth,
        fixed: true,
        textAlign: 'left',
        bodyRender: (goodsInfo: any) => this.renderGoodsInfo(goodsInfo)
      },
      {
        title: '单价(元)/数量',
        width: 120,
        textAlign: 'right',
        bodyRender: (goodsInfo: any) => this.renderPrice(goodsInfo)
      },
      {
        title: '优惠价(元)',
        width: 120,
        textAlign: 'right',
        bodyRender: ({ discountUnitPrice }: any) =>
          discountUnitPrice ? convertFenToYen(discountUnitPrice) : 0
      },
      {
        title: '优惠折扣',
        width: 120,
        textAlign: 'right',
        // 优惠折扣字段存在，并且优惠折扣不为1(为1代表没有折扣；为0代表免费)
        bodyRender: ({ goodsDiscountRate }: any) =>
          goodsDiscountRate !== undefined && +goodsDiscountRate !== 1 ? goodsDiscountRate : '无折扣'
      },
      {
        title: '积分价',
        width: 120,
        textAlign: 'right',
        judgement: this.isUglyPoints(),
        bodyRender: (goodsInfo: any) => this.renderPoints(goodsInfo)
      },
      {
        judgement: this.showCollectCardInfo(),
        title: '集点兑换',
        textAlign: 'right',
        bodyRender: (goodsInfo: any) => this.renderPointCar(goodsInfo)
      },
      {
        title: (
          <>
            交易额
            <Pop
              className="buyway-detail__pop"
              wrapperClassName="buyway-detail__pop-trigger"
              trigger="hover"
              centerArrow
              position="top-center"
              content="交易额 = 优惠价 * 商品数量"
            >
              <Icon className="price-info-item__icon" type="help-circle" />
            </Pop>
          </>
        ), // 和原来的小计(元)后端逻辑不一致
        width: 120,
        textAlign: 'right',
        bodyRender: (goodsInfo: any) => this.renderSubtotalled(goodsInfo)
      },
      {
        title: '优惠明细',
        name: 'goodsActivities',
        textAlign: 'left',
        width: 160,
        bodyRender: (goodsInfo: any) => this.renderDiscountDetails(goodsInfo)
      },
      // 商品税费
      {
        title: '税费',
        textAlign: 'right',
        hidden: !hasTariffGood(sortItems.map(({ extra }) => extra)),
        bodyRender: ({ extra }: any) => {
          try {
            return renderTariff(deepJsonParse(extra)?.orderItemExtraInTotal || []);
          } catch (err) {
            Notify.error(err.msg);
            return '-';
          }
        }
      },
      {
        title: '售后状态',
        textAlign: 'left',
        width: 160,
        hidden: !isIncludesExchangeOrder,
        bodyRender: (goodsInfo: IOrderItemInfo) => this.renderExchangeStatus(goodsInfo)
      },
      {
        title: '发货状态',
        textAlign: 'left',
        width: 160,
        bodyRender: (goodsInfo: any) => this.renderExpressState(goodsInfo)
      },
      {
        judgement: !!this.props.isProcessSettingOn,
        title: '作业状态',
        textAlign: 'left',
        width: 160,
        bodyRender: (goodsInfo: any) => this.renderProcessStatus(goodsInfo)
      },
      {
        judgement: this.showFulfillInfo(),
        title: '发货方',
        textAlign: 'left',
        width: 160,
        bodyRender: (_, { row }: IGridCellPos) => {
          const { itemInfo = [], mainOrderInfo } = this.props;
          const sortItems =
            isHqStore ||
            isPartnerStore ||
            isUnifiedOnlineBranchStore ||
            isRetailMinimalistShop ||
            isFxStore
              ? this.sortItemByFulfillNo(itemInfo)
              : itemInfo;
          const fulfillGroups =
            isHqStore ||
            isPartnerStore ||
            isUnifiedOnlineBranchStore ||
            isRetailMinimalistShop ||
            isFxStore
              ? this.getFulfillGroups(sortItems)
              : [];
          return this.renderFulfillInfo(fulfillGroups, row, mainOrderInfo!);
        }
      },
      {
        title: '退款状态',
        bodyRender: (goodsInfo: any, { row }: IGridCellPos) => {
          return this.renderRefundState(goodsInfo, row, sortItems.length);
        },
        width: 160,
        fixed: 'right',
        textAlign: 'center',
        className: 'item--refund'
      }
    ].filter(({ hidden }) => !hidden);

    return (
      <div className={cx(style['goods-list'], 'info-block')} ref={this.wrapperIns}>
        {/* 整单退款 */}
        {this.props.isOnlineOrder() && !isMeituanOrder && isRefund && (
          <div className="refund">
            {this.isCanOnlyRefundAll ? null : (
              <Checkbox
                className="checkbox-refund"
                checked={checkedAll}
                indeterminate={indeterminate}
                onChange={this.handleCheckedAll}
                disabled={!itemIdStrs.length}
              />
            )}
            <SuperButton
              loading={loading}
              disabled={!this.isCanOnlyRefundAll && !checkedList.length}
              onClick={() => this.onAllRefund(this.isCanOnlyRefundAll)}
              samName="主动退款"
            >
              整单退款
            </SuperButton>
          </div>
        )}
        {isFulfillOrder ? (
          <Grid
            disableHoverHighlight
            className={cx('list-table', style['grid-wrapper'])}
            rowClassName="goods-item"
            columns={handleColumnFilter(FulfillOrderColumns)}
            datasets={sortItems}
            rowKey="itemIdStr"
          />
        ) : (
          <Grid
            disableHoverHighlight
            className={cx('list-table', style['grid-wrapper'])}
            rowClassName={data => {
              return cx(
                'goods-item',
                /** 门店订单赠品排不了序，不做赠品特殊样式展示 */ !sourceInfo?.isOfflineOrder &&
                  data.isPresent &&
                  'goods-item--gift'
              );
            }}
            scroll={{ x: 1400 }}
            columns={handleColumnFilter(DefaultColumns)}
            datasets={sortItems}
            rowKey="itemIdStr"
          />
        )}
        {/* 主动退款的弹窗 */}
        {fetchData && (
          <Dialog
            onClose={this.onClose}
            style={{ width: 720 }}
            visible
            title="主动退款"
            className={sellerRefundStyle['refund-container']}
          >
            <Content
              allItems={itemInfo}
              itemInfo={checkedItem}
              mainOrderInfo={mainOrderInfo}
              multiOrderInfo={multiOrderInfo}
              reload={reload}
              isOnline
              canSelectItem={!this.props.isOnlineOrder()}
              onClose={this.onClose}
              fetchData={fetchData}
              // isAnyExpressed={this.checkIsAnyExpressed()}
              isVirtual={fetchData?.orderInfo?.supportVirtualTicketReturnNos}
            />
          </Dialog>
        )}
      </div>
    );
  }
}

export default GoodsList;
