import * as React from 'react';
import { Grid, Pop } from 'zent';
import { LotCodeItem } from 'definition/lot-code';

import style from './style.scss';

export function LotCodePopButton({ datasets = [] }: { datasets: LotCodeItem[] }): JSX.Element {
  const columns = [
    {
      title: '批号',
      name: 'lotCode'
    },
    {
      title: '商品数量',
      name: 'num'
    }
  ];
  const content = (
    <div className={style['grid-wrapper']}>
      <Grid columns={columns} datasets={datasets} bordered={false} />
    </div>
  );
  return (
    <Pop trigger="click" content={content}>
      <span className={style.button}>查看批号</span>
    </Pop>
  );
}
