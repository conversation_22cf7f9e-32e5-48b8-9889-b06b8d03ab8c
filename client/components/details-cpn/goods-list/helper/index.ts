import { deepJsonParse } from '@youzan/retail-utils';
import { convertFenToYen } from 'common/fns/format';

interface IFees {
  name: string;
  originPay: number;
  pay: number;
  realPay: number;
}

const TariffName = 'GOODS_TARIFF';

function findTariffByExtra(fees: IFees[] = []) {
  const fee = fees.find(({ name }) => TariffName === name);
  return fee;
}

// 找到商品详情-海淘商品的税费值
export function renderTariff(fees: IFees[] = []) {
  const fee = findTariffByExtra(fees);

  if (!fee) {
    return '-';
  }

  return convertFenToYen(fee.realPay);
}

// 商品是否包含有税费的条目
export function hasTariffGood(extras: Array<any>) {
  const match = extras.some(extra => {
    try {
      const fee = (deepJsonParse(extra)?.orderItemExtraInTotal || []).find(
        ({ name }: { name: string }) => TariffName === name
      );
      return !!fee;
    } catch (err) {
      return false;
    }
  });

  return match;
}

// FIX: https://jira.qima-inc.com/browse/ONLINE-707662
// 分销海淘订单总税费计算，除商品税费外，需要把 POSTAGE_TARIFF 计算上
const OrderTariffNames = ['GOODS_TARIFF', 'POSTAGE_TARIFF'];
export function findOrderTotalTariffByExtra(fees: IFees[] = []) {
  const realFees = fees.filter(({ name }) => OrderTariffNames.includes(name));
  return realFees;
}

// 订单详情-税费总和
export function calculateTariffTotal(goods: any) {
  const total = goods.reduce((cur: number, { extra }: any) => {
    try {
      const fees = findOrderTotalTariffByExtra(deepJsonParse(extra)?.orderItemExtraInTotal || []);
      if (fees && fees.length > 0) {
        return (
          cur +
          fees.reduce((midVal, val) => {
            return midVal + (val.realPay || 0);
          }, 0)
        );
      }
      return cur;
    } catch (err) {
      return cur;
    }
  }, 0);

  return convertFenToYen(total);
}
