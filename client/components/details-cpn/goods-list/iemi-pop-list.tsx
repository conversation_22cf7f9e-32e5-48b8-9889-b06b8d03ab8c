import type { IGoodsInfoProps } from 'components/goods-info';

import * as React from 'react';
import { Sweetalert } from 'zent';
import GoodsInfo from 'components/goods-info';

import style from './style.scss';

const STATUS_MAP = {
  ORIGINAL: {
    key: 5,
    title: '原'
  },
  DELIVERED: {
    key: 1,
    title: '已发货'
  },
  REFUND: {
    key: 2,
    title: '已退货'
  },
  EXCHANGE: {
    key: 3,
    title: '已换货'
  },
  UNDELIVERED: {
    key: 4,
    title: '未发货'
  }
};

function getFormattedSerialNum(
  data: Record<number, string[]>
): Array<{ title: string; data: string[] }> {
  return Object.keys(STATUS_MAP)
    .filter((status: keyof typeof STATUS_MAP) => data[STATUS_MAP[status].key])
    .map((status: keyof typeof STATUS_MAP) => {
      const item = STATUS_MAP[status];
      return {
        title: item.title,
        data: data[item.key]
      };
    });
}

function showSerialPop(data: Record<number, string[]>, goods: IGoodsInfoProps): void {
  const formattedSerialNum = getFormattedSerialNum(data);

  Sweetalert.alert({
    title: '查看唯一码',
    confirmText: '确定',
    content: (
      <div className={style['serial-list']}>
        <div className="goods-info__combined">
          {/*
          // @ts-ignore */}
          <GoodsInfo
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...goods}
          />
          <div className="goods-info__unit">{`单位：${goods.unit}`}</div>
        </div>
        {formattedSerialNum.map(list => {
          return (
            <div key={list.title} className="list">
              <p className="title">{`${list.title}商品(${list.data.length})`}</p>
              {list.data.map(item => (
                <p key={item}>{item}</p>
              ))}
            </div>
          );
        })}
      </div>
    )
  });
}

export default ({
  data = {},
  goods
}: {
  data: Record<number, string[]>;
  goods: IGoodsInfoProps;
}): JSX.Element => {
  return (
    <span className={style['goods-iemi']} onClick={(): void => showSerialPop(data, goods)}>
      查看唯一码
    </span>
  );
};
