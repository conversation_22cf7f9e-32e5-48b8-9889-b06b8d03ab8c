@import '~shared/style';

$BORDER_COLOR: $border-color-base;

.goods-info {
  text-align: left;
}

:local(.goods-iemi) {
  color: $color-link;
  cursor: pointer;
}

:local(.serial-list) {
  .goods-info__combined {
    display: flex;
    background: $background-color-base;
    width: auto;
    align-items: center;

    .goods-info {
      width: 310px;
    }
  }

  .list {
    margin-top: 8px;
    padding-bottom: 8px;
    box-shadow: inset 0 -1px 0 0 $border-color-base;

    p {
      margin-bottom: 5px;
    }
  }

  .title {
    color: $color-text-light-primary;
    margin-bottom: 8px;
  }
}

.ump-info__item {
  span {
    white-space: nowrap;
  }
}

:local(.goods-list) {
  .refund {
    margin: 0 0 10px 12px;

    .checkbox-refund {
      margin-right: 10px;
    }
  }

  .shipment_num {
    span,
    a {
      word-break: break-all;
    }
  }

  .list-table {
    text-align: center;
    min-width: 100%;
    max-width: 100%;

    .align-left {
      padding-left: 20px;
      text-align: left;
    }

    .aling-right {
      text-align: right;
    }

    &__head {
      background-color: $background-color-base;
      box-shadow: inset 0 -1px 0 0 $BORDER_COLOR;

      th {
        padding: 12px 0;
        font-weight: bold;

        &:first-child {
          padding-left: 12px;
          text-align: left;
        }

        &:last-child {
          text-align: right;
          padding-right: 12px;
        }
      }
    }

    td {
      min-width: 82px;

      .goods-remark {
        font-size: $font-size-small;
        color: $color-text-secondary;
        text-align: left;
        padding-left: 22px;
        margin-bottom: 8px;
      }

      .goods-img {
        font-size: $font-size-small;
        color: $color-text-secondary;
        text-align: left;
        margin-top: 8px;
        &-box {
          min-width: 50px;
          display: inline-block;
          margin: 0 5px 5px 0;
          img {
            vertical-align: top;
            width: 40px;
            height: 40px;
            margin-left: 2px;
            object-fit: contain;
            background-color: $background-color-base;
          }
        }
      }
    }

    .price-info-item__icon {
      cursor: pointer;
      font-size: $font-size-small;
      margin-left: 4px !important;
    }

    .goods-item {
      td {
        vertical-align: middle;
      }

      .goods-info {
        min-width: 220px;
      }

      .goods-name {
        text-align: left;
      }

      .out-of-date-price {
        color: $color-text-secondary;
        text-decoration: line-through;
      }

      .tag {
        display: inline-block;
        border: 1px solid currentColor;
        border-radius: 3px;
        padding: 0 4px;
        text-align: center;
        margin-right: 5px;
        font-size: $font-size-small;

        &:last-child {
          margin-right: 0;
        }
      }

      .present-tag {
        color: $color-alert;
      }

      .item--refund {
        .zent-btn {
          font-size: $font-size-small;
        }
      }

      .item--ump-info {
        color: $color-n8;
        padding: 0 12px;
      }

      .disabled,
      .item-goods__num {
        color: $color-text-secondary;
      }
      .item-goods_discount-item {
        display: flex;
        justify-content: space-between;
        color: $color-text-secondary;
        &-value {
          white-space: nowrap;
          flex-shrink: 1;
          text-align: right;
        }
      }
    }

    /** `.goods-item +` 用于防止单个赠品商品时, 也使用赠品样式 */

    .goods-item + .goods-item--gift {
      $gift-goods-bg-color: #f7f7f7;

      background-color: $gift-goods-bg-color;

      &:hover {
        background-color: $gift-goods-bg-color;
      }

      /** 赠品定制商品样式 */
      .goods-item__info {
        .rc-goods-cell-img {
          .retail-lazyimage-container {
            display: flex;
            justify-content: flex-end;
          }

          img {
            width: 40px;
            height: 40px;
          }
        }
      }
    }
  }
}

.with-border-left {
  border-left: 1px solid $BORDER_COLOR;
}

.with-border {
  border-right: 1px solid $BORDER_COLOR;
  border-left: 1px solid $BORDER_COLOR;
}

.price-line {
  text-decoration: line-through;
  color: $color-text-secondary;
}

:local(.button) {
  color: $color-link;
  cursor: pointer;
}

:local(.grid-wrapper) {
  // 被全局样式污染了, 这里覆盖一下
  .zent-grid-td {
    border-right: none;
  }
  .zent-grid-td-multiple-row {
    border-right: 1px solid var(--theme-stroke-6, #ebedf0);
  }
}
