.group-info-area {
  background-color: #f7f8fa;
  border: 1px solid #f7f8fa;
  margin-bottom: 14px;
  padding: 12px 0;
  box-sizing: border-box;

  .group-info-area_title {
    font-size: 14px;
    margin-bottom: 14px;
    color: #323233;
    font-weight: 700;
    padding: 0 12px;
  }

  .group-info-content {
    display: flex;
  }

  .group-info,
  .commission-info,
  .order-info {
    width: 25%;
    min-width: 300px;
    padding: 0 12px;
    box-sizing: border-box;
  }

  ul {
    font-size: 12px;
    li {
      display: flex;
      line-height: 21px;
    }

    label {
      flex-shrink: 0;
      text-align: left;
      vertical-align: top;
      color: #969799;
    }

    .text {
      display: flex;
      flex-grow: 1;
      word-break: break-all;
      vertical-align: top;
      color: #323233;
    }

    .group-text {
      display: flex;
      flex-direction: column;
    }

    .note-title {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      word-break: break-all;
    }

    .btn-box {
      display: flex;
      margin-top: 2px;
    }
  }

  .mt-14 {
    margin-top: 14px;
  }

  .link-btn {
    margin-right: 4px;
    white-space: nowrap;
  }
}
.dialog-empty {
  height: 8px;
}
