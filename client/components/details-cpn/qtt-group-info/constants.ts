/* eslint-disable @youzan/yz-retail/prefer-pascal-case-const */

/**
 * note.settleType: 结算模式
 * 1/确认收货后结算
 * 2/每月10号结算上月确认收货订单
 */
export const SETTLE_TYPE_MAP = {
  AFTER_CONFIRM_RECEIVE: 1,
  NEXT_MONTH: 2,
  PAID_SUCCESS: 3,
};

/**
 * 展示的结算模式文案
 */
export const SETTLE_TEXT_MAP = {
  [SETTLE_TYPE_MAP.AFTER_CONFIRM_RECEIVE]: '确认收货后结算',
  [SETTLE_TYPE_MAP.NEXT_MONTH]: '每月10号结算上月确认收货订单',
  [SETTLE_TYPE_MAP.PAID_SUCCESS]: '实时结算',
};

/**
 * note.promoterSettleStatus: 帮卖佣金结算状态
 * 101/未结算
 * 102/结算成功
 */
export const PROMOTER_SETTLE_STATUS_MAP = {
  NOT: 101,
  SUCCESS: 102
};

/**
 * note.inviterSettleStatus: 邀请佣金结算状态
 * 101/未结算
 * 102/结算成功
 */
export const INVITER_SETTLE_STATUS_MAP = {
  NOT: 101,
  SUCCESS: 102
};

/**
 * note.promoterTaxSignStatus: 帮卖团长个税签约状态
 * 0/未签约
 * 1/已签约
 * 2/无需签约（开团团长未开通、店铺身份无需签约）
 */

export const PROMOTER_TAX_SIGN_STATUS_MAP = {
  NOT: 0,
  COMPLETE: 1,
  NOT_NEED: 2
};

/**
 * note.inviterTaxSignStatus: 邀请团长个税签约状态
 * 0/未签约
 * 1/已签约
 * 2/无需签约（开团团长未开通、店铺身份无需签约）
 */
export const INVITER_TAX_SIGN_STATUS_MAP = {
  NOT: 0,
  COMPLETE: 1,
  NOT_NEED: 2
};

/**
 * 佣金结算状态
 * 101/未结算
 * 102/结算成功
 */
export const SETTLE_STATUS = {
  NONE_SETTLE: 101,
  SETTLE_SUCCESS: 102
};
