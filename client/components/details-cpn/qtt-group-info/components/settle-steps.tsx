import React from 'react';
import { Steps } from 'zent';
import formatDate from '@youzan/utils/date/formatDate';
import { SETTLE_STATUS } from '../constants';

interface ICommissionSettle {
  isNeedTax: boolean; // 是否需要缴税
  settleStatus: number; // 结算状态
  settledStartAt: number; // 结算开始时间
  settledEndAt: number; // 结算到账时间
}

interface IProps {
  settle: ICommissionSettle;
}

const { Step } = Steps;

const getFormatDate = time => {
  return time ? formatDate(time, 'YYYY-MM-DD HH:mm:ss') : '';
};

export default function StatusStep(props: IProps) {
  const { settle = {} as ICommissionSettle } = props;
  const hasSettleSuc = settle.settleStatus === SETTLE_STATUS.SETTLE_SUCCESS;
  const hasCommission = !!settle.settledStartAt;

  const STEPS = [
    {
      title: `佣金${hasCommission ? '已计费' : '计费中'}`,
      description: `${getFormatDate(settle.settledStartAt)}`
    },
    {
      // eslint-disable-next-line no-nested-ternary
      title: `佣金${hasSettleSuc ? '已发放到账' : hasCommission ? '发放中' : '发放'}`,
      description: `${getFormatDate(settle.settledEndAt)}`
    }
  ];

  // eslint-disable-next-line no-nested-ternary
  const current = hasSettleSuc ? 2 : settle.settledStartAt ? 1 : 0;

  return (
    <div style={{ width: 200, padding: 16 }}>
      <Steps
        current={current}
        status={current === STEPS?.length ? 'finish' : 'process'}
        direction="vertical"
      >
        {STEPS.map((item, index) => (
          <Step title={item.title} description={item.description} key={index} />
        ))}
      </Steps>
    </div>
  );
}
