import * as React from 'react';
import { BlankLink } from '@youzan/react-components';
import { Pop, Icon, Dialog, Grid, IGridColumn, CopyButton } from 'zent';
import type { Contains } from 'definition/common';
import createOrderCpm from 'components/create-order-cpm';
import {
  IMainOrderInfo,
  IQttOrderInfo,
  IQttNote,
  IQttOrder,
  IQttRefundRecord
} from 'definition/order-info';
import formatMoney from '@youzan/utils/money/format';
import formatDate from '@youzan/utils/date/formatDate';
import SettleSteps from './components/settle-steps';
import { generateShortLink, getQrCodeImg } from './api';
import {
  SETTLE_TEXT_MAP,
  PROMOTER_SETTLE_STATUS_MAP,
  INVITER_SETTLE_STATUS_MAP,
  PROMOTER_TAX_SIGN_STATUS_MAP,
  INVITER_TAX_SIGN_STATUS_MAP
} from './constants';
import './style.scss';

interface IState {
  note: IQttNote;
  order: IQttOrder;
  refundRecords: IQttRefundRecord[];
  dialogVisible: boolean;
  mpShortLink: string;
  mpQrcode: string;
}

@createOrderCpm()
class QttGroupInfo extends React.Component<
  Contains<{
    mainOrderInfo: IMainOrderInfo;
    qttOrderInfo: IQttOrderInfo;
    reload: () => void;
  }>
> {
  state: IState = {
    note: {} as IQttNote,
    order: {} as IQttOrder,
    refundRecords: [],
    dialogVisible: false,
    mpShortLink: '',
    mpQrcode: ''
  };

  componentDidMount() {
    // 此处是历史逻辑修改来的，原先此处有接口请求，后挪到外部了，setState逻辑保持不懂
    const { qttOrderInfo } = this.props;
    const { note = {}, refundRecords = [], order = {} } = qttOrderInfo;
    this.setState(
      {
        note,
        order,
        refundRecords
      },
      () => {
        this.getLinkAndQrcode();
      }
    );
  }

  // 获取小程序短链和二维码
  getLinkAndQrcode() {
    const { note } = this.state;
    const pageUrl = `packages/detail-page/view/index?type=distributor&noteId=${note.noteId}`;

    Promise.all([
      generateShortLink({
        pageTitle: '点我跟团买👈🏻',
        pageUrl
      }),
      getQrCodeImg({
        weappUrl: pageUrl
      })
    ]).then(([res1, res2]) => {
      const { miniProgramUrl = '' } = res1 || {};
      this.setState({
        mpShortLink: miniProgramUrl,
        mpQrcode: res2
      });
    });
  }

  // 分 -> 元
  formatMoney(amount: number) {
    return formatMoney(amount);
  }

  setDialogVisible(visible: boolean) {
    this.setState({
      dialogVisible: visible
    });
  }

  getColumns() {
    const { mainOrderInfo } = this.props;
    const { orderNo } = mainOrderInfo;

    const columns: IGridColumn[] = [
      {
        title: '维权单号',
        name: 'refundId',
        bodyRender: data => {
          const { refundId } = data;
          const url = `/v2/order/refunddetail#/?order_no=${orderNo}&refund_id=${refundId}`;
          return <BlankLink href={url}>{refundId}</BlankLink>;
        },
        width: 230,
        fixed: true
      },
      {
        title: '维权状态',
        bodyRender: () => {
          return '退款成功';
        },
        width: 100
      },
      {
        title: '应退佣金',
        bodyRender: data => {
          const { fee } = data;
          return `¥${this.formatMoney(fee)}`;
        },
        width: 100
      },
      {
        title: '佣金类型',
        name: 'feeName',
        width: 100
      },
      {
        title: '佣金状态',
        name: 'feeRefundStatusStr'
      },
      {
        title: '佣金退款时间',
        bodyRender: data => {
          const { refundAt } = data;
          if (!refundAt) {
            return '';
          }
          return formatDate(refundAt, 'YYYY-MM-DD HH:mm:ss');
        },
        width: 200
      },
      {
        title: '团长',
        bodyRender: data => {
          const { partyName = '', partyPhone = '' } = data;
          return `${partyName} ${partyPhone}`;
        },
        width: 200
      }
    ];
    return columns;
  }

  render() {
    const { mainOrderInfo } = this.props;
    const { note, order, refundRecords, dialogVisible, mpShortLink, mpQrcode } = this.state;
    const refundRecordsLen = refundRecords.length;
    const columns = this.getColumns();

    const outerBizNo = mainOrderInfo?.extra?.oRIGIN_OUT_BIZ_NO;

    return note.noteId ? (
      <div className="group-info-area">
        <div className="group-info-area_title">团购信息</div>
        <div className="group-info-content">
          <div className="group-info">
            <ul>
              <li>
                <label>所属团购：</label>
                <div className="text group-text">
                  <Pop
                    trigger="hover"
                    position="top-left"
                    content={<div style={{ maxWidth: 400 }}>{note.noteTitle}</div>}
                  >
                    <div className="note-title">{note.noteTitle}</div>
                  </Pop>
                  <div className="btn-box">
                    {mpShortLink ? (
                      <CopyButton text={mpShortLink}>
                        <a className="link-btn" href="javascript:void(0)">
                          复制链接
                        </a>
                      </CopyButton>
                    ) : null}{' '}
                    {mpQrcode ? (
                      <Pop
                        trigger="click"
                        position="top-left"
                        content={<img src={mpQrcode} width="150" height="150" alt="小程序码" />}
                      >
                        <a className="link-btn" href="javascript:void(0)">
                          查看团购
                        </a>
                      </Pop>
                    ) : null}
                  </div>
                </div>
              </li>
              {note.promoterId ? (
                <li>
                  <label>帮卖团长：</label>
                  <div className="text">
                    {note.promoterName} {note.promoterPhone}
                  </div>
                </li>
              ) : null}
              {note.inviterId ? (
                <li>
                  <label>邀请人：</label>
                  <div className="text">
                    {note.inviterName} {note.inviterPhone}
                  </div>
                </li>
              ) : null}
              {order.participateNo ? (
                <li>
                  <label>群团团跟团号：</label>
                  <div className="text">{order.participateNo}</div>
                </li>
              ) : null}
            </ul>
          </div>

          {note.promoterId ? (
            <div className="commission-info">
              <ul>
                {/* 帮卖佣金 */}
                <li>
                  <label>帮卖+专属佣金：</label>
                  <div className="text">￥{this.formatMoney(note.promoterFee)}</div>
                </li>
                {note.promoterTaxSignStatus !== PROMOTER_TAX_SIGN_STATUS_MAP.NOT_NEED ? (
                  <li>
                    <label>个税：</label>
                    <div className="text">
                      ￥{this.formatMoney(note.promoterTaxFee)}
                      {note.promoterTaxSignStatus === PROMOTER_TAX_SIGN_STATUS_MAP.NOT ? (
                        <div style={{ display: 'inline-block', color: '#ED6A0C' }}>
                          （未签约
                          <Pop
                            trigger="hover"
                            position="top-left"
                            content="该团长还未完成个税代缴认证签约，已到达结算时间的佣金将在认证签约后次日缴税并结算"
                          >
                            <Icon
                              type="help-circle"
                              className="refund-help-circle"
                              style={{ color: '#969799' }}
                            />
                          </Pop>
                          ）
                        </div>
                      ) : null}
                    </div>
                  </li>
                ) : null}
                <li>
                  <label>状态：</label>
                  <div className="text">
                    {note.promoterSettleStatus === PROMOTER_SETTLE_STATUS_MAP.NOT
                      ? '未结算'
                      : '已结算'}{' '}
                    {SETTLE_TEXT_MAP[note.settleType]}
                    {note.promoterCommissionSettle && (
                      <Pop
                        trigger="click"
                        position="bottom-center"
                        content={<SettleSteps settle={note.promoterCommissionSettle} />}
                      >
                        <div style={{ marginLeft: 12, color: '#155bd4' }}>查看结算进度</div>
                      </Pop>
                    )}
                  </div>
                </li>
              </ul>

              {/* 邀请佣金 */}
              {note.inviterId ? (
                <ul className="mt-14">
                  <li>
                    <label>邀请佣金：</label>
                    <div className="text">￥{this.formatMoney(note.inviterFee)}</div>
                  </li>
                  {note.inviterTaxSignStatus !== INVITER_TAX_SIGN_STATUS_MAP.NOT_NEED ? (
                    <li>
                      <label>个税：</label>
                      <div className="text">
                        ￥{this.formatMoney(note.inviterTaxFee)}
                        {note.inviterTaxSignStatus === INVITER_TAX_SIGN_STATUS_MAP.NOT ? (
                          <div style={{ display: 'inline-block', color: '#ED6A0C' }}>
                            （未签约
                            <Pop
                              trigger="hover"
                              position="top-left"
                              content="该团长还未完成个税代缴认证签约，已到达结算时间的佣金将在认证签约后次日缴税并结算"
                            >
                              <Icon
                                type="help-circle"
                                className="refund-help-circle"
                                style={{ color: '#969799' }}
                              />
                            </Pop>
                            ）
                          </div>
                        ) : null}
                      </div>
                    </li>
                  ) : null}
                  <li>
                    <label>状态：</label>
                    <div className="text">
                      {note.inviterSettleStatus === INVITER_SETTLE_STATUS_MAP.NOT
                        ? '未结算'
                        : '已结算'}{' '}
                      {SETTLE_TEXT_MAP[note.settleType]}
                      {note.inviterCommissionSettle && (
                        <Pop
                          trigger="click"
                          position="bottom-center"
                          content={<SettleSteps settle={note.inviterCommissionSettle} />}
                        >
                          <div style={{ marginLeft: 12, color: '#155bd4' }}>查看结算进度</div>
                        </Pop>
                      )}
                    </div>
                  </li>
                </ul>
              ) : null}

              {/* 佣金退款记录 */}
              {refundRecordsLen ? (
                <ul className="mt-14">
                  <li>
                    <label>佣金退款记录：</label>
                    <a
                      className="link-btn"
                      href="javascript:void(0)"
                      onClick={() => this.setDialogVisible(true)}
                    >
                      查看
                    </a>
                  </li>
                </ul>
              ) : null}
            </div>
          ) : null}

          <div className="order-info">
            {/* FixMe: 这里别用note.promoterId，后端那边写了特殊逻辑，特殊场景下会把promoterId置空 */}
            <ul>
              {!note.promoterYzId && outerBizNo && (
                <li>
                  <label>批量跟团外部订单号：</label>
                  <div className="text">{outerBizNo}</div>
                </li>
              )}
            </ul>
          </div>
        </div>

        {/* 佣金退款记录弹窗 */}
        <Dialog
          visible={dialogVisible}
          onClose={() => this.setDialogVisible(false)}
          title="佣金退款记录"
          style={{ width: '1000px' }}
        >
          <Grid
            className="dialog-grid"
            columns={columns}
            datasets={refundRecords}
            rowKey="refundId"
            scroll={{ x: 1100 }}
          />
          <div className="dialog-empty" />
        </Dialog>
      </div>
    ) : (
      <></>
    );
  }
}

export default QttGroupInfo;
