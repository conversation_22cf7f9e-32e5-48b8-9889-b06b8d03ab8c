import { request } from '@youzan/retail-utils';

interface IShortLink {
  pageTitle: string;
  pageUrl: string;
  env?: string;
}

interface IQrcode {
  weappUrl: string;
  env?: string;
}

// 小程序短链
export const generateShortLink = (data: IShortLink): Promise<any> => {
  return request({
    url: '/v4/fenxiao/wx-url/generateShortLink.json',
    data
  });
};

// 小程序二维码
export const getQrCodeImg = (data: IQrcode): Promise<any> => {
  return request({
    url: '/v4/fenxiao/wx-code/getWxQrCodeUltra.json',
    data
  });
};
