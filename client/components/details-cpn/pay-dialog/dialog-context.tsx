/* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
import * as React from 'react';
import { BlockHeader, Input, CopyButton, Button, Notify, Tabs } from 'zent';
import { setUrlDomain } from '@youzan/retail-utils';

import download from 'downloadjs';
import * as api from './api';
import CountDownButton from './count-down-button';
import './dialog-context.scss';
import { ISendMessage, IActiveId } from './type';

const { TabPanel } = Tabs;

const DialogContext = (props: ISendMessage) => {
  const { orderNo = '', buyerPhone = '', bizScene = 40, salesKdtId } = props;
  const codeOutSize = 400;
  const [qrcodeImageSrc, setQrcodeImageSrc] = React.useState('');
  const [weChatQrcodeImageSrc, setWeChatQrcodeImageSrc] = React.useState('');
  const [activeId, setActiveId] = React.useState<IActiveId>(IActiveId.H5);
  const [shareLink, setShareLink] = React.useState('');
  const QrCodeUrl = `https://cashier.youzan.com/pay/wsctrade_pay?order_no=${orderNo}&kdt_id=${salesKdtId}`;
  const weChatQrCodeUrl = `packages/trade-pay/pay/index?orderNo=${orderNo}`;

  // 获取二维码接口
  const getCode = () => {
    return api
      .getQrCode(QrCodeUrl)
      .then(imageSrc => {
        setQrcodeImageSrc(imageSrc);
      })
      .catch(error => Notify.error(error.message));
  };

  const getWeChatCode = () => {
    const { rootKdtId: initialRootKdtId = 0, kdtId = 0 } = _global?.business?.shopMetaInfo || {};
    const rootKdtId = initialRootKdtId || kdtId;

    return api
      .getWeChatQrCode({
        isShare: 0, // 0 专享版小程序，1 公共版小程序
        hyaLine: false, // 是否为透明底色
        kdtId: rootKdtId, // 店铺ID, 公共版写死为40419900
        page: 'pages/common/blank-page/index', // 落地页，写死
        params: {
          kdtId: rootKdtId, // 店铺ID，必须与外部 kdtId 一致
          guestKdtId: kdtId, // 真正的 kdtId
          page: 'packages/trade-pay/pay/index', // 跳转的小程序路径
          orderNo
        }
      })
      .then(res => {
        setWeChatQrcodeImageSrc(`data:image/jpeg;base64,${res.data}`);
      })
      .catch(error => Notify.error(error.message));
  };

  const getShareLink = () => {
    return api
      .getShareLink({
        linkType: 'short_link',
        scene: 'retail-node-order_create_order',
        referenceId: orderNo,
        isPermanent: false,
        sourceUrl: weChatQrCodeUrl
      })
      .then(res => {
        setShareLink(res.miniProgramUrl);
      })
      .catch(error => Notify.error(error.message));
  };

  React.useEffect(() => {
    getCode();
    getWeChatCode();
    getShareLink();
  }, []);

  const downloadCode = (src: string) => () => {
    const fileName = `img${Date.now()}`;
    try {
      const img = new Image();
      img.src = src;
      const c = document.createElement('canvas') as HTMLCanvasElement;
      const ctx = c.getContext('2d') as CanvasRenderingContext2D;
      c.width = codeOutSize;
      c.height = codeOutSize;
      img.onload = function () {
        ctx.drawImage(img, 0, 0, img.width, img.height, 0, 0, codeOutSize, codeOutSize);
        download(c.toDataURL('image/png'), fileName);
      };
    } catch (error) {
      Notify.error(error.message);
    }
  };

  return (
    <div className="dialog-wrapper">
      <BlockHeader title="方式1: 将付款码发送给客户" position="top-center" />
      <Tabs activeId={activeId} onChange={id => setActiveId(id)}>
        <TabPanel key={IActiveId.H5} tab="H5页面" id={IActiveId.H5}>
          <div className="send-qrcode">
            <div className="send-qrcode__img">
              {qrcodeImageSrc ? <img src={qrcodeImageSrc} alt="" /> : null}
            </div>
            <div className="send-qrcode__link">
              <span className="send-qrcode__link-title">分享链接</span>
              <Input className="send-qrcode__link-input" disabled value={QrCodeUrl} width={224} />
              <CopyButton text={QrCodeUrl}>
                <Button type="primary" className="send-qrcode__link-button">
                  复制
                </Button>
              </CopyButton>
              <div className="send-qrcode__download">
                <a onClick={downloadCode(qrcodeImageSrc)}>保存二维码</a>
              </div>
            </div>
          </div>
        </TabPanel>
        <TabPanel key={IActiveId.WeChat} tab="微信小程序" id={IActiveId.WeChat}>
          <div className="send-qrcode">
            <div className="send-qrcode__img">
              {weChatQrcodeImageSrc ? <img src={weChatQrcodeImageSrc} alt="" /> : null}
            </div>
            <div className="send-qrcode__link">
              <span className="send-qrcode__link-title">分享链接</span>
              <Input className="send-qrcode__link-input" disabled value={shareLink} width={224} />
              <CopyButton text={shareLink}>
                <Button type="primary" className="send-qrcode__link-button">
                  复制
                </Button>
              </CopyButton>
              <div className="send-qrcode__download">
                <a onClick={downloadCode(weChatQrcodeImageSrc)}>保存小程序码</a>
              </div>
            </div>
          </div>
        </TabPanel>
      </Tabs>

      <BlockHeader title="方式2: 发短信给客户完成付款" position="top-center" />
      <div className="send-message">
        <div className="send-message__img">
          <img
            src={setUrlDomain('/upload_files/2021/11/02/FnGp4qfuLuX0fl5ugtuTjGVaYb5t.jpg', 'imgqn')}
            alt="短信"
            width="100%"
          />
        </div>
        <div className="send-message__operation">
          <div className="send-message__operation-tel">接收手机号: {buyerPhone}</div>
          <div className="send-message__operation-btn">
            <CountDownButton orderNo={orderNo} buyerPhone={buyerPhone} bizScene={bizScene} />
          </div>
        </div>
      </div>

      <>
        <BlockHeader
          title="方式3: 引导会员进入个人中心完成付款（小程序或H5）"
          position="top-center"
        />
        <div className="page">
          <div>
            <img
              src={setUrlDomain(
                '/upload_files/2021/11/02/FkpQxaMgwJS3pdS01z9krAMGrxlp.jpg',
                'imgqn'
              )}
              alt="pic"
              width="100%"
            />
          </div>
        </div>
      </>
    </div>
  );
};

export default DialogContext;
