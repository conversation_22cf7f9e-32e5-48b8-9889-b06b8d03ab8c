import { request } from '@youzan/retail-utils';
import ajax from 'zan-pc-ajax';

/**
 * 生成二维码
 */
export const getQrCode = text => {
  return request({
    url: '/v2/order/orderDetail/getQrCodes.json',
    method: 'GET',
    data: {
      text
    }
  });
};

export const getWeChatQrCode = data => {
  return ajax(`/v4/shop/api/weappCodeUltra`, {
    rawResponse: true,
    data: { params: JSON.stringify(data) }
  });
};

export const getShareLink = data => {
  return request({
    url: '/v4/deco/channels/generate-share-link.json',
    method: 'POST',
    data
  });
};

export const sendMessage = ({ orderNo, buyerPhone, bizScene }) => {
  return request({
    url: 'youzan.retail.msg.apply.notification/1.0.0',
    method: 'GET',
    data: {
      orderNo,
      buyerPhone,
      bizScene
    }
  });
};
