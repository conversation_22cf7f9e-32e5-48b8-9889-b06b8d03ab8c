import * as React from 'react';
import { Button, Notify } from 'zent';
import { usePolling } from '@youzan/react-hooks';
import { useEffect, useState } from 'react';
import { sendMessage } from './api';
import { ISendMessage } from './type';

const CountDownButton = (props: ISendMessage) => {
  const { orderNo = '', buyerPhone, bizScene } = props;
  const maxCount = 15;
  const [showCountDown, setShowCountDown] = useState(false);
  const [count, setCount] = useState(maxCount);
  const [startPolling, setPolling] = useState(false);

  usePolling(
    () => {
      setCount(count => {
        return count - 1;
      });
    },
    1000,
    startPolling
  );
  useEffect(() => {
    if (count <= 0) {
      setPolling(false);
      setShowCountDown(false);
    }
  }, [count]);

  // 发送模板短信成功
  const success = () => {
    // 进入倒计时开始轮询
    setCount(maxCount);
    setShowCountDown(true);
    setPolling(true);
  };

  const startCountdown = () => {
    // 发送短信模板
    try {
      sendMessage({ orderNo, buyerPhone, bizScene })
        .then(res => {
          if (res) {
            Notify.success('发送短信成功');
          } else {
            Notify.error('发送短信失败');
          }
        })
        .catch(err => {
          Notify.error(err.message || '发送短信失败');
        });
    } catch (err) {
      Notify.error('发送短信失败');
    }
    success();
  };

  return !showCountDown ? (
    <Button type="primary" onClick={startCountdown}>
      发送短信
    </Button>
  ) : (
    <Button disabled>重新发送（{count}秒）</Button>
  );
};

export default CountDownButton;
