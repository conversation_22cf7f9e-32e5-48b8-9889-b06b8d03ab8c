import React from 'react';
import cx from 'classnames';
import { Icon, Pop, Dialog } from 'zent';
import { isNil, isObject, find, isNaN, get, sum } from 'lodash';
import { formatDatetime, minus } from '@youzan/retail-utils';
import createOrderCpm from 'components/create-order-cpm';
import { convertFenToYen } from 'common/fns/format';
import { combineUmpInfo, withOrderHelper } from 'common/helper';
import {
  isMeituanOrder,
  checkIsElemeOrder,
  isMeituanShangouOrder,
  isTiktokSxtOrder,
  isJdwmOrder
} from 'common/biz-helper';
import { ChannelType } from 'common/constants/common';

import '@youzan/retail-components/lib/styles/beautiful-table.css';
import LinkButton from 'components/link-button';
import PayItem from './components/pay-item';

import style from './style.scss';
import { getStringifiedValue } from './helper';
import {
  renderPriceItem,
  renderPostage,
  renderTariff,
  renderDiscount,
  renderPriceItemWithExtraInfo,
  renderExpressType,
  renderBuyWay,
  renderExtraPrice,
  renderMeituanOrderPromotion,
  judgeBuyWayRender,
  renderBuyWayDetail,
  renderStoredDiscount,
  renderOrderPrice
} from './render-items';
import { SMALL_TRANSFER_DIALOG_ID, GOODS_CHANGE_PRICE, ActivitiesSortMap } from './constants';
import { getOrderDetailForPc } from './api';

const { openDialog } = Dialog;

const OFFLINE = ChannelType.Offline;
const ONLINE = ChannelType.Online;

@createOrderCpm()
@withOrderHelper
class PaymentInfo extends React.Component {
  static defaultProps = {
    paymentInfo: {},
    mainOrderInfo: {},
    orderPostagePromotions: [],
    itemInfo: [],
    orderActivities: [],
    paymentActivityDetails: []
  };

  state = {
    qttOrder: {}
  };

  get isMeituanOrder() {
    return isMeituanOrder(this.props);
  }

  get isElemeOrder() {
    return checkIsElemeOrder(this.props);
  }

  get isMeituanShangouOrder() {
    return isMeituanShangouOrder(this.props);
  }

  get isTiktokSxtOrder() {
    return isTiktokSxtOrder(this.props);
  }

  get isJdwmOrder() {
    return isJdwmOrder(this.props);
  }

  componentDidMount() {
    const { isQttOrder, mainOrderInfo } = this.props;
    if (isQttOrder) {
      const { orderNo } = mainOrderInfo;
      getOrderDetailForPc({
        orderNo
      }).then(data => {
        this.setState({
          qttOrder: data.order
        });
      });
    }
  }

  renderDetailItem = (label, value, rightChild = '') => {
    let formatValue = value;
    const numValue = isNil(value) ? 0 : +value;

    if (!isNaN(numValue)) {
      formatValue =
        numValue >= 0 ? `￥${convertFenToYen(numValue)}` : `-￥${convertFenToYen(-numValue)}`;
    }
    return (
      <p className="price-info-item">
        <span className="price-info-item__label">{label}</span>
        <span className="price-info-item__value">
          {formatValue}
          {rightChild}
        </span>
      </p>
    );
  };

  renderUmpActivities = () => {
    const { orderActivities } = this.props;
    const umpActivities = combineUmpInfo(orderActivities, 'type').sort(
      (first, second) =>
        (ActivitiesSortMap[first.type] || 100) - (ActivitiesSortMap[second.type] || 100)
    );

    // 返现金额不计算在内
    return umpActivities.length > 0
      ? umpActivities
          .filter(({ type }) => type !== 'cashBack')
          .map(({ name, displayDecrease }) => renderPriceItem(`${name}：`, -displayDecrease))
      : null;
  };

  renderDetailItem = (label, value, rightChild = '') => {
    let formatValue = value;
    const numValue = isNil(value) ? 0 : +value;

    if (!isNaN(numValue)) {
      formatValue =
        numValue >= 0 ? `￥${convertFenToYen(numValue)}` : `-￥${convertFenToYen(-numValue)}`;
    }
    return <PayItem label={label} val={`${formatValue}${rightChild}`} />;
  };

  handleShowSmallMoneyDetail = transferOrderList => {
    const renderSingleTransferView = ({
      typeStr,
      amount,
      targetPhoneNo,
      operatorPhoneNo,
      transferTime,
      finishedTime,
      targetTypeStr,
      statusStr
    }) => {
      const singleTransferRenderData = [
        ['打款类型：', typeStr],
        ['打款金额：', `￥${convertFenToYen(amount)}`],
        ['打款对象：', `${targetPhoneNo}（${targetTypeStr}）`],
        ['操作人：', operatorPhoneNo],
        ['申请打款时间：', formatDatetime(transferTime)],
        ['打款成功时间：', formatDatetime(finishedTime)],
        ['打款状态：', statusStr]
      ];
      return singleTransferRenderData.map(item => (
        <div className="small-transfer-item">
          <span className="small-transfer-item__name">{item[0]}</span>
          <span className="small-transfer-item__val">{item[1]}</span>
        </div>
      ));
    };
    openDialog({
      dialogId: SMALL_TRANSFER_DIALOG_ID, // id is used to close the dialog
      title: '小额打款详情',
      children: (
        <div>
          {transferOrderList.map(item => (
            <div className="small-transfer-container">{renderSingleTransferView(item)}</div>
          ))}
        </div>
      )
    });
  };

  renderSmallMoneyDetail = () => {
    const { microTransferInfo = {} } = this.props;
    const {
      microTransferAbility = false,
      transferOrderList = [],
      transferStat: { totalFinishedAmount } = {}
    } = microTransferInfo;
    // eslint-disable-next-line unicorn/explicit-length-check
    if (!microTransferAbility || !transferOrderList.length) return null;
    return (
      <div className="small-money">
        {this.renderDetailItem(
          '小额打款：',
          `${totalFinishedAmount}`,
          <LinkButton
            className="small-money__detail-link"
            onClick={() => this.handleShowSmallMoneyDetail(transferOrderList)}
          >
            明细
          </LinkButton>
        )}
      </div>
    );
  };

  renderExpressType = () => {
    const {
      mainOrderInfo: { expressTypeDesc, saleWay },
      isVirtualTicket,
      isHotelOrder,
      isVirtualOrder,
      isPeriodOrder,
      isSelfFetch,
      isFreeGo
    } = this.props;
    const isOfflinePreOrder = +get(this.props, 'mainOrderInfo.extra.iS_STORE_PRESALE_ORDER', 0);
    const showWay = isOfflinePreOrder === 1 || isFreeGo() ? ONLINE : saleWay;

    if (showWay === OFFLINE || isVirtualTicket() || isHotelOrder() || isVirtualOrder()) {
      return null;
    }
    if (isPeriodOrder()) {
      return isSelfFetch() ? renderPriceItem('配送方式：', '到店自提') : null;
    }
    return expressTypeDesc ? renderPriceItem('配送方式：', expressTypeDesc) : null;
  };

  renderBuyWayDetail = () => {
    const {
      mainOrderInfo: { buyWayDetailList = [] }
    } = this.props;
    return (
      <ul>
        {buyWayDetailList.map(item => (
          <li>{item}</li>
        ))}
      </ul>
    );
  };

  renderMeituanOrderPromotion(activities) {
    const decreaseSum = sum(activities.map(item => item.decrease));

    return renderPriceItemWithExtraInfo(
      '商家活动补贴',
      -decreaseSum,
      activities.map(activity => ({ name: activity.name, price: activity.decrease }))
    );
  }

  // 渲染附加费用
  renderExtraPrice = () => {
    const { mainOrderInfo } = this.props;

    const extraPrices = mainOrderInfo?.extra?.extraPrices ?? [];

    if (extraPrices.length > 0) {
      return extraPrices.map(priceItem =>
        renderPriceItem(`${priceItem.name}：`, priceItem.realPay, priceItem.desc)
      );
    }

    return null;
  };

  /**
   * 渲染顾客实际支付金额
   *
   * @param {Number} price
   * @returns
   */
  renderCustomRealPay(price) {
    const { paymentInfo } = this.props;
    const { paymentDescInfos } = paymentInfo;

    return renderPriceItemWithExtraInfo('顾客实际支付', price, paymentDescInfos);
  }

  // 计算 非美团/饿了么订单 实收金额
  calculateCustomerRealPay(pay, phasePayDetailList = []) {
    let realPay = 0;
    // 预售订单实付金额计算
    if (phasePayDetailList.length > 0) {
      realPay = phasePayDetailList.reduce((pre, cur) => {
        return pre + cur.pay;
      }, 0);
    } else {
      realPay = pay;
    }
    return realPay;
  }

  render() {
    const {
      paymentInfo: {
        payDeduction,
        postage,
        pointsPrice,
        boxPrice,
        subtotal,
        collectPointsPrice,
        orderDiscountRate, // 订单级别折扣率为1时，不展示
        purchaseOrderPaymentInfo = {},
        paymentDescInfos,
        totalPayment, // 实收金额
        realIncome, // 订单实收
        totalOriginUnitPrice // 商品总额
      },
      exchangeInfo,
      itemInfo = [],
      priceOrderPromotions = [],
      orderPostagePromotions,
      orderPromotionInfo = {},
      isNewFxOrder,
      orderActivities = [],
      merchantReceiveDetail = {},
      mainOrderInfo,
      paymentActivityDetails = []
    } = this.props;
    const { excludeActivityBuyWayDetailList = [] } = mainOrderInfo;
    // paymentActivityDetails有值，则展示储值支付的UI
    const showStoredValuePriceInfo = paymentActivityDetails?.length;
    const { qttOrder } = this.state;

    // 团购返现
    const { tuanCashBack } = orderPromotionInfo;

    const isExchangeOrder = Boolean(exchangeInfo?.exchangeNo);
    const isOfflineExchangeV2 = !isNil(mainOrderInfo?.extra.aTTR_OFFLINE_EXCHANGE_FLOW);

    // 订单返现
    const orderCashPrice = orderPromotionInfo.cashBack;

    // 商品总价需要计算itemInfo里面的payPrice累加，并计算退差价总额
    const goodsValue = itemInfo.reduce(
      (ret, goods) => {
        ret.goodsPay += +goods.payPrice;
        ret.spread += +(goods.totalDiffAmount || 0);
        return ret;
      },
      { goodsPay: 0, spread: 0 }
    );

    // 商品总价与订单金额, 需要区分是老的 订单详情 还是新的 分销供货单详情
    // 原payment改为totalPayment | goodsValue.goodsPay改为totalOriginUnitPrice 后端逻辑发生变更，为不影响其他端展示
    const bifurcationInfo = {
      goodsPrice: isNewFxOrder ? purchaseOrderPaymentInfo.totalItemPay : totalOriginUnitPrice,
      orderPrice: isNewFxOrder ? purchaseOrderPaymentInfo.totalOrderPay : totalPayment,
      postage: isNewFxOrder ? purchaseOrderPaymentInfo.fxPostage : postage
    };

    // 改价金额(订单改价非运费改价)
    const orderEditPrice = find(priceOrderPromotions, {
      changeType: GOODS_CHANGE_PRICE
    });

    // 计算运费
    const calculatedPostage = getStringifiedValue(bifurcationInfo.postage);

    const paymentRenderer = [
      {
        content: () => renderPriceItem('商品总额：', bifurcationInfo.goodsPrice)
      },
      {
        content: () => renderExpressType('配送方式：', this.props)
      },
      {
        // 运费
        content: () => renderPostage(orderPostagePromotions, calculatedPostage)
      },
      {
        judgement: isNewFxOrder,
        content: () => renderPriceItem('分销推广补差价：', purchaseOrderPaymentInfo.orderFxPatchPay)
      },
      {
        // 优惠
        judgement: orderActivities.length > 0,
        content: () => renderDiscount(orderActivities, undefined, qttOrder.usePromoterSubsidy)
      },
      {
        // 税费
        content: () => renderTariff(itemInfo)
      },
      {
        judgement: payDeduction > 0,
        content: () => renderPriceItem('支付优惠：', -payDeduction)
      },
      {
        judgement: isObject(orderEditPrice),
        content: () =>
          renderPriceItem('改价：', minus(orderEditPrice.newValue, orderEditPrice.oldValue))
      },
      {
        judgement: goodsValue.spread > 0,
        content: () => renderPriceItem('已退差价：', -goodsValue.spread)
      },
      {
        content: () => renderExtraPrice(this.props)
      }
    ];

    // 美团的支付信息渲染
    const meituanPaymentRenderer = [
      {
        judgement: boxPrice > 0,
        content: () => renderPriceItem('打包费：', boxPrice)
      },
      {
        content: () => renderPriceItem('小计：', subtotal)
      },
      {
        judgement: !this.props.isSelfFetch(),
        content: () => renderPriceItem('配送费：', calculatedPostage)
      },
      {
        judgement: orderActivities.length > 0,
        content: () => renderMeituanOrderPromotion(orderActivities)
      },
      {
        judgement: !isNil(merchantReceiveDetail.agentServiceFeeInfo),
        content: () => {
          return renderPriceItemWithExtraInfo(
            '平台服务费',
            -merchantReceiveDetail.agentServiceFeeInfo.totalAgentFee,
            merchantReceiveDetail.agentServiceFeeInfo.agentServiceFeeDetailList.map(item => ({
              name: item.name,
              price: item.amount
            }))
          );
        }
      }
    ];

    const isTakeawayOrder =
      this.isMeituanOrder ||
      this.isElemeOrder ||
      this.isMeituanShangouOrder ||
      this.isTiktokSxtOrder ||
      this.isJdwmOrder;
    let totalPriceLabel = isTakeawayOrder ? '预计收入' : '实收金额';
    let totalPriceBefore = isTakeawayOrder
      ? merchantReceiveDetail.income
      : bifurcationInfo.orderPrice;
    // 京东外卖有点特殊，只有交易完成之后才有预计收入，其他订单状态都不展示
    if (this.isJdwmOrder && merchantReceiveDetail.income === undefined) {
      totalPriceBefore = bifurcationInfo.orderPrice;
      totalPriceLabel = '实收金额';
    }
    const totalPrice = `￥${convertFenToYen(totalPriceBefore)}`;
    // 支付明细
    const paymentSumDetails = [
      {
        key: '订单折扣率',
        // 字段存在，并且折扣率!=1，不展示; 1代表没有折扣，0代表免费
        judgement: orderDiscountRate !== undefined && +orderDiscountRate !== 1 && !isExchangeOrder,
        content: () => (
          <PayItem
            label="订单折扣率："
            val={
              <>
                <span>{orderDiscountRate}</span>
                <Pop
                  className="buyway-detail__pop"
                  wrapperClassName="buyway-detail__pop-trigger"
                  trigger="hover"
                  centerArrow
                  position="top-right"
                  content="订单折扣率 = (实收金额-运费) / 商品总额"
                >
                  <Icon className="price-info-item__icon" type="help-circle" />
                </Pop>
              </>
            }
          />
        )
      },
      {
        key: '总额',
        content: () => {
          const isExchangeRefundOrder =
            isExchangeOrder && exchangeInfo.isRefund && !isOfflineExchangeV2;
          const payItemProps = isExchangeRefundOrder
            ? {
                label: '退款金额：',
                val: convertFenToYen(exchangeInfo.refundFee)
              }
            : {
                label: `${totalPriceLabel}：`,
                val: totalPrice
              };
          // 如果是储值支付，并且预期展示为“实收金额”，则此时不再展示，改为展示订单实付。
          if (showStoredValuePriceInfo && !isExchangeRefundOrder && !isTakeawayOrder) {
            return null;
          }
          return (
            <PayItem
              key="总额"
              className={['price-info-item--order-price']}
              valClassName={['order-price']}
              label={payItemProps.label}
              val={payItemProps.val}
            />
          );
        }
      },
      {
        key: '顾客实际支付金额',
        judgement: isTakeawayOrder,
        content: renderPriceItemWithExtraInfo.bind(
          this,
          '顾客实际支付',
          bifurcationInfo.orderPrice,
          paymentDescInfos
        )
      },
      {
        key: '积分兑换',
        judgement: pointsPrice > 0,
        content: () => <PayItem key="积分兑换" label="积分兑换：" val={`${pointsPrice}积分`} />
      },
      {
        key: '集点兑换：',
        judgement: collectPointsPrice > 0,
        content: () => (
          <PayItem key="集点兑换" label="集点兑换：" val={`${collectPointsPrice}集点`} />
        )
      },
      {
        key: '订单返现：',
        judgement: orderCashPrice > 0,
        content: renderPriceItem.bind(this, '订单返现：', -orderCashPrice)
      },
      {
        key: '团购返现：',
        judgement: tuanCashBack > 0,
        content: renderPriceItem.bind(this, '团购返现：', -tuanCashBack)
      }
    ];

    // 支付方式
    const buyWayDetails = [
      {
        key: '支付方式',
        content: () => renderBuyWay('支付方式：', this.props)
      },
      {
        key: '支付方式详情',
        content: () => renderBuyWayDetail(this.props, totalPrice)
      }
    ];

    // 渲染基础支付明细信息
    const renderBasePayDetailInfo = () => {
      if (!judgeBuyWayRender(this.props)) {
        return null;
      }
      return (
        <div className="payment-buy-ways payment-group ">
          {buyWayDetails.map(item => {
            if (item.judgement === undefined || item.judgement === true) return item.content();
            return undefined;
          })}
        </div>
      );
    };

    // 渲染储值支付的UI
    const renderStoredValuePriceInfo = () => {
      // 储值订单实付
      const orderTotalPriceDetails = [
        {
          key: '订单实付',
          content: () => renderOrderPrice('订单实付：', totalPrice)
        },
        {
          key: '储值支付方式详情',
          content: () => renderBuyWayDetail(this.props, totalPrice)
        }
      ];
      // 储值订单实收
      const orderRealIncomeDetails = [
        {
          key: '订单实收',
          content: () => renderOrderPrice('订单实收：', realIncome)
        },
        {
          judgement: paymentActivityDetails?.length > 0,
          content: () => renderStoredDiscount(paymentActivityDetails)
        },
        {
          judgement: !!excludeActivityBuyWayDetailList.length,
          content: () =>
            // 复用原组件的渲染逻辑，替换数据源
            renderBuyWayDetail(
              {
                mainOrderInfo: {
                  buyWayDetailList: excludeActivityBuyWayDetailList
                }
              },
              realIncome
            )
        }
      ];
      return (
        <>
          <div className="payment-buy-ways payment-group ">
            {orderTotalPriceDetails.map(item => {
              if (item.judgement === undefined || item.judgement === true) return item.content();
              return undefined;
            })}
          </div>
          <div className="payment-buy-ways payment-group ">
            {orderRealIncomeDetails.map(item => {
              if (item.judgement === undefined || item.judgement === true) return item.content();
              return undefined;
            })}
          </div>
        </>
      );
    };

    // 支付方式明细渲染函数，可能存在多种渲染方式，所以需要一个函数来进行判断
    const renderPayDetailInfo = () => {
      if (showStoredValuePriceInfo) {
        return renderStoredValuePriceInfo();
      }

      return renderBasePayDetailInfo();
    };

    return (
      <div className={cx('info-block', style['payment-info'])}>
        <div className="payment-detail payment-group ">
          {(isTakeawayOrder ? meituanPaymentRenderer : paymentRenderer).map(item => {
            if (item.judgement === undefined || item.judgement === true) return item.content();
            return null;
          })}
        </div>
        <div className="payment-sum payment-group ">
          {paymentSumDetails.map(item => {
            if (item.judgement === undefined || item.judgement === true) return item.content();
            return undefined;
          })}
        </div>

        {/* 支付方式明细 */}
        {renderPayDetailInfo()}
        {/* 小额打款详情 */}
        {this.renderSmallMoneyDetail()}
      </div>
    );
  }
}

export default PaymentInfo;
