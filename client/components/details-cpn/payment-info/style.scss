@import '~shared/style';

@mixin pay_icon($color) {
  color: $color;
  font-size: 14px;
  margin-left: 2px;
  cursor: pointer;
}

.pop-postage {
  .zenticon {
    @include pay_icon($color-b4);
  }
}

:local(.payment-info) {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 2px 16px 1px 0;
  color: $color-text-light-primary;

  &.info-block {
    margin-bottom: 0;
  }

  .price-info-item {
    text-align: right;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 24px;
    position: relative;

    &__label {
      flex-shrink: 1;
      flex-grow: 1;
      color: #636566;
      white-space: nowrap;
    }

    &__value {
      width: 130px;
      color: #636566;
      &__hover {
        cursor: pointer;
      }
    }

    &__icon {
      @include pay_icon($color-n5);
    }

    .packaging {
      position: absolute;
      top: 3px;
      right: -16px;
      color: $color-n6;
    }

    @mixin boldPrice {
      font-weight: bold;
      color: #323233;
      line-height: $font-line-height-xlarge;
    }
    &--order-price {
      color: $color-text-primary;
      line-height: $font-line-height-xlarge;
      .price-info-item__label {
        @include boldPrice;
      }

      .price-info-item__value {
        color: $color-alert;
        font-size: $font-size-large;
      }
    }
    &--buy-way {
      .price-info-item__label,
      .price-info-item__value {
        @include boldPrice;
      }
    }
    &--order-price {
      .price-info-item__label,
      .price-info-item__value {
        @include boldPrice;
      }
      .price-info-item__value {
        color: $color-alert;
      }
    }
  }

  .small-money {
    display: flex;
    margin-top: 4px;
    justify-content: flex-end;

    &__detail-link {
      margin-left: 4px;
      font-size: $font-size-base;
    }
  }
  .payment-group {
    padding-bottom: 8px;
    line-height: $font-line-height-small;
  }

  .payment-sum {
    line-height: $font-line-height-xlarge;
  }

  .payment-buy-ways {
    display: flex;
    flex-direction: column;
    align-items: end;
    text-align: right;
    &::before {
      display: block;
      content: '';
      height: 2px;
      width: 240px;
      background-color: $color-n2;
      margin-bottom: 8px;
    }
  }

  .buyway-detail {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: $color-text-secondary;

    &__pop-trigger {
      margin-left: 8px;
      font-size: $font-size-medium;

      .zenticon {
        @include pay_icon($color-text-disable);
      }
    }
  }
}

.buyway-detail__pop {
  box-sizing: border-box;
  min-width: 206px;
}

.small-transfer-container {
  margin: 0 24px;
  &:first-child {
    padding-bottom: 16px;
  }
  &:not(:first-child) {
    padding-top: 16px;
    padding-bottom: 16px;
    border-top: 1px solid $border-color-base;
  }
  .small-transfer-item {
    color: $color-text-primary;
    font-size: $font-size-base;

    &:not(:first-child) {
      margin-top: 16px;
    }

    &__name {
      display: inline-block;
      text-align: right;
      min-width: 98px;
    }
  }
}

.beautiful-table {
  .table__body {
    overflow-x: hidden;
  }
}

.discounts-info {
  @include pay_icon($color-b4);
}

// 详细的金额信息展示内容
:local(.payment-detail) {
  min-width: 400px;

  .title {
    font-size: $font-size-medium;
    padding-bottom: 16px;
  }

  .detail-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    &--gray {
      color: #969799;
      font-size: 13px;
    }
  }

  .detail-desc {
    margin-bottom: 8px;
    color: #969799;
    font-size: 13px;
  }

  .summary {
    display: flex;
    justify-content: space-between;
    padding-top: 8px;
    border-top: 1px solid $border-color-dark-base;
  }
}
