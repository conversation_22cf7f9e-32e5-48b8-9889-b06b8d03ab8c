import * as React from 'react';
import { Pop, Icon, Grid } from 'zent';
import { sum, get } from 'lodash';
import { BeautifulTable } from '@youzan/retail-components';
import { renderPriceString } from 'common/fns/format';
import { ChannelType, OrderPayWayType, OrderBuyWayMap } from 'common/constants/common';

import { safeJsonParse } from 'common/utils';
import PayItem from './components/pay-item';

import { getDiscountPriceText, getStringifiedValue } from './helper';
import style from './style.scss';
import { calculateTariffTotal, hasTariffGood } from '../goods-list/helper';
import { IExtraPricesItem } from './types';
import { CustomizedPackagingFeeDesc } from './components/customized-packaging-fee-desc';

const { Column } = BeautifulTable;

type IPriceItem = Array<{ name: string; price: number }>;
type IPriceDetailItems = Array<{
  name: string;
  price: number;
  subPaymentDescList?: IPriceItem;
  subIncomeDetails?: IPriceItem;
}>;

export const renderExtraItems = (title: string, price: number, items: IPriceDetailItems) => {
  return (
    <div className={style['payment-detail']}>
      <p className="title">{title}</p>

      {items &&
        items
          .filter(item => item.price !== 0)
          .map(item => {
            const subDescList = item.subPaymentDescList || item.subIncomeDetails;
            return (
              <>
                <div className="detail-row" key={item.name}>
                  <span>{item.name}</span>
                  <span>{`${renderPriceString(item.price)}`}</span>
                </div>
                {subDescList &&
                  subDescList.map(subItem => {
                    return (
                      <div className="detail-row detail-row--gray" key={subItem.name}>
                        <span>{subItem.name}</span>
                        <span>{renderPriceString(subItem.price)}</span>
                      </div>
                    );
                  })}
              </>
            );
          })}

      <div className="summary">
        <span>合计：</span>
        <span>{`${renderPriceString(price)}`}</span>
      </div>
    </div>
  );
};

export const renderPriceItemWithExtraInfo = (
  title: string,
  total: number,
  detail: IPriceDetailItems
) => {
  return (
    <PayItem key={title} label={`${title}：`} val={renderPriceString(total)}>
      <Pop
        trigger="hover"
        position="top-right"
        centerArrow
        content={renderExtraItems(title, total, detail)}
      >
        <Icon className="price-info-item__icon" type="help-circle" />
      </Pop>
    </PayItem>
  );
};

function getPackagingPopContent({ realPay, desc = '' }: IExtraPricesItem) {
  const { packagingFeeType, itemsFeeDetails = [] } = safeJsonParse(desc) || {};
  return (
    <CustomizedPackagingFeeDesc
      desc={desc}
      realPay={realPay}
      packagingFeeType={packagingFeeType}
      itemsFeeDetails={itemsFeeDetails}
    />
  );
}

/**
 * 用于处理标准的 价格 item，例如 「商品总额：¥40.00」
 * @param label 标题
 * @param data 订单数据
 */
export const renderPriceItem = (
  label: string,
  data: string,
  desc = '',
  options = {},
  priceItem = {}
): JSX.Element => {
  const formattedValue = getStringifiedValue(data);
  const isPackaging = label === '打包费：' && desc;

  return (
    <PayItem key={label} label={label} val={formattedValue} {...options}>
      {isPackaging ? (
        <Pop
          trigger="hover"
          position="top-right"
          centerArrow
          content={getPackagingPopContent(priceItem)}
        >
          <Icon className="price-info-item__icon" type="help-circle" />
        </Pop>
      ) : undefined}
    </PayItem>
  );
};

interface IOrderPostagePromotion {
  type: string;
  name: string;
  content: string;
}

/**
 * 订单中的运费信息, 在订单详情中会出现, 与 mainOrderInfo 同级别
 */
type IOrderPostagePromotions = IOrderPostagePromotion[];

/**
 * 渲染运费
 */
export const renderPostage = (
  orderPostagePromotions: IOrderPostagePromotions,
  calculatedPostage: string,
  label = '运费：'
): JSX.Element => {
  return (
    <PayItem
      key={label}
      label="运费："
      val={
        (orderPostagePromotions?.length ?? 0) > 0 ? (
          <span className="pop-postage">
            {calculatedPostage}
            <Pop
              trigger="hover"
              position="top-right"
              centerArrow
              content={
                <BeautifulTable dataSets={orderPostagePromotions} width={400}>
                  <Column label="优惠类型" name="type" />
                  <Column label="优惠名称" name="name" />
                  <Column label="优惠内容" name="content" />
                </BeautifulTable>
              }
            >
              <Icon type="info-circle-o" />
            </Pop>
          </span>
        ) : (
          calculatedPostage
        )
      }
    />
  );
};

/**
 * 渲染税费
 */
export const renderTariff = (itemInfo: any): JSX.Element | null => {
  // 没有扣税费的商品，不需要展示
  if (!hasTariffGood(itemInfo.map(({ extra }: { extra: string }) => extra))) {
    return null;
  }

  const tariffTotal = calculateTariffTotal(itemInfo);
  return <PayItem label="税费：" val={`¥ ${tariffTotal}`} />;
};

/**
 * 渲染优惠及优惠详情
 * @param data 优惠总金额
 */
interface OrderActivities {
  decrease: number;
  type: string;
  name: string;
  activityName: string;
  [propName: string]: any;
}

export const renderDiscount = (
  data: OrderActivities[],
  label = '优惠',
  isQttHelpsellCoupon: boolean
): JSX.Element => {
  const decreaseSum = sum(data.map(item => item.decrease));
  const renderDiscountsInfoGrid = () => {
    const columns = [
      {
        title: '优惠类型',
        name: 'name',
        width: '30%',
        noWrap: true
      },
      {
        title: '优惠名称',
        name: 'activityName'
      },
      {
        title: '金额(元)',
        width: '30%',
        bodyRender: (data: OrderActivities) => {
          return getDiscountPriceText(data.decrease);
        }
      }
    ];
    return (
      <div style={{ width: '400px' }}>
        <Grid columns={columns} datasets={data} />
      </div>
    );
  };
  return (
    <PayItem
      key={label}
      label={isQttHelpsellCoupon ? '活动优惠(由帮卖团长承担)：' : '活动优惠：'}
      val={
        <>
          {getDiscountPriceText(decreaseSum)}
          <Pop trigger="hover" position="top-right" centerArrow content={renderDiscountsInfoGrid()}>
            <Icon type="info-circle-o" className="discounts-info" />
          </Pop>
        </>
      }
    />
  );
};

interface PaymentActivityDetail {
  name: string;
  type: string;
  decrease: number;
}
// 支付优惠
export const renderStoredDiscount = (
  data: PaymentActivityDetail[],
  label = '支付优惠：'
): JSX.Element => {
  const decreaseSum = sum(data.map(item => item.decrease));
  const renderDiscountsInfoGrid = () => {
    const columns = [
      {
        title: '优惠类型',
        name: 'type',
        width: '30%',
        noWrap: true
      },
      {
        title: '优惠名称',
        name: 'name'
      },
      {
        title: '金额(元)',
        width: '30%',
        bodyRender: (data: OrderActivities) => {
          return getDiscountPriceText(data.decrease);
        }
      }
    ];
    return (
      <div style={{ width: '400px' }}>
        <Grid columns={columns} datasets={data} />
      </div>
    );
  };
  return (
    <PayItem
      key={label}
      label={label}
      val={
        <>
          {getDiscountPriceText(decreaseSum)}
          <Pop trigger="hover" position="top-right" centerArrow content={renderDiscountsInfoGrid()}>
            <Icon type="info-circle-o" className="discounts-info" />
          </Pop>
        </>
      }
    />
  );
};

// 配送方式
const OFFLINE = ChannelType.Offline;
const ONLINE = ChannelType.Online;
interface ExpressType {
  mainOrderInfo: { expressTypeDesc: string; saleWay: number };
  isVirtualTicket: () => boolean;
  isHotelOrder: () => boolean;
  isVirtualOrder: () => boolean;
  isPeriodOrder: () => boolean;
  isSelfFetch: () => boolean;
  isFreeG: () => boolean;
  [propName: string]: any;
}

export const renderExpressType = (label: string, data: ExpressType) => {
  const {
    mainOrderInfo: { expressTypeDesc, saleWay },
    isVirtualTicket,
    isHotelOrder,
    isVirtualOrder,
    isPeriodOrder,
    isSelfFetch,
    isFreeGo
  } = data;
  const isOfflinePreOrder = +get(data, 'mainOrderInfo.extra.iS_STORE_PRESALE_ORDER', 0);
  const showWay = isOfflinePreOrder === 1 || isFreeGo() ? ONLINE : saleWay;

  if (showWay === OFFLINE || isVirtualTicket() || isHotelOrder() || isVirtualOrder()) {
    return null;
  }
  if (isPeriodOrder()) {
    return isSelfFetch() ? renderPriceItem(label, '到店自提') : null;
  }
  return expressTypeDesc ? renderPriceItem(label, expressTypeDesc) : null;
};

// 支付方式
interface BuyWay {
  mainOrderInfo: { buyWayDesc: string; buyWay: number; buyWayDetailList?: [] };
  partPayDetail: any;
  [propName: string]: any;
}
export const judgeBuyWayRender = (data: BuyWay) => {
  const {
    mainOrderInfo: { buyWay, buyWayDesc },
    partPayDetail
  } = data;

  let buyWayText = buyWayDesc;

  // 代付订单增加判断有没有付齐
  if (buyWay === OrderPayWayType.PeerPay && partPayDetail && !partPayDetail.remainFee) {
    buyWayText = '找人代付，已付齐';
  }
  if (!buyWayText && OrderBuyWayMap[buyWay]) {
    buyWayText = OrderBuyWayMap[buyWay];
  }

  return buyWayText;
};

export const renderBuyWay = (label: string, data: BuyWay) => {
  const buyWayText = judgeBuyWayRender(data);
  return renderPriceItem(label, buyWayText, '', {
    className: ['price-info-item--buy-way'],
    needSafeLabel: true
  });
};

// 订单价格展示样式
export const renderOrderPrice = (label: string, priceStr: string) => {
  return renderPriceItem(label, priceStr, '', {
    className: ['price-info-item--order-price'],
    needSafeLabel: true
  });
};

// 支付方式明细
export const renderBuyWayDetail = (data: BuyWay, defaultPrice: string) => {
  // 支付明细： mainOrderInfo.buyWayDetailList 首选
  // paymentInfo.phasePayDetailList这个字段组合支付也有值，但是单次支付（非组合支付）没有值
  const {
    mainOrderInfo: { buyWayDetailList = [] }
  } = data;
  return (
    <>
      {buyWayDetailList.map((item: string) => {
        // tip: 组合支付['现金支付:¥10','标记支付-微信:¥10']；非组合支付['现金支付']；非组合支付需要自己拼上订单实收金额
        const [buyWay, amount] = item.split(':');
        return <PayItem key={buyWay} label={`${buyWay}：`} val={amount || defaultPrice} />;
      })}
    </>
  );
};

// 渲染附加费用
interface ExtraPrice {
  mainOrderInfo: { extra: any };
}
export const renderExtraPrice = (data: ExtraPrice) => {
  const { mainOrderInfo } = data;

  const extraPrices = mainOrderInfo?.extra?.extraPrices ?? [];

  if (extraPrices.length > 0) {
    return extraPrices.map((priceItem: { name: string; realPay: string; desc: string }) =>
      renderPriceItem(`${priceItem.name}：`, priceItem.realPay, priceItem.desc, {}, priceItem)
    );
  }

  return null;
};

// 美团订单
interface Activity {
  name: string;
  price: string;
  decrease: number;
}
export const renderMeituanOrderPromotion = (activities: Activity[]) => {
  const decreaseSum = sum(activities.map(item => item.decrease));

  return renderPriceItemWithExtraInfo(
    '商家活动补贴',
    decreaseSum,
    activities.map(activity => ({ name: activity.name, price: activity.decrease }))
  );
};
