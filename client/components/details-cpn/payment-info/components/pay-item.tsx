import * as React from 'react';
import classnames from 'classnames';
import TextInSafeArea, { Hidden } from 'components/details-cpn/text-in-safe-area';

interface PayInfo {
  className?: string[];
  label: string;
  labelClassName?: string[];
  val: string | JSX.Element;
  valClassName?: string[];
  children?: JSX.Element;
  needSafeLabel?: boolean;
}

export default (props: PayInfo): JSX.Element => {
  const {
    className = [''],
    label,
    labelClassName = [''],
    val,
    valClassName = [''],
    needSafeLabel
  } = props;

  return (
    <p className={classnames('price-info-item', ...className)}>
      <span className={classnames('price-info-item__label', ...labelClassName)}>{label}</span>
      {needSafeLabel ? (
        <TextInSafeArea width="130px" hidden={Hidden.Right}>
          <span className={classnames('price-info-item__value', ...valClassName)}>{val}</span>
        </TextInSafeArea>
      ) : (
        <span className={classnames('price-info-item__value', ...valClassName)}>{val}</span>
      )}
      {props.children}
    </p>
  );
};
