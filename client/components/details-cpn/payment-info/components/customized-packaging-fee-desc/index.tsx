import * as React from 'react';
import { useMemo } from 'react';
import { div, keepRoundDecimal, times } from '@youzan/retail-utils';
import { convertFenToYen } from 'common/fns/format';
import { PackagingFeeType } from '../../constants';
import { IExtraPricesItem } from '../../types';
import * as style from './style.scss';

function formatPrice(price = 0, decimal = 2) {
  return keepRoundDecimal(div(price, 100), decimal, true);
}

export function CustomizedPackagingFeeDesc({
  realPay = 0,
  desc = '',
  packagingFeeType,
  itemsFeeDetails = []
}: IPackagingFeeDescProps) {
  const isByGoods = useMemo(() => {
    return (
      [PackagingFeeType.RatioPrice, PackagingFeeType.ItemsPrice].includes(packagingFeeType) &&
      itemsFeeDetails.length > 0
    );
  }, [itemsFeeDetails.length, packagingFeeType]);

  const isByOrder = useMemo(() => {
    return [PackagingFeeType.Simple].includes(packagingFeeType);
  }, [packagingFeeType]);

  const feeDetails = useMemo(() => {
    return itemsFeeDetails.length > 0
      ? itemsFeeDetails.map(item => {
          const { n: name, c: nums = 0, p: price = 0 } = item;
          return {
            name,
            nums,
            price,
            totalPrice: formatPrice(times(price, nums))
          };
        })
      : [];
  }, [itemsFeeDetails]);

  function Detail() {
    if (isByOrder) {
      return <span> 标准{convertFenToYen(+realPay)}元/单。 </span>;
    }
    if (isByGoods) {
      return (
        <div v-if="isByGoods" className="fee__list">
          <h2>收费标准:</h2>
          {feeDetails.map(({ name, nums, totalPrice }) => {
            return (
              <div className={style.goods}>
                <span className="goods__name">{name} </span>
                <span>x {nums}</span>
                <span className="goods__price">￥{totalPrice}</span>
              </div>
            );
          })}
        </div>
      );
    }
    return <span>{desc}</span>;
  }

  return (
    <div className={style.fee}>
      <h1 className="fee__title">打包费说明</h1>
      <span>
        根据国家有关政策要求，不得免费提供塑料袋。为保障你的商品安全、卫生送达，基于外卖/外带业务特性将对你的商品，使用购物袋进行打包，会适当对包装材料收费。
      </span>
      <Detail />
    </div>
  );
}

interface IPackagingFeeDescProps extends Pick<IExtraPricesItem, 'realPay' | 'desc'> {
  packagingFeeType: PackagingFeeType;
  itemsFeeDetails?: { n: string; c: number; p: number }[];
}
