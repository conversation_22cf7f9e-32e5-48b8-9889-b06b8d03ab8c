import * as React from 'react';
import { convertFenToYen } from 'common/fns/format';

/**
 * 将数字计算为带 ¥ 符号的字符串
 * @param value
 */
export const getStringifiedValue = (data: string): string => {
  let formattedValue = data;
  const numValue = +(data ?? 0);

  // eslint-disable-next-line no-self-compare
  if (numValue === numValue) {
    formattedValue =
      numValue >= 0 ? `￥${convertFenToYen(numValue)}` : `-￥${convertFenToYen(-numValue)}`;
  }

  return formattedValue;
};

/**
 * 获取优惠展示金额
 */
export const getDiscountPriceText = (value?: string | number): string => {
  const numValue = Number(value ?? 0);

  if (Number.isNaN(numValue)) {
    return '';
  }

  return numValue >= 0 ? `-￥${convertFenToYen(numValue)}` : `+￥${convertFenToYen(-numValue)}`;
};

/**
 * 用于处理标准的 价格 item，例如 「商品总额：¥40.00」
 * @param label 标题
 * @param data 订单数据
 */
export const renderDetailItem = (label: string, data: string): JSX.Element => {
  const formattedValue = getStringifiedValue(data);
  return (
    <p className="price-info-item" key={label}>
      <span className="price-info-item__label">{label}</span>
      <span className="price-info-item__value">{formattedValue}</span>
    </p>
  );
};
