/* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
/* eslint-disable jsx-a11y/alt-text */
import React, { useState, useEffect } from 'react';
import { Card, LayoutRow as Row, LayoutCol as Col, LayoutGrid as Grid, Icon } from 'zent';
import { SuperButton } from '@youzan/react-components';
import { formatDatetime } from '@youzan/retail-utils';
import GoodsSwiper from 'components/pack-info/goods-swiper';
import { PackStateStep } from '../pack-info/pack-state';
import { ITakeoutOrders, IDistItemInfo } from './types';
import { getHistoryRecords } from './api';
import styles from './index.scss';

interface IRecord extends ITakeoutOrders {
  distItemInfo?: IDistItemInfo[];
}
interface IHistoryRecordItem {
  record: IRecord;
}

const HistoryRecordItem: React.FC<IHistoryRecordItem> = ({ record }) => {
  const [showMore, setShowMore] = useState<boolean>(false);
  const {
    createTime,
    companyName,
    statusDesc,
    transporterName,
    transporterPhone,
    feeDesc,
    deliveryNo,
    outDeliveryNo,
    data,
    distItemInfo,
    extraInfo,
    deliveryRemark
  } = record;
  const outDeliveryNumber = outDeliveryNo === '' ? '无' : outDeliveryNo;
  const sentInfo = transporterName === '' ? '无' : `${transporterName} ${transporterPhone}`;

  return (
    <div className={styles.box}>
      <h1 className={styles.h1}>
        <span>发货时间：{formatDatetime(createTime)}</span>
        <span>服务商：{companyName} </span>
      </h1>
      <Card>
        <Grid>
          <Row className={styles.row}>
            <Col span={9}>配送状态：{statusDesc}</Col>
            <Col span={10}>配送员：{sentInfo}</Col>
            <Col span={5}>配送费：{feeDesc}</Col>
          </Row>
          <Row className={styles.row}>
            <Col span={9}>有赞单号：{deliveryNo}</Col>
            <Col span={10}>第三方配送单号：{outDeliveryNumber}</Col>
            <Col span={5}>发货人：{extraInfo.sentName}</Col>
          </Row>
          {deliveryRemark && (
            <Row className={styles.row}>
              <Col span={9}>发货备注：{deliveryRemark}</Col>
            </Row>
          )}
        </Grid>
        <SuperButton className={styles.link} type="link" onClick={() => setShowMore(!showMore)}>
          查看详情
          <Icon type={showMore ? 'up' : 'down'} className="triangleIcon" />
        </SuperButton>
        {showMore && (
          <Card className={styles.detailBox}>
            <Grid>
              <Row>
                <Col span={12}>
                  <h2>商品信息：</h2>
                  <GoodsSwiper goodsList={distItemInfo} packId={deliveryNo} />
                </Col>
                <Col span={12}>
                  <h2>物流信息：</h2>
                  <PackStateStep steps={data} className={styles.packStateStep} />
                </Col>
              </Row>
            </Grid>
          </Card>
        )}
      </Card>
    </div>
  );
};

interface IHistoryRecords {
  orderNo: string;
  fulfillNo: string;
}

export const HistoryRecords: React.FC<IHistoryRecords> = props => {
  const { orderNo, fulfillNo } = props;
  const [records, setRecords] = useState<IRecord[]>([]);

  useEffect(() => {
    getHistoryRecords({ orderNo, fulfillNo }).then(res => {
      const data: IRecord[] = [...res.takeoutOrders];
      data.map(item => {
        item.distItemInfo = res.packInfos.filter(
          pack => pack.distId === item.distId
        )[0].distItemInfo;
        return item;
      });
      setRecords(data);
    });
  }, [orderNo, fulfillNo]);

  return (
    <div>
      {records.length > 0 ? (
        records.map(record => <HistoryRecordItem record={record} />)
      ) : (
        <div className={styles.imgbox}>
          <img src="https://img01.yzcdn.cn/upload_files/2021/11/30/Fm371Yk1JMrpxhKqGkePQEFhOkfD.png" />
          <p>当前无历史呼叫记录</p>
        </div>
      )}
    </div>
  );
};
