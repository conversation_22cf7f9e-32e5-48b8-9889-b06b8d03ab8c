@import '~shared/style';

:local(.h1) {
  margin-bottom: 10px;
  font-size: $font-size-medium;
  font-weight: bold;

  span:first-child {
    display: inline-flex;
    width: 310px;
  }
}

:local(.box) {
  margin-bottom: 20px;
}

:local(.row) {
  margin-bottom: 10px;
}

:local(.link) {
  padding-left: 0 !important;
}

:local(.detailBox) {
  margin-top: 10px;
  background-color: $color-n1 !important;
}

:local(.packStateStep) {
  height: auto;
  margin-top: 0;
}

:local(.imgbox) {
  margin: 100px;
  text-align: center;

  img {
    width: 82px;
    height: 82px;
    margin-bottom: 16px;
  }
}
