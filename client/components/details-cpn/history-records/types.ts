export interface IHistoryRecords {
  takeoutOrders: ITakeoutOrders[];
  packInfos: IPackInfos[];
}

export interface ITakeoutOrders {
  distId: number;
  deliveryNo: string;
  outDeliveryNo: string;
  companyName: string;
  statusDesc: string;
  transporterName: string;
  transporterPhone: string;
  feeDesc: string;
  createTime: string;
  deliveryRemark?: string;
  data: IData[];
  extraInfo: IExtraInfo;
}

export interface IData {
  context: string;
  status: string;
  time: number;
}

export interface IExtraInfo {
  sentName: string;
}

export interface IPackInfos {
  distId: number;
  distItemInfo: IDistItemInfo[];
}

export interface IDistItemInfo {
  imgUrl: string;
  itemId: string;
  num: number;
  weight: number;
  title: string;
}
