import React from 'react';
import cx from 'classnames';
import { numberWithCommas, div } from '@youzan/retail-utils';
import { IOrderInfo } from 'definition/order-info';
import styles from './index.scss';

interface IProps {
  fulfillInfo: IOrderInfo;
}

const FulfillOrderPaymentInfo = (props: IProps) => {
  const { fulfillInfo } = props;

  const { paymentInfo, fulfillOrder } = fulfillInfo || {};
  const { totalPayment = 0 } = paymentInfo || {};
  const { fulfillTotalPayment = 0, multiWarehouse = false } = fulfillOrder || {};

  const { label, price } = multiWarehouse
    ? { label: '商品总计', price: fulfillTotalPayment }
    : { label: '实收金额', price: totalPayment };

  return (
    <div className={styles.box}>
      <div className={cx(styles.left, styles.label1)}>{label}：</div>
      <div className={cx(styles.right, styles.text1)}>
        ￥{numberWithCommas(div(price, 100).toFixed(2))}
      </div>
    </div>
  );
};

export default FulfillOrderPaymentInfo;
