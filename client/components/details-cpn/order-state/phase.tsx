import * as React from 'react';
import { format } from 'date-fns';
import { div } from '@youzan/retail-utils';
import cx from 'classnames';
import type { IPhasePayDetail } from 'definition/order-info';
import { getMapWithDefault } from 'common/helper';

interface IPhaseProps {
  data: IPhasePayDetail[];
}

const PHASE_INFO = [
  {
    name: '一',
    text: '预付定金'
  },
  {
    name: '二',
    text: '支付尾款'
  }
];

const PREORDER_MAP = {
  SUCCESS: 100,
  PAIED: 20,
  TO_PAY: 10
};

/**
 * 某个阶段是否开始
 *
 * @param {number} time
 */
const isPhaseStarted = (time: number) => Date.now() >= time;

enum PhasePaymentsStatusEnum {
  WaitPay = 10, // 待支付
  Paid = 20, // 已支付
  Close = 99, // 已关闭
  Success = 100 // 已完成
}

const PhasePaymentsStatusMap = new Map([
  [PhasePaymentsStatusEnum.WaitPay, '待支付'],
  [PhasePaymentsStatusEnum.Paid, '已支付'],
  [PhasePaymentsStatusEnum.Close, '已关闭'],
  [PhasePaymentsStatusEnum.Success, '已完成']
]);

export default function Phase(props: IPhaseProps): JSX.Element | null {
  const { data } = props;

  if (data.length === 0) return null;

  const endPhaseData = data?.[1] ?? {};

  return (
    <div className="order-phase">
      {data.map((item, index) => {
        const phase = PHASE_INFO[index];
        const isEndPhaseStarted = index === 1 && isPhaseStarted(endPhaseData.payStartTime);

        return (
          <p
            key={item.payStartTime}
            className={cx('order-phase__item', { orange: isEndPhaseStarted })}
          >
            <p>
              {`阶段${phase.name}：${phase.text}（${getMapWithDefault(
                PhasePaymentsStatusMap,
                item.status,
                '未知状态'
              )}）`}
            </p>
            <span className="order-phase__money">
              {`￥${div(
                item.status === PREORDER_MAP.PAIED ? item.pay : item.realPrice,
                100
              ).toFixed(2)}`}
            </span>
          </p>
        );
      })}
      {endPhaseData.status === PREORDER_MAP.TO_PAY ? (
        <p className="endphase-desc">
          {`尾款：${format(endPhaseData.payStartTime, 'YYYY-MM-DD HH:mm:ss')} - ${format(
            endPhaseData.payEndTime,
            'YYYY-MM-DD HH:mm:ss'
          )}`}
        </p>
      ) : null}
    </div>
  );
}
