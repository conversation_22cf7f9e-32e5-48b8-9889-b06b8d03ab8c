@import '~shared/style';

:local(.order-state) {
  border: 1px solid $background-color-base;

  .state-info-actions {
    padding: 28px 20px;
    border-right: 1px solid $background-color-base;

    .state-desc {
      margin-bottom: 14px;
      font-size: 20px;
      font-weight: 700;

      .yz-secured {
        float: right;
        color: $color-success;
        font-size: 12px;
        img {
          height: 18px;
        }
      }
    }

    .state-autoCall {
      color: #2da641;
      margin-top: 8px;
    }

    .order-actions {
      &.fulfill-actions {
        align-items: center;
        margin-top: 60px;

        .action-list__item {
          margin-bottom: 0;
        }
      }

      .action-list {
        padding: 10px 0 10px;
        display: flex;
        flex-direction: column;

        .button-with-desc,
        .refund-desc {
          margin-bottom: 10px;
        }

        &__item {
          margin-right: 15px;
          display: flex;
          margin-bottom: 10px;

          .zent-btn {
            margin-right: 5px;
          }

          .refund-desc {
            min-width: 115px;
          }
        }

        .first-show__smallmoney {
          border: 1px solid $border-color-dark-base;
          padding: 10px 9px 10px 8px;
          border-radius: 2px;
        }

        &__smallmoney-content {
          &__btn {
            font-size: 14px;
          }
          &__icon {
            color: #d7d9db;
            margin-left: 4px;
            font-size: 14px;
          }
        }
      }
      .action-notice-to-pay {
        padding: 30px 0 15px;
        display: flex;
      }
      .actions-remark-star {
        display: flex;
        height: 30px;

        .action-remark {
          margin-right: 13px;
        }
      }
    }
  }

  .order-steps {
    padding: 40px 20px 10px;

    .zent-steps-tail {
      margin-left: 55px;
    }

    .zent-steps-step {
      .zent-step-head {
        margin-left: 40px;
      }

      .zent-step-main {
        width: 124px;
      }
    }
  }

  .order-phase {
    padding: 16px 32px;
    border-top: 1px solid $background-color-base;

    &__item {
      display: flex;
      justify-content: space-between;
      margin-top: 8px;
    }

    &__money {
      text-align: right;
    }

    .endphase-desc {
      color: #999;
      margin-left: 56px;
      margin-top: 8px;
    }
  }

  .order-tips {
    border-top: 1px solid $background-color-base;
    padding: 14px 20px;

    .seller-remark {
      margin-bottom: 10px;

      &__value {
        width: 300px;
        vertical-align: top;
        word-wrap: break-word;
        display: inline-block;

        &.large-line {
          width: 500px;
        }
      }

      &__pic {
        width: 80px;
        height: 80px;
        margin: 12px 12px 0 0;
      }
    }

    .yz-tips {
      display: flex;
    }
  }
}

.orange {
  color: $color-warn;
}

.grey {
  color: $color-text-light-primary;
}
