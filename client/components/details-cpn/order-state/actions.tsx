import type {
  IMicroTransferInfo,
  IOperation,
  IOrderInfo,
  IQttOrderInfo
} from 'definition/order-info';

import * as React from 'react';
import { Notify, Pop, Icon } from 'zent';
import cx from 'classnames';
import { omit, filter, get } from 'lodash';
import { isRetailSingleStore } from '@youzan/utils-shop';
import isFxStore from 'common/is-fx-store';
import { addStar } from 'components/star/api';
import Star from 'components/star/star';
import { orderTypeValue } from 'common/constants/models';
import { OptComponent } from 'components/opt-components';
import { NEW_OFFLINE_REFUND_CODES } from 'components/opt-components/code-map';
import { BUTTON, BUTTON_LINK } from 'components/opt-components/type-map';
import { notOptRemarkAndStar } from 'common/constant';
import LinkButton from 'components/link-button';
import { global } from '@youzan/retail-utils';
import openMicroTransfer from '@youzan/micro-transfer-dialog';
import '@youzan/micro-transfer-dialog/lib/index.css';
import { isRetailHqStore } from '@youzan/utils-shop';
import { EXCHANGE_DETAIL, EXCHANGE_LIST } from 'components/opt-components/constant';
import { SaleWay } from 'common/constants/order';
import { IsAdvancedVersion } from '../../../common/utils';

const {
  KDT_ID,
  USER_INFO: { adminId: USER_ID }
} = global;
const SMALL_MONEY_LOCAL_STORAGE_KEY = `${KDT_ID}_${USER_ID}_DO_SMALL_MONEY`;

const { GIFT, WISH } = orderTypeValue;

export type ActionsProps = Partial<
  Pick<
    IOrderInfo,
    | 'operations'
    | 'mainOrderInfo'
    | 'remarkInfo'
    | 'fulfillOrder'
    | 'isFulfillOrder'
    | 'microTransferInfo'
    | 'exchangeType'
  > & {
    className: string;
    reload: () => void;
    onStarChange: (star: number) => void;
    buyerInfo: any;
    /** 是否展示备注 */
    withRemark?: boolean;
    /** 是否展示标星  */
    withStar?: boolean;
    /** 屏蔽某些 Operation */
    disabledOperationList?: string[];
    isQttOrder: boolean;
    qttOrderInfo: IQttOrderInfo;
  }
>;

class Actions extends React.Component<ActionsProps> {
  static defaultProps = {
    operations: [],
    mainOrderInfo: {},
    remarkInfo: {},
    fulfillOrder: {},
    isFulfillOrder: false,
    isQttOrder: false,
    qttOrderInfo: {}
  };

  state = {
    isFirstShowSmallMoney: false
  };

  hideSmallMoney = (e: MouseEvent): void => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore 我也不知道这个 this 是什么鬼
    if (this.smallMoneyRef && !this.smallMoneyRef.contains(e.target)) {
      this.setState({
        isFirstShowSmallMoney: false
      });
    }
  };

  componentDidMount() {
    const isExistDoMoneyValue = !!JSON.parse(localStorage.getItem(SMALL_MONEY_LOCAL_STORAGE_KEY)!);
    if (!isExistDoMoneyValue) {
      localStorage.setItem(SMALL_MONEY_LOCAL_STORAGE_KEY, '1');
      this.setState({
        isFirstShowSmallMoney: true
      });
      window.addEventListener('click', this.hideSmallMoney);
    }
  }

  componentWillUnmount() {
    window.removeEventListener('click', this.hideSmallMoney);
  }

  handleDoSmallMoney = () => {
    const { orderNo } = this.props.mainOrderInfo!;
    const { reload } = this.props;
    openMicroTransfer({
      orderNo: orderNo!,
      src: 'retail-pc',
      onSuccess: () => {
        reload && reload();
      }
    });
  };

  // 操作按钮分组
  getGroupOpts = () => {
    const { operations = [] } = this.props;
    // 过滤不显示的操作按钮
    const displayOpts = filter(
      operations,
      opt => NEW_OFFLINE_REFUND_CODES.indexOf(opt.code) === -1
    );
    const groupOpts = {
      buttonWithDesc: [],
      button: [],
      links: [] as IOperation[]
    };

    displayOpts.forEach((opt = {} as IOperation) => {
      //  按钮组
      if ([BUTTON, BUTTON_LINK].indexOf(opt.type) > -1) {
        // 文案的字段名称
        const descPropNames = ['goodsName', 'refundDesc'];

        // 有文案的 把文案放到buttonDesc组
        const itemDesc = descPropNames.reduce((pre: string[], name) => {
          const value: string = get(opt, `attributes.${name}`);
          value && pre.push(value);
          return pre;
        }, []);

        const btnGroups: IOperation[] =
          itemDesc.length > 0 ? groupOpts.buttonWithDesc : groupOpts.button;
        opt.desc = itemDesc;
        btnGroups.push(opt);
      } else {
        // link组
        groupOpts.links.push(opt);
      }
      if (opt.code === EXCHANGE_DETAIL) {
        /** 原换货记录，保持原逻辑不变，走新组件渲染 */
        opt.code = EXCHANGE_LIST;
        opt.text = '换货记录';
      }
    });
    return groupOpts;
  };

  handleStarChange = (star: number): void => {
    const { orderNo } = this.props.mainOrderInfo!;
    addStar({
      order_no: orderNo,
      star
    })
      .then(() => {
        Notify.success('加星成功！');
        this.props.onStarChange && this.props.onStarChange(star);
      })
      .catch(err => {
        Notify.error(err.msg || '加星失败！');
      });
  };

  render() {
    const {
      reload,
      buyerInfo,
      mainOrderInfo,
      remarkInfo,
      className,
      fulfillOrder,
      isFulfillOrder,
      microTransferInfo = {} as IMicroTransferInfo,
      withRemark = true,
      withStar = true,
      disabledOperationList,
      isQttOrder,
      qttOrderInfo,
      exchangeType
    } = this.props;
    const { buttonWithDesc, button, links } = this.getGroupOpts();

    const isOnlineOrder = mainOrderInfo?.saleWay === SaleWay.Online;

    const useOldDelivery =
      !isRetailSingleStore &&
      (mainOrderInfo!.isPeriodBuy || mainOrderInfo!.orderType === (GIFT || WISH));

    const options = {
      orderInfo: omit(this.props, 'reload'),
      reload,
      useOldDelivery
    };

    const disabled = notOptRemarkAndStar(mainOrderInfo!.kdtId);
    const { isFirstShowSmallMoney } = this.state;
    const { orderNo, buyerPhone, kdtId } = buyerInfo || {};

    return (
      <div
        className={cx('order-actions', className, {
          'fulfill-actions': isFulfillOrder
        })}
      >
        <div className="action-list">
          {buttonWithDesc.length > 0 &&
            buttonWithDesc.map((btnWithDesc: { desc: string[] }, i) => (
              <div key={i} className="button-with-desc">
                <p className="grey refund-desc">{btnWithDesc.desc.join('   ')}</p>
                <OptComponent operation={btnWithDesc} options={options} />
              </div>
            ))}
          <div className="action-list__item">
            {button.length > 0 &&
              button.map((buttonOpt: { code: string }) => (
                <OptComponent
                  operation={buttonOpt}
                  options={options}
                  key={buttonOpt.code}
                  orderNo={orderNo}
                  buyerPhone={buyerPhone}
                  salesKdtId={kdtId}
                  isOnlineOrder={isOnlineOrder}
                  // 换货流程模型
                  exchangeType={exchangeType}
                />
              ))}
          </div>
          <div className="actions-list__link">
            {links.length > 0 &&
              links.map((linkOpt, i) => {
                // 钱款去向金额
                const { typeDesc, fee } = get(linkOpt, 'attributes', {});

                let operationElem;
                if (!disabledOperationList || disabledOperationList?.indexOf(linkOpt.code) === -1) {
                  operationElem =
                    linkOpt.code === 'refund_fund' && isFxStore ? (
                      <Pop trigger="hover" content="供货店暂不支持查看，请前往总部查看。">
                        <div>
                          <OptComponent
                            operation={{ ...linkOpt, disabled: true }}
                            options={options}
                          />
                        </div>
                      </Pop>
                    ) : (
                      <OptComponent operation={linkOpt} options={options} />
                    );
                }

                return (
                  <p key={i} className="grey refund-desc">
                    {typeDesc && `${typeDesc}：${fee}  `}
                    {operationElem}
                  </p>
                );
              })}
          </div>
          {!!microTransferInfo.microTransferAbility &&
            (mainOrderInfo?.kdtId === _global.kdtId || (IsAdvancedVersion && isRetailHqStore)) && (
              <div
                className="action-list__smallmoney"
                ref={component => {
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  // @ts-ignore this来源未知
                  this.smallMoneyRef = component;
                }}
              >
                <Pop
                  trigger="none"
                  visible={isFirstShowSmallMoney}
                  position="top-left"
                  content={
                    <>
                      <p>新增小额打款功能。若你与买家之间存在补运</p>
                      <p>费、补差价等小额补偿需求，可使用此功能。</p>
                    </>
                  }
                >
                  <LinkButton
                    className={cx('action-list__smallmoney-content', {
                      'first-show__smallmoney': isFirstShowSmallMoney
                    })}
                    onClick={this.handleDoSmallMoney}
                  >
                    <span className="action-list__smallmoney-content__btn">小额打款</span>
                    <Pop
                      trigger="hover"
                      position="top-left"
                      content={
                        <>
                          <p>若你与买家之间存在补运费、补差价</p>
                          <p>等小额补偿需求，请使用小额打款功能。</p>
                        </>
                      }
                    >
                      <Icon type="help-circle" className="action-list__smallmoney-content__icon" />
                    </Pop>
                  </LinkButton>
                </Pop>
              </div>
            )}
        </div>
        <div className="actions-remark-star">
          <div className="action-remark">
            {withRemark ? (
              <OptComponent
                operation={{ code: 'remark' }}
                options={{
                  reload,
                  isFulfillOrder,
                  deliveryNo: fulfillOrder!.deliveryNo,
                  orderNo: mainOrderInfo!.orderNo,
                  disabled: disabled || (isFxStore && isFulfillOrder),
                  remark: isFulfillOrder ? remarkInfo!.deliveryRemark : remarkInfo!.sellerMemoDesc,
                  remarkPics: remarkInfo?.sellerRemarkPics,
                  founderToMemberMark: qttOrderInfo?.order?.founderToMemberMark || '',
                  isQttOrder
                }}
              />
            ) : null}
          </div>
          {!isFulfillOrder && withStar ? (
            <Star
              star={remarkInfo!.sellerStar}
              onStarChange={this.handleStarChange}
              orderNo={mainOrderInfo!.orderNo}
              noMouseEvent={disabled}
            />
          ) : null}
        </div>
      </div>
    );
  }
}

export default Actions;
