import * as React from 'react';
import { Steps } from 'zent';
import { isArray, findIndex, get } from 'lodash';
import { formatDatetime } from '@youzan/retail-utils';
import { isPeriodOrder } from 'common/helper';
import { OrderGoodsType } from 'common/constants/common';
import { IOrderInfo } from 'definition/order-info';
import QttPop from './qtt-pop';

const { Step } = Steps;

const isWestcake = !!get(window, '_global.business.isWestcake', false);

const TO_TUAN = {
  origin: 50,
  transform: 5
};

// 商家备货状态
const PREPARE = {
  origin: 20,
  transform: 5.5
};

const transformCodeMap = {
  [TO_TUAN.origin]: TO_TUAN.transform,
  [PREPARE.origin]: PREPARE.transform
};

const SELLER_DELIVERY = '商家发货';

const StateSteps = (
  props: Pick<
    IOrderInfo,
    'mainOrderInfo' | 'orderDetailStateTips' | 'refundStateAssemblyList' | 'refundStateProgress'
  >
) => {
  let {
    mainOrderInfo: { state, orderGoodsType },
    sourceInfo,
    orderDetailStateTips: { stateAssemblyList }
  } = props;
  let isRefundOrder = false;

  // 是否是群团团虚拟商品订单
  const isQttVirtualOrder =
    sourceInfo?.orderMark === 'quntuantuan' && orderGoodsType === OrderGoodsType.NormalVirtual;

  if (props.refundStateAssemblyList) {
    // 退款详情复用step
    isRefundOrder = true;
    state = props.refundStateProgress;
    stateAssemblyList = props.refundStateAssemblyList;
  }
  if (!isArray(stateAssemblyList) || stateAssemblyList.length === 0) {
    return null;
  }

  // 订单状态需要对待成团特殊处理
  const compareStateTips = stateAssemblyList.map(tip => {
    tip.compareState = tip.code;
    // 待成团备货订单会存在特殊code，这里需要转换一下，方便下面的对比
    if (!isRefundOrder && transformCodeMap[tip.code]) {
      tip.compareState = transformCodeMap[tip.code];
    }
    return tip;
  });

  const currentIndex = findIndex(compareStateTips, item => {
    // 对待成团特殊处理，待成团订单code【50: 待成团、 5: 待发货】
    if (state === TO_TUAN.origin) {
      return item.code === state || item.compareState > TO_TUAN.transform;
    }
    return item.compareState > state;
  });

  let current = currentIndex === -1 ? compareStateTips.length : currentIndex;
  let specialIndex = -1;
  // 幸福西饼订单的商家备货状态，此时未发货，state为5
  if (isWestcake) {
    const index = findIndex(stateAssemblyList, item => item.prepareStock === 1);
    if (index > -1) specialIndex = index;
  }
  /**
   * @宋锡铨
   * 周期购订单，只要发货过一期，发货节点就要点亮，这里发过一期，就会有record字段，如果没有，就证明没发过货，不需点亮
   * */
  if (isPeriodOrder(props)) {
    const index = findIndex(stateAssemblyList, item => !!item.record);
    if (index > -1) specialIndex = index;
  }

  if (specialIndex >= 0) {
    current = specialIndex + 1;
  }

  return (
    <div className="order-steps">
      <Steps current={current} status="process">
        {compareStateTips.map(stepInfo => {
          const dateStr = formatDatetime(stepInfo.time);
          return (
            <Step
              key={stepInfo.compareState}
              title={
                // 群团团 虚拟商品订单，增加一个说明pop
                stepInfo.content === SELLER_DELIVERY && isQttVirtualOrder ? (
                  <div style={{ display: 'flex', justifyContent: 'center' }}>
                    {stepInfo.content}
                    <QttPop />
                  </div>
                ) : (
                  stepInfo.content
                )
              }
              description={
                <div>
                  {dateStr || ''}
                  <br />
                  {stepInfo.record || ''}
                </div>
              }
            />
          );
        })}
      </Steps>
    </div>
  );
};

StateSteps.defaultProps = {
  orderDetailStateTips: {
    stateAssemblyList: []
  },
  mainOrderInfo: {}
};

export default StateSteps;
