import * as React from 'react';

// 将 a{{bb}}cc{{ddd}}  变成 【a,bb,cc,ddd】后，奇数的index肯定是被大括号包裹的，将其变红
export default function Renderer(str: string): string | JSX.Element[] | null {
  if (!str) return null;
  const replacedStr = str.replace(/{{/g, '}}');
  const arr = replacedStr.split('}}');
  if (arr.length < 2) {
    return arr[0];
  }
  return arr.map((subStr, idx) => {
    if (idx % 2 === 0) return <span>{subStr}</span>;
    return <span style={{ color: 'red' }}>{subStr}</span>;
  });
}
