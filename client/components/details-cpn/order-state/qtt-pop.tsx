import * as React from 'react';
import { Icon, Pop } from 'zent';

const QttPop = () => {
  return (
    <div style={{ marginLeft: '2px' }}>
      <Pop
        trigger="hover"
        position="bottom-left"
        // eslint-disable-next-line youzan/youzan-standard-words
        content="群团团订单中，虚拟商品的交易完成时间与实物商品保持一致（即你配置的自动确认收货时间）。"
      >
        <Icon style={{ color: '#ccc' }} type="help-circle" />
      </Pop>
    </div>
  );
};

QttPop.defaultProps = {};

export default QttPop;
