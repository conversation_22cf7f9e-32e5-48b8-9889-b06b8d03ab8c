import type { IOrderInfo, IQttOrderInfo } from 'definition/order-info';
import { previewImage } from 'zent';
import * as React from 'react';
import { get, compact } from 'lodash';

const Tips = (props: { qttOrderInfo: IQttOrderInfo } & Pick<IOrderInfo, 'tips' | 'remarkInfo'>) => {
  const {
    tips,
    remarkInfo: { sellerMemoDesc, buyerMemoDesc, deliveryRemark, sellerRemarkPics = [] },
    qttOrderInfo
  } = props;

  const yzTips = get(tips, 'orderDetailStateTips.yzTips', '') as string;

  if (compact([sellerMemoDesc, buyerMemoDesc, deliveryRemark, yzTips]).length === 0) {
    return null;
  }

  const handlePreview = (e, index: number) => {
    e.persist();
    previewImage({
      images: sellerRemarkPics,
      index,
      parentComponent: this,
      scaleRatio: 3,
      showRotateBtn: false
    });
  };

  return (
    <div className="order-tips">
      {sellerMemoDesc && (
        <div className="seller-remark grey">
          <span className="seller-remark__label"> 商家备注：</span>
          <span className="seller-remark__value large-line">
            {sellerMemoDesc}
            <br />
            {sellerRemarkPics.map((url: string, index) => {
              return (
                <span key={url} onClick={e => handlePreview(e, index)}>
                  <img className="seller-remark__pic" src={url} alt="商家备注图片" />
                </span>
              );
            })}
          </span>
        </div>
      )}
      {qttOrderInfo?.order?.founderToMemberMark && (
        <div className="seller-remark grey">
          <span className="seller-remark__label"> 团长备注：</span>
          <span className="seller-remark__value">{qttOrderInfo.order.founderToMemberMark}</span>
        </div>
      )}
      {buyerMemoDesc && (
        <div className="seller-remark grey">
          <span className="seller-remark__label"> 买家备注：</span>
          <span className="seller-remark__value">{buyerMemoDesc}</span>
        </div>
      )}
      {deliveryRemark && (
        <div className="seller-remark grey">
          <span className="seller-remark__label"> 发货单备注：</span>
          <span className="seller-remark__value">{deliveryRemark}</span>
        </div>
      )}
      {yzTips && (
        <div className="yz-tips">
          <span className="orange">有赞提醒：</span>
          <ul className="grey">
            {yzTips.split('\\n').map((lineText, index) => (
              <li key={index}>{lineText}</li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

Tips.defaultProps = {
  tips: {},
  remarkInfo: {}
};

export default Tips;
