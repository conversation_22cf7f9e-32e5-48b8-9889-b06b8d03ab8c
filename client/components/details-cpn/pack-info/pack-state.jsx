/* eslint-disable @youzan/domain/forbid-hardcode-domain-name */
import React, { useState } from 'react';
import cx from 'classnames';
import { get } from 'lodash';
import { differenceInDays } from 'date-fns';
import { formatDatetime } from '@youzan/retail-utils';
import { OptComponent } from 'components/opt-components';
import { LOGISTICS_REMARK, LOGISTICS_PROCESSED } from 'components/opt-components/constant';
import { ExpressType } from 'common/constants/common';
import { Link as SamLink } from '@youzan/sam-components';
import { Dialog } from 'zent';
import styles from './style.scss';

const acceptedOpts = {
  add_tips: true,
  delivery_cancel: true,
  delivery_recall: true,
  delivery_arrived: true
};

/** 物流备注操作项 */
const LogisticsRemarkOpts = [LOGISTICS_REMARK, LOGISTICS_PROCESSED];

const { Express, City, SelfFetch } = ExpressType;

export const PackStateStep = props => {
  if (props.steps.length === 0) {
    return null;
  }
  return (
    <div className={cx(styles.packStepWrapper, props.className)}>
      <ul className="pack-step">
        {props.steps.map((step, index) => {
          const timeDesc = formatDatetime(step.time);
          return (
            <li
              key={index}
              className={cx({
                'last-child': index === props.steps.length - 1
              })}
            >
              <p>
                <span>{timeDesc}</span>
                <span>{step.context}</span>
              </p>
            </li>
          );
        })}
      </ul>
    </div>
  );
};

PackStateStep.defaultProps = {
  steps: []
};

const PickUppictures = props => {
  const { courierPickupOrderPhotos = [], courierDeliveryOrderPhotos = [] } = props.expressInfo;
  const [visible, setVisible] = useState(false);
  return (
    <div className="pick-up">
      <span className="link-button">
        <SamLink type="link" onClick={() => setVisible(true)}>
          取派照片
        </SamLink>
      </span>
      <Dialog visible={visible} onClose={() => setVisible(false)}>
        <div className="pick-up__pictures">
          {!!courierPickupOrderPhotos.length && (
            <section>
              <h3>取件照片</h3>
              <div>
                <div>
                  {courierPickupOrderPhotos.map(src => (
                    <img src={src} alt="" />
                  ))}
                </div>
              </div>
            </section>
          )}
          {!!courierDeliveryOrderPhotos.length && (
            <section>
              <h3>派件照片</h3>
              <div>
                {courierDeliveryOrderPhotos.map(src => (
                  <img src={src} alt="" />
                ))}
              </div>
            </section>
          )}
        </div>
      </Dialog>
    </div>
  );
};

const PackState = props => {
  const opts = get(props, 'packInfo.operations', []);

  const { expressType } = props;

  // 自提不显示
  if (expressType === SelfFetch) {
    return null;
  }

  const getEmptyMsg = function (expressTime) {
    // eslint-disable-next-line
    return expressTime && differenceInDays(new Date(), new Date(expressTime)) > 180
      ? '超过6个月的包裹信息不再展示'
      : '未获取到物流信息';
  };

  const expressTypeMap = {
    [Express]: {
      emptyMsg: '无物流信息',
      data: get(props, 'packInfo.expressDetail.tansitInfo.data', []),
      stateDesc: get(props, 'packInfo.expressDetail.tansitInfo.stateDesc', ''),
      abnormalLogisticsInfo: props.packInfo.expressDetail?.tansitInfo?.abnormalLogisticsInfo || '',
      render() {
        return (
          <p className="pack-state__survey survey-opt-box">
            <span className="survey-label">物流备注：</span>
            <span className="survey-text">
              {props.packInfo.expressDetail?.logisticsRemark || '-'}
            </span>
            <span className="pack-state__operations">
              {opts
                .filter(ele => LogisticsRemarkOpts.includes(ele.code))
                .map(ele => (
                  <OptComponent operation={ele} options={props} />
                ))}
            </span>
          </p>
        );
      }
    },
    [City]: {
      emptyMsg: getEmptyMsg(get(props, 'packInfo.expressTime')),
      data: get(props, 'packInfo.takeoutExpressDetail.data', []),
      stateDesc: get(props, 'packInfo.takeoutExpressDetail.statusDesc', ''),
      courierDeliveryOrderPhotos: get(
        props,
        'packInfo.takeoutExpressDetail.courierDeliveryOrderPhotos'
      ),
      courierPickupOrderPhotos: get(props, 'packInfo.takeoutExpressDetail.courierPickupOrderPhotos')
    }
  };

  const expressInfo = expressTypeMap[expressType];

  if (!expressInfo) {
    return null;
  }

  const stateDesc =
    expressInfo.data.length === 0 && !expressInfo.stateDesc
      ? expressInfo.emptyMsg
      : expressInfo.stateDesc;

  const hasPhotos =
    expressInfo.courierDeliveryOrderPhotos?.length || expressInfo.courierPickupOrderPhotos?.length;

  return (
    <div className="pack-state">
      {expressInfo.render ? expressInfo.render() : null}
      {stateDesc && (
        <p className="pack-state__survey survey-opt-box">
          <span className="survey-label">物流状态：</span>
          <span className="survey-text">
            {stateDesc}
            {expressInfo.abnormalLogisticsInfo ? (
              <span className="orange">（{expressInfo.abnormalLogisticsInfo}）</span>
            ) : null}
          </span>
          {expressType === City && hasPhotos && <PickUppictures expressInfo={expressInfo} />}
          <span className="pack-state__operations">
            {opts
              .filter(ele => acceptedOpts[ele.code])
              .map(ele => (
                <OptComponent operation={ele} options={{ ...props, ...props.mainOrderInfo }} />
              ))}
          </span>
        </p>
      )}
      {expressInfo.data.length > 0 && <PackStateStep steps={expressInfo.data} />}
    </div>
  );
};

export default PackState;
