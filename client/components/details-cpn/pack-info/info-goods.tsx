/* eslint-disable react/jsx-props-no-spreading */
import type { Contains } from 'definition/common';

import * as React from 'react';
import { get, omit } from 'lodash';
import { PopInfo } from '@youzan/retail-components';
import { formatDatetime } from '@youzan/retail-utils';
import { isHqStore, isPartnerStore } from '@youzan/utils-shop';

import { IMainOrderInfo, IOperation, IOrderAddressInfo, IPackageInfo } from 'definition/order-info';
import { pickItemsByItemIds } from 'common/helper';
import { OptComponent } from 'components/opt-components';
import { convertFenToYen } from 'common/fns/format';
import { ExpressType } from 'common/constants/common';
import { SupportParallelCallInLocalDelivery } from 'common/constant';

import GoodsSwiper from 'components/pack-info/goods-swiper';
import {
  LOGISTICS_PROCESSED,
  LOGISTICS_REMARK,
  CHANGE_EXPRESS
} from 'components/opt-components/constant';

import { SelfFetchInfo } from './self-fetch-info';

const { Express, City } = ExpressType;

const CANCELED_CODE = 20;

const acceptedOpts = {
  delivery_contact: true
};
// 物流信息
const ExpressInfo = (
  props: Contains<{
    packInfo: Contains<{
      operations: IOperation[];
      expressDetail?: Contains<{
        expressNo: string;
        expressName?: string;
        tansitInfo?: Contains<Record<string, never>>;
        waybillVersion?: 1 | 2;
      }>;
    }>;
  }>
) => {
  const {
    expressNo,
    expressName = '',
    tansitInfo = {},
    waybillVersion = 1
  } = props.packInfo.expressDetail ?? {};
  const isCanceled = tansitInfo.wayBillStatusCode === CANCELED_CODE;

  const { operations = [] } = props.packInfo;
  const filteredOperations = operations.filter(
    /** Operation 黑名单, 这部分 Operation 不在这里展示 */
    operation =>
      ![
        /** 物流备注和物流处理放 pack-state 中展示 */
        LOGISTICS_REMARK,
        LOGISTICS_PROCESSED
      ].includes(operation.code)
  );

  // 如果操作按钮中有修改物流，就不用校验expressNo
  const changeExpressOperation = filteredOperations.find(
    operation => operation.code === CHANGE_EXPRESS
  );

  return expressNo || changeExpressOperation ? (
    <div className="info-item">
      <span>运单号：</span>
      {!expressName && !expressNo ? (
        '-'
      ) : (
        <span>
          {expressName} {expressNo} {`${isCanceled ? '(已取消)' : ''}`}
        </span>
      )}
      <p className="flex">
        {filteredOperations.length > 0 &&
          filteredOperations.map((operation, key) => (
            <OptComponent
              operation={operation}
              options={{
                orderInfo: omit(props, 'reload'),
                reload: props.reload,
                waybillVersion
              }}
              key={key}
            />
          ))}
      </p>
    </div>
  ) : null;
};

// 同城配送配送员信息
const DeliveryInfo = (
  props: Contains<{
    packInfo?: Contains<{
      takeoutExpressDetail?: Contains<{
        deliveryNo: string;
        transporterName: string;
        transporterPhone?: string;
        deliveryFee: number;
        tipFee: number;
        insuranceProductDesc: string;
        deliveryFeeDesc: string;
        outDeliveryNo: string;
        distanceDesc: string;
        weightDesc: string;
        buyerDeliveryPwd?: string;
        transporterOrderCount?: number;
      }>;
    }>;
  }>
) => {
  const {
    deliveryNo,
    transporterName,
    transporterPhone = '',
    tipFee,
    insuranceProductDesc = '',
    deliveryFeeDesc = '',
    outDeliveryNo = '',
    distanceDesc = '',
    weightDesc = '',
    buyerDeliveryPwd = '',
    transporterOrderCount = 0
  } = props?.packInfo?.takeoutExpressDetail ?? {};

  return (
    <div>
      {SupportParallelCallInLocalDelivery && (
        <div className="info-item">
          <span className="info-item__lable">运单号：</span>
          <span>{deliveryNo}</span>
        </div>
      )}
      {transporterName && (
        <div className="info-item">
          <span>配送员：</span>
          <span>
            {transporterName} {transporterPhone}{' '}
            {!!transporterOrderCount && (
              <span className="info-item__weaken">(共服务{transporterOrderCount}次)</span>
            )}
          </span>
        </div>
      )}
      {insuranceProductDesc && (
        <div className="info-item">
          <span>商品保价：</span>
          <span>{insuranceProductDesc}</span>
        </div>
      )}
      {weightDesc && (
        <div className="info-item">
          <span>商品重量：</span>
          <span>{weightDesc}</span>
        </div>
      )}
      {distanceDesc && (
        <div className="info-item">
          <PopInfo
            content={
              <>
                <span>配送距离：</span>
                <span>{distanceDesc}</span>
              </>
            }
            popContent="该距离由服务商计算返回"
          />
        </div>
      )}
      {deliveryFeeDesc && (
        <div className="info-item delivery-fee">
          <span>配送费：</span>
          <span>{deliveryFeeDesc}</span>
        </div>
      )}
      {/* {deliveryFee && (
        <div className="info-item delivery-fee">
          <span>配送费：</span>
          <span>￥{convertFenToYen(deliveryFee)}</span>
        </div>
      )} */}
      {tipFee ? (
        <div className="info-item delivery-fee">
          <span>小费：</span>
          <span>￥{convertFenToYen(tipFee)}</span>
        </div>
      ) : null}
      {outDeliveryNo && (
        <div className="info-item">
          <span>服务商单号：</span>
          <span>{outDeliveryNo}</span>
        </div>
      )}
      {buyerDeliveryPwd && (
        <div className="info-item">
          <span>收货码：</span>
          <span>{buyerDeliveryPwd}</span>
        </div>
      )}
    </div>
  );
};

const expressCpnMap = {
  [Express]: ExpressInfo,
  [City]: DeliveryInfo
};

// 物流基本信息以及商品信息
const InfoGoods = ({
  ...props
}: Contains<{
  itemInfo?: Array<Contains<{ itemIdStr: string }>>;
  packInfo: IPackageInfo;
  mainOrderInfo: IMainOrderInfo;
  orderAddressInfo: IOrderAddressInfo;
}>) => {
  const opts = props.packInfo.operations ?? [];
  const {
    sendTypeDesc = '-',
    extraInfo = {},
    expressTime = 0,
    expressType,
    itemIds = [],
    distItemInfo = [],
    packId,
    userName,
    warehouseName,
    sendType,
    deliveryRemark,
    takeoutExpressDetail
  } = props.packInfo;
  // @ts-ignore
  const packItems = pickItemsByItemIds(itemIds, props.itemInfo, distItemInfo);
  const ExtraPackInfo = (expressCpnMap as any)[expressType!];
  const showOpts = get(props, 'mainOrderInfo.expressType') === 2 && sendType !== 22;
  const isSelfFetch = props.packInfo?.expressType === ExpressType.SelfFetch;

  if (isSelfFetch) {
    return (
      <div className="pack-info-goods">
        <div className="pack-info__item">
          {isSelfFetch && (
            <SelfFetchInfo
              addressInfo={props.orderAddressInfo}
              packInfo={props.packInfo as IPackageInfo}
            />
          )}
          {ExtraPackInfo && <ExtraPackInfo {...props} />}
          <GoodsSwiper goodsList={packItems} packId={packId} />
        </div>
      </div>
    );
  }

  return (
    <div className="pack-info-goods">
      <div className="pack-info__item">
        {(isHqStore || isPartnerStore) && warehouseName && (
          <div className="info-item">
            <span className="info-item__label">发货仓库：</span>
            <span>{warehouseName}</span>
          </div>
        )}
        <div className="info-item">
          <span className="info-item__label">发货方式：</span>
          <span>{sendTypeDesc}</span>
        </div>
        {SupportParallelCallInLocalDelivery && (
          <>
            <div className="info-item">
              <span className="info-item__label">呼叫方式：</span>
              <span>{takeoutExpressDetail?.callTypeDesc ?? '-'}</span>
            </div>
            <div className="info-item">
              <span className="info-item__label">服务商：</span>
              <span className="info-item__value">
                {takeoutExpressDetail?.companyName ?? '-' + ' '}
              </span>
              {showOpts &&
                opts
                  // @ts-ignore
                  .filter((ele: Contains<{ code: string }>) => (acceptedOpts as any)[ele.code])
                  .map(ele => (
                    <OptComponent operation={ele} options={{ ...props, ...props.mainOrderInfo }} />
                  ))}
            </div>
          </>
        )}
        {extraInfo.sentName && (
          <div className="info-item">
            <span className="info-item__label">发货人：</span>
            <span>
              {extraInfo.sentName} {extraInfo.sentPhone || ''}
            </span>
          </div>
        )}
        {userName && (
          <div className="info-item">
            <span className="info-item__label">核销人：</span>
            <span>{userName}</span>
          </div>
        )}
        <div className="info-item">
          <span className="info-item__label">发货时间：</span>
          <span>{formatDatetime(+expressTime)}</span>
        </div>

        {ExtraPackInfo && <ExtraPackInfo {...props} />}
        {!!deliveryRemark && (
          <div className="info-item">
            <span className="info-item__label">发货备注：</span>
            <span>{deliveryRemark}</span>
          </div>
        )}
        <GoodsSwiper goodsList={packItems} packId={packId} />
      </div>
    </div>
  );
};

InfoGoods.defaultProps = {
  itemInfo: [],
  packInfo: {},
  mainOrderInfo: {}
};

export default InfoGoods;
