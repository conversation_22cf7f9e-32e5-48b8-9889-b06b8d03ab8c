/* eslint-disable react/jsx-props-no-spreading */
import type { Contains } from 'definition/common';

import * as React from 'react';
import { deepJsonParse } from '@youzan/retail-utils';
import { isRetailSingleStore } from '@youzan/utils-shop';

import CityDeliveryMap from 'components/city-delivery-map';
import InfoGoods from 'components/details-cpn/pack-info/info-goods';
import PackState from 'components/details-cpn/pack-info/pack-state';
import { LocalDeliveryStateType } from '@youzan/zan-hasaki';

import { ExpressType } from 'common/constants/common';
import style from './style.scss';

const { Component } = React;

export default class PackInfo extends Component<
  Contains<{
    mainOrderInfo: Contains<{
      orderNo: string;
      storeId?: number;
      orderType: number;
    }>;
    orderAddressInfo: Contains<{
      addressExtra: string;
    }>;
    packInfo?: Contains<{}>;
  }>
> {
  state: Contains<{}> = {};

  static defaultProps = {
    showButton: true
  };

  renderCityDeliveryMap = (
    packInfo: Contains<{
      takeoutExpressDetail?: Contains<{
        statusCode: number;
      }>;
      estimateArriveTimeTip?: string;
      packId?: string;
      warehouseId?: number;
    }>
  ): JSX.Element | null => {
    const { takeoutExpressDetail, packId, warehouseId } = packInfo;
    const { orderNo, storeId = 0 } = this.props.mainOrderInfo;
    const { addressExtra } = this.props.orderAddressInfo;
    const buyerPos = deepJsonParse(addressExtra);

    if (
      [LocalDeliveryStateType.Received, LocalDeliveryStateType.Sending].indexOf(
        +(takeoutExpressDetail?.statusCode ?? NaN)
      ) === -1
    ) {
      return null;
    }
    // 连锁版请求参数是不一样的
    const extraProps = isRetailSingleStore ? { storeId } : { warehouseId };

    return (
      <CityDeliveryMap
        {...extraProps}
        packId={packId}
        orderNo={orderNo}
        buyerPos={buyerPos}
        estimateArriveTimeTip={takeoutExpressDetail?.estimateArriveTimeTip}
      />
    );
  };

  renderPackItem = (
    packInfo: Contains<{
      takeoutExpressDetail?: Contains<{
        statusCode: number;
      }>;
      packId?: string;
      warehouseId?: number;
      expressType?: ExpressType;
    }>
  ): JSX.Element => {
    return (
      <div>
        {this.renderCityDeliveryMap(packInfo ?? {})}
        <div className="pack-info">
          <InfoGoods packInfo={packInfo} {...this.props} />
          <div className="pack-info-state">
            <PackState
              {...this.props}
              mainOrderInfo={this.props.mainOrderInfo}
              packInfo={packInfo}
              expressType={packInfo.expressType}
            />
          </div>
        </div>
      </div>
    );
  };

  render(): JSX.Element {
    const { packInfo } = this.props;
    return (
      <div className={style.packs}>
        {this.state.optNodes || null}
        {this.renderPackItem(packInfo ?? {})}
      </div>
    );
  }
}
