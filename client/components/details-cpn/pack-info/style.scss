@import '~shared/style';

:local(.packs) {
  margin-bottom: 10px;

  .hide-nav {
    .zent-tabs-nav {
      display: none;
    }
  }

  .pack-info {
    border: 1px solid $background-color-base;
    padding: 35px 15px 20px;
    display: flex;

    .link-button + .link-button {
      // margin-left: 10px;
    }

    &-goods {
      flex: 1;
      max-width: 310px;
      padding-left: 20px;

      .info-item {
        line-height: 22px;
        word-break: break-all;

        &__value {
          margin-right: 6px;
        }
        &__weaken {
          color: #646566;
        }

        .link-button {
          margin-right: 10px;
        }

        .flex {
          display: flex;
        }

        &.delivery-fee {
          color: $color-alert;

          .action {
            margin-left: 15px;
          }
        }
      }

      .goods-swiper {
        display: flex;
        padding: 20px 0;

        .goods-item {
          color: $color-text-secondary;
          width: 60px;
          margin-right: 10px;

          &__img {
            width: 60px;
            height: 60px;
          }

          &__text {
            width: 100%;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
      }
    }

    &-state {
      flex: 2;
    }
  }

  .pack-state__survey + .pack-state__survey {
    margin-top: 4px;
  }

  .pack-state__operations {
    margin-left: 6px;
  }

  .survey-text {
    word-break: break-word;
  }
}

:local(.packStepWrapper) {
  margin-top: 20px;
  height: 200px;
  padding-left: 6px;
  overflow-y: scroll;

  p > span {
    display: block;

    &:first-child {
      float: left;
      width: 150px;
      text-align: right;
    }

    &:last-child {
      margin-left: 180px;
    }
  }

  .pack-step {
    border-left: 1px solid $border-color-base;
    margin-top: 20px;
    margin-right: 30px;

    li {
      position: relative;
      margin-left: 10px;
      padding-top: 12px;
      padding-bottom: 12px;
      color: $color-text-light-primary;

      &::before {
        content: '';
        position: relative;
        display: block;
        top: 17px;
        right: 13px;
        width: 16px;
        height: 16px;
      }

      &::after {
        content: '';
        position: absolute;
        display: block;
        top: 33px;
        left: -15px;
        border-radius: 4px;
        width: 8px;
        height: 8px;
        background-color: $color-text-disable;
      }

      &:first-child span:last-child {
        font-weight: bold;
      }

      &:first-child::after {
        background-color: $color-warn;
      }

      &.last-child {
        padding-bottom: 0;
      }
    }
  }

  .survey-opt-box {
    display: flex;
  }
}

.pick-up {
  display: inline-block;
  .link-button {
    margin-left: 8px;
    margin-right: 2px;
  }
}

.pick-up__pictures {
  width: 500px;
  height: 400px;

  section img {
    width: 400px;
  }
}
