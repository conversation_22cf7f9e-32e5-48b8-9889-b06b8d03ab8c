import * as React from 'react';
import { isNil } from 'lodash';
import { format } from 'date-fns';
import { IOrderAddressInfo, IPackageInfo } from 'definition/order-info';

export const SelfFetchInfo: React.FC<SelfFetchInfoProps> = function SelfFetchInfo({
  addressInfo: { selfFetchInfo = {} } = {},
  packInfo
}) {
  const expressTime = isNil(packInfo?.expressTime)
    ? packInfo?.expressTime
    : format(packInfo.expressTime, 'YYYY-MM-DD HH:mm:ss');
  return (
    <>
      <div className="info-item">
        <span className="info-item__label">核销方式：</span>
        <span>{packInfo?.sendTypeDesc}</span>
      </div>
      <div className="info-item">
        <span className="info-item__label">核销人：</span>
        <span>{packInfo?.userName /*  ?? packInfo?.extraInfo?.sentName */}</span>
      </div>
      <div className="info-item">
        <span className="info-item__label">核销时间：</span>
        <span>{expressTime}</span>
      </div>
      <div className="info-item">
        <span className="info-item__label">核销自提点：</span>
        <span>{selfFetchInfo?.name}</span>
      </div>
    </>
  );
};

interface SelfFetchInfoProps {
  addressInfo: IOrderAddressInfo;
  packInfo: IPackageInfo;
}
