import * as React from 'react';
import { Pop } from 'zent';
import IPopProps from 'zent/es/pop/index';

export enum Hidden {
  Left = 'left',
  Right = 'right'
}

interface SafeText {
  children?: React.ReactElement;
  width?: string;
  hidden?: Hidden;
  val?: string;
  popProps?: IPopProps;
}

const TextOverflowBasicStyle = {
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap'
};

const TextOverflowLeftStyle = {
  ...TextOverflowBasicStyle,
  direction: 'rtl',
  textAlign: 'right'
};

const CursorPointer = {
  cursor: 'pointer'
};

export default (props: SafeText): JSX.Element => {
  const { children, width, hidden = Hidden.Left, val, popProps = {} } = props;

  // 判断文案是否被省略，若被省略加入Pop...
  const [valScrollWidth, setScrollWidth] = React.useState(0);
  const [valOffsetWidth, setOffsetWidth] = React.useState(0);
  const valRef = React.useCallback(node => {
    if (node !== null) {
      setScrollWidth(node.scrollWidth);
      setOffsetWidth(node.offsetWidth);
    }
  }, []);

  const renderChildren = (style: React.CSSProperties) => {
    return React.Children.map(children, (child): JSX.Element | undefined => {
      return child
        ? React.cloneElement(child, {
            style: {
              display: 'block',
              ...(width ? { width } : {}),
              ...style
            },
            ref: valRef
          })
        : undefined;
    });
  };

  const renderText = (style: React.CSSProperties) => (
    <span style={{ display: 'block', ...(width ? { width } : {}), ...style }}>{val}</span>
  );

  // 优先使用val
  const renderTarget = val ? renderText : renderChildren;

  const baseStyle = hidden === Hidden.Left ? TextOverflowBasicStyle : TextOverflowLeftStyle;

  return (
    <>
      {!valOffsetWidth || !(valScrollWidth > valOffsetWidth) ? renderTarget(baseStyle) : undefined}
      {valOffsetWidth && valOffsetWidth && valScrollWidth > valOffsetWidth ? (
        <Pop trigger="hover" position="top-right" content={val || children} {...popProps}>
          {renderTarget({ ...baseStyle, ...CursorPointer })}
        </Pop>
      ) : undefined}
    </>
  );
};
