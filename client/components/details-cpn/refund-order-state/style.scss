@import '~shared/style';

:local(.order-state) {
  margin-top: 14px;
  border: 1px solid $background-color-base;

  .state-info-actions {
    padding: 28px 20px;
    border-right: 1px solid $background-color-base;

    &.no-border {
      border-right: none;
    }

    .state-desc {
      margin-bottom: 14px;
      font-size: 20px;
      font-weight: 700;
    }

    .refund-detail-status {
      li {
        margin-bottom: 5px;
      }
    }

    .refund-actions {
      .action-list {
        margin: 20px 0;

        .zent-btn,
        .link-button {
          margin-right: 10px;
        }

        .link-button {
          vertical-align: middle;
        }
      }
    }
  }

  .order-steps {
    padding: 20px 20px 10px;
  }

  .order-tips {
    border-top: 1px solid $background-color-base;
    padding: 14px 20px;

    .yz-tips {
      display: flex;
    }
  }
}

.orange {
  color: $color-warn;
}

.grey {
  color: $color-text-light-primary;
}
