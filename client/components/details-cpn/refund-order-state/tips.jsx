import React from 'react';

const Tips = ({ tipContent = '' }) => {
  if (!tipContent) return null;
  return (
    <div className="order-tips">
      <div className="yz-tips">
        <span className="orange">有赞提醒：</span>
        <ul className="grey">
          {tipContent.split('\n').map((lineText, index) => (
            <li key={index}>{lineText}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default Tips;
