import React, { Component, PureComponent } from 'react';

class RemainTime extends (PureComponent || Component) {
  constructor(props) {
    super(props);
    this.state = {
      remainTime: props.remainTime
    };
  }

  componentDidMount() {
    this.startInterval();
  }

  componentWillUnmount() {
    this.stopInterval();
  }

  componentWillReceiveProps(nextProps) {
    this.setState({
      remainTime: nextProps.remainTime
    });
  }

  startInterval = () => {
    if (this.state.remainTime <= 0) {
      return;
    }
    this.timer = setInterval(() => {
      const { remainTime: rt } = this.state;
      const remainTime = rt - 1000;
      if (remainTime < 0) {
        this.stopInterval();
        return;
      }
      this.setState({
        remainTime
      });
    }, 1000);
  };

  stopInterval = () => {
    clearInterval(this.timer);
  };

  getRemainTimeText = () => {
    const keepDigit = num => (num >= 10 ? num : `0${num}`);
    let remainTime = Math.floor(this.state.remainTime / 1000);
    const second = keepDigit(remainTime % 60);
    remainTime = Math.floor((remainTime - second) / 60);
    const minute = keepDigit(remainTime % 60);
    remainTime = Math.floor((remainTime - minute) / 60);
    const hour = remainTime % 24;
    remainTime = Math.floor((remainTime - hour) / 24);
    const day = remainTime;

    return `${day}天${hour}小时${minute}分钟${second}秒`;
  };

  render() {
    return <em className="orange">{this.getRemainTimeText()}</em>;
  }
}

export default RemainTime;
