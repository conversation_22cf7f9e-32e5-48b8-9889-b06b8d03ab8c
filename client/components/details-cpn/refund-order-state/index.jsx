/*
 * 退款维权详情页 - 退款单状态信息
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2018-06-01 17:10:56
 * @Last Modified by: z<PERSON><PERSON><PERSON>
 * @Last Modified time: 2018-06-28 15:25:01
 */

import React from 'react';
import cx from 'classnames';
import { LayoutRow as Row, LayoutCol as Col, LayoutGrid as Grid } from 'zent';
import Steps from 'components/details-cpn/order-state/steps';
import Actions from './actions';
import Tips from './tips';
import { getRefundDetailStatus } from './helper';
import style from './style.scss';

const RefundOrderState = ({ refundDetailPageInfo, reload }) => {
  const {
    refundTipInfo,
    refundOrderInfo,
    operation = [],
    refundStateAssemblyList
  } = refundDetailPageInfo;
  const statusDesc = refundOrderInfo?.status || '',
    isHideSteps = refundStateAssemblyList && refundStateAssemblyList.length;

  return (
    <div className={cx(style['order-state'], 'info-block')}>
      <Grid>
        <Row>
          <Col span={8} className={cx('state-info-actions', { 'no-border': !isHideSteps })}>
            <div>
              <h2 className="state-desc">{statusDesc}</h2>
              <ul className="refund-detail-status">{getRefundDetailStatus(refundTipInfo || {})}</ul>
            </div>
            {operation.length > 0 && (
              <Actions
                operation={operation}
                options={{
                  ...refundDetailPageInfo,
                  reload
                }}
              />
            )}
          </Col>
          {isHideSteps && (
            <Col span={16} className="state-steps">
              <Steps
                refundStateProgress={refundOrderInfo.refundStateProgress}
                refundStateAssemblyList={refundStateAssemblyList}
              />
            </Col>
          )}
        </Row>
      </Grid>
      <Tips {...refundTipInfo} />
    </div>
  );
};

export default RefundOrderState;
