import React from 'react';
import cx from 'classnames';
import { OptComponent } from 'components/opt-components';

export default function Actions({ operation = [], className, options = {} }) {
  return (
    <div className={cx('refund-actions', className)}>
      <div className="action-list">
        {operation.map(buttonOpt => (
          <OptComponent
            key={buttonOpt.code}
            operation={buttonOpt}
            options={{
              ...options,
              orderInfo: {
                mainOrderInfo: {
                  orderNo: options.simpleOrderInfo.orderNo
                }
              }
            }}
          />
        ))}
      </div>
    </div>
  );
}
