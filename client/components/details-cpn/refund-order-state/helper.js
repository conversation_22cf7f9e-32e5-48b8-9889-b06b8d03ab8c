import React from 'react';
import { trim } from 'lodash';
import { Pop } from 'zent';
import { BlankLink } from '@youzan/react-components';
import isFxStore from 'common/is-fx-store';
import RemainTime from './remain-time';

/**
 * 获取文案中占位符数组
 * @param {String} str 每行文案
 * @returns {Array} ret 返回占位符数组
 */
function getPlaceHolders(str) {
  const ret = [];
  if (!str) return ret;
  str.replace(/{{(.*?)}}/g, ($0, $1) => {
    if ($1) {
      ret.push(trim($1));
    }
  });
  return ret;
}

function fillPlaceHolder(line, extra) {
  const placeHolders = getPlaceHolders(line);
  return line.split(/{{.*?}}/).reduce((prev, current, index) => {
    const holderName = placeHolders.shift(),
      extraItm = extra[holderName];
    prev.push(current);
    if (extraItm) {
      if (extraItm.type === 'TIME') {
        prev.push(<RemainTime key={`time${index}`} remainTime={+extraItm.text * 1000} />);
      } else if (extraItm.type === 'LINK') {
        if (extraItm.text === '到账进度' && isFxStore) {
          prev.push(
            <Pop trigger="hover" content="供货店暂不支持查看，请前往总部查看。">
              <span style={{ color: 'darkgray' }}>{extraItm.text}</span>
            </Pop>
          );
        } else {
          prev.push(
            <BlankLink key={`link${index}`} href={extraItm.url}>
              {extraItm.text}
            </BlankLink>
          );
        }
      }
    }
    return prev;
  }, []);
}

/**
 * 解析文案中的占位符，并替换成对应的组件
 * @param {Object} param0
 */
export const getRefundDetailStatus = ({ tips = '', extra = [] }) =>
  tips
    .split('\n')
    .map((line, idx) => (
      <li key={`line${idx}`}>
        {/^{{indent}}/.test(line) ? (
          <div style={{ marginLeft: '70px' }}>{fillPlaceHolder(line, extra)}</div>
        ) : (
          fillPlaceHolder(line, extra)
        )}
      </li>
    ));
