@import '~shared/style';

:local(.detail-basic-info) {
  display: flex;
  flex-wrap: wrap;

  .basic-item {
    margin-right: 25px;

    .source-no {
      margin-left: 10px;
    }

    .link-blue-color {
      color: $color-link;
      cursor: pointer;
      margin-left: 10px;
    }

    .point {
      display: inline-block;
      border-radius: 3px;
      margin-right: 6px;
      margin-bottom: 3px;
      width: 6px;
      height: 6px;
      vertical-align: middle;
      background-color: $color-link;

      &.online {
        background-color: $color-alert;
      }
    }
  }
}

:local(.pop-content) {
  .label-item {
    display: flex;
  }
}
