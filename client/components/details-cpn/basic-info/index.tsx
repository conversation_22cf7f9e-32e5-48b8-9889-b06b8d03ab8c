import * as React from 'react';
import { Pop } from 'zent';
import cx from 'classnames';
import { get, isEmpty, isNil } from 'lodash';
import { formatDatetime, setUrlDomain, deepJsonParse } from '@youzan/retail-utils';
import {
  isHqStore,
  isPartnerStore,
  isRetailMinimalistShop,
  isRetailSingleStore
} from '@youzan/utils-shop';
import { BlankLink } from '@youzan/react-components';

import type { RefundOrderInfo } from 'components/opt-components/buyer-refund/content/interface';
import type {
  IFulfillOrder,
  IMainOrderInfo,
  IOrderExchangeInfo,
  IOrderInfo,
  IPaymentInfo
} from 'definition/order-info';
import createOrderCpm from 'components/create-order-cpm';
import { withOrderHelper } from 'common/helper';
import { hasLiteAbility } from 'common/constants/common';
import SupplierName from 'components/supplier-name';

import { isCandaoOrder, isGYYOrder } from 'common/biz-helper';
import { basicInfoTypeMap } from '../constants';
import style from './style.scss';

const showTypeSource = (orderTypeDesc = '', orderSource = '', marketingTypeDesc = ''): string => {
  const textArr = [];
  // 零售单店/lite版不展示分销员/导购员标签
  if (!isRetailSingleStore && !hasLiteAbility && marketingTypeDesc) {
    textArr.push(marketingTypeDesc);
  }
  if (orderTypeDesc) {
    textArr.push(orderTypeDesc);
  }
  if (orderSource) {
    textArr.push(orderSource);
  }
  return textArr.join('/');
};

/**
 * 渲染导购员信息
 *
 * @param saleName 销售导购
 * @param serviceSalesmanName 专属导购
 */
const getSaleManText = (saleName: string, serviceSalesmanName: string): string => {
  const hasSaleMan = !isEmpty(saleName);
  const hasServiceSaleMan = !isEmpty(serviceSalesmanName);
  const saleManText = `销售导购员：${saleName}`;
  const serviceSaleManText = `专属导购员：${serviceSalesmanName}`;

  if (hasSaleMan && hasServiceSaleMan) return `${saleManText} ${serviceSaleManText}`;
  if (hasSaleMan) return saleManText;
  if (hasServiceSaleMan) return serviceSaleManText;
  return '';
};

const renderSaleManInfo = (saleName: string, serviceSalesmanName: string): JSX.Element | null => {
  const text = getSaleManText(saleName, serviceSalesmanName);
  if (text.length === 0) return null;
  return <div className="basic-item">{text}</div>;
};

const { useMemo } = React;

const BasicInfo = (
  props: Partial<{
    refundOrderInfo: RefundOrderInfo;
    displayType: keyof typeof basicInfoTypeMap;
    displayNo: string;
    displayTime: string;
    isFulfillOrder: boolean;
    isOfflineOrder: (orderInfo?: IOrderInfo) => boolean;
    paymentInfo: IPaymentInfo;
    isOnlineOrder: () => boolean;
  }> &
    IOrderInfo
) => {
  const {
    mainOrderInfo = {},
    fulfillOrder = {} as IFulfillOrder,
    paymentInfo = {} as IPaymentInfo,
    refundOrderInfo = {},
    displayType,
    displayNo,
    displayTime,
    isFulfillOrder,
    sourceInfo = { orderSource: undefined },
    goodsSupplierInfo,
    exchangeInfo = {} as IOrderExchangeInfo
  } = props;
  const orderInfo: Partial<IOrderInfo & IMainOrderInfo> = isEmpty(mainOrderInfo)
    ? refundOrderInfo
    : mainOrderInfo;
  const offlineOrderNo = props.isOfflineOrder!() ? orderInfo.outBizNo : '';
  /** 获取餐道的外部订单编号，字段隐藏太深了，都怪潘海涛不肯改 */
  const extraInfo = JSON.parse(orderInfo?.extraInfo || '{}');
  const orderExtraInfo = extraInfo?.EXTERNAL_ORDER_EXTRA_INFO;
  const businessSettlementDay = JSON.parse(
    extraInfo?.BUSINESS_SETTLEMENT || '{}'
  )?.businessSettlementDay;
  const outOrderNo = JSON.parse(orderExtraInfo || '{}')?.outOrderNo;
  const { outerTransactionNumberList = [], innerTransactionNumberList = [] } = paymentInfo;
  const hasFulfillNos = Boolean(get(orderInfo, 'fulfillNos', []).length);
  const prescriptionNo = orderInfo.prescriptionNo || '';
  const tagsInfo = deepJsonParse(orderInfo?.tagsInfo || '{}');
  const showSupplier = tagsInfo?.IS_FENXIAO_ORDER && goodsSupplierInfo;

  const isExchangeItem = typeof exchangeInfo?.exchangeNo !== 'undefined';
  const sourceOrderNo = exchangeInfo?.sourceOrderNo;
  let localDisplayType = displayType;
  if (isExchangeItem) {
    localDisplayType = 'exchangeOrder';
  }

  // 判断显示更多
  const isShowMore = useMemo(() => {
    return (
      outerTransactionNumberList.length > 0 ||
      innerTransactionNumberList.length > 0 ||
      offlineOrderNo ||
      hasFulfillNos
    );
  }, [outerTransactionNumberList, innerTransactionNumberList, offlineOrderNo, hasFulfillNos]);

  const moreContent = (
    <div className={style['pop-content']}>
      {prescriptionNo && (
        <p>
          {`处方单号：${prescriptionNo} `}
          <BlankLink
            href={setUrlDomain(
              `/v2/order/prescription#/detail/${orderInfo.kdtId}/${prescriptionNo}`,
              'store'
            )}
          >
            详情
          </BlankLink>
        </p>
      )}
      {offlineOrderNo && <p>{`离线订单编号：${offlineOrderNo}`}</p>}
      {isCandaoOrder(props) && <p>{`餐道系统订单号：${outOrderNo}`}</p>}
      {isGYYOrder(props) && <p>{`管易订单号：${outOrderNo}`}</p>}
      {outerTransactionNumberList[0] && <p>{`外部订单编号：${outerTransactionNumberList[0]}`}</p>}
      {innerTransactionNumberList[0] && <p>{`支付流水号：${innerTransactionNumberList[0]}`}</p>}
      {hasFulfillNos && (
        <div>
          {orderInfo.fulfillNos!.map((no, index) => (
            <div className="label-item" key={no}>
              <span>发货单号{orderInfo.fulfillNos!.length === 1 ? '' : index + 1}：</span>
              <p key={no}>{no}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const { numLabel, timeLabel } = basicInfoTypeMap[localDisplayType!];
  const isPreBuy = +get(orderInfo, 'extra.iS_STORE_PRESALE_ORDER', 0) === 1;

  return (
    <div className={cx(style['detail-basic-info'], 'info-block')}>
      {isFulfillOrder && <div className="basic-item">{`发货单号： ${fulfillOrder.fulfillNo}`}</div>}
      <div className="basic-item">
        {isFulfillOrder && (isHqStore || isPartnerStore) ? (
          <span>
            {isExchangeItem ? '换货单号' : '订单编号：'}
            <BlankLink href={`/v2/order/orderdetail#/?order_no=${displayNo}`}>
              {displayNo}
            </BlankLink>
          </span>
        ) : (
          `${numLabel}：${displayNo}`
        )}
        {sourceOrderNo && (
          <span className="source-no">
            原订单号：
            <BlankLink href={`/v2/order/orderdetail#/?order_no=${sourceOrderNo}`}>
              {sourceOrderNo}
            </BlankLink>
          </span>
        )}
        {isShowMore && (
          <Pop trigger="click" position="bottom-left" content={moreContent}>
            <span className="link-blue-color">更多</span>
          </Pop>
        )}
      </div>
      <div className="basic-item">{`${timeLabel}： ${displayTime}`}</div>
      {businessSettlementDay ? (
        <div className="basic-item">归属营业日：{businessSettlementDay}</div>
      ) : null}
      {!isFulfillOrder && !isRetailMinimalistShop && (
        <div className="basic-item">
          <i
            className={cx('point', {
              online: props.isOnlineOrder!() || orderInfo.saleWay === 0
            })}
          />
          {orderInfo.saleWayDesc}
        </div>
      )}
      {(orderInfo.storeName || ((isHqStore || isPartnerStore) && orderInfo.shopName)) && (
        <div className="basic-item">
          {orderInfo.storeName ||
            (isHqStore
              ? orderInfo.saleWayShopName || orderInfo.shopName // https://jira.qima-inc.com/browse/ONLINE-682754
              : orderInfo.shopName)}
        </div>
      )}
      {renderSaleManInfo(orderInfo.saleName!, orderInfo.serviceSalesmanName!)}
      {isPreBuy ? (
        <span>门店预定</span>
      ) : (
        <span className="basic-item">
          {showTypeSource(
            orderInfo.orderTypeDesc,
            sourceInfo.orderSource,
            orderInfo.marketingTypeDesc
          )}
        </span>
      )}
      <span>{orderInfo.refundPhaseAndTypeDesc}</span>
      {/* 扫码点单订单展示桌号 */}
      {!isNil(orderInfo.tableNum) && orderInfo.tableNum !== '' && (
        <div className="basic-item">{`桌号：${orderInfo.tableNum}`}</div>
      )}
      {/* 扫码点单订单展示人数 */}
      {!isNil(extraInfo.DINER_NUM) && extraInfo.DINER_NUM !== '' && (
        <div className="basic-item">{`人数：${extraInfo.DINER_NUM}`}</div>
      )}
      {/* 供货商名称 */}
      {showSupplier && <SupplierName supplierInfo={goodsSupplierInfo} />}
    </div>
  );
};

BasicInfo.defaultProps = {
  mainOrderInfo: {},
  paymentInfo: {}
};

const WrapperBasicInfo = withOrderHelper(props => {
  const displayNo = get(props, 'mainOrderInfo.orderNo');
  const displayTime = formatDatetime(get(props, 'mainOrderInfo.createTime'));
  return (
    // eslint-disable-next-line react/jsx-props-no-spreading
    <BasicInfo displayType="order" displayNo={displayNo} displayTime={displayTime} {...props} />
  );
});

const WrapperRefundBasicInfo = withOrderHelper(props => {
  const displayNo = get(props, 'refundOrderInfo.refundId');
  const displayTime = formatDatetime(get(props, 'refundOrderInfo.createTimeStamp'));
  return (
    // eslint-disable-next-line react/jsx-props-no-spreading
    <BasicInfo displayNo={displayNo} displayTime={displayTime} displayType="refund" {...props} />
  );
});

// 订单详情页使用
export const OrderBasicInfo = createOrderCpm([
  'mainOrderInfo',
  'fulfillOrder',
  'paymentInfo',
  'sourceInfo',
  'goodsSupplierInfo',
  'exchangeInfo'
])(WrapperBasicInfo);

// 退款详情页使用
export const RefundBasicInfo = createOrderCpm(['refundOrderInfo'])(WrapperRefundBasicInfo);
