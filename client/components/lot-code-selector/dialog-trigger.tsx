import * as React from 'react';
import { useEffect, useState, cloneElement } from 'react';
import cx from 'classnames';
import { Button, Dialog, IDialogProps, Pop } from 'zent';
import { formatLotCodeSelectorValue } from './utils';
import styles from './styles.scss';
import { LotCodeSelector, LotCodeSelectorProps, LotCodeSelectorValue } from '.';

export type LotCodeSelectorDialogTriggerProps = Omit<IDialogProps, 'footer' | 'title' | 'visible'> &
  Omit<LotCodeSelectorProps, 'className'> & {
    children: React.ReactElement;
    disabled: boolean;
  };

const DefaultState = { selectedLotCodes: [], selectedLotCodeNumMap: {} };

export function LotCodeSelectorDialogTrigger({
  value,
  goodsId,
  skuId,
  orderNo,
  orderKdtId,
  orderItemId,
  children,
  onChange,
  ...props
}: LotCodeSelectorDialogTriggerProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [innerValue, setInnerValue] = useState<LotCodeSelectorValue>(value || DefaultState);

  const { max } = props;

  const totalNum = innerValue.selectedLotCodes.reduce(
    (total, lotCode) => total + innerValue.selectedLotCodeNumMap[lotCode] || 0,
    0
  );

  const isMaxExceeded = max !== undefined ? totalNum > max : false;

  useEffect(() => {
    if (isVisible) {
      // 重置状态
      setInnerValue(value || DefaultState);
    }
  }, [value, isVisible]);

  const confirmButton = (
    <Button
      type="primary"
      disabled={isMaxExceeded || totalNum === 0}
      onClick={() => {
        onChange(formatLotCodeSelectorValue(innerValue));
        setIsVisible(false);
      }}
    >
      确定
    </Button>
  );

  return (
    <>
      {cloneElement(children, {
        onClick(e: MouseEvent) {
          if (!props.disabled) {
            setIsVisible(true);
          }
          children.props.onClick?.(e);
        }
      })}
      <Dialog
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...props}
        className={cx(styles['lot-code-selector-dialog'], props.className)}
        footer={
          <>
            <Button onClick={() => setIsVisible(false)}>取消</Button>
            {isMaxExceeded ? (
              <Pop trigger="hover" content={`发货数量超过上限 “${max}“`}>
                {confirmButton}
              </Pop>
            ) : (
              confirmButton
            )}
          </>
        }
        title="选择批号"
        visible={isVisible}
        onClose={e => {
          setIsVisible(false);
          props.onClose?.(e);
        }}
      >
        <>
          <div className={styles['lot-code-selector-info']}>
            已选择 {totalNum}
            {max ? `/${max}` : ''} 个商品
          </div>
          <LotCodeSelector
            className={styles['lot-code-selector']}
            goodsId={goodsId}
            skuId={skuId}
            orderNo={orderNo}
            orderKdtId={orderKdtId}
            orderItemId={orderItemId}
            value={innerValue}
            onChange={setInnerValue}
          />
        </>
      </Dialog>
    </>
  );
}
