import * as React from 'react';
import { get } from 'lodash';
import { CloudSlot } from '@youzan/ranta-cloud-react';
import { TableColumnConfig } from '@youzan-cloud/cloud-biz-types';

import { IOrderListConfigSource, IOrderListGoodsListConfig } from 'definition/order-list-config';
import { IOrderInfo } from 'definition/order-info';
import { checkIsElemeOrderByChannelType, checkIsMeituanPlatformOrder } from 'common/biz-helper';
import GoodsList from './goods-list';
import Operation from './operation';
import PackInfo from './pack-info';
import ReceiverInfo from './receiver-info';
import ExpressType from './express-type';
import OrderState from './order-state';
import FulfillStatus from './fulfill-status';
import RealPay from './realpay';

export interface IItemBodyProps {
  config: Partial<IOrderListConfigSource>;
  // config: Omit<IOrderListConfig, 'goodsList'> & { goodsList: IOrderListGoodsListConfig };
  data: IOrderInfo;
  cloudColumns?: TableColumnConfig[];
  reload: () => void;
}

export default function ItemBody(props: IItemBodyProps): JSX.Element {
  const { config, data, reload, cloudColumns = [] } = props;
  const {
    encryptStr,
    itemInfo,
    orderExpressInfo,
    mainOrderInfo,
    fulfillOrder,
    orderAddressInfo,
    buyerInfo,
    paymentInfo
  } = data;

  const { orderNo, extra } = mainOrderInfo;
  const { customTags } = extra || {};

  return (
    <div className="order-item__body">
      {config.goodsList && (
        <GoodsList
          orderInfo={data}
          config={config.goodsList as IOrderListGoodsListConfig}
          isFulfillOrder={!!config.isFulfillOrder}
          reload={reload}
          orderConfig={config}
        />
      )}
      {config.packInfo && (
        <PackInfo
          itemInfo={itemInfo}
          orderExpressInfo={orderExpressInfo}
          mainOrderInfo={mainOrderInfo}
        />
      )}
      {config.receiverInfo && (
        <ReceiverInfo
          encryptStr={encryptStr}
          orderAddressInfo={orderAddressInfo}
          buyerInfo={buyerInfo}
          mainOrderInfo={mainOrderInfo}
          withBuyer={get(config, 'receiverInfo.withBuyer')}
          widthAddressDetail={get(config, 'receiverInfo.widthAddressDetail')}
          isFulfillOrder={!!config.isFulfillOrder}
          isMeituanOrder={checkIsMeituanPlatformOrder(mainOrderInfo.channelType)}
          isElemeOrder={checkIsElemeOrderByChannelType(mainOrderInfo.channelType)}
        />
      )}
      {config.expressType && <ExpressType mainOrderInfo={mainOrderInfo} />}
      {config.fulfillStatus && <FulfillStatus fulfillOrder={fulfillOrder} />}
      {config.realpay && (
        <RealPay
          mainOrderInfo={mainOrderInfo}
          paymentInfo={paymentInfo}
          isHeader={buyerInfo.groupIsHeader}
        />
      )}
      {config.orderState && <OrderState mainOrderInfo={mainOrderInfo} />}
      {cloudColumns?.map((column, index) => (
        <div key={column.name} className="cell cell--cloud-column">
          <CloudSlot
            cloudKey="table-column"
            index={index}
            name={column.name}
            data={{ orderNo, customTags }}
          />
        </div>
      ))}
      {/* 下面这个 props spreading 可能不改更好, 也改不动 */}
      {/* eslint-disable-next-line react/jsx-props-no-spreading */}
      {config.operation && <Operation {...data} reload={reload} />}
    </div>
  );
}
