import * as React from 'react';
import { noop, partition } from 'lodash';
import { isRetailSingleStore } from '@youzan/utils-shop';
import { useCloudHook } from '@youzan/ranta-cloud-react';
import { OrderManageListBeforeTableOperateColumnBtnClick } from '@youzan-cloud/cloud-biz-types';

import { OptComponent } from 'components/opt-components';
import {
  PRINT_PICKING_TICKET,
  SELF_FETCH_REMIND,
  SELF_FETCH_REMIND_REPEAT
} from 'components/opt-components/constant';
import {
  IOperations,
  IOrderInfo,
  OrderManageListTableOperateColumnBtnEnum
} from 'definition/order-info';

export interface IOperationCpnProps extends IOrderInfo {
  operations: IOperations;
  reload: () => void;
}

/*
 * 操作按钮组件
 * props : operations, callbacks
 *
 */
const Operation = (props: IOperationCpnProps) => {
  /**
   * beforeTableOperateColumnBtnClick
   * @desc 表格操作列按钮点击前触发
   */
  const invokeBeforeTableOperateColumnBtnClick =
    useCloudHook<OrderManageListBeforeTableOperateColumnBtnClick>(
      'beforeTableOperateColumnBtnClick',
      { allowMultiple: true }
    );

  const beforeOperateClick = (type: OrderManageListTableOperateColumnBtnEnum) => {
    return new Promise(resolve => {
      // @ts-ignore
      invokeBeforeTableOperateColumnBtnClick({ type })
        .then(() => resolve(true))
        .catch(() => resolve(false));
    });
  };

  const { operations = [], reload, ...orderInfo } = props;

  // 下面这个代码, sort 的用法很诡异, 历史代码了, 不敢改, 看不懂不要喷我. @zhengzixiao
  const optsSortByType = operations.sort(item => +(item.type.indexOf('link') > -1));

  const { orderType, isPeriodBuy } = orderInfo.mainOrderInfo;

  const useOldDelivery = !isRetailSingleStore && (isPeriodBuy || orderType === undefined);

  if (!Array.isArray(optsSortByType) || optsSortByType.length <= 0) {
    return <div className="cell cell--opts" />;
  }

  // NOTE：pickedOpts 要求进行特殊的样式布局，所以进行这样的处理
  const [pickedOpts, otherOpts] = partition(optsSortByType, ({ code }) =>
    [PRINT_PICKING_TICKET, SELF_FETCH_REMIND, SELF_FETCH_REMIND_REPEAT].includes(code)
  );

  return (
    <div className="cell cell--opts">
      {otherOpts.map(opt => {
        return (
          <div key={opt.code} className="cell--opts__item">
            <OptComponent
              operation={{
                ...opt,
                outline: true
              }}
              options={{
                reload,
                useOldDelivery,
                orderInfo,
                beforeOperateClick
              }}
            />
          </div>
        );
      })}
      {pickedOpts.length > 0 && (
        <div className="cell--opts__item">
          {pickedOpts.map(opt => (
            <div key={opt.code}>
              <OptComponent
                operation={{
                  ...opt,
                  outline: true
                }}
                options={{
                  reload,
                  orderInfo,
                  beforeOperateClick
                }}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

Operation.defaultProps = {
  operations: [],
  mainOrderInfo: {},
  reload: noop
};

export default Operation;
