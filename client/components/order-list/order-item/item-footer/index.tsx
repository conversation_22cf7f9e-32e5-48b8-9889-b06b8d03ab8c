import * as React from 'react';
import { compact } from 'lodash';
import { Icon, Pop } from 'zent';
import { SuperButton } from '@youzan/react-components';
import { IBuyerInvoiceInfo, IRemarkInfo, IItemInfo } from 'definition/order-info';
import { IOrderListConfigSource } from 'definition/order-list-config';
import { UserPrivacyViewButton, useUserPrivacyData, styleUtils } from 'components/user-privacy';
import { MultiChannelProductManageAbilityValidOrExpired } from 'common/constant';
import {
  isChainStore,
  checkAbilityValid,
  checkAbilityExpired,
  ShopAbility
} from '@youzan/utils-shop';
import { getProcessErrorHandleUrl } from '../item-body/goods-item';
import { ExternalChannelErrorContext } from '../../context';

export interface IItemFooterRemarkInfo extends Partial<IRemarkInfo> {
  deliveryMemo: string;
}

interface IItemFooterProps {
  encryptStr?: string;
  buyerInvoiceInfo: IBuyerInvoiceInfo;
  remarkInfo: IItemFooterRemarkInfo;
  itemInfo?: IItemInfo;
  config?: Partial<IOrderListConfigSource>;
  founderToMemberMark?: boolean;
  includeMockItem?: boolean;
}

/**
 * 显示订单备注信息和同城配送第三方配送信息
 *  props: {
 *  orderExpressInfo，
 *  remarkInfo
 * }
 * @param {any} props
 * @returns
 */
const ItemFooter = (props: IItemFooterProps) => {
  const {
    encryptStr,
    remarkInfo,
    itemInfo = [],
    config = {},
    includeMockItem = false,
    founderToMemberMark = ''
  } = props;
  const {
    data: { invoiceTitle, receivingMailbox = '-', taxId = '-' },
    decrypt
  } = useUserPrivacyData({
    encryptKey: encryptStr,
    data: props.buyerInvoiceInfo,
    mapDecryptedData: data => (data as { buyerInvoiceInfo: IBuyerInvoiceInfo }).buyerInvoiceInfo
  });

  const { sellerMemoDesc, deliveryMemo, buyerMemoDesc } = (remarkInfo ??
    {}) as IItemFooterRemarkInfo;
  const showRemark = !!(
    compact([buyerMemoDesc, sellerMemoDesc, founderToMemberMark, deliveryMemo]).length > 0 ||
    invoiceTitle
  );

  const { canShow: canShowChannelError, onCanShowChange } = React.useContext(
    ExternalChannelErrorContext
  );

  const handleCloseExternalChannelBindError = () => {
    onCanShowChange && onCanShowChange(false);
  };

  /**
   * 多渠道商品管理能力是否可用或过期
   */
  const isMultiChannelProductManageAbilityValidOrExpired =
    checkAbilityValid(ShopAbility.MultiChannelProductManageAbility) ||
    checkAbilityExpired(ShopAbility.MultiChannelProductManageAbility);

  /**
   * 是否显示渠道商品
   */
  const isShowChannelProduct = isChainStore && isMultiChannelProductManageAbilityValidOrExpired;

  return (
    <div>
      {showRemark && (
        <div
          className="order-item__footer remark-info"
          style={invoiceTitle ? { backgroundColor: '#FFF5ED' } : {}}
        >
          {buyerMemoDesc && (
            <p>
              <span>买家留言：</span>
              <span className="remark-info__memo">{buyerMemoDesc}</span>
            </p>
          )}
          {sellerMemoDesc && (
            <p>
              <span>卖家备注：</span>
              <span className="remark-info__memo">{sellerMemoDesc}</span>
            </p>
          )}
          {founderToMemberMark && (
            <p>
              <span>团长备注：</span>
              <span className="remark-info__memo">{founderToMemberMark}</span>
            </p>
          )}
          {deliveryMemo && (
            <p>
              <span>发货单备注：</span>
              <span className="remark-info__memo">{deliveryMemo}</span>
            </p>
          )}
          {invoiceTitle && (
            <p style={{ color: '#ed6a0c' }}>
              <span>{`申请开票：发票抬头：${invoiceTitle}， 企业税号：${taxId}， 收票邮箱：${receivingMailbox}`}</span>
              <UserPrivacyViewButton className={styleUtils.privacyButtonGap} onDecrypt={decrypt} />
            </p>
          )}
        </div>
      )}
      {config.showProcessOperationError && (
        <div>
          {itemInfo.map(v => {
            const { processOperationErrorInfo } = v;
            if (!(processOperationErrorInfo && processOperationErrorInfo.errorMessage)) return null;

            const { errorCode, errorMessage, paramJson } = processOperationErrorInfo;
            const url = getProcessErrorHandleUrl(errorCode, paramJson);

            return (
              <div className="order-item__footer process-operation-error-info">
                <Icon type="warning" />
                <span>{errorMessage}</span>
                <SuperButton mode="link" isBlank href={url} style={{ fontSize: 12 }}>
                  前往处理
                </SuperButton>
              </div>
            );
          })}
        </div>
      )}
      {config.itemFooter?.showExternalChannelBindError && includeMockItem && canShowChannelError && (
        <div className="order-item__footer channel-error-info">
          <Icon type="warning" />
          <span>
            {`该订单有部分商品，未做关联绑定，请前往“商品-${
              isShowChannelProduct ? '渠道商品' : '外卖通商品'
            }”做关联绑定`}
          </span>
          <Pop
            trigger="click"
            content="关闭之后，后续有类似问题的订单，也将不会再进行提示"
            position="auto-top-left"
            onConfirm={handleCloseExternalChannelBindError}
          >
            <Icon type="close" />
          </Pop>
        </div>
      )}
    </div>
  );
};

export default ItemFooter;
