import React, { Component, PureComponent } from 'react';
import classnames from 'classnames';
import assign from 'lodash/assign';
import { Checkbox } from 'zent';

class DetailType extends (PureComponent || Component) {
  handleCheckboxChange = (e, type) => {
    const newValue = assign({}, this.props.value);
    newValue[type] = e.target.checked;
    if (type === 'is_invoice' && !newValue.is_invoice) {
      newValue.is_invoice_default = false;
    }
    if (type === 'is_return' && !newValue.is_return) {
      newValue.is_return_default = false;
    }
    this.props.onChange(newValue);
  };

  render() {
    const showError = this.props.isTouched && this.props.error;
    const typeClassName = classnames({
      'zent-form__control-group ': true,
      'zent-form__type-wrap': true,
      'show-type': this.props.showType,
      'has-error': showError
    });
    const returnTypeClassName = classnames({
      'return-location': true,
      show: this.props.value.is_return
    });
    const invoiceTypeClassName = classnames({
      'invoice-location': true,
      show: this.props.value.is_invoice
    });

    return (
      <div className={typeClassName}>
        <label className="zent-form__control-label">地址类型：</label>
        <div className="zent-form__controls" style={{ paddingTop: 1 }}>
          <Checkbox
            style={{ marginTop: 6 }}
            checked={this.props.value.is_return}
            disabled={this.props.disableReturnDefault}
            onChange={e => this.handleCheckboxChange(e, 'is_return')}
          >
            退货地址
          </Checkbox>
          <Checkbox
            className={returnTypeClassName}
            checked={this.props.value.is_return_default}
            disabled={this.props.disableReturnDefault}
            onChange={e => this.handleCheckboxChange(e, 'is_return_default')}
          >
            设为默认退货地址
          </Checkbox>
          <div>
            <Checkbox
              checked={this.props.value.is_invoice}
              onChange={e => this.handleCheckboxChange(e, 'is_invoice')}
            >
              收票地址
            </Checkbox>
            <Checkbox
              className={invoiceTypeClassName}
              checked={this.props.value.is_invoice_default}
              onChange={e => this.handleCheckboxChange(e, 'is_invoice_default')}
            >
              设为默认收票地址
            </Checkbox>
          </div>
          {showError && <p className="zent-form__help-block">{this.props.error}</p>}
        </div>
      </div>
    );
  }
}

export default DetailType;
