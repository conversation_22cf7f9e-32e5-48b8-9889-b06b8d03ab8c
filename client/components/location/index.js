import React, { Component, PureComponent } from 'react';
import { But<PERSON>, Notify } from 'zent';
import { Form as ZentForm, Select } from '@zent/compat';
import { RegionSelect } from '@youzan/react-components';
import { assign, cloneDeep } from 'lodash';
import cx from 'classnames';
import ContactPhone from './contact-phone';
import DetailType from './detail-type';
import * as Actions from './api';

import './style.scss';

const { createForm, getControlGroup, Form, Field, InputField } = ZentForm;
const { SelectTrigger } = Select;

const renderAddress = getControlGroup(props => (
  <div>
    <Select
      value={props.region_type}
      data={props.region_type_list}
      optionValue="type"
      optionText="value"
      trigger={SelectTrigger}
    />
    <RegionSelect
      value={props.value}
      onChange={data => {
        props.onChange(data.county_id);
        props.onRegionSet(data);
      }}
    />
  </div>
));

class NewLocation extends (PureComponent || Component) {
  constructor(props) {
    super(props);
    this.state = {
      contact_name: '',
      contry_index: 0,
      country_code_list: [],
      mobile: '',
      region_type: '',
      region_type_list: [],
      region_id: '',
      address: '',
      is_return: false,
      is_invoice: false,
      is_return_default: false,
      is_invoice_default: false,
      disableReturnDefault: false,
      locationid: props.locationid || 0,
      showType: !!this.props.showType,
      province: '',
      city: '',
      area: ''
    };
  }

  static defaultProps = {
    submitText: '同意退货，发送退货地址',
    className: '',
    title: ''
  };

  componentDidMount() {
    this.initAddressList();
  }

  initAddressList() {
    const { locationid } = this.state;
    const param = {
      id: locationid
    };

    Actions.fetchShopAddressList(param)
      .then(data => {
        data.is_return = !!+data.is_return;
        data.is_invoice = !!+data.is_invoice;
        data.is_return_default = !!+data.is_return_default;
        data.is_invoice_default = !!+data.is_invoice_default;
        data.disableReturnDefault = !!+data.is_return_default;
        this.setState(data);
      })
      .catch(err => {
        Notify.error(err.msg);
      });
  }

  // 提交保存地址数据
  saveinfo = data => {
    const param = cloneDeep(data);
    // 这里就是退货，is_return就是1，不是发票地址
    param.is_return = '1';
    param.is_invoice = '0';
    param.is_return_default = '1';
    param.is_invoice_default = '0';
    this.props.beforeSubmit && this.props.beforeSubmit();
    Actions.saveShopAddress(param)
      .then(() => {
        Notify.success('保存成功');
      })
      .catch(err => {
        Notify.error(err.msg);
      })
      .finally(() => {
        this.props.callback();
      });
  };

  validMobile = (value = {}) => {
    const countryIndex = +value.areacode || 0;
    const mobile = value.mobile !== '' ? +value.mobile : '';
    const normal = /^(0|86|17951)?(1)[0-9]{10}$/;
    const mobileReg = /^\d{1,10}$/;
    let res = true;
    if (!countryIndex) {
      res = normal.test(mobile);
    } else {
      res = mobileReg.test(mobile);
    }
    return res;
  };

  submit = values => {
    const { contactPhone, detailType } = values;
    delete values.contactPhone;
    delete values.detailType;

    if (!contactPhone.areacode) {
      contactPhone.areacode = 0;
    }
    if (!this.validMobile(contactPhone)) {
      Notify.error('请输入正确的手机号');
      return;
    }
    const data = assign(
      {
        id: this.state.locationid,
        province: this.state.province,
        city: this.state.city,
        area: this.state.area,
        region_type: 'china',
        country_index: contactPhone.areacode
      },
      contactPhone,
      detailType,
      values
    );

    if (this.props.handleSave) {
      this.props.handleSave(data);
    } else {
      this.saveinfo(data);
    }
  };

  filterHandler = (item, keyword) =>
    keyword && item.text.trim().toLowerCase().indexOf(keyword.trim().toLowerCase()) > -1;

  handleRegionSet = data => {
    this.setState({
      province: data.province,
      city: data.city,
      area: data.area
    });
  };

  render() {
    const { className } = this.props;

    return (
      <Form className={cx('new-location', className)} horizontal>
        {this.props.title && <h3>{this.props.title}</h3>}
        <Field
          name="contact_name"
          type="text"
          label="联系人："
          value={this.state.contact_name}
          placeholder="请填写联系人姓名"
          component={InputField}
          validations={{
            required: true,
            validName: (values, value) => value.length < 20
          }}
          validationErrors={{
            required: '联系人不能为空',
            validName: '请删减姓名长度~'
          }}
        />
        <Field
          name="contactPhone"
          value={{
            areacode: this.state.country_index,
            mobile: this.state.mobile
          }}
          areadata={this.state.country_code_list}
          filterHandler={this.filterHandler}
          component={ContactPhone}
        />
        <Field
          name="region_id"
          label="联系地址："
          region_type={this.state.region_type}
          region_type_list={this.state.region_type_list}
          value={this.state.region_id}
          onRegionSet={this.handleRegionSet}
          component={renderAddress}
          validations={{
            required: true,
            validRegion: (values, value) => !!value
          }}
          validationErrors={{
            required: '请选择地址~',
            validRegion: '请选择完整地址信息~'
          }}
        />
        <Field
          name="address"
          type="text"
          label="详细地址："
          value={this.state.address}
          placeholder="请填写详细地址，如街道名称，门牌号等信息"
          component={InputField}
          validations={{
            required: true,
            validAddress: (values, value) => value.length < 150
          }}
          validationErrors={{
            required: '请填写地址',
            validAddress: '详细地址太长，请删减~'
          }}
        />
        {this.props.showDetailType && (
          <Field
            name="detailType"
            value={{
              is_return: this.state.is_return,
              is_return_default: this.state.is_return_default,
              is_invoice: this.state.is_invoice,
              is_invoice_default: this.state.is_invoice_default
            }}
            showType={this.state.showType}
            disableReturnDefault={this.state.disableReturnDefault}
            component={DetailType}
            validations={{
              validReturn: (values, value) => value.is_invoice || value.is_return
            }}
            validationErrors={{
              validReturn: '请勾选地址类型'
            }}
          />
        )}
        <div className="submit-btn-content">
          <Button htmlType="button" type="primary" onClick={this.props.handleSubmit(this.submit)}>
            {this.props.submitText}
          </Button>
        </div>
      </Form>
    );
  }
}

export default createForm()(NewLocation);
