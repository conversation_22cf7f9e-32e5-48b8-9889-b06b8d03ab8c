/**
 * 模块内部ajax请求
 */
import { request } from '@youzan/retail-utils';

const urlMap = {
  fetchShopAddressListUrl: `${window._global.url.www}/setting/shopAddress/detail.json`,
  saveShopAddressUrl: `${window._global.url.www}/setting/shopAddress/detail.json`,
  addShopAddress: '/youzan.shop.address/3.0.0/create'
};

export function fetchShopAddressList(data) {
  return request({
    url: urlMap.fetchShopAddressListUrl,
    method: 'get',
    withCredentials: true,
    data
  });
}

export function saveShopAddress(data) {
  return request({
    url: urlMap.addShopAddress,
    method: 'post',
    withCredentials: true,
    data
  });
}
