import React, { Component, PureComponent } from 'react';
import classnames from 'classnames';
import { Input } from 'zent';
import { Select } from '@zent/compat';

const { SelectTrigger } = Select;

class ContactPhone extends (PureComponent || Component) {
  handleSelectChange = (e, selectedItem) => {
    this.props.onChange({
      ...this.props.value,
      areacode: selectedItem.index
    });
  };

  handlePhoneChange = e => {
    this.props.onChange({
      ...this.props.value,
      mobile: e.target.value.replace(/\s+/g, '')
    });
  };

  render() {
    return (
      <div
        className={classnames({
          'zent-form__control-group': true,
          'has-error': this.props.isTouched && this.props.error
        })}
      >
        <label className="zent-form__control-label">联系方式：</label>
        <div className="zent-form__controls">
          <Select
            className="areacode"
            value={this.props.value.areacode}
            data={this.props.areadata}
            filter={this.props.filterHandler}
            optionValue="index"
            optionText="value"
            trigger={SelectTrigger}
            onChange={this.handleSelectChange}
          />
          <Input
            className="input-short"
            value={this.props.value.mobile}
            onChange={this.handlePhoneChange}
            placeholder="请填写手机号"
          />
          {this.props.isTouched && this.props.error && (
            <p className="zent-form__error-desc">{this.props.error}</p>
          )}
        </div>
      </div>
    );
  }
}

export default ContactPhone;
