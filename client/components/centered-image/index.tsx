import * as React from 'react';
import style from './style.scss';

const CenteredImage = ({
  width = 60,
  height = 60,
  href,
  src,
  onClick = () => null
}: {
  width?: number;
  height?: number;
  href: string;
  src: string;
  onClick?: () => void;
}) => {
  const boxStyle: React.CSSProperties = {};
  const imgStyle: React.CSSProperties = {};

  if (width) {
    boxStyle.width = width;
    imgStyle.maxWidth = width;
    imgStyle.minWidth = width;
  }

  if (height) {
    boxStyle.height = height;
    imgStyle.maxHeight = height;
    imgStyle.minHeight = height;
  }

  return (
    <div className={style['centered-image']} style={boxStyle} onClick={onClick}>
      {href ? (
        <a href={href} target="_blank" rel="noopener noreferrer">
          <img src={src} alt="" style={imgStyle} />
        </a>
      ) : (
        <img src={src} alt="" style={imgStyle} />
      )}
    </div>
  );
};

export default CenteredImage;
