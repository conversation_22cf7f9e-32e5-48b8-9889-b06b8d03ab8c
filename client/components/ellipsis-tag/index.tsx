import * as React from 'react';
import cx from 'classnames';
import { ITagProps, Pop, Tag } from 'zent';

import * as styles from './index.scss';

export interface EllipsisTagProps extends ITagProps {
  onGetRefreshLayoutEffectDep?: () => unknown;
}

/**
 * 超出宽度显示省略号的 Tag
 */
function EllipsisTag(props: EllipsisTagProps) {
  const { onGetRefreshLayoutEffectDep, ...restProps } = props;
  const ref = React.useRef<HTMLDivElement | null>(null);
  const [isEllipsis, setIsEllipsis] = React.useState(false);

  const refreshLayout = () => {
    if (!ref.current) {
      return;
    }
    const dom = ref.current.firstElementChild;
    if (!dom) {
      return;
    }
    const actualWidth = dom.scrollWidth;
    const visibleWidth = dom.clientWidth;
    setIsEllipsis(actualWidth > visibleWidth);
  };

  const refreshLayoutDep = onGetRefreshLayoutEffectDep?.();

  React.useEffect(() => {
    refreshLayout();
  }, [refreshLayoutDep]);

  const tagElem = (
    <Tag
      // eslint-disable-next-line react/jsx-props-no-spreading
      {...restProps}
      ref={ref}
      className={cx(restProps.className, styles['ellipsis-tag'])}
    />
  );

  return isEllipsis ? (
    <Pop trigger="hover" content={restProps.children}>
      {tagElem}
    </Pop>
  ) : (
    tagElem
  );
}

export default EllipsisTag;
