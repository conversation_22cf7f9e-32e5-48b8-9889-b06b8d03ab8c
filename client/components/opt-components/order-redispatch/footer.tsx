import * as React from 'react';
import { Button, LayoutRow, Pop } from 'zent';

interface IFooterProps {
  confirmText?: string;
  loading: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

export function Footer({ loading, confirmText, onCancel, onConfirm }: IFooterProps) {
  return (
    <LayoutRow justify="end">
      <Button onClick={onCancel} disabled={loading}>
        取消
      </Button>
      {confirmText ? (
        <Pop
          trigger="click"
          style={{ width: 320 }}
          position="auto-top-right"
          content={confirmText}
          onConfirm={onConfirm}
        >
          <Button type="primary" loading={loading}>
            改派
          </Button>
        </Pop>
      ) : (
        <Button type="primary" loading={loading} onClick={onConfirm}>
          改派
        </Button>
      )}
    </LayoutRow>
  );
}
