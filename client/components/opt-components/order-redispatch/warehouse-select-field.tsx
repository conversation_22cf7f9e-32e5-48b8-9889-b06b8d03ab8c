import * as React from 'react';
import { FormSelectField, ISelectItem, Notify, Validators } from 'zent';

import { useBoolean, useDebouncedEffect } from '@youzan/react-hooks';

import { queryWarehouseList } from './api';

import { field } from './index.scss';

interface IWarehouseSelectFieldProps {
  orderNo: string;
}

export function WarehouseSelectField({ orderNo }: IWarehouseSelectFieldProps) {
  const [loading, { setTrue, setFalse }] = useBoolean(false);
  const [name, setName] = React.useState<string>('');
  const [keyword, setKeyword] = React.useState<string>('');
  const [options, setOptions] = React.useState<ISelectItem[]>([]);

  React.useEffect(() => {
    if (orderNo) {
      setTrue();
      queryWarehouseList({
        name,
        orderNo
      })
        .then(({ items }) => {
          setOptions(
            items.map(item => ({
              key: item.warehouseId,
              text: item.warehouseName
            }))
          );
        })
        .catch(err => {
          Notify.error(err.msg || '网络错误');
        })
        .finally(() => setFalse());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [name, orderNo]);

  useDebouncedEffect(() => {
    setName(keyword);
  }, 1000);

  const onKeywordChange = (value: string) => {
    setKeyword(value);
  };

  return (
    <FormSelectField
      className={field}
      required
      name="warehouseId"
      label="改派门店/仓："
      validators={[Validators.required('请选择改派门店/仓库')]}
      props={{
        placeholder: '请选择或输入搜索',
        width: 260,
        loading,
        options,
        keyword,
        onKeywordChange
      }}
    />
  );
}
