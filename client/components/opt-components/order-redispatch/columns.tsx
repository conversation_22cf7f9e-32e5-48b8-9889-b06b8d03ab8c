import * as React from 'react';

import { isNil } from 'lodash';
import { IGridColumn } from 'zent';
import { GoodsCell } from '@youzan/react-components';

import { error } from './index.scss';

export function getColumns() {
  const columns: IGridColumn[] = [
    {
      title: '商品',
      name: 'itemId',
      width: 350,
      bodyRender: item => (
        <GoodsCell
          name={item.title}
          photoUrl={item.imgUrl}
          skuNo={item.skuNo || item.goodsNo}
          brandName={item.brandName}
          specifications={item.skuDesc || item.specifications}
        />
      )
    },
    {
      title: '原发货方',
      name: 'warehouseName',
      width: 180
    },
    {
      title: '发货数量',
      name: 'deliveryNum',
      textAlign: 'center',
      width: 120
    },
    {
      title: '改派门店库存',
      name: 'warehouseSockNum',
      textAlign: 'center',
      width: 120,
      bodyRender: ({ warehouseSockNum, notSupportReason = [] }) => {
        if (isNil(warehouseSockNum)) {
          return '-';
        }
        if (notSupportReason?.length > 0) {
          return (
            <div className={error}>
              <p>{warehouseSockNum}</p>
              <p>
                {notSupportReason.map((item: string) => (
                  <span>{item}</span>
                ))}
              </p>
            </div>
          );
        }
        return warehouseSockNum;
      }
    }
  ];
  return columns;
}
