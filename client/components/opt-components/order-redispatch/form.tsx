import * as React from 'react';
import { find } from 'lodash';

import { Space } from '@youzan/react-components';
import { useBoolean } from '@youzan/react-hooks';
import { Form, FormStrategy, Grid, Notify } from 'zent';

import { Footer } from './footer';
import { getColumns } from './columns';
import { WarehouseSelectField } from './warehouse-select-field';

import { applyChangeDispatch, queryOrderDetail, queryGoodsStockInWarehouse } from './api';

import { grid } from './index.scss';

interface IFormBodyProps {
  orderNo: string;
  onCancel: () => void;
}

export function FormBody({ orderNo, onCancel }: IFormBodyProps) {
  const columns = getColumns();
  const originDatasets = React.useRef<any[]>([]);
  const [datasets, setDatasets] = React.useState<any[]>([]);
  const [warnTips, setWarnTips] = React.useState<string>('');
  const [loading, { setTrue, setFalse }] = useBoolean();

  const form = Form.useForm(FormStrategy.View);

  const handleSubmit = React.useCallback(form => {
    const { warehouseId } = form.getValue();
    applyChangeDispatch({
      orderNo,
      warehouseId: warehouseId.key
    })
      .then(() => {
        Notify.success('改派成功');
        onCancel();
      })
      .catch(err => Notify.error(err.msg || '改派失败'));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    setTrue();
    queryOrderDetail({
      orderNo
    })
      .then(({ itemInfos = [] }) => {
        setDatasets(itemInfos);
        originDatasets.current = itemInfos;
      })
      .catch(err => Notify.error(err.msg || '查询订单信息失败'))
      .finally(() => setFalse());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderNo]);

  React.useEffect(() => {
    const warehouseField = form.model.get('warehouseId');
    const $ = warehouseField?.value$.subscribe(value => {
      const warehouseId = value?.key;
      if (warehouseId) {
        setTrue();
        queryGoodsStockInWarehouse({
          orderNo,
          warehouseId
        })
          .then(({ itemInfos = [], ...rest }) => {
            const copy = originDatasets?.current?.map(item => ({
              ...item,
              ...find(itemInfos, { orderItemIdStr: item.orderItemIdStr })
            }));
            setDatasets(copy);
            setWarnTips(rest.warnTips);
          })
          .catch(err => Notify.error(err.msg || '查询改派门店/仓信息失败'))
          .finally(() => setFalse());
      }
    });
    return () => $?.unsubscribe();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form]);

  return (
    <Form form={form} layout="horizontal" onSubmit={handleSubmit}>
      <Space direction="vertical">
        <WarehouseSelectField orderNo={orderNo} />
        <Grid
          className={grid}
          datasets={datasets}
          columns={columns}
          rowKey="orderItemIdStr"
          loading={loading}
        />
        <Footer
          confirmText={warnTips}
          loading={form.isSubmitting}
          onCancel={onCancel}
          onConfirm={() => form.submit()}
        />
      </Space>
    </Form>
  );
}
