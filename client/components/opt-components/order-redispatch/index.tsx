import * as React from 'react';

import { Dialog } from 'zent';
import { DialogProvider } from '@youzan/retail-components';

import { IOperation, IFulfillOrder } from 'definition/order-info';

import { FormBody } from './form';

import typeMap from '../type-map';

interface IChangeDispatchProps {
  orderNo: string;
  operation: IOperation;
  fulfillOrder: IFulfillOrder;
}

export function ChangeDispatch(props: IChangeDispatchProps) {
  const { orderNo, fulfillOrder, operation } = props;

  const Opt = typeMap[operation.type];

  return (
    <DialogProvider>
      {({ visible, hideDialog, showDialog }) => (
        <>
          <Dialog
            style={{
              width: '800px'
            }}
            title="整单改派"
            visible={visible}
            onClose={hideDialog}
          >
            <FormBody orderNo={orderNo} fulfillNo={fulfillOrder?.fulfillNo} onCancel={hideDialog} />
          </Dialog>
          <Opt onClick={showDialog} {...operation} />
        </>
      )}
    </DialogProvider>
  );
}

export default ChangeDispatch;
