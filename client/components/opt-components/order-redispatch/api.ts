import { request } from '@youzan/retail-utils';

/**
 * 获取整单改派仓库列表
 */
export interface IQueryWarehouseListParams {
  /** 搜索名称 */
  name?: string;
  /** 订单号 */
  orderNo: string;
  /** 分页页码 */
  pageNo?: number;
  /** 分页大小 */
  pageSize?: number;
}

/**
 * 获取整单改派仓库列表返回值
 */
export interface IQueryWarehouseListResp {
  items: {
    warehouseId: number;
    warehouseName: string;
  }[];
}

export function queryWarehouseList(
  data: IQueryWarehouseListParams
): Promise<IQueryWarehouseListResp> {
  return request({
    url: 'youzan.retail.redispatcher.order.search.warehouselist/1.0.0',
    data
  });
}

export interface IQueryOrderDetailParams {
  /** 订单号 */
  orderNo: string;
  /** 履约单号 */
  fulfillNo?: string;
}

export interface IQueryOrderDetailResp {
  itemInfos: {
    skuId: number;
    skuCode: string;
    goodsNo: string;
    title: string;
    unit: string;
    imgUrl: string;
    skuDesc: string;
    specifications: string;
    /** 订单商品唯一标识 */
    orderItemIdStr: string;
    /** 发货数量 */
    deliveryNum: number;
    /** 原发货方名称 */
    warehouseName: string;
  }[];
}

/**
 * 获取整单改派数据
 */
export function queryOrderDetail(data: IQueryOrderDetailParams): Promise<IQueryOrderDetailResp> {
  return request({
    url: 'youzan.retail.redispatcher.order.get.window/1.0.0',
    data
  });
}

export interface IQueryGoodsStockInWarehouseParams {
  orderNo: string;
  warehouseId: number;
  fulfillNo?: string;
}

export interface IQueryGoodsStockInWarehouseResp {
  warnTips: string;
  warehouseId: number;
  warehouseName: string;
  itemInfos: {
    orderItemIdStr: string;
    warehouseSockNum: number;
    notSupportReason?: string;
  }[];
}

/**
 * 查询商品在改派仓库库存信息
 */
export function queryGoodsStockInWarehouse(
  data: IQueryGoodsStockInWarehouseParams
): Promise<IQueryGoodsStockInWarehouseResp> {
  return request({
    url: 'youzan.retail.redispatcher.order.query.warehouse/1.0.0',
    data
  });
}

export interface IApplyChangeDispatchParams {
  orderNo: string;
  fulfillNo?: string;
  warehouseId: number;
}

/**
 * 确认改派
 */
export function applyChangeDispatch(data: IApplyChangeDispatchParams): Promise<boolean> {
  return request({
    url: 'youzan.retail.redispatcher.order.apply.change/1.0.0',
    data
  });
}
