import React from 'react';
import { Sweetalert } from 'zent';
import createOptComponent from '../create-opt-component';

@createOptComponent
class InvoiceIng extends React.Component {
  openDialog = config => {
    const { options } = config;
    Sweetalert.confirm({
      content: '电子发票开具中，请稍后重试',
      title: '电子发票开具中',
      onConfirm: options.reload,
      confirmText: '刷新'
    });
  };

  render() {
    const { OptTypeCpn } = this.props;
    return <OptTypeCpn onClick={this.openDialog} outline={false} />;
  }
}

export default InvoiceIng;
