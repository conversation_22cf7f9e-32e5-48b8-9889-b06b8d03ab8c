import React from 'react';
import { Dialog, But<PERSON>, Notify, Alert, Grid } from 'zent';
import styled from 'styled-components';
import { BlankLink, GoodsCell } from '@youzan/react-components';
import { global, div } from '@youzan/retail-utils';
import createOptComponent from '../create-opt-component';
import { confirm } from './api';

const { useState } = React;

const Wrapper = styled.div`
  padding: 20px 10px;
`;

const Footer = styled.footer`
  margin-top: 10px;
  text-align: right;
`;

const Items = styled.div`
  margin: 20px auto;
  text-align: center;
`;

const ItemAmount = styled.span`
  margin-left: 20px;
`;

const Notice = styled.p`
  color: #999;
  text-align: right;
`;

function getOversaleItems(items) {
  return items.filter(item => {
    let extra;

    try {
      extra = JSON.parse(item.extra);
    } catch (err) {
      extra = {};
    }

    // 订单的扩展字段里读是否超卖(值为 1)
    return +extra?.OVER_SALE_ORDER === 1;
  });
}

function ConfirmRestock(props) {
  const { operation = {}, options, OptTypeCpn } = props;
  const { text } = operation;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const doConfirm = () => {
    setLoading(true);
    confirm(options.orderInfo.mainOrderInfo.orderNo)
      .then(() => {
        Notify.success('确认补货成功');
        setVisible(false);
        options.reload && options.reload();
      })
      .catch(err => {
        Notify.error(err.msg);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const items = getOversaleItems(options.orderInfo.itemInfo);

  const columns = [
    {
      title: '商品',
      textAlign: 'left',
      bodyRender: data => {
        return (
          <GoodsCell
            name={
              <>
                {data.alias ? (
                  <BlankLink
                    href={`//shop${192168 + global.KDT_ID}.youzan.com/v2/showcase/goods?alias=${
                      data.alias
                    }`}
                  >
                    {data.title}
                  </BlankLink>
                ) : (
                  data.title
                )}
                {data.showSnapshot && data.goodsSnapUrl && (
                  <BlankLink href={data.goodsSnapUrl}>[商品交易快照]</BlankLink>
                )}
              </>
            }
            photoUrl={data.imgUrl}
            skuNo={data.skuCode || data.goodsNo}
            brandName={data.brandName}
            specifications={data.skuDesc}
            tags={data.renderTags}
          />
        );
      }
    },
    {
      title: '补货数量',
      textAlign: 'left',
      bodyRender: data => {
        return <div>{div(data.num, 1000)}件</div>;
      }
    }
  ];
  return (
    <>
      <Dialog
        visible={visible}
        title={text}
        onClose={() => setVisible(false)}
        style={{ width: '500px' }}
      >
        <Alert>
          超卖订单商家确认可补货后，买家端将显示付款成功待发货。补货商品无需在商品管理中编辑库存，需单独备货。
        </Alert>
        <Wrapper>
          <p style={{ marginBottom: '10px' }}>需要对以下商品进行补货：</p>
          <Grid rowKey="goodsId" columns={columns} datasets={items} />
        </Wrapper>
        <Footer>
          <Button type="primary" loading={loading} outline onClick={() => setVisible(false)}>
            取消
          </Button>
          <Button type="primary" loading={loading} onClick={doConfirm}>
            确认补货
          </Button>
        </Footer>
      </Dialog>
      <OptTypeCpn outline={false} onClick={() => setVisible(true)} />
    </>
  );
}

export default createOptComponent(ConfirmRestock);
