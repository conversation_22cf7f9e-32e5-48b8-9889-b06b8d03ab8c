import * as React from 'react';
import { Dialog } from 'zent';
import { DialogProvider } from '@youzan/retail-components';
import { ExchangeFlowType } from 'common/constants/order';
import { IOperation } from 'definition/order-info';
import { FormBody } from './form';
import typeMap from '../type-map';

interface IChangeDispatchProps {
  orderNo: string;
  operation: IOperation;
  isOnlineOrder: boolean;
  exchangeType?: ExchangeFlowType;
}

/**
 * 订单换货记录
 * @param props
 * @returns
 */
export function OrderExchangeList(props: IChangeDispatchProps) {
  const { orderNo, operation, isOnlineOrder, exchangeType } = props;

  const Opt = typeMap[operation.type];

  return (
    <DialogProvider>
      {({ visible, hideDialog, showDialog }) => (
        <>
          <Dialog
            style={{
              width: '800px'
            }}
            title="换货记录"
            visible={visible}
            onClose={hideDialog}
          >
            <FormBody
              orderNo={orderNo}
              onCancel={hideDialog}
              isOnlineOrder={isOnlineOrder}
              exchangeType={exchangeType}
            />
          </Dialog>
          <Opt onClick={showDialog} {...operation} />
        </>
      )}
    </DialogProvider>
  );
}

export default OrderExchangeList;
