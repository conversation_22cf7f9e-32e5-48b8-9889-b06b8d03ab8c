import { request } from '@youzan/retail-utils';
import { IOrderExchangeInfo } from 'definition/order-info';

export interface IQueryOrderDetailParams {
  /** 订单号 */
  orderNo: string;
}

export interface IQueryOrderDetailResp {
  exchangeOrderList: IOrderExchangeInfo[];
}

/**
 * 获取换货单换货记录数据
 */
export function queryOrderExchangeList(
  data: IQueryOrderDetailParams
): Promise<IQueryOrderDetailResp> {
  return request({
    url: '/youzan.retail.trademanager.exchange.order.list/1.0.0',
    data
  });
}
