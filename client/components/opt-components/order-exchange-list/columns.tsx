import * as React from 'react';
import { IGridColumn } from 'zent';
import { BlankLink } from '@youzan/react-components';
import { formatDatetime, track } from '@youzan/retail-utils';
import { head } from 'lodash';
import { convertFenToYen } from 'common/fns/format';
import { getOnlineExchangeUrl } from 'common/url';
import { ExchangeFlowType } from 'common/constants/order';

type GridColumn = IGridColumn & {
  hidden?: boolean;
};

interface IColumns {
  isOnlineOrder: boolean;
  exchangeType?: ExchangeFlowType;
}

export function getColumns(options: IColumns) {
  const { isOnlineOrder, exchangeType } = options;

  const handleTrack = (params: {
    // eslint-disable-next-line camelcase
    source_order_no: string;
    // eslint-disable-next-line camelcase
    exchange_order_no: string;
    // eslint-disable-next-line camelcase
    exchange_type: ExchangeFlowType;
  }) => {
    track({
      et: 'click',
      ei: 'click_show_exchange_detail_btn',
      en: '查看换货详情',
      params,
      pt: ''
    });
  };
  const columns: GridColumn[] = [
    {
      title: '换货编号',
      name: 'itemId',
      width: 250,
      bodyRender: ({ exchangeNo, itemNum }) => (
        <div>
          <div>{exchangeNo || '-'}</div>
          <div>{itemNum ? `共${itemNum}件商品` : '-'}</div>
        </div>
      )
    },
    {
      title: '订单金额',
      name: 'realPrice',
      width: 100,
      bodyRender: data => {
        const { realPrice, isRefund, refundFee } = data;
        return (
          <div>
            {isRefund
              ? `退款${convertFenToYen(refundFee)}元`
              : `收款${convertFenToYen(realPrice)}元`}
          </div>
        );
      },
      hidden: isOnlineOrder
    },
    {
      title: '换货时间',
      name: 'exchangeTime',
      textAlign: 'center',
      width: 200,
      bodyRender: ({ exchangeTime }) => (
        <div>{formatDatetime(exchangeTime, 'YYYY-MM-DD HH:mm') || '-'}</div>
      )
    },
    {
      title: '操作人员',
      name: 'operator',
      textAlign: 'center',
      width: 120
    },
    {
      title: '操作',
      name: 'itemId',
      bodyRender({ orderNo, exchangeNo, isOnlineOrder, itemIds, refundId }) {
        let url = '';
        if (isOnlineOrder) {
          /** isOnlineOrder 后端说旧的网店换货不会有这个操作返回，所以只要是网店换货就是新的换货流程 */
          url = getOnlineExchangeUrl({ orderNo, refundId, itemId: head(itemIds) });
        } else {
          /** 老换货/门店换货1.0/门店换货2.0 */
          url = `/v2/order/exchange/detail#/?order_no=${orderNo}&exchange_no=${exchangeNo}&exchange_type=${exchangeType}`;
        }

        return (
          <BlankLink
            href={url}
            onClick={() =>
              handleTrack({
                source_order_no: orderNo,
                exchange_order_no: exchangeNo,
                exchange_type: exchangeType as ExchangeFlowType
              })
            }
          >
            查看
          </BlankLink>
        );
      }
    }
  ];
  return columns.filter(i => !i.hidden);
}
