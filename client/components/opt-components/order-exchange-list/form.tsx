import * as React from 'react';
import { Space } from '@youzan/react-components';
import { useBoolean } from '@youzan/react-hooks';
import { ExchangeFlowType } from 'common/constants/order';
import { Button, Form, FormStrategy, Grid, LayoutRow, Notify } from 'zent';
import { getColumns } from './columns';
import { queryOrderExchangeList } from './api';

interface IFormBodyProps {
  orderNo: string;
  onCancel: () => void;
  isOnlineOrder: boolean;
  exchangeType?: ExchangeFlowType;
}

interface IFooterProps {
  loading: boolean;
  onCancel: () => void;
}

function Footer({ loading, onCancel }: IFooterProps) {
  return (
    <LayoutRow justify="end">
      <Button onClick={onCancel} disabled={loading} type="primary">
        确定
      </Button>
    </LayoutRow>
  );
}

export function FormBody({ orderNo, onCancel, isOnlineOrder, exchangeType }: IFormBodyProps) {
  const columns = getColumns({ isOnlineOrder, exchangeType });
  const [datasets, setDatasets] = React.useState<any[]>([]);
  const [loading, { setTrue, setFalse }] = useBoolean();

  const form = Form.useForm(FormStrategy.View);

  React.useEffect(() => {
    setTrue();
    queryOrderExchangeList({
      orderNo
    })
      .then(({ exchangeOrderList = [] }) => {
        setDatasets(exchangeOrderList);
      })
      .catch(err => Notify.error(err.msg || '查询订单信息失败'))
      .finally(() => setFalse());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderNo]);

  return (
    <Form form={form} layout="horizontal">
      <Space direction="vertical">
        <Grid datasets={datasets} columns={columns} rowKey="orderItemIdStr" loading={loading} />
        <Footer loading={form.isSubmitting} onCancel={onCancel} />
      </Space>
    </Form>
  );
}
