import React from 'react';
import { Dialog, Button, Notify } from 'zent';
import styled from 'styled-components';
import { convertFenToYen } from 'common/fns/format';
import createOptComponent from '../create-opt-component';
import { confirm } from './api';

const { useState } = React;

const Wrapper = styled.div`
  padding: 20px 0;
`;

const Footer = styled.footer`
  margin-top: 10px;
  text-align: right;
`;

function RefundAndClose(props) {
  const { operation = {}, options, OptTypeCpn } = props;
  const { text } = operation;
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const doConfirm = () => {
    setLoading(true);
    confirm(options.orderInfo.mainOrderInfo.orderNo)
      .then(() => {
        Notify.success('关闭订单并退款成功');
        setVisible(false);
        options.reload && options.reload();
      })
      .catch(err => {
        Notify.error(err.msg);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <>
      <Dialog
        visible={visible}
        title={text}
        onClose={() => setVisible(false)}
        style={{ width: '500px' }}
      >
        <Wrapper>
          <p>
            超卖订单退款后，买家端将显示订单关闭，已支付金额{' '}
            {`￥${convertFenToYen(options.orderInfo.paymentInfo.payment)}`} 将自动退回买家支付账户。
          </p>
        </Wrapper>
        <Footer>
          <Button type="primary" loading={loading} outline onClick={() => setVisible(false)}>
            取消
          </Button>
          <Button type="primary" loading={loading} onClick={doConfirm}>
            关闭订单并退款
          </Button>
        </Footer>
      </Dialog>
      <OptTypeCpn buttonType="default" outline onClick={() => setVisible(true)} />
    </>
  );
}

export default createOptComponent(RefundAndClose);
