/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @youzan/yz-retail/typescript/prefer-pascal-case-enums */
import React, { FC, useState, useEffect } from 'react';
import { Button, openDialog, closeDialog, IGridColumn, Grid } from 'zent';
import format from '@youzan/utils/money/format';
import { BlankLink } from '@youzan/react-components';
import { IGridOnChangeConfig } from 'zent/es/grid/types';
import find from 'lodash/find';
import { track } from '@youzan/retail-utils';

const getRefundDetailUrl = (orderNo: string) => {
  return `/v2/order/orderdetail#/?order_no=${orderNo}`;
};

export enum FEEDBACK_TYPE {
  // 仅退款
  ONLY_REFUND = 1,
  // 退货退款
  REFUND_AND_RETURN = 2,
  // 换货
  EXCHANGE = 3
}

const getTextFromVal = (
  list: { value: number; key: string; text: string }[],
  val = -1,
  valueKey = 'value',
  textKey = 'text'
) => {
  // @ts-ignore
  const item = find(list, o => o[valueKey] === val);
  if (item) {
    // @ts-ignore
    return item[textKey];
  }
  return '';
};

const type = {
  [FEEDBACK_TYPE.ONLY_REFUND]: '退款',
  [FEEDBACK_TYPE.REFUND_AND_RETURN]: '退货退款',
  [FEEDBACK_TYPE.EXCHANGE]: '换货'
};

const DELIVERY_STATUS = [
  {
    value: -1,
    key: 'all',
    text: '-'
  },
  {
    value: 1,
    key: 'not_delivery',
    text: '未发货'
  },
  {
    value: 2,
    key: 'has_delivery',
    text: '已发货'
  }
];

const errCode = 102010024;

interface IRequestParams {
  api: any;
  refundType: FEEDBACK_TYPE;
  isCheckWxRefund?: boolean;
  orderNo?: string;
  source?: string;
  onCancel?: (isCloseAll?: boolean) => void;
  onContinue?: () => void;
}

interface IDialogParams {
  refundText: string;
  wxRefundList?: any;
  orderNo?: string;
  source?: string;
  onCancel?: (isCloseAll?: boolean) => void;
  onContinue?: () => void;
}

const WxRefundList: FC<any> = ({ list }) => {
  const [pageInfo, setPageInfo] = useState({
    current: 1,
    pageSize: 5,
    total: 0
  });
  const [isWxRefundList, setIsWxRefundList] = useState([]);
  const columns: IGridColumn<any>[] = [
    {
      title: '售后编号',
      width: '20%',
      bodyRender: ({ orderNo, refundId }) => (
        <BlankLink href={getRefundDetailUrl(orderNo)}>{refundId}</BlankLink>
      )
    },
    {
      title: '售后方式',
      width: '10%',
      bodyRender: ({ demand }: { demand: FEEDBACK_TYPE }) => type[demand] || '-'
    },
    {
      title: '发货状态',
      width: '10%',
      bodyRender: ({ deliveryStatus }: { deliveryStatus: number }) =>
        getTextFromVal(DELIVERY_STATUS, deliveryStatus)
    },
    {
      title: '退款金额（元）',
      width: '10%',
      bodyRender: ({ refundFee }) => <span>{format(refundFee)}</span>
    }
  ];

  const onChange = (conf: IGridOnChangeConfig) => {
    const { current = 1 } = conf;
    if (pageInfo.current !== current) {
      setPageInfo({
        ...pageInfo,
        current
      });
      setIsWxRefundList(
        list
          .filter((item: { hasWeChatComplaint: any }) => item.hasWeChatComplaint)
          .slice((current - 1) * pageInfo.pageSize, current * pageInfo.pageSize)
      );
    }
  };

  useEffect(() => {
    const isWxRefundList = list.filter(
      (item: { hasWeChatComplaint: boolean }) => item.hasWeChatComplaint
    );
    setPageInfo({
      ...pageInfo,
      total: isWxRefundList.length
    });
    setIsWxRefundList(isWxRefundList.slice(0, pageInfo.pageSize));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [list]);

  return (
    <div>
      <div>
        勾选的{list.length}笔退款单中，有
        <span style={{ color: '#d40000' }}> {pageInfo.total}笔 </span>
        售后单买家向微信平台发起了交易投诉，请先前往微信小程序后台-交易保障-交易投诉处理，避免重复退款造成资损。如已处理，可忽略。
      </div>
      <div style={{ marginTop: 40 }}>
        <p style={{ marginBottom: 10 }}>售后单详情:</p>
        <Grid
          columns={columns}
          datasets={isWxRefundList}
          rowKey="refundId"
          pageInfo={pageInfo}
          onChange={onChange}
        />
      </div>
    </div>
  );
};

const refundStatusListenDialog = ({
  wxRefundList,
  refundText,
  source,
  orderNo,
  onCancel,
  onContinue
}: IDialogParams) => {
  const dialogId = `refund-status-listen-dialog-${Date.now()}`;

  track({
    et: 'view', // 事件类型
    ei: 'wechat_complaint_remind_view', // 事件标识
    en: '微信交易投诉提醒弹窗曝光', // 事件名称
    params: {
      order_no: orderNo || '',
      page_source: source || '',
      component: 'wechat_complaint_remind'
    } // 事件参数
  });

  const handleToWx = () => {
    track({
      et: 'click', // 事件类型
      ei: 'deal_click', // 事件标识
      en: '去处理点击', // 事件名称
      params: {
        order_no: orderNo || '',
        page_source: source || '',
        component: 'wechat_complaint_remind'
      } // 事件参数
    });
    // eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
    window.open('https://mp.weixin.qq.com/');
  };
  const handleClose = (isCloseAll = true) => {
    if (isCloseAll) {
      track({
        et: 'click', // 事件类型
        ei: 'no_refund_click', // 事件标识
        en: '暂不退款点击', // 事件名称
        params: {
          order_no: orderNo || '',
          page_source: source || '',
          component: 'wechat_complaint_remind'
        } // 事件参数
      });
    }
    closeDialog(dialogId);
    onCancel?.(isCloseAll);
  };

  const handleContinue = () => {
    track({
      et: 'click', // 事件类型
      ei: 'continue_to_refund_click', // 事件标识
      en: '继续退款点击', // 事件名称
      params: {
        order_no: orderNo || '',
        page_source: source || '',
        component: 'wechat_complaint_remind'
      } // 事件参数
    });
    closeDialog(dialogId);
    onContinue?.();
  };

  const children = wxRefundList ? (
    <WxRefundList list={wxRefundList} />
  ) : (
    <>
      买家针对该订单向微信平台发起交易投诉，请先前往微信公众平台-交易保障-交易投诉处理，避免重复退款造成资损。如已处理，可忽略。
    </>
  );

  return openDialog({
    dialogId,
    title: '提示',
    children,
    footer: (
      <>
        <Button onClick={() => handleClose(true)}>暂不{refundText}</Button>
        <Button onClick={handleContinue}>已处理，继续{refundText}</Button>
        <Button type="primary" onClick={handleToWx}>
          去处理
        </Button>
      </>
    ),
    onClose: () => handleClose(false),
    style: { width: 800 }
  });
};

const makeRefundRequest = ({
  api,
  refundType,
  isCheckWxRefund = true,
  orderNo,
  source,
  onCancel,
  onContinue
}: IRequestParams) => {
  return new Promise((resolve, reject) => {
    api()
      .then((res: unknown) => {
        resolve(res);
      })
      .catch((err: { code: number }) => {
        // 如果是微信投诉订单拦截掉，需要弹窗
        if (err?.code === errCode && isCheckWxRefund) {
          refundStatusListenDialog({ refundType, onCancel, onContinue, orderNo, source });
        } else {
          reject(err);
        }
      });
  });
};

export { makeRefundRequest };

export default refundStatusListenDialog;
