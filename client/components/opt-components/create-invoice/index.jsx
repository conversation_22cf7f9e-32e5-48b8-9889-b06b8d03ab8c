import React from 'react';
import { Notify } from 'zent';
import { get } from 'lodash';
import createOptComponent from '../create-opt-component';
import { checkInviceStatus } from './api';

@createOptComponent
class Remark extends React.Component {
  openDialog = config => {
    const { options, operation } = config;
    const orderNo = get(options, 'orderInfo.mainOrderInfo.orderNo');
    checkInviceStatus(orderNo)
      .then(() => {
        const invoiceUrl = get(operation, 'attributes.url');
        window.open(invoiceUrl, '_blank');
      })
      .catch(err => {
        Notify.error(err.msg || '无法开票！');
      });
  };

  render() {
    const { OptTypeCpn } = this.props;
    return <OptTypeCpn onClick={this.openDialog} />;
  }
}

export default Remark;
