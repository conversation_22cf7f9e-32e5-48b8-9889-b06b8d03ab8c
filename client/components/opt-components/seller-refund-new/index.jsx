import React from 'react';
import { Dialog, Notify, Sweetalert } from 'zent';
import { track } from '@youzan/retail-utils';
import { get } from 'lodash';
import { orderTypeHelper } from 'common/model-helper';
import { RefundContent } from './enjoy-buy-refund/content';
import typeMap from '../type-map';
import * as api from './api';
import Content from './content';
import style from './style.scss';

const enjoyBuyOrderType = 32766;

class SellerRefund extends React.Component {
  state = {
    loading: false,
    fetchData: null,
    visible: false
  };

  click = () => {
    const params = {};
    const { options = {} } = this.props;

    if (options.mainOrderInfo.orderType === enjoyBuyOrderType) {
      this.setState({
        visible: true
      });
      return;
    }

    // 网店订单 且 不是美团订单
    if (options.isOnline && !options.isMeituanOrder) {
      params.itemId = get(options, 'itemInfo[0].itemIdStr');
    }

    params.orderNo = get(options, 'mainOrderInfo.orderNo');

    params.isExchangeOrder = !!get(options, 'exchangeInfo.sourceOrderNo');

    this.setState({ loading: true });

    const promises = Promise.all([
      api.fetchRefundData(params),
      api.checkIsDeposited(params.orderNo)
    ]);

    if (params.isExchangeOrder) {
      track({
        et: 'click',
        ei: 'click_exchange_order_refund_btn',
        en: '换货订单二次售后',
        params: {
          source_order_no: params.orderNo,
          exchange_order_no: get(options, 'exchangeInfo.exchangeNo')
        },
        pt: ''
      });
    }

    promises
      .then(([refundData, depositInfo]) => {
        const failReason = get(refundData, 'orderInfo.refuseRefundReason');
        if (failReason) {
          Sweetalert.alert({
            content: <p>{failReason}</p>,
            parentComponent: this,
            closeBtn: true,
            maskClosable: true
          });
          return;
        }

        if (depositInfo.isDeposited) {
          Sweetalert.confirm({
            content: <p>{depositInfo.remindInfo}</p>,
            onConfirm: () => {
              this.setState({ fetchData: refundData });
            },
            onCancel: () => {},
            parentComponent: this
          });
          return;
        }

        this.setState({ fetchData: refundData });
      })
      .catch(err => {
        Notify.error(err.msg || '获取退款信息失败');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  onCLose = () => {
    this.setState({ fetchData: null });
  };

  hideDialog = () => {
    this.setState({
      visible: false
    });
  };

  checkIsAnyExpressed() {
    const allItems = get(this.props, 'options.allItems', []);
    return allItems.some(item => item.hasExpressed === 1);
  }

  render() {
    const { options = {}, operation } = this.props;
    const Opt = typeMap[operation.type];
    const isQrCode = orderTypeHelper.isQrcode(get(options, 'mainOrderInfo.orderType')) || false;
    const { fetchData, loading, visible } = this.state;
    const { orderNo, orderType } = options.mainOrderInfo;
    const itemId = options.itemInfo[0].itemIdStr;

    return (
      <>
        <Opt loading={loading} onClick={this.click} {...operation} />
        {enjoyBuyOrderType === orderType && (
          <Dialog onClose={this.hideDialog} style={{ width: 720 }} visible={visible} title="退款">
            <RefundContent
              reload={options.reload}
              onClose={this.hideDialog}
              itemId={itemId}
              orderNo={orderNo}
            />
          </Dialog>
        )}
        {fetchData && enjoyBuyOrderType !== orderType && (
          <Dialog
            onClose={this.onCLose}
            style={{ width: options.isFxOrder ? 808 : 720 }}
            visible
            title="主动退款"
            className={style['refund-container']}
          >
            <Content
              {...options}
              onClose={this.onCLose}
              isOnline={options.isOnline || isQrCode}
              canSelectItem={!options.isOnline}
              fetchData={fetchData}
              isAnyExpressed={this.checkIsAnyExpressed()}
              isVirtual={fetchData.orderInfo.supportVirtualTicketReturnNos}
            />
          </Dialog>
        )}
      </>
    );
  }
}

export default SellerRefund;
