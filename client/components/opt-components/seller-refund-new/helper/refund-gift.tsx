import { Sweetalert } from 'zent';

/**
 * 展示赠品退款提示
 * @return 是否继续退款
 */
export function showRefundGiftConfirm() {
  return new Promise<boolean>(resolve => {
    Sweetalert.confirm({
      title: '提示',
      content: '该赠品己发货，退款后消费者无法再次兑换。',
      confirmText: '确定退款',
      cancelText: '暂时不退',
      onConfirm: () => {
        resolve(true);
      },
      onCancel: () => {
        resolve(false);
      }
    });
  });
}

/**
 * 特殊情况的赠品退款提示
 * @return 是否继续退款
 */
export function checkAndShowRefundGiftConfirm({
  mainOrderInfo,
  itemInfos = []
}: {
  mainOrderInfo: {
    /** 订单是否拆单 */
    isDivided: boolean;
  };
  itemInfos: {
    isPresent: boolean;
    /** 是否已发货 */
    hasExpressed: 0 | 1;
  }[];
}) {
  const shouldShow =
    !mainOrderInfo.isDivided &&
    itemInfos.length === 1 &&
    itemInfos[0].isPresent &&
    Boolean(itemInfos[0].hasExpressed);
  if (shouldShow) {
    return showRefundGiftConfirm();
  }

  return Promise.resolve(true);
}
