import { div, minus, plus } from '@youzan/retail-utils';

export const RefundWay = {
  // 原路退回
  Original: 0,
  // 现金退款
  Cash: 1,
  // 标记退款
  Mark: 2
};

/**
 * 枚举太多了, 如果有增加, 看 https://doc.qima-inc.com/pages/viewpage.action?pageId=21675788
 */
export const BuyWay = {
  // 现金
  OfCash: 201,
  // 会员余额
  UnifiedPrepaidCard: 35,
  // 有赞 E 卡
  ECard: 28,
  // 礼品卡支付
  EnchashmentGiftCard: 90
};

// 请看 BuyWay 注释
export const BuyWayDesc = {
  // 现金
  OfCash: '现金'
};

const RefundWayList = Object.values(RefundWay);

/**
 * 根据退款类型 + 组合支付剩余退款列表 + 退款金额, 生成退款方式+金额列表
 */
export function createPhasePaymentRefundAmountList(
  refundWay = RefundWay.Original,
  combinePayRemainDetailList = [],
  totalRemainAmount = 0
) {
  let total = totalRemainAmount;
  if (RefundWayList.indexOf(refundWay) === -1) {
    return [];
  }

  const resultMap = new Map();
  for (let index = 0; index < combinePayRemainDetailList.length; index++) {
    const detailItem = combinePayRemainDetailList[index];
    let { buyWay, buyWayDesc, remainAmount } = detailItem;
    remainAmount = div(remainAmount, 100);
    const amount = Math.min(remainAmount, total);

    if (refundWay === RefundWay.Cash) {
      // 现金退回/原路+现金退回
      if (
        buyWay !== BuyWay.UnifiedPrepaidCard &&
        buyWay !== BuyWay.ECard &&
        buyWay !== BuyWay.EnchashmentGiftCard
      ) {
        // 部分支付方式原路, 其他支付强制为现金退回
        buyWay = BuyWay.OfCash;
        buyWayDesc = BuyWayDesc.OfCash;
      }
    }

    const key = `${buyWay}-${buyWayDesc}`;

    const item = resultMap.get(key);
    if (item) {
      item.amount = plus(item.amount, amount);
    } else {
      resultMap.set(key, {
        buyWayDesc,
        amount
      });
    }

    const neoRemainAmount = minus(total, remainAmount);
    if (neoRemainAmount < 0) {
      break;
    }

    total = neoRemainAmount;
  }

  return [...resultMap.values()].filter(item => item.amount !== 0);
}
