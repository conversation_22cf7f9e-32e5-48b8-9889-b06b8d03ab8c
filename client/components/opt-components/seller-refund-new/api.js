import { request } from '@youzan/retail-utils';

export const fetchRefundData = data =>
  request({
    url: '/youzan.retail.trade.refund.remain.list/1.0.2',
    method: 'post',
    data: {
      ...data
    }
  });

export const doRefund = data =>
  request({
    url: '/youzan.retail.trade.refund/1.0.1/create',
    method: 'post',
    data
  });

export const checkIsDeposited = orderNo =>
  request({
    url: '/youzan.retail.trade.misc.goodsstorage.order.joinstate/1.0.0/get',
    data: {
      orderNo
    }
  });

export const getShareOfRefundFee = data =>
  request({
    url: '/v2/order/orderDetail/getShareOfRefundFee.json',
    data
  });
