@import '~shared/style';

:local(.refund-container) {
  .zent-table .tbody .cell {
    min-height: 65px;
    padding-top: 15px;
    align-items: flex-start;
  }

  .refund-postage,
  .refund-order-type {
    margin-bottom: 20px;
  }

  .refund-postage-tag,
  .refund-type-tag {
    color: $color-text-primary;
    vertical-align: bottom;
  }

  .block-radio-label {
    vertical-align: bottom;
  }

  .rf-postage-radio-group,
  .rf-type-radio-group {
    padding-left: 10px;
  }

  .refund-goods-info {
    margin-top: 7px;

    &__item {
      margin-top: 4px;

      &:first-child {
        margin-top: 0;
      }
    }
  }

  .tag-list {
    &__item {
      margin-left: 4px;

      &:first-child {
        margin-left: 0;
      }
    }
  }

  .refund-num-wrapper {
    width: 100%;

    &:not(:last-child) {
      margin-bottom: 15px;
    }
  }

  .refund-sku {
    width: 140px;
    margin-top: 5px;
    color: $color-text-secondary;

    .zent-popover-wrapper {
      width: 100%;
      vertical-align: top;
    }
  }

  .normal-desc {
    font-size: $font-size-small;
    color: $color-text-secondary;
  }

  .single-radio {
    margin-left: 10px;
  }

  .badge-radio-item {
    width: 100px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin-right: 15px;
    display: inline-block;
    margin-bottom: 10px;
    font-size: $font-size-small;
    cursor: pointer;
  }

  .refund-order-radio {
    display: inline-block;
  }

  .seprater {
    margin-bottom: 16px;
  }

  .warning {
    margin-bottom: 20px;

    mark {
      background-color: transparent;
      color: $color-warn;
    }
  }

  .retail-form__control-group-label {
    width: unset;
    display: inline-block;
  }

  .retail-form__controls {
    display: inline-block;
  }

  .zent-radio-group {
    vertical-align: top;
  }

  .zent-input-wrapper {
    width: 80px;

    input.zent-input {
      width: 80px;
    }

    .zent-textarea {
      height: 75px;
    }
  }

  .money-input-wrapper {
    display: flex;
    align-items: center;
  }

  .money-input {
    text-align: right;

    .zent-input-wrapper {
      display: inline-flex;
    }
  }

  .info-warn {
    color: $color-alert;
    font-size: $font-size-small;
    line-height: 1.5;
  }

  .sum-money-text {
    display: flex;
    .info-icon {
      margin-left: 5px;
    }
  }

  .zent-grid-td {
    border-right: none;
  }

  .refund_dialog_footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;

    &__left {
      flex-grow: 1;
    }

    &__right {
      flex-shrink: 0;
      min-width: 0;
    }
  }
}
