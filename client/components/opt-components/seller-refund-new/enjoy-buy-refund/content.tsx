/* eslint-disable @youzan/yz-retail/prefer-pascal-case-const */
import React from 'react';
import { Alert, Select, DateRangePicker, IGridColumn, Grid, Notify, Button } from 'zent';
import { Space } from '@youzan/react-components';
import { plus, formatDate, div, times } from '@youzan/retail-utils';
import { useState, useEffect, useMemo } from 'react';
import { compareAsc, format } from 'date-fns';
import zhCn from 'date-fns/locale/zh_cn';
import { usePageInfo } from '@youzan/react-hooks';
import { NumberInput } from '@youzan/retail-components';

import {
  EnjoyBuyDeliveryStatus,
  EnjoyBuyRefundStatus,
  IRefundIssueItem,
  queryList,
  submitRefund,
  getRemainList
} from './api';
import style from './index.m.scss';

const deliveryStatusTextMap = new Map([
  [EnjoyBuyDeliveryStatus.Waiting, '待配送'],
  [EnjoyBuyDeliveryStatus.Refunded, '已退款'],
  [EnjoyBuyDeliveryStatus.RefundInProgress, '退款中']
]);
const enableShowDeliveryStatus = Array.from(deliveryStatusTextMap.keys());

const totalOption = {
  key: null,
  text: '全部状态'
};

const deliveryStatusOptions = [
  totalOption,
  ...Array.from(deliveryStatusTextMap).map(([key, text]) => ({
    text,
    key
  }))
];

const columns: IGridColumn<IRefundIssueItem>[] = [
  {
    title: '期数',
    width: '50%',
    bodyRender: ({ issue, deliveryDate }) => (
      <>
        <span>第{issue}期</span>
        <span className={style.gray}>
          （{formatDate(deliveryDate)}&nbsp;周
          {format(deliveryDate, 'dd', {
            locale: zhCn
          })}
          ）
        </span>
      </>
    )
  },
  {
    title: '可退金额(元)',
    bodyRender: ({ issuePrice }) => div(issuePrice, 100)
  },
  {
    title: '状态',
    textAlign: 'right',
    bodyRender: ({ deliveryStatus }) => (
      <span className={deliveryStatus === EnjoyBuyDeliveryStatus.Waiting && style.highlight}>
        {deliveryStatusTextMap.get(deliveryStatus)}
      </span>
    )
  }
];

function calcDeliveryStatus(
  deliveryStatus: EnjoyBuyDeliveryStatus,
  refundStatus: EnjoyBuyRefundStatus
) {
  if (refundStatus === EnjoyBuyRefundStatus.Success) {
    return EnjoyBuyDeliveryStatus.Refunded;
  }
  if (
    [
      EnjoyBuyRefundStatus.WaitSellerAgree,
      EnjoyBuyRefundStatus.SellerRefuseBuyer,
      EnjoyBuyRefundStatus.WaitBuyerReturnGoods,
      EnjoyBuyRefundStatus.WaitSellerConfirmGoods,
      EnjoyBuyRefundStatus.SellerRefuseReturnGoods,
      EnjoyBuyRefundStatus.WaitBuyerConfirmGoods
    ].includes(refundStatus)
  ) {
    return EnjoyBuyDeliveryStatus.RefundInProgress;
  }
  return deliveryStatus;
}

export function RefundContent({
  orderNo,
  itemId,
  onClose,
  reload
}: {
  orderNo: string;
  itemId: string;
  onClose: () => void;
  reload: () => void;
}) {
  const [loading, setLoading] = useState(true);
  const [postage, setPostage] = useState<number>(0);
  const [status, setStatus] = useState<{
    key: EnjoyBuyDeliveryStatus | null;
    text: string;
  }>(totalOption);
  const [issue, setIssue] = useState('');
  const [date, setDate] = useState<[string, string]>(['', '']);
  const [datasets, setDatasets] = useState<IRefundIssueItem[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<[]>([]);
  const [refunding, setRefunding] = useState(false);
  const selectedCount = selectedRowKeys.length;

  useEffect(() => {
    setLoading(true);
    queryList({ orderNo, itemId })
      .then(res => {
        setLoading(false);
        setDatasets(
          res.issueActiveRefundableFeeDTOS.map(item => ({
            ...item,
            deliveryStatus: calcDeliveryStatus(item.deliveryStatus, item.refundStatus)
          }))
        );
        setPostage(res.postage);
      })
      .catch(err => {
        setLoading(false);
        Notify.error(err?.msg || '获取退款期数失败');
      });
  }, [orderNo, itemId]);

  const filteredDatasets = useMemo(
    () =>
      datasets.filter(item => {
        if (!enableShowDeliveryStatus.includes(item.deliveryStatus)) {
          return false;
        }
        if (!!issue && item.issue !== +issue) {
          return false;
        }
        if (totalOption.key !== null && item.deliveryStatus !== status.key) {
          return false;
        }
        const [startDate, endDate] = date;

        if (
          (!!startDate && compareAsc(item.deliveryDate, startDate) === -1) ||
          (!!endDate && compareAsc(item.deliveryDate, endDate) === 1)
        ) {
          return false;
        }
        return true;
      }),
    [datasets, issue, date, status]
  );

  const refundFee = useMemo(() => {
    if (selectedRowKeys.length === 0) {
      return null;
    }
    let fee = selectedRowKeys.reduce((acc, cur) => {
      const issueData = datasets.find(item => item.issue === cur);
      if (issueData) {
        return plus(acc, issueData.issuePrice);
      }
      return acc;
    }, 0);
    const hasFinishDeliveryIssue = datasets.find(
      item => item.deliveryStatus === EnjoyBuyDeliveryStatus.Done
    );
    if (
      !hasFinishDeliveryIssue &&
      datasets.filter(item => item.deliveryStatus === EnjoyBuyDeliveryStatus.Waiting).length ===
        selectedRowKeys.length
    ) {
      fee = plus(fee, postage);
    }
    return div(fee, 100);
  }, [selectedRowKeys, datasets, postage]);

  const gridProps = usePageInfo({
    items: filteredDatasets,
    pageSize: 6
  });

  const onOptionChange = (item: any) => {
    setStatus(item);
  };

  const onConfirm = async () => {
    if (!selectedCount || typeof refundFee !== 'number') {
      Notify.error('请选择需要退款的期数');
      return;
    }
    setRefunding(true);
    let productType = null;
    try {
      const { list } = await getRemainList({
        orderNo,
        itemId
      });
      if (Array.isArray(list) && list.length > 0) {
        productType = list[0].productType;
      }
    } catch (error) {
      Notify.error(error?.msg || '获取退款商品信息失败');
    }
    if (productType === null) {
      setRefunding(false);
      return;
    }
    const formatedRefundFee = times(refundFee, 100);

    submitRefund({
      sourceOrderNo: orderNo,
      itemId,
      refundType: 0,
      // 待确认
      returnType: 2,
      mode: 1,
      refundFee: formatedRefundFee,
      refundOrderItems: [
        {
          orderItemId: itemId,
          productType,
          refundFee: formatedRefundFee
        }
      ],
      requestId: selectedRowKeys
        .slice()
        .sort((a, b) => a - b)
        .join('_'),
      enjoyBuyIssues: selectedRowKeys.map(issue => ({
        issue
      }))
    })
      .then(() => {
        setRefunding(false);
        onClose();
        Notify.success('退款成功');
        reload();
      })
      .catch(error => {
        setRefunding(false);
        Notify.error(error?.msg || '退款失败');
      });
  };

  return (
    <Space direction="vertical">
      <Alert type="warning" closable>
        <p>商家主动退款功能仅作为退款维权业务的补充功能，请勿过度依赖和使用</p>
        <p>目前随心购仅支持按照期数退款，退款金额不支持修改。退款前建议先和买家协商一致再操作</p>
      </Alert>
      <Space size="small" className={style.filter}>
        <Select
          value={status}
          onChange={onOptionChange}
          width={120}
          options={deliveryStatusOptions}
        />
        <DateRangePicker value={date} onChange={setDate as any} />
        <NumberInput
          value={issue}
          onChange={setIssue as any}
          width={120}
          icon="search"
          placeholder="搜索期数"
          min={1}
          decimal={0}
        />
      </Space>
      <Grid
        selection={{
          selectedRowKeys,
          onSelect: setSelectedRowKeys as any,
          getCheckboxProps: ({ deliveryStatus }) => ({
            disabled: [
              EnjoyBuyDeliveryStatus.RefundInProgress,
              EnjoyBuyDeliveryStatus.Refunded
            ].includes(deliveryStatus)
          })
        }}
        loading={loading}
        rowKey="issue"
        columns={columns}
        paginationType="lite"
        className={style.grid}
        {...gridProps}
      />
      <div className={style.footer}>
        {selectedCount > 0 && `（共${selectedCount}期）`}退款给买家/付款人：
        {selectedCount > 0 && <span className={style.highlight}>{`￥${refundFee}`}</span>}
      </div>
      <div className={style.footer}>
        <Button onClick={onClose} disabled={refunding}>
          取消
        </Button>
        <Button onClick={onConfirm} type="primary" loading={refunding}>
          确定
        </Button>
      </div>
    </Space>
  );
}
