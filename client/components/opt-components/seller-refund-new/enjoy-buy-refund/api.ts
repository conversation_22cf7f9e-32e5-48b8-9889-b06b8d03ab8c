import { request } from '@youzan/retail-utils';

interface IQueryListParams {
  orderNo: string;
  itemId: string;
}

/**
 * 随心购配送状态
 */
export enum EnjoyBuyDeliveryStatus {
  /** 待配送 */
  Waiting = 0,
  /** 已配送 */
  Done,
  /** 无需配送 */
  NoNeed,
  /** 已退款 */
  Refunded,
  /** 待退款 */
  RefundInProgress
}

/**
 * 随心购退款状态
 */
export enum EnjoyBuyRefundStatus {
  WaitSellerAgree = 1,
  SellerRefuseBuyer = 10,
  WaitBuyerReturnGoods = 20,
  WaitSellerConfirmGoods = 30,
  SellerRefuseReturnGoods = 40,
  WaitBuyerConfirmGoods = 45,
  Closed = 50,
  Success = 60
}

export interface IRefundIssueItem {
  issue: number;
  deliveryStatus: EnjoyBuyDeliveryStatus;
  issuePrice: number;
  deliveryDate: number;
  itemId: number;
  refundStatus: EnjoyBuyRefundStatus;
}

export function queryList(data: IQueryListParams) {
  return request<{
    issueActiveRefundableFeeDTOS: IRefundIssueItem[];
    postage: number;
  }>('/v2/order/orderDetail/getIssueRefundableFee.json', data);
}

interface ISubmitRefundRequest {
  sourceOrderNo: string;
  itemId: string;
  // 退款模式: 0-原路退 1-现金退 2-标记退
  refundType: 0 | 1 | 2;
  // 退款方式 1-整单全部退款，2-整单部分退款
  returnType: 1 | 2;
  // 退款模式 0：退货退款 1：仅退款 (默认值为退货退款)
  mode: 0 | 1;
  refundOrderItems: {
    orderItemId: string;
    productType: number;
    refundFee: number;
  }[];
  refundFee: number;
  enjoyBuyIssues: {
    issue: number;
  }[];
  requestId: string;
}

export function getRemainList(data: { itemId: string; orderNo: string }) {
  return request('youzan.retail.trade.refund.remain.list/1.0.2', data);
}

/**
 * 主动退款
 */
export function submitRefund(data: ISubmitRefundRequest): Promise<boolean> {
  return request('youzan.retail.trade.refund/1.0.1/create', 'POST', data);
}
