import { get } from 'lodash';

const hasExchange = exchangeRefunds => Array.isArray(exchangeRefunds) && exchangeRefunds.length > 0;

const getRefundAmountOrWeight = (allValues, { itemIdStr, exchangeRefunds }) => {
  if (hasExchange(exchangeRefunds)) {
    return exchangeRefunds.reduce((ret, { skuId }) => {
      return Number(get(allValues, `${itemIdStr}-refundAmountOrWeight-${skuId}`, 0)) + ret;
    }, 0);
  }

  return Number(get(allValues, `${itemIdStr}-refundAmountOrWeight`, 0));
};

export { getRefundAmountOrWeight, hasExchange };
