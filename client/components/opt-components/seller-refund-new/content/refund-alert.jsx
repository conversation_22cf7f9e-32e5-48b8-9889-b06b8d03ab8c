import React from 'react';
import { Alert } from 'zent';
import { PayCodeType } from 'common/constants/common';

const WXPAY = PayCodeType.WxPay,
  WXPAY_DAIXIAO = PayCodeType.WxPayDaiXiao,
  OF_CASH = PayCodeType.OfCash,
  COMB_PAY = PayCodeType.CombPay,
  MARK_PAY_WXPAY = PayCodeType.MarkPayWxPay,
  MARK_PAY_ALIPAY = PayCodeType.MarkPayAliPay,
  MARK_PAY_POS = PayCodeType.MarkPayPos,
  ALLIN_SWIPECARD = PayCodeType.AllInSwipeCard;

// eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
const wxHomeUrl = 'https://pay.weixin.qq.com/index.php/home/<USER>';
const REFUND_OPERATION_MAP = {
  [WXPAY]: (
    <span>
      请登录
      <a href={wxHomeUrl} className="orange">
        微信商户后台
      </a>
      操作退款
    </span>
  ),
  [WXPAY_DAIXIAO]: '商家退款后，退款将自动原路退回至买家付款账户。',
  [OF_CASH]: '退款需线下退回现金给买家。',
  [MARK_PAY_WXPAY]: '退款可通过现金退回或自有支付渠道操作退回。',
  [MARK_PAY_ALIPAY]: '退款可通过现金退回或自有支付渠道操作退回。',
  [MARK_PAY_POS]: '退款可通过现金退回或自有支付渠道操作退回。',
  [COMB_PAY]: '退款金额将优先退回至储值余额，超出部分根据具体退款方式进行退回。',
  [ALLIN_SWIPECARD]: '退款需线下退回现金给买家。退款方式为现金退款'
};

const DEFAULT_VALUE = 10;
const RefundAlert = ({ buyWay, buyWayDesc, payDeduction, fetchData, isFxOrder, isCouponPay }) => {
  if (isFxOrder) {
    return (
      <Alert type="warning" className="warning">
        <p>商家主动退款功能仅作为退款维权业务的补充功能，请勿过度依赖和使用。</p>
        <p>供货商主动退款可能给分销商造成损失，请确保已经与分销商协商一致。</p>
        <p>退款将返还实际入账金额，免充值的支付优惠金额将从退款金额中扣除。</p>
      </Alert>
    );
  }

  if (isCouponPay) {
    return (
      <Alert type="warning" className="warning">
        使用平台券的订单不支持换货。
      </Alert>
    );
  }

  // 存在支付优惠，当前只有微信支付存在这个情况
  if (payDeduction) {
    return (
      <Alert type="warning" className="warning">
        <p>退款将返还实际支付金额，支付优惠金额将从退款金额中扣除。</p>
      </Alert>
    );
  }
  // 退款渠道为储值金
  const isStoredValue = fetchData?.orderInfo?.backedStoredValue;
  if (isStoredValue) {
    return (
      <Alert type="warning" className="warning">
        该订单已返{isStoredValue}元储值金，主动退款时不会收回已返储值金。
      </Alert>
    );
  }

  const operation = REFUND_OPERATION_MAP[buyWay] || REFUND_OPERATION_MAP[DEFAULT_VALUE];

  return (
    <Alert type="info" className="warning">
      <p>
        该订单通过“
        <span className="orange">{buyWayDesc}</span>
        ”收款，
        {operation}
      </p>
    </Alert>
  );
};

export default RefundAlert;
