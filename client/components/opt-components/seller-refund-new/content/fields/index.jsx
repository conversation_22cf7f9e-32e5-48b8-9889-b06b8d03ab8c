import React, { useState, useEffect } from 'react';
import { NumberInput } from '@youzan/retail-components';
import { Field } from '@youzan/retail-form';
import { Notify } from 'zent';
import { times, keepDecimal, request } from '@youzan/retail-utils';
import TextButton from '../text-button';

const refundReasonItems = [
  { value: 105, text: '质量问题' },
  { value: 2, text: '不想要了' },
  { value: 101, text: '破损缺件' },
  { value: 104, text: '买错/多买' },
  { value: 107, text: '其他' }
];

export function RefundReasonField({ isMeituan, orderNo }) {
  const [reasons, setReasons] = useState(refundReasonItems);

  useEffect(() => {
    request({
      url: '/v2/order/manager/reason.json',
      data: {
        method: 2,
        orderNo
      }
    }).then(({ reasonModelList }) => {
      setReasons(
        reasonModelList.map(item => {
          return {
            text: item.reason,
            value: item.code
          };
        })
      );
    });
  }, [isMeituan, orderNo]);

  return <Field label="退款原因：" name="refundResaon" component="SelectField" data={reasons} />;
}

export function RefundReasonDescField() {
  return (
    <Field
      label="退款说明："
      name="remark"
      component="InputField"
      type="textarea"
      props={{
        placeholder: '选填，最多200字',
        maxLength: 200
      }}
      width={500}
      height={200}
    />
  );
}

// 退款数量
export function AmountNumberInput(props) {
  const { input, onFieldChange, orderItemIdStr, univalence, maxMoney } = props;
  const AmountNumberInputChange = value => {
    onFieldChange(input.name, value);
    if (value)
      onFieldChange(
        `${orderItemIdStr}-refundMoney`,
        keepDecimal(Math.min(times(value, univalence), maxMoney), 2)
      );
  };
  return <NumberInput value={input.value} onChange={AmountNumberInputChange} {...props} />;
}

// 退款金额
export function SmartMoneyNumberInput(props) {
  const {
    input,
    keyMax,
    realMax,
    relativeAmountDisable,
    disabled,
    orderItemIdStr,
    onFieldChange,
    getRefundRemainAmount,
    isFxOrder = false,
    msg = '',
    isXhsLocalLifeOrder = false,
    isShowRefundAll = false,
    setMax = () => {}
  } = props;
  const isShowInfo = realMax > keyMax && +input.value === +realMax;
  const getRefundMoney = value => {
    if (!isFxOrder) return;
    getRefundRemainAmount &&
      getRefundRemainAmount(value)
        .then(res => {
          const { supplierRefundFee, sellerRefundFee } = res;
          onFieldChange(`${orderItemIdStr}-supplierRefundFee`, supplierRefundFee);
          onFieldChange(`${orderItemIdStr}-sellerRefundFee`, sellerRefundFee);
        })
        .catch(err => {
          Notify.error(err.msg || '供货商,分销商承担金额获取失败');
        });
  };

  return (
    <div>
      <div className="money-input-wrapper">
        <NumberInput
          className="money-input"
          decimal={2}
          value={input.value}
          onChange={input.onChange}
          min={0}
          disabled={relativeAmountDisable || disabled}
          onBlur={value => {
            // keyMax是商品剩余可退的金额，如果超过了 要把运费一起退了
            if (value >= keyMax) {
              getRefundMoney(realMax);
              return input.onChange(`${realMax}`);
            }
            if (!value) {
              input.onChange('0');
              onFieldChange(`${orderItemIdStr}-refundAmountOrWeight`, 0);
            } else {
              getRefundMoney(value);
            }
          }}
        />
        {isXhsLocalLifeOrder && isShowRefundAll ? (
          <TextButton onClick={setMax}>退全部</TextButton>
        ) : null}
      </div>
      {isShowInfo && <div className="info-warn">{msg}</div>}
    </div>
  );
}
