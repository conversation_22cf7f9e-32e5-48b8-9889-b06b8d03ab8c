import React from 'react';
import { Radio } from 'zent';
import { isNil } from 'lodash';

export default class RefundType extends React.Component {
  get refundTypeRadios() {
    const { refundWayDetailDto } = this.props;
    const refundTypeRadios = refundWayDetailDto.map(data => ({
      content: data.refundWay,
      value: data.refundType
    }));
    return refundTypeRadios;
  }

  componentDidMount() {
    const { refundType, onChange } = this.props;
    if (isNil(refundType)) {
      onChange({ refundType: this.refundTypeRadios[0].value });
    }
  }

  render() {
    const { refundType, onChange } = this.props;
    return (
      <div className="refund-radios">
        <span className="refund-type-tag">结算方式：</span>
        <Radio.Group
          className="rf-type-radio-group"
          value={refundType}
          onChange={e => onChange({ refundType: e.target.value })}
        >
          {this.refundTypeRadios.map(item => (
            <Radio key={item.value} value={item.value}>
              {item.content}
            </Radio>
          ))}
        </Radio.Group>
      </div>
    );
  }
}
