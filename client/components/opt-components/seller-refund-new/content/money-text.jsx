import React from 'react';
import { PayCodeType } from 'common/constants/common';

import { createPhasePaymentRefundAmountList } from '../helper/combine-pay';

export default function MoneyText({
  total = 0,
  mainOrderInfo: { buyWay },
  refundType,
  combinePayRemainDetailList
}) {
  const moneyRemainDetail = createPhasePaymentRefundAmountList(
    refundType,
    combinePayRemainDetailList,
    total
  );
  const moneyRemainDetailDescs = moneyRemainDetail.map(
    ({ amount, buyWayDesc }) => `${buyWayDesc} ￥${amount}`
  );
  return (
    <div className="sum-money-text">
      <span className="sum-money-text__label">退款给买家/付款人：</span>
      <div className="sum-money-text__remian-detail">
        <span style={{ color: 'red' }}>￥{total}</span>
        {buyWay === PayCodeType.CombPay && (
          <span style={{ color: '#969799' }}>{`（${moneyRemainDetailDescs.join(' + ')}）`}</span>
        )}
      </div>
    </div>
  );
}
