import React from 'react';
import { Icon, Pop } from 'zent';
import { isNil } from 'lodash';

export default class InfoIcon extends React.Component {
  state = { show: false };

  popShow = () => {
    this.setState({ show: true });
  };

  popClose = () => {
    this.setState({ show: false });
  };

  render() {
    const { content = null } = this.props;
    if (isNil(content)) return null;
    return (
      <Pop onShow={this.popShow} onClose={this.popClose} trigger="hover" content={content}>
        <Icon className="info-icon" type={this.state.show ? 'help-circle' : 'help-circle-o'} />
      </Pop>
    );
  }
}
