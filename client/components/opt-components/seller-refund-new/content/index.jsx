import React, { Component } from 'react';
import { get, sumBy } from 'lodash';
import { Button, Notify, Dialog, Alert } from 'zent';
import { createForm, connect, getFormValues } from '@youzan/retail-form';
import { times, deepJsonParse, plus, div } from '@youzan/retail-utils';
import { UmpActivityType, ProductType } from 'common/constants/common';
import { ORDER_TYPE, ORDER_STATE, ReturnType, SaleWay } from 'common/constants/order';
import ReStock from 'components/modals/stock';
import { checkIsMeituanOrder, checkIsXhsLocalLife } from 'common/biz-helper';
import { mergeArrayBy } from 'common/utils';
import { isOnlineOrder } from 'common/helper';
import {
  openPrepaidRefundDialog,
  PrepaidRefundCode
} from 'components/opt-components/prepaid-refund-dialog';

import openWeixinRefundAuthDialog from 'components/opt-components/weixin-refund-auth-dialog';

import accAdd from '@youzan/utils/number/accAdd';
import RefundType from './refund-type';
import RefundAlert from './refund-alert';
import RFType from './refund-order-type';
import Table from './table';
import { RefundReasonField, RefundReasonDescField } from './fields';
import MoneyText from './money-text';
import { getRefundAmountOrWeight, hasExchange } from '../help';
import * as api from '../api';
import { checkAndShowRefundGiftConfirm } from '../helper/refund-gift';
import RefundPostage from './refund-postage';

const { openDialog, closeDialog } = Dialog;

/**
 * 检验是否全为无码商品
 * @param {*} list
 */
const checkSkuNo = list =>
  list.length > 0 &&
  list?.findIndex(tableData => tableData.productType !== ProductType.GoodsTypeNoSku) === -1;

/**
 * 是否展示退款类型
 *
 * @param {Object} params
 */
function hideRefundType({ mainOrderInfo, fetchData }) {
  const { list = [] } = fetchData;

  return (
    (get(mainOrderInfo, 'orderType') === ORDER_TYPE.OFFLINE_PRE_BUY.value &&
      get(mainOrderInfo, 'state') === ORDER_STATE.TO_SEND.value) ||
    checkSkuNo(list)
  );
}

/**
 * 初始化退款类型
 *
 * @param {Object} params
 */
function initType({ isOnline, isVirtual, mainOrderInfo, fetchData }) {
  const { list = [] } = fetchData;
  // 无🐴商品 或者 网店订单
  if (checkSkuNo(list) || isOnline) return 1;
  if (isVirtual) return 0;
  if (hideRefundType({ isOnline, mainOrderInfo, fetchData })) return 1;
  return 0;
}

function getIsPartDeliveryOrder({ itemInfo }) {
  return itemInfo.some(item => item.deliveryedNum > 0 && +item.deliveryedNum !== +item.num);
}

@connect(state => ({ allValues: getFormValues('seller-refund-new')(state) }))
@createForm({ form: 'seller-refund-new' })
class Content extends Component {
  state = {
    refundType: get(this.props, 'fetchData.orderInfo.refundWayDetailDto[0].refundType'),
    refundOrderType: initType(this.props),
    isPartDeliveryOrder: getIsPartDeliveryOrder(this.props),
    tableData: this.getTableData(),
    fetching: false,
    refundId: null,
    selectedArr: this.getTableData(),
    needRefundPostage: 0
  };

  getTableData() {
    const tableData = get(this.props, 'fetchData.list', []);
    const rawItemInfos = get(this.props, 'itemInfo', []);
    const itemInfos = rawItemInfos.map(ele => ({
      ...ele,
      orderItemIdStr: ele.itemIdStr
    }));
    const rstArr = mergeArrayBy('orderItemIdStr', tableData, itemInfos);
    return rstArr.map(ele => {
      const parsedGoodsInfo = deepJsonParse(ele.goodsInfo);
      const isWeight = !!get(parsedGoodsInfo, 'extra.weight');
      return {
        ...ele,
        isWeight
      };
    });
  }

  /** 抖音融合场景 */
  get isDyMix() {
    return get(this.props.fetchData, 'orderInfo.isDyMix', false);
  }

  get orderRemainPostage() {
    return sumBy(get(this.props.fetchData, 'list', []), item => item.remainPostage || 0);
  }

  /** 判断退运费的场景
   *  如果勾选了退运费且在抖音融合场景下，这个值才有效
   */
  get currentOrderRemainPostage() {
    const { needRefundPostage } = this.state;
    if (needRefundPostage === 0 || !this.isDyMix) {
      return 0;
    }

    return this.orderRemainPostage;
  }

  getCurrentRefundMoney() {
    const { allValues, isVirtual } = this.props;
    const { selectedArr } = this.state;
    const allMoney = this.tableDataFilter(selectedArr).reduce((total, currentEle) => {
      const money = get(allValues, `${currentEle.orderItemIdStr}-refundMoney`, 0);

      if (
        this.state.refundOrderType === 0 &&
        getRefundAmountOrWeight(allValues, currentEle) <= 0 &&
        !isVirtual
      ) {
        return total;
      }
      return plus(Number(money), total);
    }, 0);

    if (this.currentOrderRemainPostage) {
      return accAdd(allMoney, div(this.currentOrderRemainPostage, 100));
    }
    return allMoney;
  }

  /**
   * 校验是否为电子卡券
   */
  checkIsVirtualTicket() {
    return this.state.tableData.every(item => item.productType === ProductType.YzVirtualTicket);
  }

  init = () => {
    const initData = {};
    const { isDyMix } = this;
    const { mainOrderInfo, isVirtual, fetchData } = this.props;
    this.setState(
      ({ tableData }) => {
        const hasDelivered = get(fetchData, 'orderInfo.hasDelivered', false);
        const data = tableData.map(item => {
          if (this.props.isFxOrder) {
            initData[`${item.itemIdStr}-refundMoney`] = undefined;
          } else if (get(this.props, 'fetchData.orderInfo.isCouponPay')) {
            initData[`${item.itemIdStr}-refundMoney`] = div(item.remainMoney, 100);
          } else {
            let remainMoney;
            if (!this.props.isAnyExpressed) {
              remainMoney =
                item.remainPostage > 0 && item.remainMoney === item.remainPostage
                  ? /** 仅剩运费时, 填入运费 */ item.remainPostage
                  : item.remainMoney - (item.remainPostage || 0);
            } else {
              remainMoney = item.remainMoney;
            }
            if (isDyMix) {
              remainMoney = item.remainMoney - (item.remainPostage || 0);
            }

            initData[`${item.itemIdStr}-refundMoney`] = div(remainMoney, 100);
          }
          if (hasExchange(item.exchangeRefunds)) {
            item.exchangeRefunds.forEach(({ skuId, remainWeight, remainNum }) => {
              initData[`${item.itemIdStr}-refundAmountOrWeight-${skuId}`] = item.isWeight
                ? div(remainWeight, 1000)
                : remainNum;
            });
          } else {
            initData[`${item.itemIdStr}-refundAmountOrWeight`] = item.isWeight
              ? div(item.remainWeight, 1000)
              : item.remainAmount;
          }
          /**
           * 电子卡券(非三方券)，退款数量、退款金额默认 0
           * isVirtual: 取 supportVirtualTicketReturnNos，false 表示三方券。https://jira.qima-inc.com/browse/ONLINE-659811
           */
          if (this.checkIsVirtualTicket() && isVirtual) {
            initData[`${item.itemIdStr}-refundAmountOrWeight`] = 0;
            initData[`${item.itemIdStr}-refundMoney`] = 0;
          }
          if (this.isFullRefundItem(item) || this.state.isPartDeliveryOrder) {
            if (mainOrderInfo?.isPeriodBuy) {
              return item;
            }
            return { ...item, unControll: true };
          }
          return item;
        });

        let needRefundPostage = 0;
        // 抖音融合订单，且非送达订单才需要叠加运费
        if (!hasDelivered && this.isDyMix) {
          needRefundPostage = 1;
        }
        return { tableData: data, needRefundPostage };
      },
      () => {
        this.props.initialize(initData);
      }
    );
  };

  componentDidMount() {
    this.init();
  }

  isFullRefundItem = () => {
    const isAllRefund =
      get(this.props, 'mainOrderInfo.activityType') === UmpActivityType.GiftCommunity;
    return isAllRefund;
  };

  setSelectedArr = arr => {
    this.setState({
      selectedArr: arr
    });
  };

  getFetchParams = () => {
    const { mainOrderInfo, realPay, allValues, isVirtual } = this.props;
    const isVirtualTicket = this.checkIsVirtualTicket();
    const refundingItems = [];
    let isRefundAllGoodsNum = true;
    let remainNum = 0;
    this.state.selectedArr.forEach(tableItem => {
      const tempItem = {
        orderItemId: tableItem.itemIdStr,
        productType: tableItem.productType
      };
      const itemRefundMoney = get(allValues, `${tableItem.orderItemIdStr}-refundMoney`, 0);

      if (!tableItem.isRefundable) return;

      tempItem.refundFee = this.state.needRefundPostage
        ? accAdd(times(itemRefundMoney, 100), tableItem.remainPostage)
        : times(itemRefundMoney, 100);

      if (this.state.refundOrderType === 0 || (isVirtualTicket && isVirtual)) {
        if (hasExchange(tableItem.exchangeRefunds)) {
          tempItem.exchangeRefundSkus = tableItem.exchangeRefunds.reduce(
            (ret, { skuId, specifications, exchange, properties, serialNos }) => {
              const refundSku = {
                skuId,
                specifications,
                exchange,
                properties,
                serialNos
              };
              const skuRefundAmountOrWeight = Number(
                get(allValues, `${tableItem.orderItemIdStr}-refundAmountOrWeight-${skuId}`, 0)
              );

              if (skuRefundAmountOrWeight > 0) {
                if (tableItem.isWeight) {
                  refundSku.weight = times(skuRefundAmountOrWeight, 1000);
                } else {
                  refundSku.num = skuRefundAmountOrWeight;
                }

                ret.push(refundSku);
              }
              return ret;
            },
            []
          );
          if (tempItem.exchangeRefundSkus.length <= 0) return;
        }
        const itemRefundAmountOrWeight = getRefundAmountOrWeight(allValues, tableItem);
        if (tableItem.isWeight) {
          remainNum = tableItem.remainWeight;
          tempItem.refundWeight = times(itemRefundAmountOrWeight, 1000);
        } else {
          remainNum = tableItem.remainAmount;
          tempItem.refundAmount = itemRefundAmountOrWeight;
        }
        if (itemRefundAmountOrWeight !== remainNum) {
          isRefundAllGoodsNum = false;
        }
        if (itemRefundAmountOrWeight <= 0 && !isVirtual) return;
      }
      refundingItems.push(tempItem);
    });

    const res = {
      sourceOrderNo: mainOrderInfo.orderNo,
      refundType: this.state.refundType,
      // 整单退还是部分退
      returnType:
        mainOrderInfo.saleWay !== SaleWay.Online &&
        times(this.getCurrentRefundMoney(), 100) === realPay &&
        this.state.tableData.length === this.state.selectedArr.length &&
        isRefundAllGoodsNum
          ? ReturnType.FullRefund
          : ReturnType.PartialRefund,
      refundFee: times(this.getCurrentRefundMoney(), 100),
      mode: isVirtual ? 1 : this.state.refundOrderType,
      remark: get(allValues, 'remark', ''),
      refundOrderItems: JSON.stringify(refundingItems),
      refundReason: get(allValues, 'refundResaon', ''),
      checkWechatComplaint: false //  后端要写死
    };

    return res;
  };

  handleRefundFail = err => {
    if (err.code === PrepaidRefundCode.Frozen || err.code === PrepaidRefundCode.CheckOut) {
      openPrepaidRefundDialog(err, { scene: '主动退款' });
      return;
    }
    // 抖音券订单，弹出弹窗再次点击可以强制退款
    if (err.code === 238000004 || err.code === 241000008) {
      const dialogId = 'tiktok-refund-error';
      openDialog({
        dialogId,
        title: '退款说明',
        style: { width: '500px' },
        footer: (
          <>
            <Button onClick={() => closeDialog(dialogId)}>取消</Button>
            <Button
              type="primary"
              onClick={() => {
                closeDialog(dialogId);
                this.handleSubmit({ forceRefundType: 1 });
              }}
            >
              已在三方平台退券，继续退款
            </Button>
          </>
        ),
        children: (
          <p>
            {err.msg || err.message ? (
              err.msg || err.message
            ) : (
              <>
                因抖音平台限制，超过1小时的订单卡券无法撤销核销，需前往抖音来客后台联系人工客服处理，请务必确保已处理后再进行退款！退款后，抖音券会撤销核销，订单支付金额会原路退还给客户，
                <span style={{ color: 'red' }}>风险自担，请商家谨慎操作。</span>
              </>
            )}
          </p>
        ),
        parentComponent: this
      });
      return;
    }
    Notify.error(err.msg || '退款失败');
    this.props.reload();
  };

  handleSubmit = (extraParams = {}) => {
    const { reload, allItems, mainOrderInfo } = this.props;

    setTimeout(async () => {
      const params = this.getFetchParams();

      const refundOrderItems = JSON.parse(params.refundOrderItems);

      if (!refundOrderItems || refundOrderItems.length === 0) {
        return Notify.error('请至少选择一个商品');
      }

      // 判断是美团订单，不能全选（数量大于 1 时）
      if (checkIsMeituanOrder(mainOrderInfo.channelType)) {
        if (refundOrderItems.length > 1 && refundOrderItems.length === allItems.length) {
          return Notify.error('全部退款请使用取消订单来操作');
        }

        if (!this.props.allValues.refundResaon) {
          return Notify.error('请选择退款原因');
        }
      }

      if (
        !(await checkAndShowRefundGiftConfirm({
          mainOrderInfo,
          itemInfos: allItems
        }))
      ) {
        return;
      }

      this.setState({ fetching: true });
      api
        .doRefund({ ...params, ...extraParams })
        .then(({ hasSupply, refundNo, hasReturnToast }) => {
          Notify.success('退款成功');
          // 展示退货入库弹窗的逻辑收敛到后端，保持统一
          if (hasReturnToast) {
            this.setState({
              refundId: refundNo,
              hasSupply
            });
          } else {
            // 判断是否网店订单
            const currentRefundConfig =
              _global.business?.refundStockConfig[
                `${isOnlineOrder({ mainOrderInfo }) ? 'online' : 'offline'}_refund_stock_type`
              ];

            // 退货退款 && 自动退货入库才需要弹
            if (currentRefundConfig === '1' && this.state.refundOrderType === 0) {
              Notify.success('退货及入库成功');
            }

            // 判断是否为网店订单
            setTimeout(() => reload(), 1000);
          }
        })
        .catch(err => {
          this.handleRefundFail(err);
        })
        .finally(() => {
          this.setState({ fetching: false });
        });
    }, 0);
  };

  tableDataFilter = data => {
    if (this.state.refundOrderType === 0 && !this.props.isVirtual) {
      // 退货退款 目前只有门店订单
      if (get(this.props, 'mainOrderInfo.state') === 100) {
        // 门店订单交易完成的，认为商品发货完成
        return data;
      }
      return data.filter(item => item.isSent);
    }
    return data;
  };

  getAlertList() {
    const list = [];

    if (this.props.multiOrderInfo?.useThirdCoupon) {
      // 拆单 + 订单使用了三方券
      list.push({
        content:
          '请注意，单独退款时不会自动退还第三方代金券，只有所有关联订单都完成退款后，系统才会自动退还三方代金券'
      });
    }

    return list;
  }

  render() {
    const {
      isOnline,
      mainOrderInfo,
      payDeduction,
      reload,
      isAnyExpressed,
      fetchData,
      isVirtual,
      onClose,
      isFxOrder
    } = this.props;
    const { isDyMix } = this;
    const refundWayDetailDto = get(fetchData, 'orderInfo.refundWayDetailDto', []);
    const combinePayRemainDetailList = get(fetchData, 'orderInfo.combinePayRemainDetailList', []);

    const virtualTicketRefundDesc = get(fetchData, 'orderInfo.virtualTicketRefundDesc', null);
    const currentMoney = this.getCurrentRefundMoney();

    const refundWayList = get(fetchData, 'orderInfo.refundInfos', []);
    const tip = get(fetchData, 'orderInfo.refundInfos[0].tips', '');
    const isCouponPay = get(fetchData, 'orderInfo.isCouponPay', false);
    const canOnlyRefundAll = get(fetchData, 'orderInfo.canOnlyRefundAll', false);

    const isShowRefundReason =
      !isFxOrder && get(fetchData, 'orderInfo.refundInfos[0].type', 0) !== 4;
    const isMeituanOrder = checkIsMeituanOrder(mainOrderInfo.channelType);
    const isVirtualTicket = this.checkIsVirtualTicket();
    const isXhsLocalLifeOrder = checkIsXhsLocalLife(mainOrderInfo);

    const { hasWechatComplaint } = mainOrderInfo;
    const refundText = this.state.selectedArr?.length ? '退货退款' : '退款';

    const hasDelivered = get(fetchData, 'orderInfo.hasDelivered', false);

    const alertList = this.getAlertList();

    return (
      <div>
        <RefundAlert
          isOnline={isOnline}
          payDeduction={payDeduction}
          buyWay={mainOrderInfo.buyWay}
          buyWayDesc={mainOrderInfo.buyWayDesc}
          fetchData={fetchData}
          isFxOrder={isFxOrder}
          isCouponPay={isCouponPay}
        />
        {tip && !isFxOrder && !isCouponPay ? (
          <Alert type="warning" className="warning">
            订单退货，抵现积分将按照商品分摊的积分量原路回退到买家账户，
            <mark>操作退货后，无需手工加回积分</mark>
          </Alert>
        ) : null}
        {alertList.length > 0
          ? alertList.map(data => {
              return (
                <Alert type="warning" className="warning">
                  {data.content}
                </Alert>
              );
            })
          : null}

        {isFxOrder || hideRefundType(this.props) ? null : (
          <RFType
            list={refundWayList}
            value={this.state.refundOrderType}
            onChange={val => this.setState({ refundOrderType: val })}
          />
        )}
        <Table
          data={this.tableDataFilter(this.state.tableData)}
          isFxOrder={isFxOrder}
          initialize={this.props.initialize}
          showAmount={this.state.refundOrderType === 0 || isMeituanOrder || isVirtualTicket}
          canJump={!isAnyExpressed}
          isVirtual={isVirtual}
          canOnlyRefundAll={canOnlyRefundAll}
          isMeituan={isMeituanOrder}
          isXhsLocalLifeOrder={isXhsLocalLifeOrder}
          change={this.props.change}
          canSelectItem={this.props.canSelectItem}
          setSelectedArr={this.setSelectedArr}
          isVirtualTicket={isVirtualTicket}
          isDyMix={isDyMix}
        />
        {isFxOrder && <Seprater />}
        {isVirtual && virtualTicketRefundDesc ? (
          <div className="warning">{virtualTicketRefundDesc}</div>
        ) : null}
        {isDyMix && (
          <RefundPostage
            remainPostage={this.orderRemainPostage}
            refundPostage={this.state.needRefundPostage}
            onChange={needRefundPostage => {
              this.setState({
                needRefundPostage
              });
            }}
            hasDelivered={hasDelivered}
          />
        )}
        {!isFxOrder && refundWayDetailDto.length > 0 && (
          <RefundType
            onChange={data => this.setState(data)}
            refundType={this.state.refundType}
            refundWayDetailDto={refundWayDetailDto}
          />
        )}
        <Seprater />
        {isShowRefundReason && (
          <RefundReasonField orderNo={mainOrderInfo.orderNo} isMeituan={isMeituanOrder} />
        )}
        <Seprater />
        {isShowRefundReason && <RefundReasonDescField />}
        <Seprater />
        <footer className="refund_dialog_footer">
          <div className="refund_dialog_footer__left">
            <MoneyText
              total={currentMoney}
              refundType={this.state.refundType}
              mainOrderInfo={mainOrderInfo}
              refundWayDetailDto={refundWayDetailDto}
              combinePayRemainDetailList={combinePayRemainDetailList}
            />
          </div>
          <div className="refund_dialog_footer__right">
            <Button onClick={onClose}>取消</Button>
            <Button
              type="primary"
              loading={this.state.fetching}
              onClick={() => {
                // @整单退款-微信校验
                if (hasWechatComplaint) {
                  openWeixinRefundAuthDialog({
                    submit: this.handleSubmit,
                    cancel: onClose,
                    refundText
                  });
                } else {
                  this.handleSubmit();
                }
              }}
            >
              确定退款
            </Button>
            {this.state.refundId && (
              <ReStock
                refundId={this.state.refundId}
                onComponentOpen={onClose}
                hasSupply={this.state.hasSupply}
                supplyMode={mainOrderInfo.supplyMode}
                onComponentClose={() => {
                  reload && reload();
                }}
              />
            )}
          </div>
        </footer>
      </div>
    );
  }
}

function Seprater() {
  return <div className="seprater" />;
}

export default Content;
