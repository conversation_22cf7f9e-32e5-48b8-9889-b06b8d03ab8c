import React, { Component } from 'react';
import { get, isNil } from 'lodash';
import { Grid, Pop, ClampLines, Tag } from 'zent';
import { div } from '@youzan/retail-utils';
import { Field, connect, getFormValues } from '@youzan/retail-form';
import cx from 'classnames';

import { getSkuText, getPropertyText } from 'common/helper';
import EllipsisTag from 'components/ellipsis-tag';

import { EnumComboType } from 'route/query/types';
import { ProductType } from '../../../../common/constants/common';

import accSub from '@youzan/utils/number/accSub';
import { AmountNumberInput, SmartMoneyNumberInput } from './fields';
import { getRefundAmountOrWeight, hasExchange } from '../help';
import { formatAddPrice } from '../../../goods-info/index';

import * as api from '../api';

/** 套餐商品明细
 * @params 套餐子商品列表
 * @return 套餐明细文字，例如：苹果 x1
 */
const comboDetails = comboSubItemInfosArr => {
  const strArr = comboSubItemInfosArr.map(combo => {
    const propDesc = combo?.pros
      ?.map(x => {
        if (x.price) {
          return `${x.valName}${formatAddPrice(x.price)}`;
        }
        return x.valName;
      })
      .flat()
      .filter(x => x)
      .join(',');
    const newAddPrice = formatAddPrice(combo?.addPrice);
    if (combo?.pros?.length) {
      return {
        detailStr:
          `${combo.goodsName || ''} ${
            combo.skuName ? `(${combo.skuName}${newAddPrice},${propDesc})` : `${propDesc}`
            // eslint-disable-next-line no-useless-concat
          }   ` + ` x${combo.num || ''}`
      };
    }
    return {
      detailStr:
        `${combo?.goodsName || ''} ${
          combo?.skuName ? `(${combo?.skuName}${newAddPrice})` : ''
          // eslint-disable-next-line no-useless-concat
        }   ` + ` x${combo?.num || ''}`
    };
  });
  return strArr;
};

/** 套餐商品详情 - 最多显示行数 */
const ComboDetailShowCount = 1;

const renderComboDetail = ({ className, comboStr }) => {
  return comboStr?.length ? (
    <div className={cx('goods-info__combo_detail_str', className)}>
      {comboStr.length < ComboDetailShowCount + 1
        ? comboStr.map(str => {
            return <div className="goods-info__combo_detail_str__info">{str.detailStr}</div>;
          })
        : comboStr.slice(0, 1).map(str => {
            return <div className="goods-info__combo_detail_str__info">{str.detailStr}</div>;
          })}
      {comboStr.length > ComboDetailShowCount && (
        <Pop
          className="combo-detail-pop"
          trigger="hover"
          position="bottom-left"
          content={comboStr.map(str => {
            return <div>{str.detailStr}</div>;
          })}
        >
          <span className="info-link">全部</span>
        </Pop>
      )}
    </div>
  ) : null;
};

const renderTags = ({ className, icons }) => {
  if (icons.length === 0) {
    return null;
  }
  return (
    <div className={cx('tag-list', className)}>
      {icons.map(icon => {
        return (
          <EllipsisTag className="tag-list__item" theme="yellow">
            {icon.text}
          </EllipsisTag>
        );
      })}
    </div>
  );
};

const getComboStr = ({ comboDetail, comboType }) => {
  if (comboDetail) {
    const { comboGroupInfos } = comboDetail;
    // 自选套餐
    if (comboType === EnumComboType.AutoCombo) {
      const comboSubItemInfosArr = comboGroupInfos.map(c => c.comboSubItemInfos).flat();
      return comboDetails(comboSubItemInfosArr);
    }
    // 固定套餐
    if (comboType === EnumComboType.FixedCombo && comboGroupInfos?.length) {
      const { comboSubItemInfos } = comboGroupInfos[0];
      return comboDetails(comboSubItemInfos);
    }
  }
};

@connect(state => ({ allValues: getFormValues('seller-refund-new')(state) }))
class ItemTable extends Component {
  constructor(props) {
    super(props);
    this.state = {
      page: {
        pageSize: 4,
        current: 1,
        total: get(this.props, 'data.length', 0)
      },
      selectedRowKeys: [],
      nowSelectedRows: []
    };
    // 初始化表格
    this.amountTitle = this.props.isVirtual || this.props.isMeituan ? '商品数量' : '可退数量';
    const { isXhsLocalLifeOrder } = props;
    this.columns = [
      {
        title: '商品名称',
        bodyRender: ({ url, title, skuDesc, comboDetail, comboType, icons }) => {
          const comboDetailStr = getComboStr({ comboDetail, comboType });
          return (
            <div className="refund-goods-info">
              <div className="refund-goods-info__item">
                <a href={url}>{title}</a>
              </div>
              {skuDesc ? <p className="normal-desc">{skuDesc}</p> : null}
              {renderComboDetail({
                className: 'refund-goods-info__item',
                comboStr: comboDetailStr
              })}
              {renderTags({
                className: 'refund-goods-info__item',
                icons
              })}
            </div>
          );
        }
      },
      {
        title: '可退金额(元)',
        width: 120,
        textAlign: 'right',
        bodyRender: ({ remainMoney, remainPostage, remainPackagingFee, remainServiceFee }) => {
          const moneyValue = this.props.isDyMix ? accSub(remainMoney, remainPostage) : remainMoney;
          return (
            <div className="refund-goods-info">
              <span>{div(moneyValue, 100)}</span>
              {!this.props.isDyMix && +remainPostage > 0 ? (
                <>
                  <br />
                  <span className="normal-desc">
                    包含运费
                    {div(remainPostage, 100)}元
                  </span>
                </>
              ) : null}
              {+remainPackagingFee > 0 ? (
                <>
                  <br />
                  <span className="normal-desc">
                    包含打包费
                    {div(remainPackagingFee, 100)}元
                  </span>
                </>
              ) : null}

              {/* @TODO: 服务费退款 */}
              {+remainServiceFee > 0 ? (
                <>
                  <br />
                  <span className="normal-desc">包含服务费{div(remainServiceFee, 100)}元</span>
                </>
              ) : null}
            </div>
          );
        }
      },
      {
        title: this.amountTitle,
        width: 100,
        textAlign: 'right',
        bodyRender: ({ remainAmount, remainWeight, isWeight, amount }) => {
          if (this.props.isVirtual) return amount;

          if (isNil(remainAmount) && isNil(remainWeight)) return '-';
          const maxNumber = isWeight ? div(remainWeight, 1000) : remainAmount;
          return (
            <div className="refund-goods-info">
              {maxNumber}
              {isWeight ? 'kg' : ''}
            </div>
          );
        }
      },
      {
        title: '退款数量',
        width: 150,
        bodyRender: row => {
          const {
            id,
            remainAmount,
            remainWeight,
            isWeight,
            orderItemIdStr,
            remainMoney,
            isRefundable,
            unControll,
            exchangeRefunds,
            realPrice,
            amount
          } = row;
          const { isVirtualTicket } = this.props;
          const maxMoney = div(remainMoney, 100);
          const totalPrice = div(realPrice, 100);
          const getField = (maxNumber, name) => (
            <Field
              /**
               * 单价
               * 电子卡券单价：总价 / 商品数量。部分核销时，可退金额返回总价。
               * 其他：可退金额 / 可退数量
               */
              univalence={isVirtualTicket ? div(totalPrice, amount) : div(maxMoney, maxNumber)}
              orderItemIdStr={orderItemIdStr}
              disabled={
                maxNumber <= 0 ||
                unControll ||
                !isRefundable ||
                (!isXhsLocalLifeOrder && this.props.canOnlyRefundAll) ||
                !this.state.selectedRowKeys.includes(id)
              }
              showStepper
              min={0}
              stepperVal={isWeight ? 0.01 : 1}
              decimal={isWeight ? 2 : 0}
              max={maxNumber || 0}
              name={name}
              component={AmountNumberInput}
              onFieldChange={this.props.change}
              maxMoney={maxMoney}
            />
          );

          if (hasExchange(exchangeRefunds)) {
            return (
              <>
                {exchangeRefunds.map(
                  ({
                    remainWeight,
                    remainNum,
                    skuId,
                    exchange,
                    specifications,
                    properties = []
                  }) => {
                    const maxNumber = isWeight ? div(remainWeight, 1000) : remainNum;

                    return (
                      <div className="refund-num-wrapper" key={skuId}>
                        <Pop {...this.getPopProps(maxNumber, remainMoney)}>
                          {getField(maxNumber, `${orderItemIdStr}-refundAmountOrWeight-${skuId}`)}
                        </Pop>
                        <div className="refund-sku">
                          <ClampLines
                            lines={1}
                            text={`可退数量：${maxNumber}（${getSkuText(
                              specifications,
                              '',
                              '、'
                            )}${getPropertyText(properties, '、')}）`}
                          />
                          {exchange && (
                            <Tag theme="blue" outline>
                              换货
                            </Tag>
                          )}
                        </div>
                      </div>
                    );
                  }
                )}
              </>
            );
          }

          const maxNumber = isWeight ? div(remainWeight, 1000) : remainAmount;
          return (
            <Pop key={`${orderItemIdStr}Amount`} {...this.getPopProps(maxNumber, remainMoney)}>
              <div>
                <div>{getField(maxNumber, `${orderItemIdStr}-refundAmountOrWeight`)}</div>
                {this.props.isVirtual && (
                  <div style={{ position: 'absolute' }} className="normal-desc">
                    {`最多可退为${maxNumber}`}
                  </div>
                )}
              </div>
            </Pop>
          );
        }
      },
      {
        title: '退款金额(元)',
        width: isXhsLocalLifeOrder ? 145 : 120,
        bodyRender: row => {
          const {
            id,
            remainAmount,
            remainWeight,
            isWeight,
            orderItemIdStr,
            remainMoney,
            unControll,
            remainPostage = 0,
            exchangeRefunds,
            ...other
          } = row;
          const { amount } = other;
          const maxNumber = isWeight ? div(remainWeight, 1000) : remainAmount;
          // 换货时，不允许退款全部
          const keyMax =
            !this.props.isDyMix && this.props.canJump
              ? div(remainMoney - remainPostage, 100)
              : div(remainMoney, 100);

          // 非电子卡券的小红书担保支付订单 展示退全部
          const isShowRefundAll = row.productType !== ProductType.YzVirtualTicket;
          return (
            <Pop key={`${orderItemIdStr}Money`} {...this.getPopProps(maxNumber, remainMoney, true)}>
              <div>
                <Field
                  disabled={
                    isXhsLocalLifeOrder ||
                    remainMoney <= 0 ||
                    unControll ||
                    !this.state.selectedRowKeys.includes(id) ||
                    this.props.canOnlyRefundAll ||
                    this.props.isDyMix
                  }
                  props={{ isXhsLocalLifeOrder, isShowRefundAll }}
                  name={`${orderItemIdStr}-refundMoney`}
                  component={SmartMoneyNumberInput}
                  keyMax={keyMax}
                  realMax={div(remainMoney, 100)}
                  relativeAmountDisable={this.relativeAmountDisable(
                    orderItemIdStr,
                    exchangeRefunds
                  )}
                  setMax={() => this.setMax(row)}
                  isFxOrder={this.props.isFxOrder}
                  msg="当退款金额大于等于商品金额时必须包含运费"
                  onFieldChange={this.props.change}
                  getRefundRemainAmount={refundMoney =>
                    this.getRefundRemainAmount({ refundMoney, ...other })
                  }
                  orderItemIdStr={orderItemIdStr}
                />
              </div>
            </Pop>
          );
        }
      }
    ];
    // 分销订单
    if (props.isFxOrder) {
      this.columns.push(
        {
          textAlign: 'right',
          title: '供货商承担金额(元)',
          bodyRender: ({ orderItemIdStr }) => (
            <div className="refund-goods-info">
              <span>
                {div(
                  get(this.props.allValues, `${orderItemIdStr}-supplierRefundFee`, 0),
                  100
                ).toFixed(2)}
              </span>
            </div>
          )
        },
        {
          textAlign: 'right',
          title: '分销商承担金额(元)',
          bodyRender: ({ orderItemIdStr }) => (
            <div className="refund-goods-info">
              <span>
                {div(
                  get(this.props.allValues, `${orderItemIdStr}-sellerRefundFee`, 0),
                  100
                ).toFixed(2)}
              </span>
            </div>
          )
        }
      );
    }
  }

  static getDerivedStateFromProps(nextProps, state) {
    const { data } = nextProps;
    return {
      page: {
        ...state.page,
        total: data.length
      }
    };
  }

  componentDidMount() {
    this.getSelectedKeys();
  }

  setMax(row) {
    const { orderItemIdStr, remainMoney, remainAmount, remainWeight, isWeight } = row;
    const maxNumber = isWeight ? div(remainWeight, 1000) : remainAmount;
    const maxMoney = div(remainMoney, 100);

    this.props.change(`${orderItemIdStr}-refundAmountOrWeight`, maxNumber);
    this.props.change(`${orderItemIdStr}-refundMoney`, maxMoney);
  }

  getRefundRemainAmount = ({ itemIdStr, refundMoney, orderNo, kdtId }) => {
    const params = {
      orderItemId: itemIdStr,
      refundFee: +(+refundMoney * 100).toFixed(),
      orderNo,
      // 订单对应的kdtId 不是当前的
      kdtId
    };
    return api.getShareOfRefundFee(params);
  };

  getSelectedKeys = () => {
    // 过滤掉disable选项
    const filterDisable = this.props.data.filter(item => item.isRefundable);
    this.setState({
      selectedRowKeys: filterDisable.map(item => item.id),
      nowSelectedRows: filterDisable
    });
  };

  // 两个数组取不同并过滤不可退的选项
  getArrDifference = (arr1, arr2) => {
    return arr1.concat(arr2).filter((v, i, arr) => {
      return arr.indexOf(v) === arr.lastIndexOf(v) && v.isRefundable;
    });
  };

  // 对象数组去重并过滤不可退的选项
  getEffectiveArr = arr => {
    const result = [];
    const obj = {};
    for (let i = 0; i < arr.length; i += 1) {
      if (!obj[arr[i].id]) {
        result.push(arr[i]);
        obj[arr[i].id] = true;
      }
    }
    return result.filter(item => item.isRefundable);
  };

  selectChange = (selectedRowKeys, selectedRows, currentRow) => {
    const { setSelectedArr, data } = this.props;

    // 批量操作
    if (Array.isArray(currentRow)) {
      // 当页全部取消勾选
      if (selectedRows.length === 0) {
        this.setState(
          prevState => {
            return {
              nowSelectedRows: this.getArrDifference(
                prevState.nowSelectedRows,
                this.getTableData(data)
              )
            };
          },
          () => setSelectedArr(this.state.nowSelectedRows)
        );
      } else {
        // 当页全选
        this.setState(
          prevState => {
            return {
              nowSelectedRows: this.getEffectiveArr(
                prevState.nowSelectedRows.concat(this.getTableData(data))
              )
            };
          },
          () => setSelectedArr(this.state.nowSelectedRows)
        );
      }
    } else {
      // 单行操作
      const { id } = currentRow;

      // 单行选中
      if (selectedRowKeys.includes(id)) {
        this.setState(
          prevState => {
            return { nowSelectedRows: prevState.nowSelectedRows.concat(currentRow) };
          },
          () => setSelectedArr(this.state.nowSelectedRows)
        );
      } else {
        // 单行取消
        this.setState(
          prevState => {
            return {
              nowSelectedRows: this.getArrDifference(prevState.nowSelectedRows, [currentRow])
            };
          },
          () => setSelectedArr(this.state.nowSelectedRows)
        );
      }
    }

    this.setState(prevState => ({
      selectedRowKeys: prevState.nowSelectedRows.map(item => {
        return item.id;
      })
    }));
  };

  relativeAmountDisable = (orderItemIdStr, exchangeRefunds) => {
    const { isVirtual, showAmount, allValues } = this.props;
    if (isVirtual) return false;
    return showAmount
      ? getRefundAmountOrWeight(allValues, { itemIdStr: orderItemIdStr, exchangeRefunds }) <= 0
      : false;
  };

  pageChange = page => {
    this.setState(({ page: prevPage }) => ({
      page: {
        ...prevPage,
        ...page
      }
    }));
  };

  getTableData(data = []) {
    const { current, pageSize } = this.state.page;
    const start = (current - 1) * pageSize;
    const end = start + pageSize;
    const addID = data.map((item, i) => {
      return Object.assign(item, { id: i });
    });
    return addID.filter((ele, index) => index >= start && index < end);
  }

  getPopProps(amount, money, isMoneyTd) {
    const props = {
      trigger: 'none',
      content: null,
      visible: false
    };

    if (this.props.isVirtual) return props;

    if (amount === 0 && this.props.showAmount) {
      props.visible = undefined;
      props.trigger = 'hover';
      props.content = '最大可退数量为0，不可输入';
    }

    if (money === 0 && isMoneyTd) {
      props.trigger = 'hover';
      props.visible = undefined;
      props.content = '最大可退金额为0，不可输入';
    }

    return props;
  }

  render() {
    const { showAmount, isVirtualTicket, isVirtual, canOnlyRefundAll, isXhsLocalLifeOrder } =
      this.props;
    const tempColumns = [...this.columns];
    let columns = showAmount
      ? tempColumns
      : tempColumns.filter(ele => !(ele.title === '退款数量' || ele.title === this.amountTitle));
    // 如果是电子卡券（三方券），不展示退款数量
    if (isVirtualTicket && !isVirtual) {
      columns = columns.filter(ele => ele.title !== '退款数量');
    }
    return (
      <Grid
        onChange={this.pageChange}
        columns={columns}
        paginationType="lite"
        datasets={this.getTableData(this.props.data)}
        pageInfo={!this.props.isFxOrder && this.state.page}
        emptyLabel="没有可退款的商品"
        selection={
          !this.props.isFxOrder &&
          this.props.canSelectItem && {
            selectedRowKeys: this.state.selectedRowKeys,
            onSelect: this.selectChange,
            getCheckboxProps: data => ({
              disabled: !data.isRefundable || (!isXhsLocalLifeOrder && this.props.canOnlyRefundAll)
            })
          }
        }
      />
    );
  }
}

export default ItemTable;
