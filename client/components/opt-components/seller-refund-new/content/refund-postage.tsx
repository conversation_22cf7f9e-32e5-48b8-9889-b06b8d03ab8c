import { div } from '@youzan/retail-utils';
import React from 'react';
import { Radio } from 'zent';

const RefundPostage = ({
  refundPostage,
  onChange,
  remainPostage,
  hasDelivered
}: {
  refundPostage: number;
  onChange: (e: number) => unknown;
  remainPostage: number;
  hasDelivered: boolean;
}) => {
  return (
    <div className="refund-postage">
      <span className="refund-postage-tag">退还运费：</span>
      <Radio.Group
        disabled={!hasDelivered}
        className="rf-type-radio-group"
        value={refundPostage}
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        onChange={e => onChange(e.target.value!)}
      >
        <Radio value={1}>是{`（运费${div(remainPostage || 0, 100)}元）`}</Radio>
        <Radio value={0}>否</Radio>
      </Radio.Group>
    </div>
  );
};

export default RefundPostage;
