import React from 'react';
import { Radio } from 'zent';

export default function RefundOrderRadio(props) {
  const { value, onChange, list } = props;

  if (list?.length > 0) {
    return (
      <div className="refund-order-type">
        <span className="refund-type-tag block-radio-label">退款方式：</span>
        {list.length === 1 ? (
          <span>{list[0].title}</span>
        ) : (
          <Radio.Group
            className="refund-order-radio"
            value={value}
            onChange={e => onChange(e.target.value)}
          >
            {list.map(item => (
              <Radio key={item.mode} value={item.mode}>
                {item.title}
              </Radio>
            ))}
          </Radio.Group>
        )}
      </div>
    );
  }

  return null;
}
