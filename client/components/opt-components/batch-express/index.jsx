import React from 'react';
import { omit } from 'lodash';

import { openModal } from 'components/modals';

import { optTypeMap } from '../type-map';

class BatchExpress extends React.Component {
  handleOpenDialog = () => {
    openModal('batch-express', omit(this.props, 'operation'));
  };

  render() {
    const { operation } = this.props;

    const OptTypeCpm = optTypeMap[operation.type];
    return <OptTypeCpm {...operation} onClick={this.handleOpenDialog} />;
  }
}

export default BatchExpress;
