/* eslint-disable react/jsx-props-no-spreading */
import React from 'react';
import { Dialog, Notify } from 'zent';
import { get } from 'lodash';
import PackInfo from 'components/details-cpn/pack-info/pack-info';

import { LinkButton } from '../type-map';
import style from './style.scss';
import * as api from './api';

/**
 * 查看同城配送和快递详情
 */
export default class CheckLocalAndExpressDeliveryDetail extends React.Component {
  state = {
    show: false,
    packInfo: null
  };

  reload = () => {
    api
      .getPacksInfo({ orderNo: get(this.props, 'options.orderNo') })
      .then(rst => {
        const data = get(rst, 'expressInfo.packs', []);
        const pack = data.find(ele => ele.packId === get(this.props, 'options.packInfo.packId'));
        this.setState({ packInfo: pack });
      })
      .catch(err => {
        Notify.error(err.msg || '获取包裹信息异常');
      });
  };

  open = () => {
    this.reload();
    this.setState({ show: true });
  };

  handlePackInfoReload = () => {
    this.reload();
    this.props.reload();
  };

  render() {
    return (
      <>
        <LinkButton onClick={this.open} {...this.props} text=" 查看详情" />
        {this.state.show && (
          <Dialog
            className={style['pack-detail']}
            visible
            title="配送详情"
            onClose={() => {
              this.setState({ show: false });
            }}
          >
            {this.state.packInfo && (
              <PackInfo
                {...this.props}
                reload={this.handlePackInfoReload}
                packInfo={this.state.packInfo}
                showButton={false}
              />
            )}
          </Dialog>
        )}
      </>
    );
  }
}
