import { request } from '@youzan/retail-utils';

const urlMap = {
  fetchStoreAddr: '/youzan.retail.trademanager.order.delivery/1.0.0/findshop',
  cancelDelivery: 'youzan.retail.trademanager.delivery/1.0.0/cancel',
  fetchDefaultAddress: `${window._global.url.www}/setting/shopAddress/defaultAddress.json`,
  getReasons: '/youzan.retail.trademanager.cancel.reasons/1.0.0/get',
  getDeductFee: 'youzan.retail.trademanager.delivery.deduct.fee/1.0.0/get'
};

export const cancelDelivery = data =>
  request({
    url: urlMap.cancelDelivery,
    method: 'post',
    data
  });

export const getDeductFee = data =>
  request({
    url: urlMap.getDeductFee,
    method: 'get',
    data
  });

// 获取默认地址
export function fetchDefaultAddress(data) {
  return request({
    url: urlMap.fetchDefaultAddress,
    data
  });
}

// 获取门店地址
export function fetchStoreAddress(data) {
  return request({
    url: urlMap.fetchStoreAddr,
    data
  });
}

export function getReasons(data) {
  return request({
    url: urlMap.getReasons,
    data
  });
}
