/* eslint-disable react/jsx-props-no-spreading */

import React from 'react';
import { get } from 'lodash';
import { global } from '@youzan/retail-utils';
import cx from 'classnames';
import { Button, Dialog, Notify, Alert } from 'zent';
import { Select } from '@zent/compat';
import * as api from './api';
import typeMap from '../type-map';
import style from './style.scss';

class CancelDelivery extends React.Component {
  state = {
    showPop: false,
    reasonId: null,
    reasonList: [],
    showError: false
  };

  cancelDelivery = () => {
    if (this.state.reasonId === null) {
      this.setState({ showError: true });
      return;
    }
    const reload = get(this.props, 'options.reload', () => {});
    const reasonObj = this.state.reasonList.find(ele => ele.id === this.state.reasonId);
    this.setState({ loading: true });
    api
      .cancelDelivery({
        orderNo: get(this.props, 'options.orderNo'),
        packId: get(this.props, 'options.packInfo.packId'),
        operatorName: get(global, 'USER_INFO.staffName', ''),
        cancelReason: reasonObj.reason,
        cancelReasonId: reasonObj.id,
        currentStatus: get(this.props, 'options.packInfo.takeoutExpressDetail.statusCode')
      })
      .then(rst => {
        if (rst) {
          Notify.success('取消呼叫成功');
        }
      })
      .catch(err => {
        Notify.error(err.msg || '取消呼叫失败');
      })
      .finally(() => {
        this.setState({ loading: false });
        this.setPopInvisible();
        reload();
      });
  };

  changeReason = event => {
    this.setState({ reasonId: get(event, 'target.value', null), showError: false });
  };

  renderPopContent() {
    return (
      <div className={style['cancel-localdelivery-dialog']}>
        {this.state.deductFeeDesc && <Alert type="warning">{this.state.deductFeeDesc}</Alert>}
        <span className="tips">*</span>
        取消原因：
        <Select
          className={cx('reason-select', {
            'reason-select_error': this.state.showError
          })}
          data={this.state.reasonList}
          optionText="reason"
          optionValue="id"
          value={this.state.reasonId || ''}
          onChange={this.changeReason}
        />
        {this.state.showError && <div className="warn">请选择取消原因</div>}
      </div>
    );
  }

  setPopVisible = () => {
    const keyPath = 'options.packInfo.takeoutExpressDetail';

    this.state.reasonList.length <= 0 &&
      api
        .getReasons({
          deliveryChannel: get(this.props, `${keyPath}.deliveryChannel`),
          appId: get(this.props, `${keyPath}.appId`)
        })
        .then(rst => {
          this.setState({
            reasonList: rst.reasonModelList
          });
        });

    api
      .getDeductFee({
        orderNo: get(this.props, 'options.orderNo'),
        packId: get(this.props, 'options.packInfo.packId')
      })
      .then(rst => {
        this.setState({ deductFeeDesc: rst.deductFeeDesc });
      })
      .catch(err => {
        Notify.error(err.msg || '获取违约金失败');
      });
    this.setState({ showPop: true });
  };

  setPopInvisible = () => {
    this.setState({ showPop: false });
  };

  render() {
    const { operation } = this.props;
    const OptTypeCpm = typeMap[operation.type];
    return (
      <div>
        <OptTypeCpm
          {...operation}
          buttonType="default"
          onClick={() => {
            this.setPopVisible();
          }}
        />
        <Dialog
          visible={this.state.showPop}
          onClose={() => this.setState({ showPop: false })}
          title="取消订单呼叫"
          footer={
            <div className="opreations">
              <Button onClick={this.setPopInvisible}>取消</Button>
              <Button loading={this.state.loading} onClick={this.cancelDelivery} type="primary">
                确定
              </Button>
            </div>
          }
        >
          {this.renderPopContent()}
        </Dialog>
      </div>
      // </Pop>
    );
  }
}

export default CancelDelivery;
