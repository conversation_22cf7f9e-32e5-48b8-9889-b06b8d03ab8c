import React, { Component } from 'react';
import { Notify } from 'zent';
import { global } from '@youzan/retail-utils';
import { get } from 'lodash';
import typeMap from '../type-map';
import * as api from './api';

const { USER_INFO } = global;
export default class Recall extends Component {
  state = {
    loading: false
  };

  click = () => {
    this.setState({ loading: true });
    api
      .recall({
        orderNo: get(this.props, 'options.orderNo'),
        packId: get(this.props, 'options.packInfo.packId'),
        deliveryChannel: get(this.props, 'options.packInfo.takeoutExpressDetail.deliveryChannel'),
        deliveryTool: get(this.props, 'options.packInfo.takeoutExpressDetail.deliveryTool'),
        operatorName: USER_INFO.staffName
      })
      .then(() => {
        Notify.success('重新呼叫成功');
      })
      .catch(err => {
        Notify.error(err.msg || '重新呼叫失败');
      })
      .finally(() => {
        this.setState({ loading: false });
        this.props.options.reload();
      });
  };

  render() {
    const Opt = typeMap[get(this.props, 'operation.type')];
    return (
      <Opt
        text={get(this.props, 'operation.text')}
        loading={this.state.loading}
        onClick={this.click}
      />
    );
  }
}
