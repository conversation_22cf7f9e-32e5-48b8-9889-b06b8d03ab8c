import React from 'react';
import { Notify } from 'zent';
import { get } from 'lodash';
import { global } from '@youzan/retail-utils';
import * as api from './api';
import typeMap from '../type-map';

const { USER_INFO } = global;
export default function CancelAutoCall(props) {
  const { operation } = props;
  const OptTypeCpm = typeMap[operation.type];
  return (
    <OptTypeCpm
      {...operation}
      onClick={() => {
        const reload = get(props, 'options.reload', () => {});
        api
          .cancelAutoCall({
            orderNo: get(props, 'options.orderNo', ''),
            operatorName: USER_INFO.staffName,
            deliveryNo: get(props, 'options.deliveryNo', '')
          })
          .then(() => {
            Notify.success('取消自动呼叫成功');
          })
          .catch(err => {
            Notify.error(err.msg || '取消自动呼叫失败');
          })
          .finally(() => {
            reload();
          });
      }}
    />
  );
}
