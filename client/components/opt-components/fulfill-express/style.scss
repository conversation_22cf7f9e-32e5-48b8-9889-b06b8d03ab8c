@import '~shared/style';

$label-width: 100px;

:local(.express-dialog) {
  width: 800px;

  .warning {
    margin-bottom: 10px;
  }

  .zent-dialog-r-title {
    margin-bottom: 10px;
  }
}

:local(.express-content) {
  .amount-content {
    margin-top: 10px;
  }

  .zent-tabs {
    margin-bottom: 10px;
  }

  .pl90 {
    padding-left: 90px;
  }

  .express-content-scroller {
    max-height: 600px;
    overflow: auto;
  }

  .express-content-item {
    margin-bottom: 15px;
    display: flex;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .city-delivery-text {
      margin-left: 8px;
      position: relative;
      top: -1px;
    }

    &.actions {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 16px;

      .disable-desc {
        margin-right: 10px;
      }

      .agree-protocol {
        margin-right: 10px;
        display: flex;
        align-items: center;

        .zent-checkbox-wrap {
          margin-right: 5px;
        }
      }
    }

    &.spread {
      margin-top: 15px;
    }

    &__label {
      flex-basis: $label-width;
      color: $color-text-primary;
    }

    &__express-company {
      &-form {
        .zent-form-label {
          flex-basis: 80px;
        }
      }

      &-container {
        width: 538px;
        max-height: 234px;
        overflow-y: auto;
        display: flex;
        padding: 12px;
        background: #ffffff;
        border: 1px solid #e0e0e0;
        box-sizing: border-box;
        align-content: flex-start;
        flex-wrap: wrap;
        padding-right: 5px;
        padding-bottom: 0;
      }

      &-item {
        width: 166px;
        height: 62px;
        background-color: #f7f7f7;
        font-size: 14px;
        font-weight: bold;
        margin-right: 7px;
        padding-left: 8px;
        padding-top: 4px;
        box-sizing: border-box;
        flex-shrink: 0;
        position: relative;
        border-radius: 2px;
        border: 1px solid #f7f7f7;
        cursor: pointer;
        margin-bottom: 12px;

        .title {
          display: flex;
          align-items: center;
        }

        .recommend {
          width: 48px;
          height: 18px;
          font-size: 12px;
          line-height: 18px;
          color: #d42f15;
          margin-left: 8px;
          border: 1px solid #d42f15;
          padding: 0 3px;
          border-radius: 3px;
          font-weight: normal;
        }

        .tips {
          color: #999999;
          font-size: 12px;
          transform: scale(0.92);
          font-weight: normal;
          margin-left: -7px;
          margin-top: 8px;
        }
      }

      &-item-checked {
        border: 1px solid #155bd4;
      }

      &-item-checked::after {
        content: '';
        display: inline-block;
        position: absolute;
        width: 30px;
        height: 30px;
        bottom: -1px;
        right: -1px;
        background: url(https://img01.yzcdn.cn/upload_files/2020/09/22/FuT1oDUt4XIF8g5jv4d-LBy2nhda.png)
          no-repeat;
        background-size: 30px auto;
      }
    }

    &__value {
      &.weight-input {
        display: flex;

        .zent-number-input-wrapper {
          margin-right: 10px;
        }

        .after-input-text {
          margin-left: 10px;
        }
      }

      .express-companys {
        width: 180px;
      }

      .express-address {
        width: 300px;
      }

      &.selffetch {
        display: flex;
        align-items: center;
      }
    }

    .express-content-item__value {
      flex: 1;
    }

    &_goods-scan-input {
      display: inline-block;
      vertical-align: middle;
    }
  }

  .express-content-extra {
    margin-left: 90px;

    .express-content-item__label {
      text-align: left;
    }
  }

  .express-type__item {
    vertical-align: middle;
  }

  .express-weight {
    margin-bottom: 15px;
    line-height: 30px;

    .express-content-item:last-child {
      margin-bottom: 0;
    }

    .check-address {
      color: $color-warn;
      font-size: $font-size-small;
      padding-left: 10px;
    }
  }

  .postage-info {
    margin-bottom: 15px;
    padding-left: 10px;
    font-size: $font-size-small;
  }

  .orange {
    color: $color-warn;
  }

  .goods-iemi.enabled,
  .goods-lot-code.enabled {
    color: $color-link;
    cursor: pointer;
  }

  .goods-sku {
    color: $color-text-light-primary;
  }

  .delivery-extra {
    &-content {
      display: flex;
      flex-direction: column;

      &__label {
        flex-basis: $label-width;
      }

      &__value {
        flex: 1;
        margin-top: 5px;
      }

      &__info {
        width: 100%;
        margin-left: 100px;
        color: $color-text-secondary;
        margin-top: 4px;
        display: flex;
        align-items: center;
      }

      &__desc {
        margin: 10px 0;
        padding: 14px;
        background-color: #f7f8fa; /* stylelint-disable-line sh-waqar/declaration-use-variable */
      }
      .delivery-price {
        display: flex;
        align-items: normal;
        .delivery-extra-content__value_1 {
          display: flex;

          .exception {
            color: #d42f15;
          }

          .error-span {
            margin-left: 4px;
            padding: 0 4px;
            color: rgb(237, 106, 24);
            border: 1px solid rgb(237, 106, 24);
            border-radius: 2px;
          }
        }
      }

      .input-item {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        &.auto-call {
          align-items: start;
        }

        .zent-radio-group {
          display: inline-flex;
          align-items: center;

          .zent-radio-wrap,
          .zent-radio + span {
            display: inline-flex;
            align-items: center;

            .zent-input-wrapper {
              margin-right: 10px;
            }
          }
        }

        .delivery-companys {
          width: 180px;
        }

        .delivery-baojia {
          width: 300px;
        }

        .delivery-custom-weight {
          width: 130px;
        }

        .delivery-extra-content__value {
          display: flex;
          flex-direction: column;

          .tips {
            color: $color-warn;
          }

          .exception {
            color: #d42f15;
          }
          &__pop-info {
            margin-left: 4px;
            height: 24px;
            line-height: 24px;
            .zent-pop-v2-inner {
              width: 840px;
            }
          }
        }

        .delivery-extra-content__value_1 {
          display: flex;

          .exception {
            color: #d42f15;
          }

          .error-span {
            margin-left: 4px;
            padding: 0 4px;
            color: rgb(237, 106, 24);
            border: 1px solid rgb(237, 106, 24);
            border-radius: 2px;
          }
        }

        &:last-child {
          flex: 1;
          margin-bottom: 0;
        }
      }

      .dist-Weight {
        .delivery-extra-content__value {
          flex-direction: row;
          align-items: center;
        }
      }

      .delivery-service {
        .delivery-extra-content__value {
          flex-direction: row;
          align-items: center;
        }
        .zent-checkbox-wrap {
          margin-right: 8px;
        }
      }
    }

    .delivery-extra-autoCall {
      color: $color-success;
      margin-top: 8px;
    }

    .local-delivery-extra__ai-select {
      align-items: flex-start;

      .ai-select__text:last-child {
        margin-top: 8px;
      }

      .ai-select__link {
        margin-left: 100px;
        margin-top: 0;
      }
    }
  }

  .delivery-extra-content__value {
    display: flex;

    .exception {
      color: #d42f15;
    }

    .error-span {
      margin-left: 4px;
      padding: 0 4px;
      color: rgb(237, 106, 24);
      border: 1px solid rgb(237, 106, 24);
      border-radius: 2px;
    }
  }

  .zent-grid {
    margin-bottom: 20px;

    &-tr__expanded td {
      width: 100%;
      padding: 0;
    }

    col:first-child,
    th:first-child,
    td:first-child {
      display: none;
    }

    .zent-grid-td {
      border-right: 0;
    }

    .express-table-expanded {
      border-bottom: 1px solid $border-color-base;
      background-color: $color-white;

      &.indent {
        padding-left: 20px;
      }

      &__content {
        margin: 0;
        padding-left: 45%;
      }

      &__item {
        display: flex;
        align-items: center;
        padding: 10px 16px;
      }
    }
  }

  .zent-table {
    .tr--expanded {
      display: flex !important;
      background-color: $color-white;

      .td {
        width: 100%;
        padding: 0;
      }
    }

    .expanded-item {
      display: none;
    }

    .cell:nth-child(2) {
      padding-left: 10px;
    }

    .cell.cell--selection {
      padding-left: 34px;
    }

    .express-table-expanded {
      margin: 0;
      padding-left: 45%;

      &__item {
        display: flex;
        align-items: center;
        padding: 10px 0 10px;
      }
    }
  }

  .spread-content {
    overflow: hidden;

    .spread-info {
      float: left;
      margin-right: 13px;
    }
  }

  .spread-price {
    color: $color-alert;
  }
}

.mb5 {
  margin-bottom: 5px;
}

.dialog-goods-info {
  padding: 0;
}

.selfFetch-margin {
  margin-left: 8px;
}

.select-custom {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  .select-custom-btn {
    font-size: $font-size-base;
    color: $color-b4;
  }
  .select-custom-text {
    margin-left: 8px;
    font-size: $font-size-base;
    color: $color-n6;
  }
}
