import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Dialog, Radio, Pagination, Button, BlockLoading, Notify } from 'zent';
import { isEmpty } from 'lodash';
import GoodsInfo from 'components/goods-info';

import { fetchSpuByPluCode } from '../../api';
import style from './style.scss';

const RadioGroup = Radio.Group;

export const getGoodsScanHandle = () => {
  let keyCodeBuffer = [];
  let timeoutFlag = null;
  return (handle, onSuccess, onFail) => e => {
    // 商品条码只支持大小写字母数字或者横杆，不支持下划线，下划线为被当成横杆处理
    if (e.which === 189) {
      // 横杆
      keyCodeBuffer.push('-');
    } else {
      keyCodeBuffer.push(String.fromCharCode(e.which));
    }
    handle && handle(e);
    clearTimeout(timeoutFlag);
    timeoutFlag = setTimeout(() => {
      if (keyCodeBuffer.length > 10) {
        const code = keyCodeBuffer
          .join('')
          .replace(/(?:\r\n|\r|\n).*$/g, '')
          // eslint-disable-next-line no-control-regex
          .replace(/\u0010/g, '');

        onSuccess && onSuccess(code);
      } else {
        onFail && onFail();
      }
      keyCodeBuffer = [];
    }, 20);
  };
};

const getKeyDownListener = getGoodsScanHandle();
// 扫码识别的商品条码是以24开头的18位数子
const codeReg = /^24\d{16}$/;

const GoodsScanEntry = ({ children, goodsSpreads, itemInfos, onGetGoodsWeight }) => {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [selectedGoods, setSelectedGoods] = useState('');
  const [goodsInfos, setGoodsInfos] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    total: goodsInfos.length,
    pageSize: 6
  });
  const weight = useRef(0);
  const disabled = isEmpty(goodsSpreads);
  const filterMatchGoods = useCallback(
    skuId => {
      return itemInfos.filter(
        goods => goodsSpreads[goods.itemIdStr] && goods.relateSkuId === skuId
      );
    },
    [goodsSpreads, itemInfos]
  );

  useEffect(() => {
    if (disabled) {
      return;
    }
    const handle = getKeyDownListener(null, code => {
      if (loading) {
        return;
      }
      if (!codeReg.test(code)) {
        Notify.error('错误的条码格式');
        return;
      }
      const pluCode = Number(code.slice(2, 7)).toString(10);

      setLoading(true);
      fetchSpuByPluCode({
        pluCode
      })
        .then(({ skuId }) => {
          const matchGoods = filterMatchGoods(skuId);

          weight.current = Number(code.slice(-6, -1));
          setGoodsInfos(matchGoods);
          if (!matchGoods.length) {
            setLoading(false);
            Notify.error('未识别到发货商品');
            return;
          }
          setSelectedGoods(matchGoods[0].itemIdStr);
          if (matchGoods.length > 1) {
            setPagination({ pageSize: 6, total: matchGoods.length, current: 1 });
            setDialogVisible(true);
          } else if (matchGoods.length === 1) {
            setLoading(false);
            onGetGoodsWeight({
              itemIdStr: matchGoods[0].itemIdStr,
              weight: weight.current
            });
          }
        })
        .catch(err => {
          Notify.error(err.msg || '未识别到发货商品');
          setLoading(false);
        });
    });
    // 事件添加到document上，打开发货弹窗即可扫码录入商品重量
    document.addEventListener('keydown', handle, false);
    return () => {
      document.removeEventListener('keydown', handle, false);
    };
  }, [disabled, onGetGoodsWeight, loading, filterMatchGoods]);

  const onCloseDialog = () => {
    setLoading(false);
    setDialogVisible(false);
  };

  const onConfirmSelect = () => {
    onCloseDialog();
    onGetGoodsWeight({
      itemIdStr: selectedGoods,
      weight: weight.current
    });
  };

  if (disabled) {
    return children;
  }

  return (
    <BlockLoading loading={loading} className={style['goods-scan-loading']}>
      <Dialog
        className={style['goods-scan-entry']}
        visible={dialogVisible}
        title="请选择正确的商品"
        onClose={onCloseDialog}
        footer={[
          <Button key="cancel" onClick={onCloseDialog}>
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={onConfirmSelect}>
            确定
          </Button>
        ]}
      >
        <RadioGroup
          value={selectedGoods}
          onChange={e => {
            setSelectedGoods(e.target.value);
          }}
          className="goods-radios"
        >
          {goodsInfos &&
            goodsInfos.map(goods => {
              return (
                <Radio value={goods.itemIdStr} key={goods.itemIdStr} className="goods-radio">
                  <GoodsInfo {...goods} />
                </Radio>
              );
            })}
        </RadioGroup>
        <Pagination
          className="goods-select-pag"
          {...pagination}
          onChange={({ current }) => {
            setPagination({ ...pagination, current });
          }}
        />
      </Dialog>
      {children}
    </BlockLoading>
  );
};

export default GoodsScanEntry;
