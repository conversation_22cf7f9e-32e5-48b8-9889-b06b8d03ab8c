import * as React from 'react';
import { ReactNode } from 'react';
import cx from 'classnames';

export interface ExpressContentItemProps {
  className?: string;
  label: ReactNode;
  children: ReactNode;
}

/**
 * 发货选项样式组件, 样式看 client/components/opt-components/fulfill-express/style.scss
 */
export function ExpressContentItem({ className, label, children }: ExpressContentItemProps) {
  return (
    <div className={cx('express-content-item', className)}>
      <div className="express-content-item__label">{label}：</div>
      <div className="express-content-item__value">{children}</div>
    </div>
  );
}
