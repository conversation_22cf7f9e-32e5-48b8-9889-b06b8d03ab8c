import React from 'react';
import { Radio, Pop, Icon, Tag } from 'zent';
import { BlankLink } from '@youzan/react-components';
import { ExpressType } from 'common/constants/common';

import { NO_EXPRESS, WX_EXPRESS, WX_ORDER_MARK, THIRD_LOCAL_DELIVERY } from '../constants';

import { trackSelectingExpressType } from '../track';

const WEIXIN_DELIVERY_HELPER = '/v4/trade/wechat-delivery-helper';
const RadioGroup = Radio.Group;

const ExpressTypes = ({
  deliveryTypes = [],
  expressType,
  value: current,
  onChange,
  orderMark,
  thirdDeliveryPromptConfig = {}
}) => {
  return (
    <div className="express-content-item">
      <div className="express-content-item__label">发货方式：</div>
      {deliveryTypes.length > 0 && (
        <RadioGroup
          value={current}
          onChange={({ target: { value } }) => {
            trackSelectingExpressType({
              expressType: value
            });
            onChange(value);
          }}
        >
          {deliveryTypes.map(({ id, text }) => {
            const radioEle = (
              <Radio value={id} key={id}>
                {text}
                {id === THIRD_LOCAL_DELIVERY && thirdDeliveryPromptConfig.message ? (
                  <>
                    <Tag theme="blue" outline className="city-delivery-text">
                      {thirdDeliveryPromptConfig.message}
                    </Tag>
                  </>
                ) : (
                  ''
                )}
              </Radio>
            );
            // 快递发货无需物流 给个气泡提示
            if (expressType === ExpressType.Express && id === NO_EXPRESS) {
              return (
                <Pop
                  trigger="hover"
                  position="top-left"
                  content="选择无需物流后无法更改发货方式，请慎重选择"
                  wrapperClassName="express-type__item"
                >
                  {radioEle}
                </Pop>
              );
            }
            // 快递发货微信物流 给个气泡提示
            if (expressType === ExpressType.Express && id === WX_EXPRESS) {
              const isWxShop = orderMark === WX_ORDER_MARK; // 是否微信小程序订单（仅微信小程序订单支持微信物流）
              return (
                <Radio value={id} disabled={!isWxShop}>
                  <Pop
                    wrapperClassName="express-type__item"
                    trigger="hover"
                    content={
                      <div style={{ width: 280 }}>
                        目前仅支持微信小程序订单使用，更多渠道订单正在接入中，敬请期待…
                      </div>
                    }
                  >
                    <span>{text}</span>
                  </Pop>
                  <Pop
                    trigger="hover"
                    content={
                      <div style={{ width: 280 }}>
                        <ul>
                          <li>· 支持市面主流快递公司在线下单；</li>
                          <li>
                            · 接入微信官方物流通知，帮助消费者快速找到订单，还能提升15%店铺回流；
                          </li>
                        </ul>
                        <BlankLink href={WEIXIN_DELIVERY_HELPER}>立即使用</BlankLink>
                      </div>
                    }
                    position="top-center"
                  >
                    <Icon
                      style={{
                        color: '#c8c9cc',
                        marginLeft: 5,
                        fontSize: 16,
                        verticalAlign: 'middle'
                      }}
                      type="help-circle"
                    />
                  </Pop>
                </Radio>
              );
            }
            return radioEle;
          })}
        </RadioGroup>
      )}
    </div>
  );
};

export default ExpressTypes;
