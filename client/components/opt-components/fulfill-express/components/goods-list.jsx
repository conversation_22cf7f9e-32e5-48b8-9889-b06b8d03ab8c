/* eslint-disable react/jsx-props-no-spreading */

import React from 'react';
import cx from 'classnames';
import { Grid } from 'zent';
import { Form } from '@zent/compat';
import { sortBy, find, pick, toNumber } from 'lodash';
import { div, numberWithCommas } from '@youzan/retail-utils';
import { compressBy } from 'common/fns/shared';
import { ExpressType, DeliveryChannelType } from 'common/constants/common';
import { Link } from 'components/opt-components/type-map';
import IemiInputPop from 'components/iemi-input-pop';
import GoodsInfo from 'components/goods-info';
import { LotCodeSelectorDialogTrigger } from 'components/lot-code-selector/dialog-trigger';

import { isSingleStore } from '@youzan/utils-shop';
import ScanShieldInput from './scan-shield-input';

const { Field } = Form;
const { SelfFetch } = ExpressType;
function renderRefundOption({ orderNo, itemIdStr, fromRefund }) {
  if (fromRefund) {
    return <span>退款中</span>;
  }
  return (
    <Link
      text="退款待处理"
      attributes={{
        url: isSingleStore
          ? `/v2/order/refunddetail#?order_no=${orderNo}&tc_order_item_id=${itemIdStr}`
          : `/v4/trade/refund/detail?orderNo=${orderNo}&itemId=${itemIdStr}`
      }}
    />
  );
}

const spreadDesc = spread => (
  <p className="spread-content">
    <span className="spread-info">
      实发重量：{spread?.formatRealWeight ? `${spread.formatRealWeight}kg` : '--'}
    </span>
    <span className="spread-info">
      重量差值：{spread?.weightDiff ? `${spread.weightDiff}kg` : '--'}
    </span>
    <span className="spread-info spread-price">
      应退差价：{spread?.spreadDiff ? `¥${spread.spreadDiff}` : '--'}
    </span>
  </p>
);

const RealWeightEntry = ({ onGoodsSpreadsChange, goods, spreads, indent }) => {
  const entryLength = Math.min(50, goods.goodsNum);
  return (
    <div className={cx('express-table-expanded', { indent })}>
      <Form className="express-table-expanded__content">
        {Array.from({ length: entryLength }).map((_, index) => {
          return (
            // eslint-disable-next-line react/no-array-index-key
            <div className="express-table-expanded__item" key={index}>
              <Field
                component={ScanShieldInput}
                name={`${goods.itemIdStr}-${index}`}
                placeholder="请输入实发重量"
                addonAfter="kg"
                decimal={3}
                max={999.999}
                min={0.001}
                onChange={(e, value) => {
                  onGoodsSpreadsChange(goods.itemIdStr, index, value);
                }}
                validateOnBlur={false}
                validations={{
                  format(values, value) {
                    return toNumber(value) > 0;
                  }
                }}
                validationErrors={{
                  format: '请输入实际重量'
                }}
                desc={spreadDesc(spreads[index])}
              />
            </div>
          );
        })}
      </Form>
    </div>
  );
};

const GoodsList = props => {
  const {
    orderNo,
    data,
    onSelect,
    selectedItems,
    expressType,
    onSerialNosChange,
    goodsSerialNos,
    goodsSpreads,
    onGoodsSpreadsChange,
    fromRefund,
    channelType,
    isShowSelection,
    selectedLotCodeDataByItemId,
    onSelectedLotCodeDataByItemIdChange
  } = props;

  const onConfirm = itemId => serialNos =>
    onSerialNosChange(
      goodsSerialNos.map(item => {
        if (item.itemId === itemId) {
          return {
            itemId,
            serialNos
          };
        }
        return { ...item };
      })
    );

  const goodsColumns = [
    {
      title: '商品',
      width: '35%',
      bodyRender: goodsInfo => <GoodsInfo {...goodsInfo} className="dialog-goods-info" />
    },
    {
      title: '数量',
      width: '10%',
      bodyRender: ({
        goodsNum,
        itemIdStr,
        isSerialItem,
        deliveryStatus,
        goodsId,
        skuId,
        isLotCodeItem,
        kdtId: orderKdtId
      }) => {
        const { serialNos = [] } = find(goodsSerialNos, { itemId: itemIdStr }) || {};
        const toDelivery = deliveryStatus === 0;
        return (
          <div>
            <p>{goodsNum}</p>
            {isSerialItem && (
              <IemiInputPop
                onChange={onConfirm(itemIdStr)}
                value={serialNos}
                disabled={!toDelivery}
              >
                <span className={cx('goods-iemi', { enabled: toDelivery })}>
                  管理唯一码(
                  {serialNos.length})
                </span>
              </IemiInputPop>
            )}
            {isLotCodeItem && (
              <LotCodeSelectorDialogTrigger
                goodsId={goodsId}
                skuId={skuId}
                orderNo={orderNo}
                orderKdtId={orderKdtId}
                orderItemId={itemIdStr}
                value={selectedLotCodeDataByItemId[itemIdStr]}
                max={goodsNum}
                disabled={!toDelivery}
                onChange={value =>
                  onSelectedLotCodeDataByItemIdChange({
                    ...selectedLotCodeDataByItemId,
                    [itemIdStr]: value
                  })
                }
              >
                <span className={cx('goods-lot-code', { enabled: toDelivery })}>
                  选择批号(
                  {(selectedLotCodeDataByItemId[itemIdStr]?.selectedLotCodes || []).length})
                </span>
              </LotCodeSelectorDialogTrigger>
            )}
          </div>
        );
      }
    },
    {
      title: '实付款',
      width: '15%',
      bodyRender: ({ realPay }) => (realPay ? `￥${numberWithCommas(div(realPay, 100))}` : '￥0')
    },
    {
      title: '发货状态',
      width: '15%',
      bodyRender: ({
        distStatusDesc,
        deliveryStatusDesc,
        deliveryStatus,
        orderNo,
        itemIdStr,
        refundState
      }) => (
        <div>
          <span className={deliveryStatus === 0 ? 'green' : ''}>
            {distStatusDesc || deliveryStatusDesc}
          </span>
          <br />
          {deliveryStatus === 0 &&
            refundState === 'refunding' &&
            renderRefundOption({
              orderNo,
              fromRefund,
              itemIdStr
            })}
        </div>
      )
    },
    {
      title: '运单号',
      width: '25%',
      textAlign: 'right',
      bodyRender: ({ expressWaybills = [] }) => {
        const pkgCount = expressWaybills.length;
        if (pkgCount === 0) {
          return null;
        }
        const { distCompanyName, expressNo } = expressWaybills[0];
        const extraText = pkgCount > 1 ? `等${pkgCount}个运单` : '';
        return <p>{`${distCompanyName} ${expressNo}${extraText}`}</p>;
      }
    }
  ];

  const isSelfFetch = expressType === SelfFetch;

  const goodsList = compressBy(
    sortBy(data, 'deliveryStatus'),
    'itemIdStr',
    (preGoods, currentGoods) => {
      const { goodsNum, distCompanyName = '', expressNo = '', expressWaybills = [] } = preGoods;
      if (!currentGoods) {
        return {
          ...preGoods,
          expressWaybills: [{ distCompanyName, expressNo }]
        };
      }

      return {
        ...preGoods,
        goodsNum: goodsNum + currentGoods.goodsNum,
        expressWaybills: expressWaybills.concat(
          pick(currentGoods, ['distCompanyName', 'expressNo'])
        )
      };
    }
  );

  const renderExpand = record =>
    goodsSpreads[record.itemIdStr] && (isSelfFetch || selectedItems.includes(record.itemIdStr));

  const selectGoodsDisabled = [
    DeliveryChannelType.Candao,
    DeliveryChannelType.Meituan,
    DeliveryChannelType.Eleme,
    DeliveryChannelType.MeituanShangou
  ].includes(channelType);

  return (
    <Grid
      columns={goodsColumns}
      rowKey="itemIdStr"
      datasets={goodsList}
      getRowConf={(itemInfo = {}) => ({ canSelect: itemInfo.canSelect })}
      selection={
        isShowSelection
          ? {
              selectedRowKeys: selectedItems,
              onSelect: (selectedRowkeys, selectedRows, currentRow) => {
                onSelect(selectedRowkeys, selectedRows, currentRow);
              },
              getCheckboxProps: (itemInfo = {}) => ({
                disabled: !itemInfo.canSelect || selectGoodsDisabled
              })
            }
          : null
      }
      expandation={{
        isExpanded(record) {
          return renderExpand(record);
        },
        expandRender(record) {
          return renderExpand(record) ? (
            <RealWeightEntry
              onGoodsSpreadsChange={onGoodsSpreadsChange}
              goods={record}
              spreads={goodsSpreads[record.itemIdStr]}
              indent={!isSelfFetch}
            />
          ) : null;
        }
      }}
    />
  );
};

export default GoodsList;
