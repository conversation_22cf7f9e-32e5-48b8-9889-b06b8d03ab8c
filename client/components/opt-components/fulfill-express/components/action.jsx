import React, { useState } from 'react';
import { Button, Checkbox } from 'zent';
import { Button as SamButton } from '@youzan/sam-components';
import { setUrlDomain } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import { PRINT_BIZ_PICKING } from 'common/constants';

const Action = ({
  disabled,
  printDisabled,
  onClick,
  onPrintReceipt,
  disabledDesc,
  expressText,
  isAgreeProtocol,
  isShowSFProtocol,
  sending,
  onChange,
  loading
}) => {
  const [printing, setPrinting] = useState(false);
  const protocolLink = setUrlDomain('/forum.php?mod=viewthread&tid=670164', 'bbs');
  const printPickReceipt = () => {
    setPrinting(true);
    onPrintReceipt(PRINT_BIZ_PICKING).finally(() => {
      setPrinting(false);
    });
  };

  return (
    <div className="express-content-item actions">
      {disabled && <span className="red disable-desc">{disabledDesc}</span>}
      {isShowSFProtocol && (
        <span className="agree-protocol">
          <Checkbox checked={isAgreeProtocol} onChange={onChange} />
          同意 <BlankLink href={protocolLink}>顺丰快件运单契约条款</BlankLink>
        </span>
      )}
      {!printDisabled && (
        <SamButton onClick={printPickReceipt} loading={printing} name="打印拣货小票">
          打印拣货小票
        </SamButton>
      )}
      <Button onClick={onClick} type="primary" loading={sending || loading} disabled={!!disabled}>
        {expressText}
      </Button>
    </div>
  );
};

export default Action;
