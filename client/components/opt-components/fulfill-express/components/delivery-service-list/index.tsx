/* eslint-disable react/no-unused-prop-types */
import React, { useEffect, useRef, useState } from 'react';
import './style.scss';
import { AuditStatus } from 'route/delivery/delivery-setting/localdelivery/container/fields/delivery-way/const';
import { transformFenToYuan } from 'common/utils';
import { EXPRESS_CHANNEL_KEY_NAME } from '../../constants';

interface IBaseInterface {
  onChannelSelect: (data: {
    target: { value: { deliveryChannel: number; appId?: string } };
  }) => void;
  refreshDeliveryChannel: (params: {
    deliveryChannel: number;
    appId: string;
    estimateDeliveryTime: string;
  }) => void;
}
interface IDeliveryServiceList<T> extends IBaseInterface {
  noWrap: boolean; // 强制不换行展示
  wrapCount: number; // 超过多少个换行
  data: T[];
  value: any;
}

interface IDeliveryServiceItem extends IBaseInterface {
  supportInsurance: boolean;
  deliveryChannelDesc: string;
  fee: number;
  inScope: boolean;
  reason: string;
  /** 配送时间展示 */
  estimateDeliveryTimeDesc: string;
  /** 后端用来刷新排序 */
  estimateDeliveryTime: string;
  maxWeight: number;
  initialWeight: number;
  deliveryChannel: number;
  optimalTag: string;
  fetchFeeExp: boolean;
  appId: string;
  unifySettle: boolean;
  unavailableType: number;
  logo: string;
  status: number;
  // 前端交互需要的数据
  selected: boolean;
}

const DeliveryChannelAuditTextMap = {
  [AuditStatus.NoAudit.toString()]: '未开通',
  [AuditStatus.Pending.toString()]: '审核中'
};

function ServiceContent(props: IDeliveryServiceItem) {
  const { unifySettle, status, estimateDeliveryTimeDesc, reason } = props;

  if (!unifySettle) {
    return (
      <div className="service-content">
        <div className="service-custom-text">自结算服务商暂不支持展示时效等信息</div>
      </div>
    );
  }

  let content = null;

  if (status === AuditStatus.Success) {
    if (reason) {
      content = <div className="service-custom-text">{reason}</div>;
    } else if (estimateDeliveryTimeDesc) {
      content = <div className="service-time">{`预计${estimateDeliveryTimeDesc}送达`}</div>;
    } else {
      content = <div className="service-custom-text">根据实时运力送达</div>;
    }
  } else if (DeliveryChannelAuditTextMap[status]) {
    content = <div className="service-custom-text">{DeliveryChannelAuditTextMap[status]}</div>;
  }

  return <div className="service-content">{content}</div>;
}

function ServiceOperate(props: IDeliveryServiceItem) {
  const {
    unifySettle,
    fee,
    fetchFeeExp,
    refreshDeliveryChannel,
    status,
    deliveryChannel,
    appId,
    reason,
    estimateDeliveryTime
  } = props;

  if (
    !unifySettle ||
    status === AuditStatus.Pending ||
    status === AuditStatus.Fail ||
    status === AuditStatus.NoOrder ||
    (status === AuditStatus.Success && reason)
  ) {
    return null;
  }

  const handleBtnRefresh = (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    e.stopPropagation();
    refreshDeliveryChannel({ deliveryChannel, appId, estimateDeliveryTime });
  };

  let content = null;

  if (status === AuditStatus.NoAudit) {
    content = <div className="service-open">申请开通</div>;
  } else if (fee) {
    content = (
      <div className="balance-num">
        ￥<span>{transformFenToYuan(fee)}</span>
      </div>
    );
  } else if (fetchFeeExp) {
    content = (
      <div className="service-refresh">
        数据超时，请
        <div className="service-refresh__btn" onClick={handleBtnRefresh}>
          刷新
        </div>
      </div>
    );
  }

  return <div className="service-operate">{content}</div>;
}

function ServiceCard(props: IDeliveryServiceItem) {
  const {
    deliveryChannelDesc,
    optimalTag,
    selected,
    appId,
    onChannelSelect,
    deliveryChannel,
    unifySettle
  } = props;
  const handleSelect = () => {
    const value: {
      deliveryChannel: number;
      appId?: string;
    } = {
      deliveryChannel
    };
    if (appId) value.appId = appId;
    onChannelSelect({ target: { value } });
  };

  return (
    <div
      className={`service-card ${selected ? 'service-card__active' : ''}`}
      onClick={handleSelect}
    >
      <div className="service-name">
        <div className="name-wrap">
          <div>{deliveryChannelDesc}</div>
        </div>
        {optimalTag && <span className="tag-name">{optimalTag}</span>}
        {unifySettle && <div className="tag-name tag-color__orange"> 有赞同城配</div>}
      </div>
      <ServiceContent {...props} />
      <ServiceOperate {...props} />
    </div>
  );
}

function MoreBtnCard(props: { onChange: React.Dispatch<React.SetStateAction<boolean>> }) {
  const { onChange } = props;
  return (
    <div className="service-card-more" onClick={() => onChange(true)}>
      查看更多 》
    </div>
  );
}

function useLocalDelivery(deliveryChannel: number) {
  if (deliveryChannel) return deliveryChannel;
  // 获取上次选择的服务商
  const localDeliveryChannel = window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME);
  return localDeliveryChannel ? Number(localDeliveryChannel) : '';
}

function DeliveryServiceList(props: IDeliveryServiceList<IDeliveryServiceItem>) {
  const { data = [], wrapCount = 3, value, onChannelSelect, refreshDeliveryChannel } = props;
  const isSelectRef = useRef(false);

  useEffect(() => {
    return () => {
      isSelectRef.current = false;
    };
  }, []);

  const handleChannelSelect = (data: {
    target: { value: { deliveryChannel: number; appId?: string } };
  }) => {
    isSelectRef.current = true;
    onChannelSelect(data);
  };

  const deliveryChannel = useLocalDelivery(value?.deliveryChannel);

  const [isUnfold, setUnfold] = useState(false);


  const isWrapCount = data.length > wrapCount;
  const moreBtnCard = isWrapCount && !isUnfold && <MoreBtnCard onChange={setUnfold} />;
  return (
    <div className="delivery-service-list">
      {data.map((channel, idx) => {
        const selected =
          channel?.deliveryChannel === deliveryChannel &&
          channel?.appId === value?.appId &&
          channel?.status === AuditStatus.Success && 
          (channel?.unifySettle || isSelectRef.current); // 统一自结算初始化才默认选择上次的值，isSelectRef 是为了标识当前是通过点击选中服务商
        const shouldUnfold = selected && idx > wrapCount - 1 && !isUnfold;
        if (shouldUnfold) {
          setUnfold(true);
        }

        if (!isUnfold && idx > wrapCount - 1) {
          return null;
        }
        return (
          <ServiceCard
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...channel}
            selected={selected}
            onChannelSelect={handleChannelSelect}
            refreshDeliveryChannel={refreshDeliveryChannel}
          />
        );
      })}
      {moreBtnCard}
    </div>
  );
}

export default DeliveryServiceList;
