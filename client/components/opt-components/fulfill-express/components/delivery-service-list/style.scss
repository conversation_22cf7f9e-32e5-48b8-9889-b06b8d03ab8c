@import '~shared/style';

.delivery-service-list {
  font-family: <PERSON>Fang SC;
  display: flex;
  flex-wrap: wrap;
  width: 650px;
  .service-card__active{
    border: 1px solid #1989FA!important;
  }
  .service-card{
    width: 165px;
    margin-top: 6px;
    height: 72px;
    border: 1px solid var(--theme-section-border-color, var(--theme-stroke-5, #e0e0e0));
    border-radius: 5px;
    padding: 6px;
    margin-right: 6px;
    cursor: pointer;
    .service-name{
      color: rgba(2, 2, 2, 0.88);
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      margin-right: 6px;
      display: flex;
      height: 24px;
      align-items: center;
      .name-wrap{
        margin-right: 6px;
      }
      .tag-name{
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        padding: 0px 2px;
        border: 1px solid #1989FA;
        border-radius: 2px;
        display: block;
        background: rgba(25, 137, 250, 0.09);
        color: var(--theme-primary-bg, var(--theme-primary-4, #155bd4));
        border-color: 1px solid var(--theme-primary-hover-bg, var(--theme-primary-6, #356fd4));
      }
      .tag-color__orange {
        margin-left: 4px;
        color: rgb(237, 106, 24);
        background-color: 1px solid var(--theme-warning-bg, var(--theme-warn-5, #ffefe6));
        border: 1px solid rgb(237, 106, 24);
      }
    }
    .service-content{
      .service-time{
        color: rgba(51, 51, 51, 0.83);
        font-size: 10px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
      }
      .service-custom-text{
        font-size: 10px;
        color: $color-n6;
      }
    }
    .service-operate{
      .balance-num{
        color: #020202;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        span{
          font-size: 15px;
        }
      }
      .balance-error{
        color: #F00;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
      }
      .service-refresh{
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        display: flex;
        .service-refresh__btn {
          color: #2C5ACC;
          cursor: pointer;
        }
      }
      .service-open{
        color: #2C5ACC;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
      }
    }
  }
  .service-card-more{
    width: 160px;
    height: 72px;
    margin-top: 6px;
    border: 1px solid var(--theme-section-border-color, var(--theme-stroke-5, #e0e0e0));;
    border-radius: 5px;
    padding: 6px;
    color: #2C5ACC;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }  
}
