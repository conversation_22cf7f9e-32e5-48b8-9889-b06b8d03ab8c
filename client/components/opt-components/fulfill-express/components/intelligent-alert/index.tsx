import React from 'react';
import { Alert } from 'zent';
import { BlankLink } from '@youzan/react-components';
import { setUrlDomain } from '@youzan/retail-utils';
import styles from './index.scss';

interface IProps {
  warehouseId: number;
}

export const IntelligentAlert: React.FC<IProps> = () => {
  return (
    <Alert className={styles.alert}>
      使用有赞同城配服务商发货，配送完成即结算。
      <BlankLink
        className={styles.link}
        href={setUrlDomain('/forum.php?mod=viewthread&tid=694266', 'bbs')}
      >
        查看详情
      </BlankLink>
    </Alert>
  );
};
