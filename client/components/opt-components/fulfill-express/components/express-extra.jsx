import React, { useEffect, useState } from 'react';
import { Input, Radio, Notify, FormDescription } from 'zent';
import { NumberInput, PopInfo } from '@youzan/retail-components';
import { BlankLink } from '@youzan/react-components';
import { get, find, isNumber } from 'lodash';
import { set } from 'lodash/fp';
import { div, global, formatDate } from '@youzan/retail-utils';
import { Select } from '@zent/compat';
import { setUrlDomain } from '@youzan/retail-utils';
import { useLocalStorage } from '@youzan/react-hooks';
import { isRetailSingleStore } from '@youzan/utils-shop';

import { convertFenToYen } from 'common/fns/format';
import { SupportParallelCallInLocalDelivery } from 'common/constant';
import { DeliveryChannelType } from 'common/constants/common';
import ExpressSelfInputs from './express-self-inputs';
import ExpressWxInputs from './express-wx-inputs';
import CallType, { ParallelOrSequenceDeliveryCallType } from './call-type';
import DeliveryTool from './delivery-tool';
import {
  AuditStatus,
  DeliveryChannel
} from '../../../../route/delivery/delivery-setting/localdelivery/container/fields/delivery-way/const';

import {
  SEND_TYPE_CODE,
  ELECTRON_ERROR_INFO,
  ONLY_DZMD,
  SF_EXPRESS_CODE,
  JD_EXPRESS_CODE
} from '../constants';
import TimeSelect from './time-select';
import { searchWechatDeliveryConfig, queryAppLink } from '../api';
import { DeliveryService } from './direct-service';

import DeliveryServiceList from './delivery-service-list';
import { ExpressWxChannelShopEWaybill } from '../wx-channel-shop/components/express-wx-channel-shop-e-waybill';

const { KDT_ID } = global;
const BASE_URL = get(window, '_global.url.store', '');

// eslint-disable-next-line @youzan/yz-retail/no-new-date
const specialBusinessType = new Date(2022, 6, 9, 0, 0, 0).valueOf() - new Date().valueOf() > 0;

const RadioGroup = Radio.Group;
const { Option } = Select;

/**
 * 系统呼叫物流，电子面单显示
 *
 */
export const ExpressExtraSystemCall = ({
  orderInfo,
  channelType,
  systemCallExpressCompanys,
  printerList = [],
  electronicSheetExceptionDesc,
  electronicSheetExceptionCode,
  express = {},
  onSystemExpressCompanyChange,
  onExpressAddressChange,
  onPrinterChange,
  printerErrDesc,
  handleExpressWeightChange,
  handlePickUpTimeChange,
  onSendTypeChange,
  onChangeExpress,
  sortSystemCallExpressCompanys
}) => {
  if (channelType === DeliveryChannelType.WxChannelShop) {
    return (
      <ExpressWxChannelShopEWaybill
        express={express}
        orderInfo={orderInfo}
        onSystemExpressCompanyChange={onSystemExpressCompanyChange}
        onExpressAddressChange={onExpressAddressChange}
        onSendTypeChange={onSendTypeChange}
        onPrinterChange={onPrinterChange}
        onChangeExpress={onChangeExpress}
      />
    );
  }

  // 在线下单，发货弹窗直接抛出的异常，
  if (electronicSheetExceptionDesc) {
    const linkInfo = ELECTRON_ERROR_INFO[electronicSheetExceptionCode];
    return (
      <p className="orange pl90">
        {electronicSheetExceptionDesc}
        {linkInfo && <BlankLink href={linkInfo.link}>{linkInfo.text}</BlankLink>}
      </p>
    );
  }

  const disableWeightInput = !express.auditNo;

  const { configErrorDesc, configErrorCode } = express;
  const errorLinkInfo = ELECTRON_ERROR_INFO[configErrorCode];

  // eslint-disable-next-line no-param-reassign
  systemCallExpressCompanys = sortSystemCallExpressCompanys(express, systemCallExpressCompanys);

  const inputDetail = (
    <>
      {express.sendType > 0 && express.sendType !== ONLY_DZMD && (
        // 不是仅电子面单的都要显示
        <div className="express-weight">
          <div className="express-content-item">
            <div className="express-content-item__label">取件时间：</div>
            <div className="express-content-item__value">
              {/* 这块逻辑现在在前端： 顺丰的尽快上门取件，其他的都是一样的选取时间取件 */}
              {express.expressId === SF_EXPRESS_CODE ? (
                <TimeSelect value={express.startTime} onChange={handlePickUpTimeChange} />
              ) : (
                <Select value={0}>
                  <Option value={0}>尽快上门取件</Option>
                </Select>
              )}
            </div>
          </div>
          {express.expressId === JD_EXPRESS_CODE && (
            <>
              <div className="express-content-item">
                <div className="express-content-item__label">物品重量：</div>
                <div className="express-content-item__value weight-input">
                  <NumberInput
                    showStepper
                    onChange={handleExpressWeightChange}
                    value={express.weight}
                    max={30}
                    placeholder="最大30千克"
                    disabled={disableWeightInput}
                    min={1}
                    decimal={0}
                  />
                  <span className="after-input-text">kg</span>
                  {disableWeightInput && <span className="check-address">请先选择地址</span>}
                </div>
              </div>

              <div className="express-content-item">
                <div className="express-content-item__label">面单模版：</div>
                <div className="express-content-item__value weight-input">
                  <Select
                    data={express.eWaybillTemplateOptions}
                    optionValue="id"
                    optionText="name"
                    onChange={e => {
                      onChangeExpress({
                        eWaybillTemplateId: e.target.value
                      });
                    }}
                    autoWidth
                    placeholder="请选择"
                    value={express.eWaybillTemplateId}
                  />
                </div>
              </div>
            </>
          )}
          {express.postage > 0 && (
            <div className="postage-info grey">
              <div>
                预估运费：
                {convertFenToYen(express.postage)}元(
                {isRetailSingleStore
                  ? '预估费用仅供参考，快递公司称重后，有赞将按照实际计费重量扣费'
                  : '实际重量以快递公司称重为准，运费有赞提前预扣，多退少补'}
                )
              </div>
              {!express.isCashPledgeEnough && (
                <p>
                  账户余额不足，
                  <BlankLink href={`${BASE_URL}/shop/v2/trade/newsettlement#/rechargeMoney`}>
                    请先充值
                  </BlankLink>
                </p>
              )}
            </div>
          )}
        </div>
      )}
      <div className="express-content-item">
        <div className="express-content-item__label">打印机：</div>
        <div className="express-content-item__value">
          {printerErrDesc ? (
            <p className="orange">{printerErrDesc}</p>
          ) : (
            <div>
              <Select
                data={printerList}
                optionValue="equipmentNumber"
                optionText="name"
                onChange={onPrinterChange}
                autoWidth
                placeholder="请选择打印机"
                value={express.printerDeviceNo}
              />
              <BlankLink href={`${BASE_URL}/setting/common/device#/`}>新增</BlankLink>
            </div>
          )}
        </div>
      </div>
    </>
  );

  return (
    <div>
      <div className="express-content-item" style={{ display: 'flex', alignItems: 'flex-start' }}>
        <div className="express-content-item__label">物流公司：</div>
        <div className="express-content-item__value">
          <div className="express-content-item__express-company-container">
            {systemCallExpressCompanys.map(item => (
              <div
                key={item.expressId}
                className={`express-content-item__express-company-item ${
                  item.expressId === express.expressId
                    ? 'express-content-item__express-company-item-checked'
                    : ''
                }`}
                onClick={() => onSystemExpressCompanyChange({ target: { value: +item.expressId } })}
              >
                <div className="title">
                  {item.expressName} {item.recommend && <span className="recommend">官方推荐</span>}
                </div>
                {Object.keys(item.logisticsRecommendInfo || {}).length > 0 && (
                  <div className="tips">{`最低${item.logisticsRecommendInfo.discount}折，每单最高省${item.logisticsRecommendInfo.discountAmount}元`}</div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
      {isNumber(express.expressId) && (
        <>
          <div className="express-content-item">
            <div className="express-content-item__label">发货地址：</div>
            <div className="express-content-item__value">
              <Select
                data={express.expressAddrList}
                optionValue="auditNo"
                optionText="displayAddress"
                className="express-address"
                autoWidth
                placeholder="请选择发货地址"
                onChange={onExpressAddressChange}
                value={express.auditNo}
                disabled={express.expressAddrList?.length === 0}
              />
              <BlankLink
                href={`${BASE_URL}/v4/ump/logisticsService/serviceManage?code=${express.expressId}`}
              >
                {express?.expressAddrList?.length > 0 ? '新增发货地址' : '去开通'}
              </BlankLink>
              <BlankLink
                style={{ marginLeft: 12 }}
                onClick={() =>
                  onSystemExpressCompanyChange({ target: { value: express.expressId } })
                }
              >
                刷新
              </BlankLink>
            </div>
          </div>
          {express.sendTypes.length > 0 && (
            <div className="express-content-item">
              <div className="express-content-item__label">发货类型：</div>
              <div className="express-content-item__value">
                <RadioGroup value={express.sendType} onChange={onSendTypeChange}>
                  {express.sendTypes.map(({ code, deliveryType }) => (
                    <Radio value={code} key={code}>
                      {deliveryType}
                    </Radio>
                  ))}
                </RadioGroup>
              </div>
            </div>
          )}
          {
            // 这里的是配置异常
            errorLinkInfo && (
              <div className="postage-info grey">
                {configErrorDesc}，
                <BlankLink href={errorLinkInfo.link}>{errorLinkInfo.text}</BlankLink>
              </div>
            )
          }
          {!get(errorLinkInfo, 'intercept') && inputDetail}
        </>
      )}
    </div>
  );
};

// 物流公司发货时需要展示的信息
export const ExpressExtraForCompany = props => {
  const {
    express = {},
    multiPackInfo = [],
    onChange,
    isMultiExpress,
    disableMulti,
    disableMultiDesc
  } = props;

  const onExpressChange = packs => {
    if (isMultiExpress) {
      onChange({ multiPackInfo: packs });
      return;
    }
    onChange({ express: packs[0] || {} });
  };

  const value = isMultiExpress ? multiPackInfo : [express];

  return (
    <ExpressSelfInputs
      onChange={onExpressChange}
      value={value}
      disableMulti={disableMulti}
      disableMultiDesc={disableMultiDesc}
      multiExpress={isMultiExpress}
    />
  );
};

// 微信物流发货时需要的extra信息
export const ExpressExtraForWxExpress = props => {
  const { onChange } = props;

  const [availableWechatExpress, setAvailableWechatExpress] = useState([]);

  useEffect(() => {
    searchWechatDeliveryConfig({
      includePrinterInfo: false,
      includeAllSupportDeliveryAddress: false
    })
      .then(result => {
        if (result) {
          const expresses = result.wechatDeliveryExpressAggDTOS || [];
          const wechatExpress = expresses.filter(express => {
            const accouts = express.deliveryBindAccountDTOS || [];
            return accouts.some(v => v.bindStatusCode === 0); // BindStatus.Success: 0
          });
          setAvailableWechatExpress(wechatExpress);
        }
      })
      .catch(err => {
        Notify.error(err);
      });
  }, []);

  return (
    <ExpressWxInputs
      wechatExpressWayBill={{}}
      availableWechatExpress={availableWechatExpress}
      onChange={onChange}
    />
  );
};

// 同城配送第三方配送，需要展示第三配送公司以及配送信息
export const ThirdDeliveryExtra = ({
  localPostage,
  calcWeightType,
  localDelivery,
  channels = [],
  handleDeliveryCompanyChange,
  localChannels = [],
  handleWeightInputTypeChange,
  handleWeightChange,
  exceptionInfo,
  autoCallInfo = {},
  supportInsurance,
  insuranceList,
  insurance,
  handleInsuranceChange,
  warehouseId,
  businessType,
  localDeliveryCallConfig,
  handleDeliveryChannelChange,
  handleRefresh,
  discountFee,
  canDirectDelivery = false,
  isDirectDelivery,
  handleDirectDeliveryChange,
  refreshDeliveryChannel,
  expressType,
  isShowDeliveryTool,
  handleChangeDeliveryTool,
  deliveryTool
}) => {
  const [appLink, setAppLink] = useState('');

  const settingUrl = isRetailSingleStore
    ? `${window._global.url.store}/shop/v2/trade/localdelivery`
    : `${window._global.url.store}/setting/common/order`;
  const { channel = {}, distWeight } = localDelivery;
  const [deliveryPriceSpecialBusinessTip, setDeliveryPriceSpecialBusinessTip] = useLocalStorage(
    'delivery-price-special-business-tip'
  );

  useEffect(() => {
    if (channel.appId && exceptionInfo && exceptionInfo.electronicSheetExceptionCode) {
      queryAppLink(channel.appId).then(link => setAppLink(link));
    }
  }, [exceptionInfo, channel.appId]);

  useEffect(() => {
    if (!isShowDeliveryTool) {
      handleChangeDeliveryTool(0);
    }
  }, [handleChangeDeliveryTool, isShowDeliveryTool]);

  if (channels.length === 0) {
    return (
      <div className="delivery-extra">
        <p>
          暂未启用第三方同城配送服务
          <BlankLink href={settingUrl}>前往开启</BlankLink>
        </p>
      </div>
    );
  }

  const { initialWeight = 1000, maxWeight } = find(channels, channel) || {};

  const onWeightChange = evt => {
    const {
      target: { value }
    } = evt;
    const weight = value * 1000 > maxWeight ? maxWeight / 1000 : value;
    handleWeightChange(set('target.value', weight)(evt));
  };

  const { isShow, autoDeliveryTime } = autoCallInfo;
  const now = new Date();

  const isNotParallelOrSequenceDeliveryCallType = !ParallelOrSequenceDeliveryCallType.includes(
    channel?.deliveryChannel
  );

  const deliveryChannels = localChannels.filter(
    item =>
      item.status === AuditStatus.NoAudit ||
      item.status === AuditStatus.Pending ||
      item.status === AuditStatus.Success
  );

  // 获取当前的 channel 名称
  const currentChannelName = get(
    deliveryChannels.find(
      c =>
        get(c, 'value.deliveryChannel') === channel.deliveryChannel &&
        get(c, 'value.appId') === channel.appId
    ),
    'rawText',
    ''
  );

  return (
    <div className="delivery-extra">
      <div className="delivery-extra-content">
        {SupportParallelCallInLocalDelivery && (
          <CallType
            value={channel}
            warehouseId={warehouseId}
            businessType={businessType}
            localDeliveryCallConfig={localDeliveryCallConfig}
            onChange={handleDeliveryChannelChange}
            onRefresh={handleRefresh}
            expressType={expressType}
          />
        )}
        {/* 非同时呼叫和非顺序呼叫时，才能选择单个配送服务商 */}
        {isNotParallelOrSequenceDeliveryCallType && (
          <div className="input-item" style={{ flexWrap: 'wrap' }}>
            <div className="delivery-extra-content__label">配送公司：</div>
            <DeliveryServiceList
              value={channel}
              data={deliveryChannels}
              onChannelSelect={handleDeliveryCompanyChange}
              onSelect={handleDeliveryCompanyChange}
              refreshDeliveryChannel={refreshDeliveryChannel}
            />
          </div>
        )}
        {canDirectDelivery && (
          <DeliveryService
            isDirectDelivery={isDirectDelivery}
            onChange={handleDirectDeliveryChange}
          />
        )}

        {isShowDeliveryTool && (
          <DeliveryTool onChange={handleChangeDeliveryTool} value={deliveryTool} />
        )}
        <div className="input-item dist-Weight">
          <div className="delivery-extra-content__label">商品重量：</div>
          <div className="delivery-extra-content__value">
            <RadioGroup onChange={handleWeightInputTypeChange} value={calcWeightType}>
              <Radio value="default">
                {div(initialWeight, 1000)}
                千克以内
              </Radio>
              <Radio value="custom">
                <Input
                  className="delivery-custom-weight"
                  disabled={calcWeightType !== 'custom'}
                  onChange={onWeightChange}
                  value={distWeight}
                />
                <span>千克</span>
              </Radio>
            </RadioGroup>
            <PopInfo
              className="delivery-extra-content__value__pop-info"
              popContent="默认重量值为网店商品重量之和，若没有填写重量，重量会当作 0 进行计算。"
            />
          </div>
        </div>

        {isNotParallelOrSequenceDeliveryCallType && supportInsurance && (
          <div className="input-item">
            <div className="delivery-extra-content__label">商品保价：</div>
            <div className="delivery-extra-content__value">
              <Select
                className="delivery-baojia"
                placeholder="请选择保价"
                value={insurance}
                onChange={value => {
                  handleInsuranceChange(value);
                }}
                data={insuranceList}
                autoWidth
              />
            </div>
          </div>
        )}

        <div className="delivery-price">
          <div className="delivery-extra-content__label">配送价格：</div>
          <div>
            <div className="delivery-extra-content__value_1">
              <span>{localPostage}</span>
              {discountFee && (
                <span className="error-span">{`已优惠${div(discountFee, 100)}元`}</span>
              )}
            </div>
            <div className="delivery-extra-content__value">
              {channel.deliveryChannel === DeliveryChannel.CanDao && (
                <p className="tips">餐道聚合配送将在8月10日0点下线，可开通呼叫其他配送服务商</p>
              )}
              {/* 第三方配送余额不足的时候提示 */}
              {exceptionInfo && channel.appId && exceptionInfo.electronicSheetExceptionDesc ? (
                <p className="exception">
                  {`你在${currentChannelName}的${exceptionInfo.electronicSheetExceptionDesc}，`}
                  {exceptionInfo.electronicSheetExceptionCode === 233116004 ? (
                    <BlankLink href={appLink}>前往充值</BlankLink>
                  ) : null}
                  {exceptionInfo.electronicSheetExceptionCode === 233116005
                    ? `请联系${currentChannelName}了解账户异常详情。`
                    : null}
                </p>
              ) : null}

              {exceptionInfo && !channel.appId && exceptionInfo.electronicSheetExceptionDesc ? (
                <p className="exception">{exceptionInfo.electronicSheetExceptionDesc}</p>
              ) : null}
            </div>

            {!deliveryPriceSpecialBusinessTip && specialBusinessType && (
              <FormDescription>
                特殊业务类型支持商家按照商品分类呼叫服务商，节省配送费用。
                <BlankLink
                  href={setUrlDomain(`/fulfillment/delivery#/local-delivery/${KDT_ID}`, 'store')}
                >
                  去设置{' '}
                </BlankLink>
                <a onClick={() => setDeliveryPriceSpecialBusinessTip(true)}>| 不再提醒 </a>
              </FormDescription>
            )}
          </div>
        </div>
      </div>
      {SupportParallelCallInLocalDelivery && isShow && (
        <div className="delivery-extra-autoCall">{autoCallInfo.autoDeliveryDesc}</div>
      )}
      {!SupportParallelCallInLocalDelivery && isShow && autoDeliveryTime - +now > 0 && (
        <div className="delivery-extra-autoCall">
          {`已开启自动呼叫，系统将在 ${formatDate(
            autoDeliveryTime,
            'YYYY-MM-DD HH:mm'
          )} 自动呼叫配送员`}
        </div>
      )}
    </div>
  );
};

// 自提有码核销，展示核销码输入框
export const SelfFetchExtra = ({ selfFetch = {}, onChange }) => (
  <div className="express-content-item">
    <div className="express-content-item__label" />
    <div className="express-content-item__value selffetch">
      提货码：
      <Input
        value={selfFetch.selfFetchNo}
        className="selfFetch-margin"
        onChange={({ target: { value: selfFetchNo } }) => {
          const reg = /^[0-9a-zA-Z]*$/;
          if (!reg.test(selfFetchNo)) return;
          onChange({
            selfFetch: {
              ...selfFetch,
              selfFetchNo: selfFetchNo.trim()
            }
          });
        }}
      />
    </div>
  </div>
);

// 无码核销，展示空
export const WithoutCodeTips = () => (
  <div className="express-content-item pl90">
    <p className="orange">请仔细核对联系人信息，避免冒领或者错误核销造成损失！</p>
  </div>
);

const {
  EXPRESS_FOR_COMPANY,
  THIRD_LOCAL_DELIVERY,
  SELFFETCH_WITH_CODE,
  SELFFETCH_WITHOUT_CODE,
  EXPRESS_SYSTEM_CALL,
  WX_EXPRESS
} = SEND_TYPE_CODE;

export const SEND_TYPE_EXTRA_MAP = {
  [EXPRESS_FOR_COMPANY]: ExpressExtraForCompany,
  [THIRD_LOCAL_DELIVERY]: ThirdDeliveryExtra,
  [SELFFETCH_WITH_CODE]: SelfFetchExtra,
  [SELFFETCH_WITHOUT_CODE]: WithoutCodeTips,
  [EXPRESS_SYSTEM_CALL]: ExpressExtraSystemCall,
  [WX_EXPRESS]: ExpressExtraForWxExpress
};
