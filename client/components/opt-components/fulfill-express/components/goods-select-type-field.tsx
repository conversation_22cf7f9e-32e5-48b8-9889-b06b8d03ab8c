import * as React from 'react';
import { Radio, Input, Dialog, Button, Notify } from 'zent';
import { isRetailMinimalistShop } from '@youzan/utils-shop';
import { setUrlDomain } from '@youzan/retail-utils';

const { useState } = React;
const RadioGroup = Radio.Group;
const { openDialog, closeDialog } = Dialog;

export enum GoodsSelectType {
  /** 勾选商品 */
  Choose,
  /** 扫码录入 */
  Scan
}

const goodsSelectTypeOptions = [
  {
    text: '勾选商品',
    value: GoodsSelectType.Choose
  },
  {
    text: '扫码录入',
    value: GoodsSelectType.Scan
  }
];

interface IItemInfo {
  /** 条目ID */
  itemIdStr: string;
  /** 商品条码 */
  skuCode: string;
  /** 商品数量 */
  goodsNum: number;
  /** 发货状态 */
  deliveryStatus: number;
  /** 退款状态 */
  refundState: string;
  /** 是否唯一码商品 */
  isSerialItem: boolean;
}

interface IScanItemInfo extends IItemInfo {
  scanGoodsNum: number;
}

interface IGoodsSelectTypeFieldProps {
  value: GoodsSelectType;
  onChange: (value?: GoodsSelectType) => void;
  itemList: IItemInfo[];
  scanItemList: IScanItemInfo[];
  updateScanItemList: (scanItemList: IScanItemInfo[]) => void;
}

const audioSuccess = new Audio(setUrlDomain('audio/warehouse/success.mp3', 'cdn_static_retail'));
const audioError = new Audio(setUrlDomain('audio/warehouse/error.mp3', 'cdn_static_retail'));

function getItemErrorMsg(item: IItemInfo, scanItemList: IScanItemInfo[]): string {
  if (item.isSerialItem) {
    // 唯一码商品
    return '不支持唯一码商品扫码发货';
  }

  if (item.deliveryStatus !== 0) {
    // 已发货商品
    if (item.refundState === 'refunding') {
      // 已退货商品
      return '商品正在退款中，无法发货';
    }

    return '该商品已发货';
  }

  const matchedScanItem = scanItemList.find(v => v.itemIdStr === item.itemIdStr);
  if (matchedScanItem && matchedScanItem.scanGoodsNum >= matchedScanItem.goodsNum) {
    return '商品数量超过发货数量';
  }

  return '';
}

export default function GoodsSelectTypeField({
  value: goodsSelectType,
  onChange,
  itemList,
  scanItemList,
  updateScanItemList
}: IGoodsSelectTypeFieldProps) {
  const [skuCodeValue, setSkuCodeValue] = useState('');

  if (isRetailMinimalistShop) {
    // 极简版不显示扫码发货
    return null;
  }

  return (
    <div className="express-content-item">
      <div className="express-content-item__label">选择商品方式：</div>
      <RadioGroup
        value={goodsSelectType}
        onChange={({ target: { value } }) => {
          const id = 'changeTipDialogId';
          openDialog({
            dialogId: id,
            title: '提示',
            children: '切换发货方式后，对商品的操作记录不会保留, 是否确认切换?',
            footer: (
              <>
                <Button onClick={() => closeDialog(id)}>取消</Button>
                <Button
                  type="primary"
                  onClick={() => {
                    setSkuCodeValue(''); // 清空扫码输入框
                    onChange(value);
                    closeDialog(id);
                  }}
                >
                  切换
                </Button>
              </>
            )
          });
        }}
      >
        {goodsSelectTypeOptions.map(({ value, text }) => (
          <Radio value={value} key={value}>
            {text}
          </Radio>
        ))}
        {goodsSelectType === GoodsSelectType.Scan && (
          <Input
            className="express-content-item_goods-scan-input"
            value={skuCodeValue}
            onChange={({ target: { value } }: { target: { value: any } }) => {
              setSkuCodeValue(value);
            }}
            onPressEnter={({ target: { value } }: { target: any }): any | void => {
              // 未输入
              if (!value) {
                Notify.error('请输入条码');
                return audioError.play();
              }

              const matchedItems = itemList.filter(v => v.skuCode === value);
              const matchedItemsCount = matchedItems.length;
              let item: IItemInfo;

              if (matchedItemsCount === 0) {
                // 商品不存在
                Notify.error('请先维护条码或勾选商品发货');
                return audioError.play();
              }

              if (matchedItemsCount === 1) {
                [item] = matchedItems;
              } else {
                // 存在多个商品skuCode重复情况
                const validMatchedItems = matchedItems.filter(
                  v => !getItemErrorMsg(v, scanItemList)
                );
                if (validMatchedItems.length > 0) {
                  [item] = validMatchedItems;
                } else {
                  item =
                    scanItemList.find(v => v.skuCode === value) ||
                    matchedItems[matchedItemsCount - 1];
                }
              }

              // 商品存在，校验商品
              const errorMsg = getItemErrorMsg(item, scanItemList);
              if (errorMsg) {
                Notify.error(errorMsg);
                return audioError.play();
              }

              const newScanItemList = [...scanItemList];
              const scanItem = newScanItemList.find(v => v.itemIdStr === item.itemIdStr);
              if (scanItem) {
                // 商品已录入
                scanItem.scanGoodsNum += 1;
              } else {
                // 商品未录入
                newScanItemList.push({
                  scanGoodsNum: 1,
                  ...item
                });
              }

              updateScanItemList(newScanItemList);
              setSkuCodeValue('');
              audioSuccess.play();
            }}
            placeholder="扫描商品条码"
          />
        )}
      </RadioGroup>
    </div>
  );
}
