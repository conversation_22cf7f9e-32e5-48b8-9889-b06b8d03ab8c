/**
 * 视频号小店额外数据
 */

import {
  IExpressAggregateDTO,
  IShopAddressInfo,
  ISiteAggregateDTO
} from 'common/features/wx-channel-shop/apis/types';
import { LocalExpressData } from '../../types';

export type WxChannelShopLocalExpressData = LocalExpressData & {
  /** 有赞内部物流 id  */
  expressId: number;
  /** 商家在某外部平台注册的店铺id(返回的是外部数据,理论上全局唯一) */
  outShopId: string;
  expressCompany: IExpressAggregateDTO;

  /** 网点id(外部) */
  outSiteId: string;
  /** 商家在该物流公司下某网点分配的面单账号id(返回的是外部数据) */
  outWaybillAccountId: string;
  expressSite: ISiteAggregateDTO;

  /** 模板 id */
  thirdTemplateId: string;

  /** 发货地址 id */
  senderAddressId: number;
  senderAddress: IShopAddressInfo;
  /** 产品类型 */
  thirdProductTypeId: number;
  /** 月结账号 */
  monthlyCardId: string;

  /** 打印机 */
  printerName: string;
};
