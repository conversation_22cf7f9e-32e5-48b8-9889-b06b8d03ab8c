import { div } from '@youzan/retail-utils';
import { Notify } from 'zent';

import { ChannelAccountType, DeliveryChannelType } from 'common/constants/common';

import { createMultiPlayWaybillOrder } from 'common/features/wx-channel-shop/apis';
import {
  CooperateType,
  IExpressAggregateDTO,
  IShopAddressInfo,
  ISiteAggregateDTO
} from 'common/features/wx-channel-shop/apis/types';

import { EXPRESS_SELF, EXPRESS_SYSTEM_CALL } from '../../constants';
import { WxChannelShopLocalExpressData } from '../types';
import { alertRechargingExpressSite } from '../components/alert-recharging-express-site';

export function checkIsDeliveringByWxChannelShopEWaybill({
  channelType,
  expressType
}: {
  channelType: number;
  expressType: number;
}) {
  return channelType === DeliveryChannelType.WxChannelShop && expressType === EXPRESS_SYSTEM_CALL;
}

export function checkCanDeliveryWxChannelShopEWaybill(
  express: WxChannelShopLocalExpressData,
  isAgreeProtocol: boolean,
  isShowSFProtocol: boolean
) {
  const { expressCompany, monthlyCardId, thirdProductTypeId } = express;
  const isDirect = expressCompany?.cooperateType === CooperateType.Direct;

  return (
    // 展示协议 并且 没有勾选协议 无法发货
    (isShowSFProtocol && !isAgreeProtocol) ||
    !express.expressId ||
    (!isDirect ? !express.outSiteId : !monthlyCardId || typeof thirdProductTypeId !== 'number') ||
    !express.senderAddressId ||
    !express.thirdTemplateId ||
    !express.printerName
  );
}

export function getExpressSiteLocalExpressData(site: ISiteAggregateDTO) {
  return {
    outSiteId: site.siteInfo.outSiteId,
    outWaybillAccountId: site.siteAccountInfo.outWaybillAccountId,
    expressSite: site
  };
}

export function createWxChannelShopEWaybillOrder({
  salesKdtId,
  mpId,
  express,
  orderNo,
  deliveryItems
}: {
  mpId: number;
  salesKdtId: number;
  orderNo: string;
  deliveryItems: Array<{
    itemId: string;
    num: number;
  }>;
  express: {
    expressId: number;
    outWaybillAccountId: string;
    outShopId: string;
    outSiteId: string;
    expressCompany: IExpressAggregateDTO;
    thirdProductTypeId: number;
    monthlyCardId: string;
    senderAddress: IShopAddressInfo;
  };
}) {
  const { senderAddress, expressCompany, thirdProductTypeId, monthlyCardId } = express;
  const isDirect = expressCompany?.cooperateType === CooperateType.Direct;
  return createMultiPlayWaybillOrder({
    salesKdtId,
    thirdAcctId: express.outWaybillAccountId,
    thirdShopId: express.outShopId,
    thirdSiteCode: !isDirect ? express.outSiteId : undefined,
    thirdProductTypeId: isDirect ? thirdProductTypeId : undefined,
    monthlyCardId: isDirect ? monthlyCardId : undefined,
    order: {
      orderItemInfos: deliveryItems.map(item => {
        return {
          itemCnt: div(item.num, 1000),
          orderItemId: item.itemId
        };
      }),
      orderNo
    },
    sender: {
      address: senderAddress.address,
      city: senderAddress.city,
      county: senderAddress.county,
      mobile: senderAddress.mobilePhone || senderAddress.telephone,
      name: senderAddress.contactName,
      province: senderAddress.province,
      street: senderAddress.area
    },
    mpId,
    expressId: express.expressId,
    channelType: ChannelAccountType.WxChannelShop
  });
}

export function getWxChannelShopEWaybillDeliveryInfo({
  expressNo,
  expressId,
  thirdWaybillId
}: {
  expressNo: string;
  expressId: number;
  thirdWaybillId: string;
}) {
  return {
    /** 视频号小店面单写死自行联系配送 */
    deliveryType: EXPRESS_SELF,
    express: {
      expressId,
      expressNo,
      thirdWaybillId,
      multiPlatExpressWaybill: 'wx_video_xiaodian'
    }
  };
}

export function createWxChannelShopEWaybillValidateArr() {
  return [
    {
      path: 'express',
      validate: (express: WxChannelShopLocalExpressData) => {
        if (express.expressCompany?.cooperateType === CooperateType.Direct) {
          return express?.monthlyCardId?.length > 0;
        }
        return express.expressSite.siteAccountInfo.balanceInfo.available > 0;
      },
      onError: (express: WxChannelShopLocalExpressData) => {
        express.expressCompany?.cooperateType === CooperateType.Direct
          ? Notify.error('请填写月结账号')
          : alertRechargingExpressSite({
              title: '面单余额不足，请线下充值后再发货或更换其他快递发货。',
              expressCompany: express.expressCompany,
              expressSite: express.expressSite
            });
      }
    }
  ];
}
