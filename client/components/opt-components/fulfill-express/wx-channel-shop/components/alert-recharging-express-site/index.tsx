import * as React from 'react';
import { Sweetalert } from 'zent';
import cx from 'classnames';

import {
  IExpressAggregateDTO,
  ISiteAggregateDTO
} from 'common/features/wx-channel-shop/apis/types';

import styles from './styles.scss';

export function alertRechargingExpressSite({
  title,
  expressCompany,
  expressSite
}: {
  title?: string;
  expressCompany: IExpressAggregateDTO;
  expressSite: ISiteAggregateDTO;
}) {
  Sweetalert.alert({
    title: '提示',
    content: (
      <section className={cx(styles.alert, title && styles['alert--with-title'])}>
        {title ? <header>{title}</header> : null}
        <div className={styles.content}>
          <p>联系物流网点进行充值：</p>
          <p>
            网点联系方式：{expressSite.siteInfo.contact.name} {expressSite.siteInfo.contact.mobile}
          </p>
          <p>小店面单账户：{expressCompany.expressAccountInfo.outShopId}</p>
          <p>物流网点客户编码：{expressSite.siteAccountInfo.outWaybillAccountId}</p>
        </div>
      </section>
    )
  });
}
