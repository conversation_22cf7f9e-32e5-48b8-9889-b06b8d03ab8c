import { request, requestWithCache } from '@youzan/retail-utils';
import { isRetailSingleStore } from '@youzan/utils-shop';
import retailAjax from '@youzan/retail-ajax';

export const queryAppLink = appId => {
  return request({
    url: '/v2/order/cloud/useApp.json',
    method: 'GET',
    data: { appId }
  });
};

const memorizeRequest = requestWithCache({
  expire: 120,
  resolver: ({ url }) => url
});

// 获取发货弹窗
export const fetchDeliveryWindowInfo = data => {
  data.isChainShop = !isRetailSingleStore;
  return request({
    url: '/youzan.retail.trademanager/1.0.0/chainwindow',
    data
  });
};

export const querySelfFetchSetting = data => {
  return request('youzan.retail.trademisc.query.selffetchsetting/2.0.0', data);
  // return request({
  //   url:
  //     '/com.youzan.retail.trade.misc.api.service.SelfFetchSettingService.querySelfFetchSetting/2.0.0',
  //   data
  // });
};

// 发货
export const sendGoods = data =>
  request({
    method: 'post',
    url: '/youzan.retail.trademanager.delivery/1.0.0/send',
    data: {
      ...data,
      deliveryItemsJson: JSON.stringify(data.deliveryItems),
      deliveryInfoJson: JSON.stringify(data.deliveryInfo)
    }
  });

// 获取物流公司
export const fetchExpressCompany = () =>
  memorizeRequest({
    url: '/youzan.logistics.express/3.0.0/get'
  });

// 获取电子面单签约的物流公司
export const fetchSystemCallExpressCompanys = () =>
  request({
    url: '/youzan.logistics.waybill.express/1.0.0/get'
  });

// 检查电子面单签约状态
export const checkShopElectronWayBill = () =>
  request({
    method: 'POST',
    url: '/v2/order/query/checkShopElectronWayBill.json'
  });

// 计算上门取件运费
export const calcExpressPostage = data =>
  request({
    url: '/youzan.retail.trade.manager.delivery.fee/1.0.0/calculate',
    data
  });

// 获取打印机列表
export const fetchPrinterList = data =>
  request({
    url: '/youzan.retail.peripheral.equipment/1.0.0/querypage',
    data: {
      ...data,
      // 1001代表是打印机
      peripheralTypeId: 1001,
      /**
       * 电子面单目前只支持：
       * 1001001: 365 云打印-M2
       * 1001003: 映美云 CLP-180
       */
      equipmentTypeIds: [1001001, 1001003],
      pageSize: 999
    }
  });

// 获取发货地址列表
export const fetchExpressAddressList = data =>
  request({
    url: '/youzan.logistics.waybill.available.address/1.0.0/get',
    data
  });

// 获取发货配置
export const fetchExpressConfig = data =>
  request({
    url: '/youzan.retail.trade.manager.express.config/1.0.0/get',
    data
  });

// 获取电子面单余额
export const fetchExpressSheetNum = data =>
  request({
    url: '/youzan.logistics.waybill.balance/1.0.0/get',
    data
  });

// 根据plu码获取商品sku，用于扫码录入商品重量时确定plu码对应的商品
export const fetchSpuByPluCode = data =>
  request({
    url: '/youzan.retail.product.spu/1.0.0/querybyplucode',
    data
  });

// 打印小票
export const printReceipt = data =>
  request({
    url: '/youzan.retail.printer.api.service/1.0.0/print',
    data
  });

// 检查是否有自动打单任务
export const queryDelayTask = data =>
  request({
    url: '/youzan.retail.delaytask.query/1.0.0',
    data
  });

// 取消自动打单任务
export const deleteDelayTask = data =>
  request({
    url: '/youzan.retail.delaytask.delete/1.0.0',
    data
  });

/**
 * 获取上一次使用的快递发货方式
 */
export const fetchLastExpressConfig = () =>
  request({ url: 'youzan.retail.trade.trademanager.lastsend/1.0.0/get' });

export const fetchAISelectResult = data =>
  request({
    url: '/youzan.retail.manager.delivery.getDeliveryStrategy/1.0.0',
    data
  });

// 查询多个服务商配送基本信息
export const fetchLocalChannels = data =>
  request({
    url: '/youzan.retail.trademanager.list.localchannels/1.0.0',
    data
  });

// 刷新单个服务商价格并重排序
export const refreshLocalChannel = data =>
  request({
    url: '/youzan.retail.trademanager.apply.refreshchannel/1.0.0',
    data,
    method: 'POST'
  });

export const searchWechatDeliveryConfig = data => {
  return retailAjax({
    url: '/v4/trade/delivery/wechat/config/search.json',
    method: 'GET',
    data
  });
};

/**
 * 获取lite网店信息
 */

export const getLiteStore = () => {
  return request({
    url: '/v2/order/query/getLiteStore.json',
    needTrim: true
  });
};

/**
 * 获取保价
 * @param {*} appId
 * @returns
 */
export const queryInsurance = ({ deliveryChannel, warehouseId }) => {
  return request({
    url: 'youzan.retail.local.insurance.query.info/1.0.0',
    method: 'GET',
    data: { deliveryChannel, warehouseId }
  });
};

export function queryDeliveryAlertVisibility(orderNo) {
  return request({
    url: 'youzan.retail.prompt.switch.query/1.0.0',
    data: { orderNo }
  });
}
