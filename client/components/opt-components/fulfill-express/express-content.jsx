/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable @youzan/yz-retail/no-new-date */
import React from 'react';
import {
  filter,
  find,
  get,
  isEmpty,
  cloneDeep,
  isNil,
  isFunction,
  isNumber,
  isArray,
  toNumber,
  omit,
  uniq
} from 'lodash';
import { debounce } from '@youzan/retail-armor';
import { Notify, Sweetalert, BlockLoading, Dialog, Button } from 'zent';
import { Form } from '@zent/compat';
import {
  global,
  isValidNumber,
  keepDecimal,
  div,
  minus,
  times,
  plus,
  track
} from '@youzan/retail-utils';
import { isRetailSingleStore } from '@youzan/utils-shop';
import { WxChannelShopPrinter } from 'wx-channel-shop-printer';

import { safeJsonParse } from 'common/utils';
import { ExpressType, DeliveryChannelType } from 'common/constants/common';
import { PRINT_BIZ_SENDING, PRINT_BIZ_PICKING, writeOffVerifyType } from 'common/constants';
import { StorageKeyJdExpressLastEWaybillTemplateId } from 'common/constants/fulfillment';
import { timesGoodsNum } from 'common/helper';
import { formatMoneyRound } from 'common/fns/format';
import {
  getMpIdFromOrderInfo,
  printWxChannelShopEWaybill
} from 'common/features/wx-channel-shop/utils';

import { DeliveryCallStrategy } from 'route/delivery/delivery-setting/localdelivery/container/fields/delivery-way/const';

import BeforeAfterActionHoc from 'components/before-after-action';
import alertContentRenderer from 'components/details-cpn/order-state/state-info';
import {
  isGYYOrder,
  isThirdPartyDelivery,
  checkIsMeituanOrder,
  checkIsMeituanPlatformOrder
} from 'common/biz-helper';
import { getLotCodeSelectedItems } from 'components/lot-code-selector/utils';
import { ORDER_TYPE } from 'common/constants/order';

import { EProcessStatus } from 'route/process-setting/types';

import { AuditStatus } from 'route/delivery/delivery-setting/localdelivery/container/fields/delivery-way/const';
import { enableProvider } from 'route/delivery/delivery-setting/localdelivery/container/fields/delivery-way/api';
import { SupportVendorDeliveryScope } from 'common/constant';
import { RecommendServiceDialog, RecommendServiceAlert } from './components/recommend-service-area';
import WarehouseInfo from './components/warehouse-info';
import ExpressTypes from './components/express-types';

import GoodsSelectTypeField, { GoodsSelectType } from './components/goods-select-type-field';
import WithoutVerifyCode from './components/with-verify-code';

import Action from './components/action';
import Alert from './components/alert';
import DeliveryScopeAlert from './components/delivery-scope-alert';
import { IntelligentAlert } from './components/intelligent-alert';
import { SEND_TYPE_EXTRA_MAP } from './components/express-extra';
import ExpressInfo from './components/express-info';
import GoodsList from './components/goods-list';
import ScanGoodsList from './components/scan-goods-list';
import Spread from './components/spread';
import GoodsScanEntry from './components/goods-scan-entry';
import { DeliveryRemark } from './components/delivery-remark';
import style from './style.scss';
import withTab from './with-tab';
import * as api from './api';
import {
  checkIsDeliveringByWxChannelShopEWaybill,
  checkCanDeliveryWxChannelShopEWaybill,
  createWxChannelShopEWaybillOrder,
  getWxChannelShopEWaybillDeliveryInfo,
  createWxChannelShopEWaybillValidateArr
} from './wx-channel-shop/utils';
import { DeliveryCallType } from './components/call-type';
import { trackThirdDeliveryTextShow } from './track';

import {
  SEND_TYPE_CODE,
  validateInfo,
  NOT_SUFFICIENT_FUNDS_CODE,
  JD_EXPRESS_CODE,
  EXPRESS_FOR_COMPANY,
  NO_EXPRESS,
  SF_EXPRESS_CODE,
  PERIOD_EXPRESS_ERR_CODE,
  PRODUCE_SHOP_SEND_ERR_CODE,
  WX_ORDER_MARK,
  WX_EXPRESS,
  CalcWeightType,
  MaxGoodsWeight,
  EXPRESS_CHANNEL_KEY_NAME,
  THIRD_LOCAL_DELIVERY,
  SELF_LOCAL_DELIVERY,
  EXPRESS_SYSTEM_CALL
} from './constants';
import { defaultState } from './default';
import { shouldRecommendServiceTipRemind, handleRecommendServiceNotRemind } from './utils';

const { openDialog, closeDialog } = Dialog;
const { createForm } = Form;
const BeforeAction = BeforeAfterActionHoc(Action);
const { USER_INFO, KDT_ID } = global;
const { expressDelivery } = global.BUSINESS;
// 是否开启了电商能力
const hasExpressDelivery = expressDelivery !== 0;

const { Express, SelfFetch, City } = ExpressType;
export const EXPRESS_TYPE_KEY_NAME = 'retail.order.localdelivery.express.type';

const SelectionWarning = '请至少选择一个商品！';

/**
 * 批号数量需要 * 1000
 */
function processLotCodeItemsNumForServer(lotCodeItems) {
  return lotCodeItems.map(item => ({
    ...item,
    num: times(item.num, 1000)
  }));
}

@createForm({ scrollToError: true })
@withTab
class ExpressContent extends React.Component {
  state = {
    sending: false,

    // 多包裹
    multiPackInfo: [],

    // 商品选择方式
    goodsSelectType: GoodsSelectType.Choose,

    // 发货方式
    expressType: get(this.props, 'data.supportDeliveryTypes[0].id'),

    localDeliveryCallConfig: get(this.props, 'data.localDeliveryCallConfigInfo', null),

    // 所有的物流公司
    expressCompanyList: [],

    // 电子面单签约的物流公司
    systemCallExpressCompanys: [],

    // 打印机列表
    printerList: [],

    selectedItemIds: [],
    scanItemList: [],

    // 商品串码
    goodsSerialNos: [],

    // 商品批号
    selectedLotCodeDataByItemId: {},

    // 商品差价
    goodsSpreads: {},

    // 商品总差价
    totalSpread: 0,

    // 是否同意协议
    isAgreeProtocol: false,

    // 是否可以发货
    deliveryDisabled: true,

    ...cloneDeep(defaultState),

    // 第三方同城配送服务Map
    channelMap: {},

    // 智选配送文案
    AISelectText: ['', ''],

    time: '',

    fetching: false,

    exceptionInfo: null,

    // 发货备注
    deliveryRemark: '',

    // 专人直送
    isDirectDelivery: false,

    /** 同城发货配送公司预计送达时间，目前只有闪送接入 */
    estimateDeliveryTimeDesc: '',

    // 发货方式，三方配送提示信息
    thirdDeliveryPromptConfig: {},

    // 配送服务商列表
    localChannels: [],

    recommendServiceBol: false,
    recommendTipVisible: false,
    recommendDialogVisible: false,
    recommendationChannel: null, // 推荐服务商
    activeRecommandClose: false, // 该次弹窗打开期间，用户主动关闭了服务商推荐提示标识
    deliveryTool: 0 // 交通工具
  };

  componentDidMount() {
    this.printer = new WxChannelShopPrinter();
    const recommendServiceBol = shouldRecommendServiceTipRemind();

    this.setState({
      recommendTipVisible: recommendServiceBol,
      recommendServiceBol
    });
  }

  componentWillUnmount() {
    if (this.printer) {
      this.printer.disconnect();
    }
  }

  // TODO: 抽个函数出来，现在里面逻辑太多了
  componentWillReceiveProps(nextProps) {
    const {
      currentTabId: nextTabId,
      data = {
        itemInfos: [],
        supportDeliveryTypes: []
      },
      channelType
    } = nextProps;
    const { expressType, expressCompanyList } = this.state;

    if (!isEmpty(data) && data !== this.props.data) {
      if (data.deliveryRemark) {
        this.setState({
          deliveryRemark: data.deliveryRemark
        });
      }
      // 发货方式为物流的需要请求展示数据
      if (data.distInfo.distType === Express && expressCompanyList.length === 0) {
        this.fetchExpressCompanys();

        // 在线下单
        if (
          get(data, 'supportDeliveryTypes', []).some(
            type => type.id === SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL
          )
        ) {
          this.fetchPrinterList();
          this.fetchElectronCompanys();
        }
      }

      // 发货方式如果是 在线下单 or 自己联系快递
      // 需要调用接口看有么有上一次记忆的方式
      if (
        get(data, 'supportDeliveryTypes', []).some(type =>
          [SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL, SEND_TYPE_CODE.EXPRESS_FOR_COMPANY].includes(type.id)
        )
      ) {
        api
          .fetchLastExpressConfig()
          .then(options => {
            const { orderMark } = this.props;
            if (options && options.deliveryType) {
              if (orderMark !== WX_ORDER_MARK && options.deliveryType === WX_EXPRESS) {
                // 非微信小程序订单不支持微信物流，上次发货方式是微信物流时，不使用记忆
                options.deliveryType = undefined;
              }
              this.initState(options, data);
            } else {
              this.setExtraState(data, expressType);
            }
          })
          .catch(() => {
            this.setExtraState(data, expressType);
          });
      } else {
        this.setExtraState(data, expressType);
      }

      this.initLocalDelivery(nextProps);

      this.initLocalDeliveryCallConfig(nextProps);

      this.setState({ goodsSpreads: this.reduceGoodsSpreads(data.itemInfos) });

      // 发货商品列表默认全选
      this.handleSelectAllItems(data);

      const isMeituanPlatformOrder = checkIsMeituanPlatformOrder(channelType);
      // 非美团平台订单 同城送优先使用本地存储的配送方式
      if (data.distInfo.distType === City && !isMeituanPlatformOrder) {
        const localExpressType = window.localStorage.getItem(EXPRESS_TYPE_KEY_NAME);
        if (localExpressType) {
          this.setState({
            expressType: +localExpressType
          });
        }
      }
      this.getDefaultGoodsWeight(data);
    }
    if (nextTabId !== this.props.currentTabId) {
      this.init(data);
      this.handleSelectAllItems(data);
      this.getDefaultGoodsWeight(data);
    }

    if (this.state.localDelivery.channel === '') {
      this.setState(prev => ({
        localDelivery: { ...prev.localDelivery, channel: { deliveryChannel: '' } }
      }));
    }

    this.setThirdDeliveryPromptConfig(data);

    const PreLocalStorageChannel = window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME);

    if (
      nextProps.data?.channels?.length &&
      !!PreLocalStorageChannel &&
      !this?.state?.localDelivery?.channel?.deliveryChannel
    ) {
      this.handleDeliveryCompanyChange(
        {
          target: {
            value: { deliveryChannel: PreLocalStorageChannel ? Number(PreLocalStorageChannel) : '' }
          }
        },
        true
      );
    }
  }

  handleSelectAllItems = data => {
    const { orderType } = this.props;
    let selectedItemIds =
      data.itemInfos?.filter(({ canSelect }) => canSelect)?.map(({ itemIdStr }) => itemIdStr) || [];
    if (orderType === ORDER_TYPE.PERIOD.value) {
      selectedItemIds = uniq(selectedItemIds);
    }
    this.setState({
      selectedItemIds
    });
  };

  // 计算商品重量默认值
  getDefaultGoodsWeight = data => {
    const { itemInfos = [], channels = [], supportDeliveryTypes = [] } = data || {};
    const { localDelivery } = this.state;
    const { channel = {} } = localDelivery;
    const { maxWeight = MaxGoodsWeight } = find(channels, channel) || {};
    const { id: expressType } = supportDeliveryTypes?.[0] || {};
    if (expressType === SEND_TYPE_CODE.THIRD_LOCAL_DELIVERY) {
      const totalGoodsWeight =
        itemInfos
          ?.filter(({ canSelect }) => canSelect)
          ?.reduce((totalGoodsWeight, itemInfo) => {
            return totalGoodsWeight + (+itemInfo.weight || 0);
          }, 0) || 0;
      const newCalcWeightType =
        totalGoodsWeight > 0 ? CalcWeightType.Custom : CalcWeightType.Default;
      const newLocalDelivery = {
        distWeight:
          totalGoodsWeight > 0
            ? `${keepDecimal(div(Math.min(+totalGoodsWeight, +maxWeight), 1000), 2)}`
            : ''
      };
      this.setState(
        prev => ({
          calcWeightType: newCalcWeightType,
          localDelivery: { ...prev.localDelivery, ...newLocalDelivery }
        }),
        () => {
          this.afterChangeDistWeight(newLocalDelivery.distWeight);
        }
      );
    }
  };

  get isMultiExpress() {
    const {
      data: { itemInfos = [] },
      supportMoreDistOrder
    } = this.props;
    const { selectedItemIds, expressType } = this.state;

    // 是否可以展示多运单发货的条件
    // 1.配置已打开
    // 2.只选了一个商品
    // 3.商品数量大于1
    // 4.发货方式为自己联系快递
    const isMultiExpress =
      expressType === EXPRESS_FOR_COMPANY &&
      selectedItemIds.length === 1 &&
      (find(itemInfos, ({ itemIdStr }) => itemIdStr === selectedItemIds[0]) || {}).num > 1000 &&
      supportMoreDistOrder;

    return isMultiExpress;
  }

  setExtraState = (data, expressType) => {
    const extraState = {};

    if (!expressType) {
      extraState.expressType = get(data, 'supportDeliveryTypes[0].id');
    }

    extraState.goodsSerialNos = data.itemInfos.map(({ itemIdStr }) => ({
      itemId: itemIdStr,
      serialNos: []
    }));

    extraState.deliveryDisabled = this.checkSystemExpressDisable(
      expressType || extraState.expressType
    );

    if (!isEmpty(extraState)) {
      this.setState(extraState);
    }
  };

  // 电子面单排序
  sortSystemCallExpressCompanys = (express, systemCallExpressCompanys) => {
    let newSystemCallExpressCompanys = [...systemCallExpressCompanys];
    newSystemCallExpressCompanys = systemCallExpressCompanys.sort(
      (pre, next) => next.recommendPriority - pre.recommendPriority
    );
    // 默认值放到最前面
    if (express.historyExpressId && newSystemCallExpressCompanys?.length > 0) {
      const max = newSystemCallExpressCompanys[0];
      newSystemCallExpressCompanys = newSystemCallExpressCompanys
        .map(item => ({
          ...item,
          recommendPriority:
            item.expressId === express.historyExpressId
              ? (max.recommendPriority || 0) + 1
              : item.recommendPriority
        }))
        .sort((pre, next) => next.recommendPriority - pre.recommendPriority);
    }
    return newSystemCallExpressCompanys;
  };

  /**
   * 根据获取到的快递配置信息，初始化 State
   *
   * @params {Object} options
   * @returns undefined
   */
  initState = (options, data) => {
    const auditNo = get(options, 'addressInfo.auditNo', '');
    const deliveryType = get(options, 'deliveryType');

    this.setState(
      prevState => {
        const express = {
          ...prevState.express,
          historyExpressId: get(options, 'expressId', ''),
          expressId: get(options, 'expressId', ''),
          expressAddrList: get(options, 'addressInfo') ? [get(options, 'addressInfo')] : [],
          auditNo,
          printerChannel: get(options, 'printerInfo.printerChannel', ''),
          printerDeviceNo: get(options, 'printerInfo.printerDeviceNo', ''),
          printerKey: get(options, 'printerInfo.printerKey', '')
        };
        let expressType = data?.supportDeliveryTypes.find(item => item.id === deliveryType)
          ? deliveryType
          : data?.supportDeliveryTypes[0].id || prevState.expressType;
        // 如果上次记忆的发货方式是电子面单，那么当没有电商能力时，电子面单选项会被隐藏，所以不要用上次记忆的发货模式
        if (!hasExpressDelivery && deliveryType === SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL) {
          expressType = prevState.expressType;
        }
        return {
          ...prevState,
          express,
          expressType
        };
      },
      () => {
        this.setExtraState(data, deliveryType);
        // 电子面单的时候才请求收货地址
        if (deliveryType === SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL) {
          this.fetchExpressAddressList({ auditNo });
        }
      }
    );
  };

  // 初始化
  init = data => {
    this.setState({
      expressType: get(data, 'supportDeliveryTypes[0].id'),
      selectedItemIds: [],
      scanItemList: [],
      // 目前只有同城配送支持发货备注
      deliveryRemark: data.distInfo.distType === City ? data.deliveryRemark : ''
    });
  };

  initLocalDelivery = props => {
    const { data } = props;

    if (data.distInfo.distType === City && data.deliveryStrategySwitch) {
      this.setState({
        channelMap: (data.channels || []).reduce((map, channel) => {
          map[channel.deliveryChannel] = channel;
          return map;
        }, {})
      });
    }
  };

  initLocalDeliveryCallConfig = props => {
    const localDeliveryCallConfig = get(props, 'data.localDeliveryCallConfigInfo', null);

    if (!localDeliveryCallConfig) return;
    this.setState({ localDeliveryCallConfig });
  };

  /**
   * 判断商品是否可退差价
   *
   * @memberof ExpressContent
   */
  canRefundSpread = ({ configInfo, canSelect, realPay, refundFee }) => {
    return !isEmpty(configInfo) && canSelect && minus(realPay, refundFee) > 0;
  };

  reduceGoodsSpreads = itemInfos => {
    const { goodsSpreads } = this.state;
    if (!isArray(itemInfos)) {
      return {};
    }
    return itemInfos.reduce((ret, goods) => {
      const { itemIdStr } = goods;
      if (this.canRefundSpread(goods)) {
        ret[itemIdStr] = goodsSpreads[itemIdStr] || [];
      }
      return ret;
    }, {});
  };

  getDeliveryDisabled = () => {
    const { express = {}, multiPackInfo = [], selectedItemIds = [] } = this.state;
    if (this.isMultiExpress) {
      if (multiPackInfo.length === 0 || selectedItemIds.length === 0) {
        return true;
      }
      return !multiPackInfo.reduce(
        (total, { expressId: expressIdP, expressNo: expressNoP }) =>
          total && expressIdP && expressNoP,
        true
      );
    }
    const { expressId, expressNo } = express;
    return !(expressId && expressNo);
  };

  // 物流禁用校验
  checkExpressDisable = () => {
    const { expressType } = this.state;
    if (expressType !== EXPRESS_FOR_COMPANY) {
      return;
    }
    const deliveryDisabled = this.getDeliveryDisabled();
    this.setState({ deliveryDisabled });
  };

  // 电子面单禁用的情况
  checkSystemExpressDisable = (expressType, isAgreeProtocol) => {
    const errorDesc = get(
      this.props,
      'data.electronicSheetExceptionInfo.electronicSheetExceptionDesc'
    );
    const agreeProtocol = isNil(isAgreeProtocol) ? this.state.isAgreeProtocol : isAgreeProtocol;

    const { expressId, isCashPledgeEnough, configErrorDesc, weight } = this.state.express;

    if (expressType !== SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL) {
      return false;
    }

    if (expressId === SF_EXPRESS_CODE) {
      return !(agreeProtocol && !errorDesc);
    }

    if (expressId === JD_EXPRESS_CODE) {
      return !!(!isCashPledgeEnough || configErrorDesc || !weight);
    }

    return !expressId || errorDesc;
  };

  // 获取物流公司
  fetchExpressCompanys = () => {
    api
      .fetchExpressCompany()
      .then(({ allExpress = [] }) => {
        const expressCompanyList = allExpress.filter(item => item.display);
        this.setState({ expressCompanyList });
      })
      .catch(err => {
        Notify.error(err.msg || '获取物流公司列表失败！');
      });
  };

  // 获取电子面单物流公司列表
  fetchElectronCompanys = () => {
    api
      .fetchSystemCallExpressCompanys()
      .then((res = []) => {
        let systemCallExpressCompanys = res;
        // 连锁店铺电子面单屏蔽京东物流
        if (!isRetailSingleStore) {
          systemCallExpressCompanys = res.filter(({ expressId }) => expressId !== JD_EXPRESS_CODE);
        }
        this.setState({ systemCallExpressCompanys });
      })
      .catch(err => {
        Notify.error(err.msg || '获取物流公司列表失败！');
      });
  };

  /**
   * 获取发货地址列表
   * @params {Object} options
   */
  fetchExpressAddressList = options => {
    const { express, expressType } = this.state;

    if (!express.expressId || expressType === NO_EXPRESS) return;

    api
      .fetchExpressConfig({ expressId: express.expressId, deliveryWay: 1 })
      .then(
        ({
          deliveryAddress = [],
          deliveryTypeList = [],
          electronicSheetExceptionInfo = {},
          ...data
        }) => {
          const expressAddrList = deliveryAddress.map(
            ({
              auditNo = '',
              address = '',
              cityName = '',
              countyName = '',
              provinceName = ''
            }) => ({
              auditNo,
              displayAddress: `${provinceName}${cityName} ${countyName} ${address}`
            })
          );

          const sendType = get(deliveryTypeList, '[0].code');
          const auditNo = get(options, 'auditNo') || get(deliveryAddress, '[0].auditNo', '');

          const newState = {
            express: {
              ...express,
              expressAddrList,
              auditNo,
              sendTypes: deliveryTypeList,
              sendType,
              configErrorCode: '',
              configErrorDesc: ''
            }
          };

          switch (express.expressId) {
            case JD_EXPRESS_CODE:
              {
                const lastId = safeJsonParse(
                  localStorage.getItem(StorageKeyJdExpressLastEWaybillTemplateId)
                );
                if (lastId) {
                  newState.express.eWaybillTemplateId = lastId;
                }
                newState.express.eWaybillTemplateOptions =
                  data.expressWaybillSizeTypes?.map(item => {
                    return {
                      id: item.templateUrl,
                      name: item.specification
                    };
                  }) || [];
              }

              break;

            default:
              break;
          }

          if (!isEmpty(electronicSheetExceptionInfo)) {
            newState.express.configErrorCode =
              electronicSheetExceptionInfo.electronicSheetExceptionCode;
            newState.express.configErrorDesc =
              electronicSheetExceptionInfo.electronicSheetExceptionDesc;
          }

          newState.deliveryDisabled = this.checkSystemExpressDisable(expressType);
          this.setState(newState);
        }
      )
      .catch(err => {
        Notify.error(err.msg || '发货地址列表数据获取失败');
        throw err;
      });
  };

  // 获取打印机列表
  fetchPrinterList = () => {
    api
      .fetchPrinterList()
      .then(({ items = [] }) => {
        this.setState({ printerList: items });
      })
      .catch(err => {
        Notify.error(err.msg || '获取打印机列表失败！');
      });
  };

  // 获取面单量情况
  fetchExpressSheetNum = () => {
    let { deliveryDisabled } = this.state;
    const {
      express: { expressId, auditNo }
    } = this.state;
    api
      .fetchExpressSheetNum({ expressId, auditNo })
      .then(({ remainNum, isLimit }) => {
        let printerErrDesc = '';

        if (isLimit && remainNum < 1) {
          printerErrDesc = '面单余量不足，请联系网点充值。';
          deliveryDisabled = true;
        }

        this.setState({
          printerErrDesc,
          deliveryDisabled
        });
      })
      .catch(err => Notify.error(err.msg || '获取面单余量情况失败！'));
  };

  setExtraParams = (params = {}) => this.setState(params);

  getGoodsSpreadInfo = itemId => {
    const { goodsSpreads } = this.state;
    if (!goodsSpreads[itemId]) {
      return null;
    }
    return goodsSpreads[itemId].map(spread => ({
      realWeight: spread.realWeight || 0,
      diffAmount: spread.spreadDiff ? times(spread.spreadDiff, 100) : 0
    }));
  };

  getGoodsSerialInfo = (itemId, isSerialItem) => {
    if (!isSerialItem) {
      return null;
    }
    const { goodsSerialNos = [] } = this.state;
    const { serialNos = [] } = find(goodsSerialNos, { itemId }) || {};

    return {
      serialNos,
      isSerialItem
    };
  };

  getDeliveryItemsParam = () => {
    const { selectedItemIds, selectedLotCodeDataByItemId } = this.state;
    const { itemInfos = [] } = this.props.data;
    // 如果不能展示 checkbox, 应该默认全选中
    return this.isShowSelection
      ? selectedItemIds.map(itemId => {
          const { num, isSerialItem, isLotCodeItem } = find(itemInfos, { itemIdStr: itemId }) || {};
          return {
            itemId,
            num,
            ...this.getGoodsSerialInfo(itemId, isSerialItem),
            ...(isLotCodeItem
              ? {
                  isLotCodeItem,
                  lotCodeInfos: processLotCodeItemsNumForServer(
                    getLotCodeSelectedItems(selectedLotCodeDataByItemId[itemId])
                  )
                }
              : {}),
            priceDiffInfo: this.getGoodsSpreadInfo(itemId)
          };
        })
      : itemInfos
          .filter(({ isSerialItem, canSelect }) => !isSerialItem || canSelect)
          .map(({ itemIdStr: itemId, num, isSerialItem, isLotCodeItem }) => ({
            itemId,
            num,
            ...this.getGoodsSerialInfo(itemId, isSerialItem),
            ...(isLotCodeItem
              ? {
                  isLotCodeItem,
                  lotCodeInfos: processLotCodeItemsNumForServer(
                    getLotCodeSelectedItems(selectedLotCodeDataByItemId[itemId])
                  )
                }
              : {}),
            priceDiffInfo: this.getGoodsSpreadInfo(itemId)
          }));
  };

  // 生成ajax参数
  getDeliveryGoodsParams = () => {
    const {
      expressType,
      localDelivery,
      express,
      selfFetch,
      printerList,
      multiPackInfo,
      insurance,
      deliveryRemark,
      isDirectDelivery,
      canDirectDelivery
    } = this.state;

    const { deliveryNo, channels = [] } = this.props.data;

    const deliveryItems = this.getDeliveryItemsParam();
    const { printerDeviceNo, weight, startTime = 0 } = express;
    const printerInfo = find(printerList, { equipmentNumber: printerDeviceNo }) || {};

    const { distWeight, channel, ...others } = localDelivery;
    const { initialWeight = 1000 } = find(channels, channel) || {};
    const pickUpStartTime = div(+startTime, 1000);
    const pickUpEndTime = pickUpStartTime + 3600;
    const distChannel = channel.deliveryChannel;

    let deliveryInfo = {
      deliveryType: expressType,
      localDelivery: {
        ...others,
        channelName: channel.deliveryChannelDesc,
        channel: distChannel,
        appId: channel.appId,
        distWeight: distWeight * 1000 || initialWeight, // 默认5kg,
        deliveryTool: expressType === THIRD_LOCAL_DELIVERY ? this.state.deliveryTool : 0
      },
      express: {
        ...omit(express, ['eWaybillTemplateId', 'eWaybillTemplateOptions']),
        weight: weight * 1000,
        // 打印机型号
        printerChannel: printerInfo.equipmentTypeId,
        // 设备编号
        printerDeviceNo,
        // 设备密钥
        printerKey: printerInfo.equipmentKey,
        startTime: express.expressId === SF_EXPRESS_CODE ? pickUpStartTime : '',
        endTime: express.expressId === SF_EXPRESS_CODE ? pickUpEndTime : '',
        ...(express.expressId === JD_EXPRESS_CODE
          ? {
              templateUrl: express.eWaybillTemplateId
            }
          : {})
      },
      selfFetch,
      deliveryRemark
    };

    const isCombinationDelivery = this.isMultiExpress;
    if (isCombinationDelivery) {
      deliveryInfo = omit(deliveryInfo, 'express');
      const deliveryPackages =
        multiPackInfo.length === 1
          ? [
              {
                express: omit(multiPackInfo[0], 'num'),
                num: get(deliveryItems, '[0].num')
              }
            ]
          : multiPackInfo.map(({ num, ...other }) => ({
              express: other,
              num: timesGoodsNum(num)
            }));
      deliveryInfo.deliveryPackages = deliveryPackages;
    }

    // 保价
    if (insurance) {
      const { insuranceAmount, insuranceProduct, insuranceProductDesc } = insurance;
      deliveryInfo.localDelivery.insuranceAmount = insuranceAmount;
      deliveryInfo.localDelivery.insuranceProduct = insuranceProduct;
      deliveryInfo.localDelivery.insuranceProductDesc = insuranceProductDesc;
    }

    return {
      operatorName: USER_INFO.staffName,
      orderNo: this.props.orderNo,
      isCombinationDelivery,
      deliveryItems,
      deliveryInfo,
      deliveryNo,
      isDirectDelivery: canDirectDelivery ? isDirectDelivery : null
    };
  };

  // 异步输入校验
  checkInputAsync = () => {
    const { zentForm } = this.props;

    return new Promise((resolve, reject) => {
      if (isEmpty(zentForm.getFormValues())) {
        this.checkInput() ? resolve() : reject();
      } else {
        zentForm.setFormDirty(true);
        zentForm.validateForm(true, () => {
          const checkResult = zentForm.isValid() && this.checkInput();

          checkResult ? resolve() : reject();
        });
      }
    });
  };

  get isShowSelection() {
    const { distInfo = {} } = this.props.data;
    if (distInfo.distType === SelfFetch) {
      return false;
    }
    return true;
  }

  // 校验输入值
  checkInput = () => {
    const {
      selectedItemIds = [],
      expressType,
      multiPackInfo,
      goodsSelectType,
      scanItemList,
      selectedLotCodeDataByItemId
    } = this.state;

    if (goodsSelectType === GoodsSelectType.Scan) {
      if (scanItemList.length === 0) {
        Notify.error('请扫码录入需要发货的商品');
        return false;
      }
      if (scanItemList.some(v => v.scanGoodsNum !== v.goodsNum)) {
        Notify.error('发货商品已扫数量需要等于总发货量');
        return false;
      }
    }

    if (selectedItemIds.length === 0 && this.isShowSelection) {
      Notify.error(SelectionWarning);
      return false;
    }

    // 获取发货商品
    const { itemInfos = [] } = this.props.data;
    const deliveryItems = this.isShowSelection
      ? selectedItemIds
          .map(itemId => find(itemInfos, { itemIdStr: itemId }))
          .filter(item => Boolean(item))
      : itemInfos;

    // 批号检验
    if (
      deliveryItems.some(item => {
        return (
          item.isLotCodeItem &&
          (selectedLotCodeDataByItemId[item.itemIdStr]?.selectedLotCodes || []).length === 0
        );
      })
    ) {
      Notify.error('请选择批号');
      return false;
    }

    let isPassValidate = true;

    if (+expressType === EXPRESS_FOR_COMPANY && this.isMultiExpress) {
      if (multiPackInfo.length === 1) {
        return true;
      }

      const sumGoodsNum = multiPackInfo.reduce((sum, { num }) => Number(sum) + Number(num), 0);
      const itemInfos = get(this.props, 'data.itemInfos', []);
      const selectGoodsNum = (
        find(itemInfos, ({ itemIdStr }) => itemIdStr === selectedItemIds[0]) || {}
      ).num;

      if (+selectGoodsNum !== timesGoodsNum(sumGoodsNum)) {
        Notify.error('各包裹的商品数量总和需要等于商品总数！');
        return false;
      }
      return true;
    }

    let validateArr;

    if (
      checkIsDeliveringByWxChannelShopEWaybill({
        channelType: this.props.channelType,
        expressType: +expressType
      })
    ) {
      validateArr = createWxChannelShopEWaybillValidateArr();
    } else {
      validateArr = validateInfo[+expressType];
    }

    if (validateArr) {
      isPassValidate = validateArr.every(({ path, errDesc, validate, onError }) => {
        const paramVal = get(this.state, path, false);
        const isValid = isNil(validate) ? paramVal : validate(paramVal);

        if (!isValid) {
          if (onError) {
            onError(paramVal);
          } else {
            Notify.error(errDesc);
          }
        }

        return isValid;
      });
    }

    return isPassValidate;
  };

  // 获取多网点id
  getStoreId = () => {
    const { storeId = 0 } = this.props;

    if (isRetailSingleStore && get(window, '_global.business.isShowMultiStore', false)) {
      return storeId;
    }

    return null;
  };

  onSimpleStateChange = stateKey => value => {
    this.setState({ [stateKey]: value });
  };

  /**
   * 根据重量计算差价
   *
   * @memberof ExpressContent
   */
  computeGoodsSpread = (rawWeight, perWeight, perRealPay) => {
    // 输入需是可用的重量，数值且只保留3位小数
    const weight = toNumber(keepDecimal(rawWeight, 3));

    if (weight > 0) {
      const realWeight = times(weight, 1000);
      if (!isNumber(perWeight) || perWeight <= 0 || !isNumber(perRealPay) || perRealPay < 0) {
        return { weight, realWeight };
      }
      const rawWeightDiff = minus(realWeight, perWeight);
      let rawSpreadDiff = 0;
      if (rawWeightDiff < 0) {
        const formatPerRealPay = keepDecimal(perRealPay, 2);
        rawSpreadDiff = div(times(formatPerRealPay, minus(perWeight, realWeight)), perWeight);
      }
      return {
        weight,
        realWeight,
        formatRealWeight: keepDecimal(div(realWeight, 1000), 3),
        weightDiff: keepDecimal(div(rawWeightDiff, 1000), 3),
        spreadDiff: formatMoneyRound(rawSpreadDiff)
      };
    }
    return { weight: rawWeight };
  };

  // 录入真实重量后更改商品差价信息
  onGoodsSpreadsChange = (itemIdStr, index, weight) => {
    const { goodsSpreads, selectedItemIds } = this.state;
    const { itemInfos = [], distInfo = {} } = this.props.data;
    this.setState(
      itemInfos.reduce(
        (ret, goods) => {
          const { itemIdStr: itemId, configInfo, realPay, refundFee, goodsNum } = goods;
          if (this.canRefundSpread(goods)) {
            const { goodsSpreads: retSpreads } = ret;
            let spreads = goodsSpreads[itemId] || [];
            if (distInfo.distType !== SelfFetch && !selectedItemIds.includes(itemId)) {
              spreads = [];
            }

            if (itemIdStr === itemId) {
              const perWeight = div(configInfo.configWeight, configInfo.configNum);
              const perRealPay = div(minus(realPay, refundFee), goodsNum);
              spreads[index] = this.computeGoodsSpread(weight, perWeight, perRealPay);
            }

            retSpreads[itemId] = spreads.map(spread => {
              if (spread && spread.spreadDiff) {
                ret.totalSpread = plus(ret.totalSpread, times(spread.spreadDiff, 100));
              }
              return spread;
            });
          }
          return ret;
        },
        { goodsSpreads: {}, totalSpread: 0 }
      )
    );
  };

  // 扫码获得商品重量的回调
  onGetGoodsWeight = ({ itemIdStr, weight }) => {
    const { goodsSpreads, selectedItemIds } = this.state;
    const spreads = goodsSpreads[itemIdStr];
    if (!spreads) {
      Notify.error('当前商品不可录入重量');
      return;
    }
    const enterGoodsWeight = () => {
      const { zentForm } = this.props;
      const mustFillNum = Object.keys(zentForm.getFormValues()).filter(
        key => key.split('-')[0] === itemIdStr
      ).length;
      let canFillSpreadIndex = spreads.findIndex(spread => !spread || !spread.realWeight);
      if (canFillSpreadIndex < 0 && mustFillNum > spreads.length) {
        canFillSpreadIndex = spreads.length;
      }
      if (canFillSpreadIndex < 0) {
        Notify.error('当前商品实发重量录入完毕，无法再次录入');
        return;
      }
      if (!isValidNumber(weight)) {
        Notify.error('录入的重量不合法');
        return;
      }
      const formatGoodsWeight = keepDecimal(div(weight, 1000), 3);
      zentForm.setFieldsValue({
        [`${itemIdStr}-${canFillSpreadIndex}`]: formatGoodsWeight
      });
      this.onGoodsSpreadsChange(itemIdStr, canFillSpreadIndex, formatGoodsWeight);
    };
    if (!selectedItemIds.includes(itemIdStr)) {
      this.onItemSelect(selectedItemIds.concat(itemIdStr), enterGoodsWeight);
    } else {
      enterGoodsWeight();
    }
  };

  // 打印小票
  printReceipt = bizType => {
    const { orderNo, orderInfo, data = {}, storeKdtId } = this.props;
    const { distInfo = {}, itemInfos = [], deliveryNo } = data;
    const { selectedItemIds } = this.state;
    const printParam = {
      adminName: USER_INFO.staffName,
      billNo: orderNo,
      bizType
    };

    if (storeKdtId) {
      printParam.buKdtId = storeKdtId;
    }

    if (deliveryNo) {
      printParam.subBillNo = deliveryNo;
    }

    if (distInfo.distType !== SelfFetch) {
      if (selectedItemIds.length === 0) {
        Notify.error(SelectionWarning);
        return Promise.resolve();
      }
      printParam.bizItemList = selectedItemIds;
    } else {
      printParam.bizItemList = itemInfos
        .filter(goods => goods.canSelect)
        .map(goods => goods.itemIdStr);
    }
    const print = () => {
      return api.printReceipt(printParam).catch(error => {
        const receiptName = bizType === PRINT_BIZ_PICKING ? '拣货小票' : '发货小票';
        Notify.error(error.msg || `打印${receiptName}失败`);
      });
    };
    const delayTastParams = {
      orderNo: get(orderInfo, 'mainOrderInfo.orderNo'),
      deliveryNo:
        get(orderInfo, 'fulfillOrder.fulfillNo') ||
        get(orderInfo, 'mainOrderInfo.fulfillNos[0]') ||
        deliveryNo
    };

    // 如果是 发货小票 ，不用检查延时打印任务
    return bizType === PRINT_BIZ_SENDING
      ? print()
      : api
          .queryDelayTask(delayTastParams)
          .then(res => {
            // 有延时打印
            if (res) {
              openDialog({
                dialogId: 'checkDelayPrintTicketDialog',
                title: '提示',
                children: '该拣货小票打印之后，系统将不会再自动打印，是否确定现在打印？',
                footer: (
                  <>
                    <Button
                      onClick={() => {
                        closeDialog('checkDelayPrintTicketDialog');
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        api.deleteDelayTask(delayTastParams);
                        print();
                        closeDialog('checkDelayPrintTicketDialog');
                      }}
                    >
                      确定
                    </Button>
                  </>
                )
              });
            } else {
              print();
            }
          })
          .catch(err => {
            Notify.error(err.msg || `打印失败`);
          });
  };

  // 获取计算运费接口的参数
  getCaclPostageParams = type => {
    const { distWeight = 0 } = this.state.localDelivery;
    let { channel } = this.state.localDelivery;
    const { weight, expressId, auditNo } = this.state.express;
    const { isDirectDelivery, canDirectDelivery, expressType } = this.state;
    const { orderNo, data = {} } = this.props;
    const { channels = [] } = data;

    // 如果没有设置过 channel ，使用在 当前 channels 的默认值
    if (!channel?.deliveryChannel) {
      let PreLocalStorageChannel = window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME);
      PreLocalStorageChannel = PreLocalStorageChannel ? Number(PreLocalStorageChannel) : '';
      if (PreLocalStorageChannel !== 9 && PreLocalStorageChannel) {
        channel = {};
        channel.deliveryChannel = PreLocalStorageChannel;
      }
    }
    const { initialWeight = 1000, supportInsurance } = find(channels, channel) || {};

    const deliveryPointId = isRetailSingleStore ? KDT_ID : data.warehouseId;

    const distChannel = channel.deliveryChannel;

    const params =
      type === 'EXPRESS'
        ? {
            distWeight: weight * 1000,
            orderNo,
            deliveryType: 14,
            expressId,
            auditNo
          }
        : {
            distWeight: distWeight * 1000 || initialWeight,
            orderNo,
            deliveryType: 21, // (目前只有同城配送会计算运费，先写死)
            distChannel,
            appId: channel.appId,
            warehouseId: deliveryPointId,
            deliveryTool: expressType === THIRD_LOCAL_DELIVERY ? this.state.deliveryTool : 0
            // deliveryPointId
          };

    const storeId = this.getStoreId();

    if (storeId) {
      params.storeId = storeId;
    }

    // 如果该服务商支持保价，则传递保价的参数
    if (supportInsurance) {
      const {
        insuranceAmount = 0,
        insuranceProduct,
        insuranceProductDesc = '不保价'
      } = this.state.insurance || {};
      params.insuranceAmount = insuranceAmount;
      params.insuranceProduct = insuranceProduct;
      params.insuranceProductDesc = insuranceProductDesc;
    }

    // 是否支持专人直送
    if (canDirectDelivery) {
      params.isDirectDelivery = isDirectDelivery;
    }

    params.itemIdList = this.state.selectedItemIds;
    return params;
  };

  // 计算运费
  calcPostage = () => {
    const params = this.getCaclPostageParams();
    if (!params.distChannel) {
      this.setState({
        localPostage: '-',
        discountFee: null,
        exceptionInfo: null
      });
      return;
    }

    // 重置异常状态
    this.setState({ exceptionInfo: null });

    api
      .calcExpressPostage(params)
      .then(
        ({
          feeDesc,
          electronicSheetExceptionInfo,
          discountFee,
          canDirectDelivery = false,
          estimateDeliveryTimeDesc,
          recommendationDesc,
          recommendationChannel
        }) => {
          this.setState({
            localPostage: feeDesc ?? '-',
            exceptionInfo: electronicSheetExceptionInfo,
            discountFee,
            canDirectDelivery,
            estimateDeliveryTimeDesc,
            recommendationDesc,
            recommendationChannel
          });
        }
      )
      .catch(err => {
        Notify.error(err.msg || '运费计算失败！');
      });
  };

  // 请求保费
  requestInsurance = () => {
    const { channel } = this.state.localDelivery;
    const { data = {} } = this.props;
    const { channels = [], warehouseId } = data;
    const { supportInsurance } = find(channels, channel) || {};
    this.setState({
      supportInsurance,
      insuranceList: [],
      insurance: {
        insuranceAmount: 0,
        insuranceProductDesc: '不保价',
        insuranceProductDescShow: '不保价'
      }
    });

    if (supportInsurance) {
      api.queryInsurance({ deliveryChannel: channel.deliveryChannel, warehouseId }).then(res => {
        let insuranceList = res.insuranceProducts || [];
        insuranceList = insuranceList.map(item => ({
          text: item.insuranceProductDescShow,
          value: item
        }));
        this.setState({
          insuranceList
        });
      });
    }
  };

  // 计算上门取件运费
  calcExpressPostage = () => {
    const params = this.getCaclPostageParams('EXPRESS');
    const { express, expressType } = this.state;

    if (!params.auditNo || !params.distWeight) {
      this.setState({ fetching: false });
      return;
    }

    api
      .calcExpressPostage(params)
      .then(({ fee, electronicSheetExceptionInfo = {}, recommendationDesc }) => {
        const isCashPledgeEnough =
          electronicSheetExceptionInfo.electronicSheetExceptionCode !== NOT_SUFFICIENT_FUNDS_CODE;
        this.setState(
          {
            express: {
              ...express,
              postage: fee,
              isCashPledgeEnough,
              recommendationDesc
            }
          },
          () => {
            const deliveryDisabled = this.checkSystemExpressDisable(expressType);
            this.setState({ deliveryDisabled });
          }
        );
      })
      .catch(err => {
        Notify.error(err.msg || '运费计算失败！');
      })
      .finally(() => {
        this.setState({ fetching: false });
      });
  };

  // 重置input信息
  resetInput = () => this.setState(cloneDeep(defaultState));

  // 发货
  doExpress = (...args) => {
    const fin = args[args.length - 1];

    const isMeituanOrder = checkIsMeituanOrder(this.props.orderInfo.mainOrderInfo.channelType);
    const { itemInfos = [] } = this.props.data;

    if (isMeituanOrder && itemInfos.some(item => item.refundState === 'refunding')) {
      return Notify.warn('订单商品正在退款处理中，订单不允许发货，请先处理退款');
    }

    this.checkInputAsync()
      .then(() => {
        let processAlertMsg = '';
        const hasProcessAlert = itemInfos.some(v => {
          const { processStatus, deliveryStatus } = v;
          // 待发货
          if (deliveryStatus === 0) {
            // 待加工 / 加工中
            if (
              [EProcessStatus.ManufactureWait, EProcessStatus.Manufacturing].includes(processStatus)
            ) {
              processAlertMsg = '部分商品加工制作中，是否继续操作';
              return true;
            }
            // 待调拨
            if (processStatus === EProcessStatus.AllotWait) {
              processAlertMsg = '部分商品正在加工制作中，是否继续操作';
              return true;
            }
          }
          return false;
        });
        const selectedDeliveryGoodsFn = this.selectDeliveryGoodsFn();
        if (hasProcessAlert) {
          Sweetalert.confirm({
            content: processAlertMsg,
            parentComponent: this,
            cancelText: '暂不发货',
            confirmText: '继续发货',
            onConfirm: () => {
              selectedDeliveryGoodsFn(...args);
            }
          });
        } else {
          selectedDeliveryGoodsFn(...args);
        }
      })
      .catch(() => {
        isFunction(fin) && fin();
      });
  };

  /**
   * 不同业务选择不同发货流程
   */
  selectDeliveryGoodsFn = () => {
    let fn;
    if (
      checkIsDeliveringByWxChannelShopEWaybill({
        channelType: this.props.channelType,
        expressType: +this.state.expressType
      })
    ) {
      fn = this.deliveryGoodsForWxChannelShopEWaybill;
    } else {
      fn = this.deliveryGoods;
    }

    return (...args) => {
      this.setState({ fetching: true });
      return fn(...args).finally(() => {
        this.setState({ fetching: false });
      });
    };
  };

  /**
   * 视频号电子小店面单发货流程
   */
  deliveryGoodsForWxChannelShopEWaybill = async () => {
    const mpId = getMpIdFromOrderInfo(this.props.orderInfo);
    const { orderNo, kdtId: salesKdtId } = this.props.orderInfo.mainOrderInfo;

    const { thirdTemplateId, printerName } = this.state.express;

    const deliveryItems = this.getDeliveryItemsParam();

    const eWaybillOrderCreateResult = await createWxChannelShopEWaybillOrder({
      express: this.state.express,
      mpId,
      deliveryItems,
      salesKdtId,
      orderNo
    });

    const commonParams = this.getDeliveryGoodsParams();

    const params = {
      ...commonParams,
      deliveryInfo: {
        ...commonParams.deliveryInfo,
        ...getWxChannelShopEWaybillDeliveryInfo(eWaybillOrderCreateResult)
      }
    };

    const deliveryResult = await api.sendGoods(params).then(() => {
      Notify.success('发货成功！');
    });

    Notify.info('开始打印');
    await printWxChannelShopEWaybill({
      ...eWaybillOrderCreateResult,
      salesKdtId,
      thirdTemplateId,
      printerName,
      mpId,
      printer: this.printer
    })
      .then(() => {
        Notify.success('打印成功');
      })
      .catch(err => {
        Notify.error(err.message || err.msg || '打印失败');
        throw err;
      });

    this.props.onClose && this.props.onClose();
    this.props.onSuccess && this.props.onSuccess(deliveryResult);
  };

  /**
   * 有赞正常发货流程
   */
  deliveryGoods = (...args) => {
    const fin = args[args.length - 1];
    const params = this.getDeliveryGoodsParams();
    const channels = this.props?.localChannels;
    const { recommendationChannel } = this.state;
    const doDelivery = (extraParams = {}) => {
      return api
        .sendGoods({ ...params, ...extraParams })
        .then(data => {
          Notify.success('发货成功！');

          try {
            const { deliveryInfo } = params;
            const channel = find(channels, {
              deliveryChannel: deliveryInfo?.localDelivery?.channel
            });

            // 最终选择的发货数据埋点
            const { thirdDeliveryPromptConfig = {} } = this.state;
            track({
              et: 'click',
              ei: 'express',
              en: '发货',
              params: {
                component: 'fulfill_express',
                goodsSelectType: this.state.goodsSelectType,
                expressType: this.state.expressType,
                deliveryType: this.state.deliveryType,
                thirdDeliveryMessage: thirdDeliveryPromptConfig.message || '',
                providerName: thirdDeliveryPromptConfig.name || '',
                deliveryChannel: deliveryInfo?.localDelivery?.channel,
                isRecommendChannel: recommendationChannel === deliveryInfo?.localDelivery?.channel,
                deliveryDefault:
                  String(deliveryInfo?.localDelivery?.channel) ===
                  window.localStorage.getItem(EXPRESS_CHANNEL_KEY_NAME)
                    ? 0
                    : 1 // 0: 默认, 1:非默认
              }
            });

            if (
              channel &&
              ![DeliveryCallType.Parallel, DeliveryCallType.Sequence].includes(channel) &&
              channel?.unifySettle // 统一自结算才本地缓存
            ) {
              window.localStorage.setItem(EXPRESS_CHANNEL_KEY_NAME, channel.deliveryChannel);
            }

            this.rememberLastOptions();
          } catch (err) {
            console.error(err);
          }

          // 管易云订单不允许打印拣货小票
          if (!isGYYOrder(this.props.orderInfo)) {
            this.printReceipt(PRINT_BIZ_SENDING);
          }

          this.props.onClose && this.props.onClose();
          this.props.onSuccess && this.props.onSuccess(data);
        })
        .catch(err => {
          /**
           * 周期购发货特殊错误码
           * @宋锡铨
           */
          if (err.code === PERIOD_EXPRESS_ERR_CODE) {
            const str = err.msg;
            const handledStr = str.replace(/\d{4}-\d{2}-\d{2}/, target => `{{${target}}}`);
            Sweetalert.alert({
              content: alertContentRenderer(handledStr),
              parentComponent: this
            });
          } else if (err.code === PRODUCE_SHOP_SEND_ERR_CODE) {
            throw err;
          } else {
            Notify.error(err.msg || '发货失败！');
          }
        })
        .finally(() => {
          isFunction(fin) && fin();
        });
    };

    return doDelivery().catch(err => {
      if (err.code === PRODUCE_SHOP_SEND_ERR_CODE) {
        Sweetalert.confirm({
          content: err.msg,
          parentComponent: this,
          cancelText: '暂不发货',
          confirmText: '改派并发货',
          onConfirm: () => {
            doDelivery({ processProduceForceSend: true }).catch(err => {
              Notify.error(err.msg || '发货失败！');
            });
          }
        });
      }
    });
  };

  rememberLastOptions = () => {
    const { express, expressType } = this.state;

    switch (expressType) {
      case EXPRESS_SYSTEM_CALL:
        switch (express.expressId) {
          case JD_EXPRESS_CODE:
            if (express.eWaybillTemplateId) {
              localStorage.setItem(
                StorageKeyJdExpressLastEWaybillTemplateId,
                JSON.stringify(express.eWaybillTemplateId)
              );
            }
            break;

          default:
            break;
        }
        break;

      default:
        break;
    }
  };

  // 商品选择
  onItemSelect = (selectedItemIds, callBack) => {
    this.setState({ selectedItemIds }, () => {
      this.checkExpressDisable();
      isFunction(callBack) ? callBack() : this.onGoodsSpreadsChange();
      this.calcPostage();
    });
  };

  // 选择商品方式修改
  onGoodsSelectTypeChange = selectType => {
    const canResetSelectedItems = ![
      DeliveryChannelType.Candao,
      DeliveryChannelType.Meituan,
      DeliveryChannelType.Eleme,
      DeliveryChannelType.MeituanShangou
    ].includes(this.props.orderInfo.mainOrderInfo.channelType);

    this.setState(prev => {
      return {
        goodsSelectType: selectType,
        selectedItemIds: canResetSelectedItems ? [] : prev.selectedItemIds,
        scanItemList: [],
        selectedLotCodeDataByItemId: {}
      };
    });
  };

  // 更新扫码商品列表
  updateScanItemList = list => {
    this.setState({
      scanItemList: list,
      selectedItemIds: list.filter(v => v.scanGoodsNum === v.goodsNum).map(v => v.itemIdStr),
      // 扫码商品数量变更, 重置批号选择
      selectedLotCodeDataByItemId: {}
    });
  };

  // 发货方式修改
  onExpressTypeChange = expressType => {
    const newState = { expressType };
    newState.deliveryDisabled = false;
    if (expressType === SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL) {
      newState.deliveryDisabled = this.checkSystemExpressDisable(expressType);
    }

    this.setState(newState, () => {
      if (expressType === SEND_TYPE_CODE.EXPRESS_FOR_COMPANY) {
        this.checkExpressDisable();
      }
      this.resetInput();
      if (expressType === SEND_TYPE_CODE.THIRD_LOCAL_DELIVERY) {
        this.initLocalDelivery(this.props);
      }
    });

    // 同城配送下，存储选择的配送方式
    const { distInfo = {} } = this.props.data;
    if (distInfo.distType === City) {
      window.localStorage.setItem(EXPRESS_TYPE_KEY_NAME, expressType);
    }
  };

  // 电子面单下的发货方式修改
  onSendTypeChange = ({ target: { value } }) => {
    const { express } = this.state;
    this.setState({
      express: {
        ...express,
        sendType: value
      }
    });
  };

  // 打印机修改
  onPrinterChange = ({ target: { value: printerDeviceNo } }) => {
    const { express } = this.state;
    this.setState({ express: { ...express, printerDeviceNo } });
  };

  // 发货地址修改
  onExpressAddressChange = ({ target: { value: auditNo } }) => {
    const { express } = this.state;
    this.setState({ express: { ...express, auditNo } }, () => {
      this.fetchExpressSheetNum();

      // 如果重量不为空，重新计算下运费
      if (express.weight) {
        this.calcExpressPostage();
      }
    });
  };

  // 电子面单物流公司修改(触发联动)
  onSystemExpressCompanyChange = ({ target: { value: expressId } }) => {
    const { express } = this.state;

    this.setState(
      {
        express: {
          ...express,
          expressId,
          weight: expressId === JD_EXPRESS_CODE ? express.weight : '',
          postage: expressId === JD_EXPRESS_CODE ? express.postage : ''
        }
      },
      this.fetchExpressAddressList
    );
  };

  onChangeExpress = newExpress => {
    this.setState(state => {
      return {
        express: {
          ...state.express,
          ...newExpress
        }
      };
    });
  };

  checkAgreeProtocol = ({ target: { checked } }) => {
    const { expressType } = this.state;
    const deliveryDisabled = this.checkSystemExpressDisable(expressType, checked);
    this.setState({
      isAgreeProtocol: checked,
      deliveryDisabled
    });
  };

  getAISelectResult = (props, distWeight, needChannel) => {
    const { orderNo, data } = props || this.props;
    const { warehouseId } = data;
    const { channel } = this.state.localDelivery;
    if (
      !data.distInfo.distType === City ||
      !data.deliveryStrategySwitch ||
      (needChannel && !channel)
    ) {
      return Promise.resolve();
    }
    return api
      .fetchAISelectResult({
        warehouseId,
        distWeight: distWeight || 1000,
        orderNo,
        itemIdList: this.state.selectedItemIds
      })
      .then(({ strategyVos }) => {
        if (!strategyVos || strategyVos.length <= 0) {
          return;
        }
        const { channelMap } = this.state;
        const validStrategy = strategyVos.filter(
          ({ deliveryChannel }) => !!channelMap[deliveryChannel]
        );
        if (validStrategy.length <= 0) {
          return;
        }
        validStrategy.sort((pre, next) => pre.fee - next.fee);
        const aiSelectedPartnerChannel = validStrategy.filter(item => item.shopSupport)[0]
          .deliveryChannel;
        this.setState({
          ...(isNil(aiSelectedPartnerChannel) ? undefined : { aiSelectedPartnerChannel }),
          AISelectText: [
            validStrategy
              .reduce((text, { deliveryChannelDesc, fee, isSupport }) => {
                if (isSupport) {
                  text.push(`${deliveryChannelDesc}：${div(fee, 100)}元`);
                }
                return text;
              }, [])
              .join('，')
          ]
        });
        return validStrategy[0];
      })
      .catch(err => {
        Notify.error(err.msg);
      });
  };

  fetchLocalChannels = () => {
    const params = this.getCaclPostageParams();
    const { distChannel, insuranceProductDesc, insuranceAmount } = params;

    // 是否支持专人直送
    if (insuranceProductDesc || insuranceAmount) {
      params.insuranceChannel = distChannel;
    }

    this.props.fetchLocalChannels(params);
  };

  // 保费修改
  handleInsuranceChange = e => {
    this.setState(
      {
        insurance: e.target.value
      },
      () => {
        this.calcPostage();
        this.fetchLocalChannels();
      }
    );
  };

  // 获取完整的三方配送服务的信息
  getDeliveryDetailInfo = val => {
    const { channels = [] } = this.props.data;
    const { localDelivery } = this.state;
    const deliveryChannel = val || { deliveryChannel: localDelivery.channel.deliveryChannel };
    return find(channels, deliveryChannel);
  };

  // 第三方配送公司修改，isInit 用于初始化第一次选中未开通的过滤
  handleDeliveryCompanyChange = (data, isInit) => {
    const { value } = data.target;
    const { localDelivery } = this.state;
    const { warehouseId, type } = this.props.data;
    const newLocalDelivery = {
      ...localDelivery,
      channel: value
    };
    const selectedChannel = this.getDeliveryDetailInfo(value);

    if (!selectedChannel) {
      return;
    }

    // 初始化状态下，未开通和非统一自结算的不需要初始化选择服务商计算价格
    if (
      (selectedChannel.status === AuditStatus.NoAudit || !selectedChannel?.unify_settle) &&
      isInit
    ) {
      return;
    }

    if (selectedChannel.status === AuditStatus.Pending) {
      Notify.info('正在审核中，请稍后再试');
    }

    if (selectedChannel.status === AuditStatus.NoAudit) {
      Sweetalert.confirm({
        title: '一键开通，无需注册',
        content: <p>该服务产生的配送费用将从店铺余额中扣除，请保证店铺余额充足。</p>,
        cancelText: '暂不开通',
        confirmText: '申请开通',
        closeBtn: true,
        onConfirm: () => {
          track({
            et: 'click', // 事件类型
            ei: 'click_dredge', // 事件标识
            en: '点击开通', // 事件名称
            pt: 'shippingCityDistribution', // 页面类型
            params: {} // 事件参数
          });
          // 申请开通
          enableProvider(
            selectedChannel.deliveryChannel,
            selectedChannel.deliveryChannelDesc,
            type,
            warehouseId,
            selectedChannel.appId
          )
            .then(result => {
              // 重新拉取下
              if (result.status === AuditStatus.Fail) {
                openDialog({
                  parentComponent: this,
                  title: '开通失败',
                  children: <span>{result.reason}</span>
                });
              }
              if (result.status === AuditStatus.Success) {
                Notify.success('开通成功');
              }
              if (result.status === AuditStatus.Pending) {
                Notify.warn('正在审核中，请稍后再试');
              }
              this.props.reload();
            })
            .catch(err => {
              Notify.error(err.msg);
            });
        }
      });
    }
    const deliveryDisabled = selectedChannel && selectedChannel.reason;
    let stateInfo = {
      deliveryDisabled,
      deliveryTool: 0,
      deliveryDisabledDesc: selectedChannel.reason || ''
    };
    if (
      selectedChannel.inScope === false ||
      selectedChannel.status === AuditStatus.Pending ||
      selectedChannel.status === AuditStatus.NoAudit
    ) {
      stateInfo = {
        localDelivery
      };
      this.setState(stateInfo);
    } else {
      stateInfo = {
        ...stateInfo,
        insurance: {
          insuranceAmount: 0,
          insuranceProductDesc: '不保价',
          insuranceProductDescShow: '不保价'
        },
        localDelivery: newLocalDelivery
      };
      this.setState(stateInfo, () => {
        this.calcPostage();
        this.requestInsurance();
      });
    }
  };

  /**
   * @description 限用于呼叫方式更新 localDelivery.channel 的场景，其他 channel 修改
   * 勿用
   */
  handleDeliveryChannelChange = ({ channel, supportCode, supportDesc }) => {
    const { localDelivery, aiSelectedPartnerChannel } = this.state;
    const exception = supportCode === 404 ? { localDeliveryCall: supportDesc } : null;

    this.setState(
      {
        localPostage: '-',
        localDelivery: {
          ...localDelivery,
          channel: { deliveryChannel: channel ?? aiSelectedPartnerChannel ?? '' }
        },
        deliveryDisabled: !isNil(supportCode) && supportCode !== 200,
        exceptionInfo: exception
      },
      () => {
        this.calcPostage();
        this.requestInsurance();
      }
    );
  };

  handleDirectDeliveryChange = isDirectDelivery => {
    this.setState(
      {
        isDirectDelivery
      },
      () => {
        this.calcPostage();
        this.fetchLocalChannels();
      }
    );
  };

  @debounce(500)
  afterChangeDistWeight() {
    this.calcPostage();

    // 更新单个服务商价格的同时，更新所有的服务商价格
    const params = this.getCaclPostageParams();
    this.props.fetchLocalChannels(params);
  }

  // 重量输入方式修改
  handleWeightInputTypeChange = ({ target: { value } }) => {
    const { localDelivery } = this.state;
    const newState = { calcWeightType: value };

    if (value === 'default') {
      newState.localDelivery = {
        ...localDelivery,
        distWeight: ''
      };
    }
    this.setState(newState, () => {
      this.afterChangeDistWeight(this.state.localDelivery?.distWeight);
    });
  };

  // 重量修改
  handleWeightChange = ({ target: { value } }) => {
    const { localDelivery } = this.state;
    const newLocalDelivery = {
      ...localDelivery,
      distWeight: keepDecimal(value, 2)
    };

    this.setState({ localDelivery: newLocalDelivery }, () => {
      this.afterChangeDistWeight(newLocalDelivery.distWeight);
    });
  };

  // 上门取件时间修改
  handlePickUpTimeChange = startTime => {
    const { express } = this.state;
    this.setState({
      express: {
        ...express,
        startTime
      }
    });
  };

  @debounce(500)
  calcExpressPostageDeb() {
    this.calcExpressPostage();
  }

  // 物流的重量修改
  handleExpressWeightChange = value => {
    const { express } = this.state;

    if (value === express.weight) {
      return;
    }

    this.setState(
      {
        fetching: true,
        express: {
          ...express,
          weight: value
        }
      },
      this.calcExpressPostageDeb
    );
  };

  onChangeInput = (value = {}) => {
    this.setState(value);
  };

  onChangeDeliveryRemark = inputEvent => {
    this.setState({
      deliveryRemark: inputEvent.target.value
    });
  };

  getExtraProps = () => {
    const {
      handleDeliveryCompanyChange,
      handleWeightInputTypeChange,
      handleWeightChange,
      handleExpressWeightChange,
      onSystemExpressCompanyChange,
      onExpressAddressChange,
      onPrinterChange,
      handlePickUpTimeChange,
      onSendTypeChange,
      handleInsuranceChange,
      handleDeliveryChannelChange,
      handleDirectDeliveryChange,
      sortSystemCallExpressCompanys,
      onChangeExpress
    } = this;

    const { expressType, exceptionInfo, estimateDeliveryTimeDesc } = this.state;
    const { orderInfo, channelType } = this.props;
    const {
      warehouseId,
      businessType,
      channels,
      electronicSheetExceptionInfo = {},
      deliveryStrategySwitch,
      autoCallInfo
    } = this.props.data;

    if (expressType === SEND_TYPE_CODE.THIRD_LOCAL_DELIVERY) {
      return {
        ...this.state,
        warehouseId,
        businessType,
        autoCallInfo,
        channels,
        localChannels: this.props.localChannels,
        handleDeliveryCompanyChange,
        handleWeightInputTypeChange,
        handleWeightChange,
        handleDeliveryChannelChange,
        handleDirectDeliveryChange,
        exceptionInfo,
        deliveryStrategySwitch,
        handleInsuranceChange,
        estimateDeliveryTimeDesc,
        handleRefresh: () => this.props.reload(),
        andleRefresh: () => this.props.reload(),
        /** 刷新服务商需要当前的 channel 和 appId，而不是选中的 */
        refreshDeliveryChannel: ({ deliveryChannel, appId, estimateDeliveryTime }) => {
          this.props.refreshDeliveryChannel({
            deliveryChannel,
            appId,
            estimateDeliveryTime,
            getCaclPostageParams: this.getCaclPostageParams
          });
        },
        isShowDeliveryTool: this.showDeliveryTool(),
        handleChangeDeliveryTool: this.handleChangeDeliveryTool,
        deliveryTool: this.state.deliveryTool
      };
    }

    if (expressType === SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL) {
      return {
        ...this.state,
        ...electronicSheetExceptionInfo,
        orderInfo,
        channelType,
        onSystemExpressCompanyChange,
        onExpressAddressChange,
        onPrinterChange,
        handleExpressWeightChange,
        handlePickUpTimeChange,
        onSendTypeChange,
        onChangeExpress,
        sortSystemCallExpressCompanys
      };
    }

    if (expressType === SEND_TYPE_CODE.WX_EXPRESS) {
      return {
        onChange: v => {
          const { expressId, expressName, accountNo } = v;
          const extraExpressInfo = expressId
            ? {
                expressId,
                expressName,
                accountNo: ''
              }
            : {
                accountNo
              };
          this.setState(prevState => {
            return {
              express: {
                ...prevState.express,
                ...extraExpressInfo
              }
            };
          });
        }
      };
    }

    if (expressType === SEND_TYPE_CODE.EXPRESS_FOR_COMPANY) {
      // 对于分销供货商订单，需要先禁用商品多运单新增订单的按钮，不然如果分销商是连锁的，逻辑会出问题
      return {
        ...this.state,
        isMultiExpress: this.isMultiExpress,
        disableMulti: false,
        disableMultiDesc: '',
        onChange: (value = {}) => this.setState(value, this.checkExpressDisable)
      };
    }

    return {
      ...this.state,
      onChange: (value = {}) => this.setState(value)
    };
  };

  setThirdDeliveryPromptConfig = (data = {}) => {
    const { thirdDeliveryPromptConfig } = this.state;
    if (thirdDeliveryPromptConfig.message) return; // 只生成一次

    const { localDeliveryOperationDesc = '' } = data;
    const config = { message: localDeliveryOperationDesc };
    this.setState({
      thirdDeliveryPromptConfig: config
    });
    trackThirdDeliveryTextShow(config);
  };

  executeFunctionsSequentially = functionsArray => {
    let index = 0;

    function executeNextFunction() {
      if (index < functionsArray.length) {
        const currentFunction = functionsArray[index];
        setTimeout(() => {
          currentFunction();
          index = index + 1;
          executeNextFunction();
        }, 10);
      }
    }

    executeNextFunction();
  };

  // 服务商推荐相关
  goRecommandLocalDelivery = () => {
    const { expressType, localDelivery, recommendationChannel } = this.state;
    const deliveryChannel = localDelivery?.channel?.deliveryChannel; // 配送服务商

    const autoselectHandleMap = {
      thirdLocalDelivery: () => this.onExpressTypeChange(THIRD_LOCAL_DELIVERY),
      deliveryUnary: () => {
        this.handleDeliveryChannelChange({ channel: DeliveryCallType.Unary });
      },
      recommandDelivery: () =>
        this.handleDeliveryCompanyChange({
          target: {
            value: { deliveryChannel: recommendationChannel }
          }
        })
    };
    if (expressType === SELF_LOCAL_DELIVERY) {
      this.executeFunctionsSequentially([
        autoselectHandleMap.thirdLocalDelivery,
        autoselectHandleMap.deliveryUnary,
        autoselectHandleMap.recommandDelivery
      ]);
    } else if (
      [DeliveryCallStrategy.Parallel, DeliveryCallStrategy.Sequence].includes(deliveryChannel)
    ) {
      this.executeFunctionsSequentially([
        autoselectHandleMap.deliveryUnary,
        autoselectHandleMap.recommandDelivery
      ]);
    } else {
      autoselectHandleMap.recommandDelivery();
    }
  };

  handleChangeDeliveryTool = val => {
    this.setState({ deliveryTool: val }, () => {
      this.calcPostage();
    });
  };

  showDeliveryTool = () => {
    const { expressType } = this.state;
    // 三方配送呼叫单个服务商，根据单个服务商展示交通工具
    const selectedChannel = this.getDeliveryDetailInfo();
    if (!selectedChannel) return false;
    const isShowDeliveryTool =
      expressType === THIRD_LOCAL_DELIVERY && selectedChannel?.supportSedan;

    return isShowDeliveryTool;
  };

  beforeDoExpress = () => {
    const channels = this.props?.localChannels;
    const { recommendServiceBol, activeRecommandClose, recommendationDesc, localDelivery } =
      this.state;
    const channel = find(channels, localDelivery?.channel) || {};
    const { unifySettle, deliveryChannel } = channel;

    const recommendRuleBol = !!(recommendServiceBol && !activeRecommandClose && recommendationDesc);

    // 同城配送-自结算需要提示服务商推荐弹窗
    if (recommendRuleBol && deliveryChannel && !unifySettle) {
      return () => {
        this.setState({
          recommendDialogVisible: true
        });
      };
    }

    return isFunction(this.props.beforeDoExpress)
      ? arg => {
          this.props.beforeDoExpress(
            arg,
            { props: this.props, state: this.state },
            this.props.onClose
          );
        }
      : null;
  };

  render() {
    const {
      selectedItemIds,
      scanItemList,
      deliveryDisabled,
      deliveryDisabledDesc,
      goodsSelectType,
      expressType,
      isAgreeProtocol,
      sending,
      express,
      goodsSerialNos,
      goodsSpreads,
      selectedLotCodeDataByItemId,
      totalSpread,
      selfFetch,
      deliveryRemark,
      thirdDeliveryPromptConfig,
      recommendationDesc,
      recommendServiceBol,
      recommendTipVisible,
      recommendDialogVisible,
      activeRecommandClose
    } = this.state;
    const { loading, verifyType, orderMark, orderNo, orderKdtId, orderInfo } = this.props;
    const { itemInfos = [], distInfo = {}, address, supportDeliveryTypes = [] } = this.props.data;
    const isMeituanOrder = checkIsMeituanOrder(orderInfo.mainOrderInfo.channelType);

    const noExpressedItemCount = filter(itemInfos, { deliveryStatus: 0 }).length;
    const ExtraComponent = SEND_TYPE_EXTRA_MAP[expressType];
    const ShowExtraInfo = !(
      verifyType === writeOffVerifyType.WITH_VERIFY_CODE && distInfo.distType === selfFetch
    );
    // 是否展示 顺丰快件运单契约条款
    const isShowSFProtocol =
      expressType === SEND_TYPE_CODE.EXPRESS_SYSTEM_CALL && express.expressId === SF_EXPRESS_CODE;

    /** 如果是自提订单, 需要根据 verifyType 来控制是否允许展示发货方式, 普通订单, 必然展示 */
    let isShowExpressType;

    // distType = 1 表示自提的订单
    if (distInfo.distType === 1) {
      isShowExpressType = isMeituanOrder
        ? false
        : verifyType === writeOffVerifyType.WITHOUT_VERIFY_CODE;
    } else {
      isShowExpressType = true;
    }

    let expressText = '发货';

    if (totalSpread) {
      expressText = '发货并退差价';
    }

    if (expressType === SEND_TYPE_CODE.THIRD_LOCAL_DELIVERY) {
      expressText = '呼叫配送员';
    }

    const isPrintingDisabled =
      this.props.channelType === DeliveryChannelType.Candao ||
      isThirdPartyDelivery(this.props.orderInfo);

    let isActionDisabled;
    let actionDisabledDesc;
    if (
      checkIsDeliveringByWxChannelShopEWaybill({
        channelType: this.props.channelType,
        expressType: +this.state.expressType
      })
    ) {
      isActionDisabled = checkCanDeliveryWxChannelShopEWaybill(
        this.state.express,
        this.state.isAgreeProtocol,
        isShowSFProtocol
      );
    } else {
      isActionDisabled =
        deliveryDisabled ||
        (expressType === SEND_TYPE_CODE.THIRD_LOCAL_DELIVERY && this.state.exceptionInfo);
      actionDisabledDesc = deliveryDisabledDesc;
    }

    const isRecommendStuffShow = recommendServiceBol && recommendationDesc;

    return (
      <GoodsScanEntry
        goodsSpreads={goodsSpreads}
        itemInfos={itemInfos}
        onGetGoodsWeight={this.onGetGoodsWeight}
      >
        <BlockLoading loading={loading}>
          <div className={style['express-content']}>
            <div className="express-content-scroller">
              {SupportVendorDeliveryScope && (
                <DeliveryScopeAlert warehouseId={this.props.data?.warehouseId} orderNo={orderNo} />
              )}
              <Alert {...this.props} />
              <IntelligentAlert warehouseId={this.props.data?.warehouseId} />
              {this.props.renderTabComponent()}
              {!isRetailSingleStore && !this.props.isFreeBuy && <WarehouseInfo address={address} />}

              <GoodsSelectTypeField
                value={goodsSelectType}
                onChange={this.onGoodsSelectTypeChange}
                itemList={itemInfos}
                scanItemList={scanItemList}
                updateScanItemList={this.updateScanItemList}
              />

              {goodsSelectType === GoodsSelectType.Scan ? (
                <ScanGoodsList
                  orderNo={orderNo}
                  orderKdtId={orderKdtId}
                  data={scanItemList}
                  updateScanItemList={this.updateScanItemList}
                  selectedItems={selectedItemIds}
                  goodsSpreads={goodsSpreads}
                  onGoodsSpreadsChange={this.onGoodsSpreadsChange}
                  goodsSerialNos={goodsSerialNos}
                  onSerialNosChange={this.onSimpleStateChange('goodsSerialNos')}
                  onSelect={this.onItemSelect}
                  selectedLotCodeDataByItemId={selectedLotCodeDataByItemId}
                  expressType={distInfo.distType}
                  fromRefund={this.props.fromRefund}
                  channelType={this.props.channelType}
                  isShowSelection={this.isShowSelection}
                  onSelectedLotCodeDataByItemIdChange={this.onSimpleStateChange(
                    'selectedLotCodeDataByItemId'
                  )}
                />
              ) : (
                <>
                  {distInfo.distType !== SelfFetch && (
                    <div className="express-content-item amount-content">
                      <div className="express-content-item__label">选择商品：</div>
                      <div className="express-content-item__value">
                        待发货 {noExpressedItemCount} 已发货{' '}
                        {itemInfos.length - noExpressedItemCount}
                      </div>
                    </div>
                  )}
                  <GoodsList
                    orderNo={orderNo}
                    orderKdtId={orderKdtId}
                    data={itemInfos}
                    selectedItems={selectedItemIds}
                    goodsSpreads={goodsSpreads}
                    onGoodsSpreadsChange={this.onGoodsSpreadsChange}
                    goodsSerialNos={goodsSerialNos}
                    selectedLotCodeDataByItemId={selectedLotCodeDataByItemId}
                    onSerialNosChange={this.onSimpleStateChange('goodsSerialNos')}
                    onSelect={this.onItemSelect}
                    expressType={distInfo.distType}
                    fromRefund={this.props.fromRefund}
                    channelType={this.props.channelType}
                    isShowSelection={this.isShowSelection}
                    onSelectedLotCodeDataByItemIdChange={this.onSimpleStateChange(
                      'selectedLotCodeDataByItemId'
                    )}
                  />
                </>
              )}

              <ExpressInfo data={this.props.data} />
              {isShowExpressType && (
                <ExpressTypes
                  expressType={distInfo.distType}
                  deliveryTypes={supportDeliveryTypes}
                  onChange={this.onExpressTypeChange}
                  value={expressType || get(supportDeliveryTypes, '[0].id')}
                  orderMark={orderMark}
                  thirdDeliveryPromptConfig={thirdDeliveryPromptConfig}
                />
              )}
              {!ShowExtraInfo && (
                <WithoutVerifyCode selfFetch={selfFetch} onChangeInput={this.onChangeInput} />
              )}
              {ExtraComponent && ShowExtraInfo && <ExtraComponent {...this.getExtraProps()} />}
              <Spread spread={totalSpread} />
              <DeliveryRemark
                expressType={distInfo.distType}
                value={deliveryRemark}
                onChange={this.onChangeDeliveryRemark}
              />

              <RecommendServiceAlert
                isRecommendStuffShow={isRecommendStuffShow && !activeRecommandClose}
                recommendTipVisible={recommendTipVisible}
                recommendationDesc={recommendationDesc}
                handleGoToUse={this.goRecommandLocalDelivery}
                handleClose={() =>
                  this.setState({ recommendTipVisible: false, activeRecommandClose: true })
                }
              />

              <RecommendServiceDialog
                isRecommendStuffShow={isRecommendStuffShow && !activeRecommandClose}
                recommendDialogVisible={recommendDialogVisible}
                recommendationDesc={recommendationDesc}
                handleNotRemind={handleRecommendServiceNotRemind}
                handleGoToUse={() => {
                  this.goRecommandLocalDelivery();
                  this.setState({
                    recommendDialogVisible: false,
                    recommendServiceBol: false
                  });
                }}
                handleClose={() =>
                  this.setState({ recommendDialogVisible: false, activeRecommandClose: true })
                }
              />
            </div>
            <BeforeAction
              actionName="DoExpress"
              onClick={this.doExpress}
              onPrintReceipt={this.printReceipt}
              disabled={isActionDisabled}
              loading={this.state.fetching}
              express={express}
              expressType={expressType}
              expressText={expressText}
              sending={sending}
              onChange={this.checkAgreeProtocol}
              isAgreeProtocol={isAgreeProtocol}
              isShowSFProtocol={isShowSFProtocol}
              disabledDesc={actionDisabledDesc}
              printDisabled={isPrintingDisabled}
              beforeDoExpress={this.beforeDoExpress()}
            />
          </div>
        </BlockLoading>
      </GoodsScanEntry>
    );
  }
}

export default ExpressContent;
