import React from 'react';
import { Tabs } from 'zent';

const withTab = Cpm =>
  class extends React.Component {
    static defaultProps = {
      data: []
    };

    state = {
      activeId: 0
    };

    get tabs() {
      return this.props.data.map((item, i) => ({
        key: i,
        title: `仓${i + 1}`
      }));
    }

    onChangeTab = id => {
      this.setState({ activeId: id });
    };

    render() {
      const { data, tabRef, ...others } = this.props;
      return (
        <div>
          <Cpm
            {...others}
            data={data[this.state.activeId] || {}}
            ref={tabRef}
            currentTabId={this.state.activeId}
            renderTabComponent={() =>
              data.length > 1 ? (
                <Tabs
                  type="card"
                  activeId={this.state.activeId}
                  onChange={this.onChangeTab}
                  tabs={this.tabs}
                />
              ) : null
            }
          />
        </div>
      );
    }
  };

export default withTab;
