import { track } from '@youzan/retail-utils';

const FulfillExpressComponentName = 'fulfill_express';

export function trackSelectingExpressType({ expressType }: { expressType: number }) {
  track({
    et: 'click',
    ei: 'select_express_type',
    en: '选择发货方式',
    params: {
      express_type: expressType,
      component: FulfillExpressComponentName
    },
    pt: ''
  });
}

export function trackThirdDeliveryTextShow(config: { message: string; name?: string }) {
  track({
    et: 'custom',
    ei: 'third_delivery_text_show',
    en: '三方配送文案曝光',
    params: {
      activity_message: config?.message,
      provider_name: config?.name ?? '',
      component: FulfillExpressComponentName
    }
  });
}

export function trackExpressRecommendTooltip({
  clickClose = 0,
  clickToUse = 0,
  promptInfo = 9
}: {
  clickClose: number;
  clickToUse: number;
  promptInfo: number;
}) {
  track({
    et: 'click',
    ei: 'express_recommend_tooltip',
    en: '发货服务商推荐提示条',
    params: {
      view: 1,
      click_close: clickClose,
      click_to_use: clickToUse,
      prompt_info: promptInfo,
      component: 'fulfill_express'
    },
    pt: ''
  });
}

export function trackExpressRecommendDialog({
  clickNotRemind = 0,
  clickToUse = 0,
  promptInfo = 9,
  clickClose = 0
}: {
  clickNotRemind: number;
  clickToUse: number;
  promptInfo: number;
  clickClose: number;
}) {
  track({
    et: 'click',
    ei: 'express_recommend_dialog',
    en: '发货服务商推荐弹窗',
    params: {
      view: 1,
      click_not_remind: clickNotRemind,
      click_to_use: clickToUse,
      prompt_info: promptInfo,
      click_close: clickClose,
      component: 'fulfill_express'
    },
    pt: ''
  });
}
