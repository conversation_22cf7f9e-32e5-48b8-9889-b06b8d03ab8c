import React from 'react';
import { Grid, IGridColumn, Notify } from 'zent';
import { format } from 'date-fns';
import zhCn from 'date-fns/locale/zh_cn';
import { formatDatetime, formatDate } from '@youzan/retail-utils';

import { useEffect, useMemo, useState } from 'react';
import { planOperationTypeTextMap } from './helper';
import { IUpdatePlanLog, queryEnjoyBuyUpdateLog, PlanOperationType } from './api';

function formatContent({ operationType, periodPostpone, planModifyList }: IUpdatePlanLog) {
  const operationTypeText = planOperationTypeTextMap.get(operationType);
  if (operationType === PlanOperationType.Postpone) {
    return `${operationTypeText}：${formatDate(periodPostpone.oldPlanDistTime)}(周${format(
      periodPostpone.oldPlanDistTime,
      'dd',
      { locale: zhCn }
    )}) 共1期 至 ${formatDate(periodPostpone.planDistTime)}(周${format(
      periodPostpone.planDistTime,
      'dd',
      { locale: zhCn }
    )})`;
  }
  if (operationType === PlanOperationType.Modify) {
    const count = planModifyList.length;
    const { planDistTime, oldPlanDistTime } = planModifyList[0];
    return `${operationTypeText}：${formatDate(oldPlanDistTime)}(周${format(oldPlanDistTime, 'dd', {
      locale: zhCn
    })}) 共${count}期 至 ${formatDate(planDistTime)}(周${format(planDistTime, 'dd', {
      locale: zhCn
    })})`;
  }
  return '';
}

const columns: IGridColumn<IUpdatePlanLog>[] = [
  {
    title: '调整时间',
    width: '180px',
    bodyRender: ({ operateTime }) => formatDatetime(operateTime)
  },
  {
    title: '调整内容',
    width: '60%',
    bodyRender: item => formatContent(item)
  },
  {
    title: '修改人',
    textAlign: 'right',
    name: 'operator.operatorName'
  }
];

const defaultPageInfo = {
  pageSize: 6,
  total: 0,
  current: 1
};

export function PlanHistory({ orderNo }: { orderNo: string }) {
  const [pageInfo, setPageInfo] = useState(defaultPageInfo);
  const [datasets, setDatasets] = useState<IUpdatePlanLog[]>([]);

  useEffect(() => {
    const fetchList = async () => {
      const result = await queryEnjoyBuyUpdateLog({ orderNo });
      // 后端接口缺少 id
      setDatasets(
        result.map((item, index) => ({
          ...item,
          index
        }))
      );
      setPageInfo(prev => ({
        ...prev,
        total: result.length
      }));
    };
    fetchList().catch(error => {
      Notify.error(error?.msg || '获取修改历史记录失败');
    });
  }, [orderNo]);

  const onChange = ({ current }: { current: number }) => {
    setPageInfo(prev => ({
      ...prev,
      current
    }));
  };

  const curDatasets = useMemo(
    () =>
      datasets.slice(
        (pageInfo.current - 1) * pageInfo.pageSize,
        pageInfo.current * pageInfo.pageSize
      ),
    [datasets, pageInfo]
  );

  return (
    <Grid
      onChange={onChange}
      paginationType="lite"
      rowKey="index"
      pageInfo={pageInfo}
      columns={columns}
      datasets={curDatasets}
    />
  );
}
