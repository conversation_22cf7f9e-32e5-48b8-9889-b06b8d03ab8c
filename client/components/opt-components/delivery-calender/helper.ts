import { IPlanItem, EnjoyBuyDeliveryStage, PlanOperationType } from './api';

export const planOperationTypeTextMap = new Map([
  [PlanOperationType.Modify, '调整'],
  [PlanOperationType.Postpone, '顺延']
]);

export function getListStage(list: IPlanItem[]) {
  return list.reduce(
    (acc, item) => {
      const result = { ...acc };
      if (item.stage === EnjoyBuyDeliveryStage.Waiting) {
        ++result.waiting;
      } else if (item.stage === EnjoyBuyDeliveryStage.InDelivery) {
        ++result.waiting;
      } else if (item.stage === EnjoyBuyDeliveryStage.Done) {
        ++result.done;
      } else if (item.stage === EnjoyBuyDeliveryStage.Refunded) {
        ++result.refunded;
      } else if (item.stage === EnjoyBuyDeliveryStage.RefundInProgress) {
        ++result.refundInProgress;
      }
      return result;
    },
    {
      waiting: 0,
      inDelivery: 0,
      done: 0,
      refunded: 0,
      refundInProgress: 0,
      total: list.length
    }
  );
}
