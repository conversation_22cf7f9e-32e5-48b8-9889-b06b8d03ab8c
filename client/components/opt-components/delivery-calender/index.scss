.rc-calendar-dialog {
  .zent-dialog-r-title {
    display: flex;

    .rc-divider-line {
      margin-left: 10px;
      color: #999 !important;
      font-size: 14px;
      cursor: default;
    }
  }

  .status__done {
    color: #ccc;
  }

  .status__waiting {
    color: #ed6a18;
  }

  .status__refund_in_progress {
    color: #d42f15;
  }
}

.rc-calendar {
  width: auto;
  border: 0;

  .react-calendar__navigation {
    height: 30px;
    justify-content: center;

    button {
      min-width: 30px;
    }

    .react-calendar__navigation__label {
      flex-grow: 0 !important;
      background-color: transparent;
      font-size: 16px;
      font-weight: 400;
      margin: 0 20px;
    }
  }

  .react-calendar__month-view__weekdays {
    abbr {
      font-weight: normal;
      color: #999;
      font-size: 16px;
      text-decoration: none;
    }
  }

  .react-calendar__month-view__days {
    align-items: baseline;
  }

  button {
    &:enabled {
      &:hover {
        cursor: default;
      }
    }
  }

  .flag__today {
    font-size: 10px;
    position: absolute;
    color: #155bd4;
    top: 0;
    right: 14px;
    font-weight: normal;
  }

  .date-tile {
    font-size: 16px;
    color: #333;
    font-weight: 400;
    background-color: transparent;
    position: relative;
    min-height: 60px;
    padding: 0;

    &:hover,
    &:focus {
      background-color: transparent;
    }

    abbr {
      display: inline-block;
      width: 28px;
      height: 28px;
      line-height: 28px;
    }

    p {
      line-height: 18px;

      &:first-child {
        margin-top: 3px;
      }
    }
  }

  .date-tile__today {
    color: #155bd4;
    font-weight: bold;
  }

  .date-tile__disabled {
    color: #ccc;

    abbr {
      background-color: #f7f7f7;

      &:hover,
      &:focus {
        background-color: #f7f7f7;
      }
    }
  }

  .date-tile__highlight {
    color: #ed6a18;

    abbr {
      background-color: #ffefe6;

      &:hover,
      &:focus {
        background-color: #ffefe6;
      }
    }

    &:enabled {
      &:hover {
        color: #ed7f3b;
        cursor: pointer;
      }
    }
  }

  .date-tile__warning {
    color: #d42f15;

    abbr {
      background-color: #ffe9e6;

      &:hover,
      &:focus {
        background-color: #ffe9e6;
      }
    }
  }

  .extra-tile__desc {
    font-size: 12px;
    margin-top: 2px;
    font-weight: normal;
  }
}

.rc-calendar-footer {
  width: 100%;
  justify-content: flex-end;
  color: #999;
}

.rc-calendar-form {
  margin-bottom: 0;

  .zent-form__control-group {
    flex-direction: column;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
  }
}
