import React from 'react';
import { Radio, Button, Notify, IRadioEvent } from 'zent';
import { getDay, isBefore, isSameDay, differenceInCalendarDays } from 'date-fns';
import { Form } from '@zent/compat';

import { PlanOperationType, updateEnjoyBuyPlan, IDetailResp, DistTimeDim } from './api';

const { createForm, FormRadioGroupField, FormDatePickerField } = Form;

interface IAdjustPlanProps
  extends Pick<
    IDetailResp,
    'distTimeDim' | 'distTimeMode' | 'customDistTimeList' | 'deliveryTime' | 'deliveryFrequency'
  > {
  orderNo: string;
  itemId: string;
  onClose: () => void;
  handleSubmit: () => void;
  zentForm: {
    isSubmitting: () => boolean;
    getFormValues: () => any;
    setFieldsValue: (data: any) => void;
  };
  onSuccessCallback: () => void;
  issues: number[];
  distTime: Date;
}

const planOperationTypeTextMap = new Map([
  [PlanOperationType.Modify, '调整'],
  [PlanOperationType.Postpone, '顺延']
]);

const workingDays = [1, 2, 3, 4, 5];
// 周日在 getDays 下返回 0
const weekends = [6, 0];

export function AdjustPlan({
  orderNo,
  itemId,
  handleSubmit,
  onClose,
  distTime,
  issues,
  zentForm,
  distTimeDim,
  deliveryTime,
  deliveryFrequency,
  distTimeMode,
  onSuccessCallback,
  customDistTimeList
}: IAdjustPlanProps) {
  const onSubmit = async (values: any) => {
    try {
      const { operationType, planDistTime, issueCount } = values;
      const params = {
        orderNo,
        itemId,
        operationType,
        ...(operationType === PlanOperationType.Modify
          ? {
              planModifyList: issues.slice(0, issueCount).map(issue => ({
                issue,
                planDistTime
              }))
            }
          : null),
        ...(operationType === PlanOperationType.Postpone
          ? {
              periodPostpone: {
                issue: issues[0],
                planDistTime
              }
            }
          : null)
      };
      const result = await updateEnjoyBuyPlan(params);
      if (result) {
        onSuccessCallback();
        Notify.success(`${planOperationTypeTextMap.get(operationType)}成功`);
      }
    } catch (error) {
      Notify.error(error?.msg || '调整配送计划失败');
    }
  };

  const onIssueCountChange = (event: IRadioEvent<number>) => {
    const { value } = event.target;
    const { operationType } = zentForm.getFormValues();
    if (value > 1 && operationType === PlanOperationType.Postpone) {
      zentForm.setFieldsValue({
        operationType: null
      });
    }
  };

  /**
   * 返回 true 表示不可选
   */
  const disabledDate = (value: Date) => {
    // 不能选择明天之前的日期
    if (differenceInCalendarDays(value, Date.now()) < 1) {
      return true;
    }
    const { operationType } = zentForm.getFormValues();
    if (
      operationType === PlanOperationType.Postpone &&
      distTimeDim !== DistTimeDim.NewWeekly &&
      (isBefore(value, distTime) || isSameDay(value, distTime))
    ) {
      return true;
    }
    const date = getDay(value);
    if (distTimeDim === DistTimeDim.Daily) {
      if (distTimeMode === 1) {
        return !workingDays.includes(date);
      }
      if (distTimeMode === 2) {
        return !weekends.includes(date);
      }
      return false;
    }
    if (distTimeDim === DistTimeDim.Weekly) {
      return date !== distTimeMode;
    }
    if (distTimeDim === DistTimeDim.Customize) {
      return !customDistTimeList.includes(date);
    }
    if (distTimeDim === DistTimeDim.NewWeekly) {
      // 判断日期是否禁用
      // eslint-disable-next-line
      const deliveryStartDate = new Date(deliveryTime);
      if (value < deliveryStartDate) {
        return true;
      }
      const diffDays = differenceInCalendarDays(value, deliveryStartDate);
      return !customDistTimeList?.includes(diffDays % (deliveryFrequency * 7));
    }
    return false;
  };

  const isSubmitting = zentForm.isSubmitting();
  const { issueCount } = zentForm.getFormValues();
  const issuesSize = issues.length;

  return (
    <Form className="rc-calendar-form" vertical onSubmit={handleSubmit(onSubmit)}>
      {issuesSize > 1 && (
        <FormRadioGroupField
          label="选择期数："
          name="issueCount"
          helpDesc={`当前日期共有${issuesSize}期待配送`}
          validations={{
            required: true
          }}
          validationErrors={{
            required: '请选择期数'
          }}
          onChange={onIssueCountChange}
        >
          {Array.from({ length: issuesSize }, (_, i) => i + 1).map(count => (
            <Radio value={count}>共{count}期</Radio>
          ))}
        </FormRadioGroupField>
      )}
      <FormRadioGroupField
        label="选择调整方式："
        name="operationType"
        validations={{
          required: true
        }}
        validationErrors={{
          required: '请选择调整方式'
        }}
      >
        <Radio value={PlanOperationType.Modify}>调整日期</Radio>
        <Radio disabled={issueCount > 1} value={PlanOperationType.Postpone}>
          顺延
        </Radio>
      </FormRadioGroupField>
      <FormDatePickerField
        label="选择日期："
        name="planDistTime"
        validations={{
          required: true
        }}
        validationErrors={{
          required: '请选择日期'
        }}
        disabledDate={disabledDate}
      />
      <div className="rc-calendar-form__footer">
        <Button disabled={isSubmitting} onClick={onClose}>
          取消
        </Button>
        <Button loading={isSubmitting} htmlType="submit" type="primary">
          确定
        </Button>
      </div>
    </Form>
  );
}

export const AdjustPlanForm = createForm()(AdjustPlan);
