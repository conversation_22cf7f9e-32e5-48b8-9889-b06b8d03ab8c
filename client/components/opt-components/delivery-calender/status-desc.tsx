import React from 'react';

interface IStatusDescProps {
  statusCount: {
    total: number;
    waiting: number;
    inDelivery: number;
    done: number;
    refunded: number;
    refundInProgress: number;
  };
}

export function StatusDesc({ statusCount }: IStatusDescProps) {
  const { total, waiting, done, refunded, refundInProgress } = statusCount;

  if (total === 0) {
    return null;
  }

  const getContent = () => {
    if (total === done) {
      return '全部已完成配送';
    }
    const doneDesc = done > 0 && `${done}期已完成配送`;
    const refundedDesc = refunded > 0 && `${refunded}期已退款`;
    const refundInProgressDesc = refundInProgress > 0 && (
      <>
        剩余<em className="status__refund_in_progress">{refundInProgress}期</em>退款中
        {(doneDesc || refundedDesc) && '，'}
      </>
    );
    const waitingDesc = waiting > 0 && (
      <>
        剩余<em className="status__waiting">{waiting}期</em>待配送
        {(doneDesc || refundedDesc || refundInProgressDesc) && '，'}
      </>
    );

    return [
      waitingDesc,
      refundInProgressDesc,
      [doneDesc, refundedDesc].filter(item => !!item).join('，')
    ];
  };

  return (
    <div>
      共{total}期，
      {getContent()}
    </div>
  );
}
