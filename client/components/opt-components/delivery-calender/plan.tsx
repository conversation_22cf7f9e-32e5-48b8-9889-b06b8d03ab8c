import React from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Button, Notify, Dialog } from 'zent';
import Calendar from 'react-calendar';
import { Space } from '@youzan/react-components';
import { getDate, isSameDay, isToday, format } from 'date-fns';
import zhCn from 'date-fns/locale/zh_cn';
import 'react-calendar/dist/Calendar.css';

import { StatusDesc } from './status-desc';
import { PlanHistory } from './plan-history';
import { AdjustPlanForm } from './adjust-plan';
import { queryEnjoyBuyPlan, IPlanItem, EnjoyBuyDeliveryStage, IDetailResp } from './api';
import { getListStage } from './helper';
import './index.scss';

const { openDialog, closeDialog } = Dialog;
const dialogId = 'plan-history';
const adjustDialogId = 'adjust-plan';

interface IPlanProps
  extends Pick<
    IDetailResp,
    'distTimeDim' | 'distTimeMode' | 'customDistTimeList' | 'deliveryTime' | 'deliveryFrequency'
  > {
  orderNo: string;
  itemId: string;
}

export function Plan({ orderNo, itemId, ...planRules }: IPlanProps) {
  const [list, setList] = useState<IPlanItem[]>([]);

  const fetchList = useCallback(async () => {
    try {
      const result = await queryEnjoyBuyPlan({ orderNo, itemId });
      setList(result);
    } catch (error) {
      Notify.error(error?.msg || '获取配送计划失败');
    }
  }, [itemId, orderNo]);

  useEffect(() => {
    fetchList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const statusCount = useMemo(() => getListStage(list), [list]);

  const showPlanHistory = () => {
    openDialog({
      dialogId,
      style: {
        width: 790
      },
      title: '调整配送计划记录',
      className: 'rc-alert-history',
      children: <PlanHistory orderNo={orderNo} />
    });
  };

  const onCloseAdjustDialog = () => {
    closeDialog(adjustDialogId);
  };

  const onAdjustSuccess = () => {
    onCloseAdjustDialog();
    fetchList();
  };

  const getTileContent = ({ date }: { date: Date }) => {
    const listStage = getListStage(list.filter(item => isSameDay(item.distTime, date)));
    const { waiting, refundInProgress, done } = listStage;
    const refundInProgressDesc = refundInProgress > 0 && (
      <p className="extra-tile__desc status__refund_in_progress">
        退款中{refundInProgress > 1 && ` ${refundInProgress}`}
      </p>
    );
    const waitingDesc = waiting > 0 && (
      <p className="extra-tile__desc status__waiting">待配送{waiting > 1 && ` ${waiting}`}</p>
    );
    const doneDesc = done > 0 && (
      <p className="extra-tile__desc status__done">已配送{done > 1 && ` ${done}`}</p>
    );
    const todayDesc = isToday(date) && <span className="flag__today">今天</span>;
    return [refundInProgressDesc, waitingDesc, doneDesc, todayDesc];
  };

  const getTileClassName = ({ date }: { date: Date }) => {
    const base = `date-tile${isToday(date) ? ' date-tile__today' : ''}`;
    const listStage = getListStage(list.filter(item => isSameDay(item.distTime, date)));
    if (listStage.waiting > 0) {
      return `${base} date-tile__highlight`;
    }
    if (listStage.refundInProgress > 0) {
      return `${base} date-tile__warning`;
    }
    if (listStage.done > 0) {
      return `${base} date-tile__disabled`;
    }
    return base;
  };

  const onClickDay = (date: Date) => {
    const deliveryDates = list.filter(item => isSameDay(item.distTime, date));
    const listStage = getListStage(deliveryDates);
    if (listStage.waiting > 0) {
      openDialog({
        dialogId: adjustDialogId,
        title: '调整配送计划',
        children: (
          <AdjustPlanForm
            {...planRules}
            itemId={itemId}
            orderNo={orderNo}
            issues={deliveryDates
              .filter(
                item =>
                  item.stage === EnjoyBuyDeliveryStage.Waiting ||
                  item.stage === EnjoyBuyDeliveryStage.InDelivery
              )
              .map(item => item.currentIssue)}
            onClose={onCloseAdjustDialog}
            distTime={date}
            onSuccessCallback={onAdjustSuccess}
          />
        )
      });
    }
  };

  return (
    <Space direction="vertical" size="large">
      <Calendar
        minDetail="month"
        maxDetail="month"
        prevLabel="<"
        prev2Label="<<"
        nextLabel=">"
        next2Label=">>"
        showNeighboringMonth={false}
        tileContent={getTileContent as any}
        tileClassName={getTileClassName}
        formatDay={(_, date) => `${getDate(date)}`}
        className="rc-calendar"
        calendarType="US"
        onClickDay={onClickDay}
        formatShortWeekday={(_, date: Date) => format(date, 'dd', { locale: zhCn })}
      />
      <Space size="small" className="rc-calendar-footer">
        <StatusDesc statusCount={statusCount} />
        <Button onClick={showPlanHistory}>查看调整记录</Button>
      </Space>
    </Space>
  );
}
