import { request } from '@youzan/retail-utils';

interface IDetailParams {
  itemId: string;
  orderNo: string;
}

/**
 * 配送日期规则
 */
export enum DistTimeDim {
  /** 每日一期 */
  Daily,
  /** 每周一期 */
  Weekly,
  /** 每月一期 */
  Monthly,
  /** 自定义日期 */
  Customize,
  /** 每m周n次 */
  NewWeekly = 5
}

export interface IDetailResp {
  /** 订单号 */
  orderNo: string;
  /** 订单商品 id */
  itemId: string;
  /** 总期数 */
  totalIssue: number;
  /** 未结束期数(未开始待配送) */
  notFinishedIssue: number;
  /** 每次配送件数 */
  periodDeliveryNum: number;
  /** 配送模式规则 */
  distTimeDim: DistTimeDim;
  /** 配送模式规则描述 */
  distTimeDimStr: string;
  /** 配送时间, 受 distTimeMid 影响 @link{https://doc.qima-inc.com/pages/viewpage.action?pageId=358110768#id-%E5%91%A8%E6%9C%9F%E8%B4%AD%E5%B1%A5%E7%BA%A6%E3%80%81%E7%89%A9%E6%B5%81%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88-queryMultiPeriodDetailByOrderNoAndItemId} */
  distTimeMode: number;
  /** 配送时间描述 */
  distTimeModeStr: string;
  /** 自选的配送时间 */
  customDistTimeList?: number[];
  /** 买家下单时选择的送达时间 */
  deliveryTime: number;
  /** 配送时间段 */
  deliveryTimeSection?: string;
  /** 拓展信息 */
  extend: string;
  /** 最大顺延次数 */
  maxPostponeSum: number;
  /** 配送周期最大修改次数 */
  maxModifyPlanNum: number;
  /** 总修改次数 */
  modifiedPlanNum: number;
  /** 买家已修改次数 */
  buyerModifiedPlanNum: number;
  /** 每 deliveryFrequency 周 */
  deliveryFrequency: number;
  /** 配送提前期 */
  leadTimeDTO?: {
    /** 配送提前期 (多久前可以下单修改配送计划) */
    leadDay: number;
    /** 几点之前下单 */
    leadHour: number;
  }[];
}

/**
 * 查询随心购配送信息
 */
export function queryEnjoyBuyDetail(params: IDetailParams) {
  return request<IDetailResp>('youzan.logistics.period.query.detail/1.0.0', params);
}

interface IPlanParams {
  itemId: string;
  orderNo: string;
  includeDelayed?: boolean;
}

/**
 * 随心购配送状态
 */
export enum EnjoyBuyDeliveryStage {
  /** 待配送 */
  Waiting = 0,
  InDelivery,
  /** 已配送 */
  Done,
  /** 已退款 */
  Refunded,
  /** 退款中 */
  RefundInProgress
}

export interface IPlanItem {
  /** 配送时间 */
  distTime: number;
  /** 当前期数 */
  currentIssue: number;
  /** 配送状态描述 */
  statusDesc: string;
  /** NOTE: 此字段不要使用 */
  status: number;
  /** 配送状态 */
  stage: EnjoyBuyDeliveryStage;
  /** 是否可以顺延取消顺延 */
  couldPostpone?: boolean;
  /** 商家是否可以顺延取消顺延 */
  sellerCouldPostpone?: boolean;
  /** 买家是否可以顺延取消顺延 */
  buyerCouldPostpone?: boolean;
}

/**
 * 查询随心购配送计划
 */
export function queryEnjoyBuyPlan(params: IPlanParams) {
  return request<IPlanItem[]>('youzan.logistics.period.query.plan/1.0.0', params);
}

/**
 * 修改配送计划方式
 */
export enum PlanOperationType {
  /** 顺延 */
  Postpone = 1,
  /** 修改日期 */
  Modify
}

interface IUpdatePlanParams {
  itemId: string;
  orderNo: string;
  /** 更新方式 */
  operationType: PlanOperationType;
  /** 调整日期计划 */
  planModifyList?: {
    issue: number;
    planDistTime: number;
  }[];
  /** 顺延计划 */
  periodPostpone?: {
    issue: number;
    planDistTime: number;
  };
}

/**
 * 更新随心购配送计划
 */
export function updateEnjoyBuyPlan(params: IUpdatePlanParams) {
  return request<boolean>('youzan.logistics.period.update.plan/1.0.0', 'POST', params);
}

export interface IUpdatePlanLog {
  orderNo: string;
  /** 操作时间 */
  operateTime: number;
  /** 更新方式 */
  operationType: PlanOperationType;
  operator: {
    operatorName: string;
  };
  /** 调整日期计划 */
  planModifyList?: {
    issue: number;
    planDistTime: number;
    oldPlanDistTime: number;
  }[];
  /** 顺延计划 */
  periodPostpone?: {
    issue: number;
    planDistTime: number;
    oldPlanDistTime: number;
  };
}

/**
 * 查询配送计划修改历史
 */
export function queryEnjoyBuyUpdateLog(params: { orderNo: string }) {
  return request<IUpdatePlanLog[]>('youzan.logistics.period.query.modifylog/1.0.0', params);
}
