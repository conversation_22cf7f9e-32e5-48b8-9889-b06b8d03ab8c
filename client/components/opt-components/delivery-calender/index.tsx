import React from 'react';
import { useState } from 'react';
import { pick } from 'lodash';
import { formatDate } from '@youzan/retail-utils';
import { Button, Dialog, Notify } from 'zent';
import { Divider } from '@youzan/react-components';

import { safeJsonParse } from 'common/utils';
import { Plan } from './plan';
import { queryEnjoyBuyDetail, DistTimeDim } from './api';

const dialogId = 'calendar';
const { openDialog } = Dialog;

const distTimeMp = new Map([
  [1, '一'],
  [2, '二'],
  [3, '三'],
  [4, '四'],
  [5, '五'],
  [6, '六'],
  [7, '七']
]);
/**
 * 格式化时间
 */
function formatCustomDistTimeList(timeList: number[]) {
  return `每周${timeList.map(time => distTimeMp.get(time)).join('、')}`;
}

interface IFooProps {
  orderNo: string;
  itemId: string;
}

export function DeliveryCalender({ orderNo, itemId }: IFooProps) {
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    setLoading(true);
    try {
      const detail = await queryEnjoyBuyDetail({ orderNo, itemId });
      const extendInfo = safeJsonParse(detail.extend);
      const title = (
        <>
          <p>配送日历</p>
          <Divider>
            <span>{formatDate(detail.deliveryTime)}开始</span>
            <span>
              {detail.distTimeDim === DistTimeDim.Customize
                ? formatCustomDistTimeList(detail.customDistTimeList)
                : detail.distTimeDimStr}
            </span>
            {detail.deliveryTimeSection && (
              <span>{(detail.deliveryTimeSection || '').replace('-', ' 至 ')}&nbsp;配送</span>
            )}
          </Divider>
        </>
      );
      openDialog({
        title,
        className: 'rc-calendar-dialog',
        style: {
          width: '790px'
        },
        dialogId,
        children: (
          <Plan
            {...pick(detail, 'distTimeDim', 'distTimeMode', 'customDistTimeList', 'deliveryTime')}
            deliveryFrequency={extendInfo?.deliveryFrequency}
            orderNo={orderNo}
            itemId={itemId}
          />
        )
      });
      setLoading(false);
    } catch (error) {
      Notify.error(error?.msg || '获取配送信息失败');
      setLoading(false);
    }
  };

  return (
    <Button loading={loading} onClick={handleClick} type="primary">
      配送日历
    </Button>
  );
}
