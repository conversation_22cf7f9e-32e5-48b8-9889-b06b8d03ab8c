import React, { useState, useCallback } from 'react';
import { Notify, Dialog, Button } from 'zent';
import { get } from 'lodash';
import { global } from '@youzan/retail-utils';
import { isRetailSingleStore } from '@youzan/utils-shop';

import { PRINT_BIZ_PICKING } from 'common/constants';
import { RE_PRINT_PICKING_ORDER, PRINT_PICKING_TICKET, PRINT_STOCK_UP_TICKET } from '../constant';
import { optTypeMap } from '../type-map';
import * as api from './api';

const { openDialog, closeDialog } = Dialog;
const { USER_INFO } = global;

const codeBizMap = {
  [RE_PRINT_PICKING_ORDER]: PRINT_BIZ_PICKING,
  [PRINT_PICKING_TICKET]: PRINT_BIZ_PICKING,
  [PRINT_STOCK_UP_TICKET]: PRINT_BIZ_PICKING
};

const checkHasAutoPrint = orderInfo => {
  return new Promise((resolve, reject) => {
    const params = {
      orderNo: get(orderInfo, 'mainOrderInfo.orderNo'),
      deliveryNo:
        get(orderInfo, 'fulfillOrder.fulfillNo') || get(orderInfo, 'mainOrderInfo.fulfillNos[0]')
    };
    api
      .queryDelayTask(params)
      .then(res => {
        // 有延时打印
        if (res) {
          openDialog({
            dialogId: 'checkDelayPrintTicketDialog',
            title: '提示',
            children: '该拣货小票打印之后，系统将不会再自动打印，是否确定现在打印？',
            footer: (
              <>
                <Button
                  onClick={() => {
                    closeDialog('checkDelayPrintTicketDialog');
                  }}
                >
                  取消
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    api.deleteDelayTask(params);
                    resolve();
                  }}
                >
                  确定
                </Button>
              </>
            )
          });
        } else {
          resolve();
        }
      })
      .catch(err => {
        reject(err);
      });
  });
};

const Print = ({ options, operation }) => {
  const [submitting, setSubmitting] = useState(false);

  const realPrint = printParam => {
    setSubmitting(true);
    api
      .printReceipt(printParam)
      .then(() => Notify.success('打印小票成功'))
      .catch(err => {
        Notify.error(err.msg || '打印小票失败');
      })
      .finally(() => {
        setSubmitting(false);
      });
  };

  const printReceipt = useCallback(() => {
    const { orderInfo } = options;

    const printParam = {
      adminName: USER_INFO.staffName,
      billNo: get(orderInfo, 'mainOrderInfo.orderNo'),
      bizType: codeBizMap[operation.code]
    };

    if (!isRetailSingleStore) {
      printParam.subBillNo = get(orderInfo, 'fulfillOrder.deliveryNo');
    }

    checkHasAutoPrint(orderInfo)
      .then(() => {
        realPrint(printParam);
        closeDialog('checkDelayPrintTicketDialog');
      })
      .catch(err => {
        Notify.error(err.msg || '打印小票失败');
      });
  }, [operation.code, options]);

  const OptTypeCpm = optTypeMap[operation.type];

  // eslint-disable-next-line react/jsx-props-no-spreading
  return (
    <OptTypeCpm
      {...operation}
      disabled={submitting}
      outline
      loading={submitting}
      onClick={printReceipt}
    />
  );
};

export default Print;
