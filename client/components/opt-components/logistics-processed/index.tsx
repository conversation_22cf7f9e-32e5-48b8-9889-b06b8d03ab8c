import * as React from 'react';
import { Notify, Sweetalert } from 'zent';
import { useState } from 'react';
import { OperationComponentProps } from '../types';
import { updateLogisticsProcessed } from './api';
import { trackClickingLogisticsProcessButton } from './tracks';

enum LogisticsProcessState {
  /** 待处理 */
  Pending = 0,
  /** 处理完成 */
  Completed = 1
}

function LogisticsProcessed({ OptTypeCpn, operation, options }: OperationComponentProps) {
  const [isLoading, setIsLoading] = useState(false);

  const resolve = () => {
    setIsLoading(true);
    return updateLogisticsProcessed({
      orderNo: options.mainOrderInfo.orderNo,
      distId: options.packInfo.packId,
      processState: LogisticsProcessState.Completed
    })
      .then(() => {
        options.reload();
      })
      .catch(err => {
        Notify.error(err);
        throw err;
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  return (
    <OptTypeCpn
      {...operation}
      options={options}
      loading={isLoading}
      size="small"
      buttonType="default"
      onClick={() => {
        trackClickingLogisticsProcessButton();
        Sweetalert.confirm({
          content: <>确定处理完结，该异常包裏将不会有“物流异常”提醒，也不在“物流监控”页面出现。</>,
          confirmText: '处理完结',
          cancelText: '暂不处理',
          onConfirm: () => {
            return resolve();
          }
        });
      }}
    />
  );
}

export default LogisticsProcessed;
