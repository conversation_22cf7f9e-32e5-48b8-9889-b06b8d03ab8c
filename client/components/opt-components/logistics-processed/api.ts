import { request } from '@youzan/retail-utils';

export interface IUpdateLogisticsProcessedParams {
  orderNo: string;
  distId: string;
  processState: number;
}

export type IUpdateLogisticsProcessedResp = boolean;

/**
 * 获取换货单换货记录数据
 */
export function updateLogisticsProcessed(
  data: IUpdateLogisticsProcessedParams
): Promise<IUpdateLogisticsProcessedResp> {
  return request({
    method: 'POST',
    contentType: 'application/json',
    url: '/youzan.retail.trademanager.abnormallogistics.processed.update/1.0.0',
    data
  });
}
