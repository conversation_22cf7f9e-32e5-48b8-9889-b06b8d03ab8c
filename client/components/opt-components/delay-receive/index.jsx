import React from 'react';
import { get } from 'lodash';
import { Sweetalert, Notify } from 'zent';

import * as api from './api';
import { optTypeMap } from '../type-map';

class DelayReceive extends React.Component {
  handleConfirm = () =>
    new Promise(resolve => {
      api
        .delayReceive({
          order_no: get(this.props, 'options.orderInfo.mainOrderInfo.orderNo')
        })
        .then(() => {
          resolve();
          Notify.success('延长收货成功！');
          this.props.reload && this.props.reload();
        })
        .catch(err => Notify.error(err.msg || '延长收货失败'));
    });

  handleOpenDialog = () => {
    Sweetalert.confirm({
      content: '确认后订单自动确认时间将会延长3天',
      title: '确定延长收货吗',
      onConfirm: this.handleConfirm
    });
  };

  render() {
    const { operation } = this.props;

    const OptTypeCpm = optTypeMap[operation.type];
    return (
      <OptTypeCpm
        {...operation}
        buttonType="primary"
        outline={false}
        onClick={this.handleOpenDialog}
      />
    );
  }
}

export default DelayReceive;
