import React from 'react';
import { get } from 'lodash';

import { openModal } from 'components/new-modals';

import { optTypeMap } from '../type-map';

class HotelReject extends React.Component {
  handleOpenDialog = () => {
    const {
      options: { orderInfo, reload }
    } = this.props;
    openModal('new-hotel-reject', {
      orderNo: get(orderInfo, 'mainOrderInfo.orderNo'),
      callback: reload
    });
  };

  render() {
    const { operation } = this.props;

    const OptTypeCpm = optTypeMap[operation.type];
    return (
      <OptTypeCpm
        {...operation}
        buttonType="primary"
        outline={false}
        onClick={this.handleOpenDialog}
      />
    );
  }
}

export default HotelReject;
