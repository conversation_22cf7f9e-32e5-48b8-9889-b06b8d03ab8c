import React from 'react';
import { Pop, Notify, Button } from 'zent';
import { get } from 'lodash';
import createOptComponent from '../create-opt-component';
import style from './style.scss';
import * as api from './api';

@createOptComponent
class Remark extends React.Component {
  state = {
    visible: false
  };

  openDialog = () => {
    this.setState({
      visible: true
    });
  };

  close = () => {
    this.setState({
      visible: false
    });
  };

  onConfirm = () => {
    const orderNo = get(this.props, 'options.orderInfo.mainOrderInfo.orderNo');
    const packId = get(this.props, 'options.orderInfo.packInfo.packId');
    return new Promise((resolve, reject) => {
      api
        .cancelPickUp({ orderNo, packId })
        .then(res => {
          Notify.success('取消成功！');
          this.props.options.reload();
          resolve(res);
        })
        .catch(err => {
          Notify.error(err.msg || '取消失败！');
          reject(err);
        })
        .finally(() => this.close());
    });
  };

  render() {
    const { operation, OptTypeCpn } = this.props;
    const { attributes = {} } = operation;
    const optElement = (
      <div>
        <OptTypeCpn onClick={this.openDialog} />
      </div>
    );

    if (attributes.clickTip) {
      return (
        <div>
          <Pop
            position="bottom-left"
            trigger="none"
            visible={this.state.visible}
            content={
              <div className={style['cancel-pick-up-content']}>
                <p className="cancel-pick-up-content__tip">{attributes.clickTip}</p>
                <p className="cancel-pick-up-content__btns">
                  <Button onClick={this.close} type="primary" size="small">
                    我知道了
                  </Button>
                </p>
              </div>
            }
          >
            {optElement}
          </Pop>
        </div>
      );
    }

    return (
      <div>
        <Pop
          position="bottom-left"
          confirmText="确认取消"
          cancelText="我再想想"
          trigger="none"
          visible={this.state.visible}
          className="cancel-pick-up-pop"
          content={
            <div className={style['cancel-pick-up-content']}>
              <div className="cancel-pick-up-content__header">取消上门取件</div>
              <p className="cancel-pick-up-content__tip">
                取消后快递员将不会进行上门取件，若第三方快递延迟，快递员已上门，你可以选择拒绝收件。
              </p>
              <p className="cancel-pick-up-content__tip red">
                注：成功取消后快递单号失效，请及时填写新运单信息，方便买家查看。
              </p>
            </div>
          }
          onConfirm={this.onConfirm}
          onCancel={this.close}
        >
          {optElement}
        </Pop>
      </div>
    );
  }
}

export default Remark;
