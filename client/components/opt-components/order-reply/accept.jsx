import React from 'react';
import { isCandaoOrder } from 'common/biz-helper';
import Reply from './base';
import { acceptOrder, confirmOrder } from './api';

const Accept = props => {
  const isCandao = isCandaoOrder(props.options.orderInfo);

  const acceptProps = {
    primaryText: '接单',
    replyAjax: isCandao ? confirmOrder : acceptOrder,
    ...props
  };

  return <Reply {...acceptProps} />;
};

export default Accept;
