import { request, global } from '@youzan/retail-utils';

const { USER_INFO } = global;
const userId = USER_INFO.adminId;

export const acceptOrder = (orderNo: string): Promise<unknown> =>
  request({
    url: '/youzan.trade/1.0.0/sellerconfirmorder',
    method: 'post',
    data: {
      orderNo,
      userId
    }
  });

// 拒单和取消订单，实际上是一个接口
export const rejectOrder = (orderNo: string): Promise<unknown> =>
  request({
    url: '/youzan.trade/1.0.0/closeorder',
    method: 'post',
    data: {
      orderNo,
      userId
    }
  });

/**
 * 接单接口（2020-08 餐道项目新增）
 *
 * @param orderNo
 */
export const confirmOrder = (orderNo: string): Promise<unknown> => {
  return request({
    url: 'com.youzan.retail.trademanager.order.confirm/1.0.0',
    method: 'POST',
    data: {
      orderNo
    }
  });
};

/**
 * 拒单接口（2020-08 餐道项目新增）
 *
 * @param orderNo
 * @param rejectReason
 */
export const rejectOrderV2 = (orderNo: string, rejectReason: number): Promise<unknown> => {
  return request({
    url: 'com.youzan.retail.trademanager.order.reject/1.0.0',
    method: 'POST',
    data: {
      orderNo,
      rejectReason
    }
  });
};
