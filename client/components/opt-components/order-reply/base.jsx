import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Dialog, Notify } from 'zent';
import { get, isFunction } from 'lodash';
import { isCandaoOrder } from 'common/biz-helper';
import GoodsList from 'components/dialog-goods-list';

import createOptComponent from '../create-opt-component';
import CancelOrderContent from '../cancel-order/cancel-order-content';
import { rejectOrderV2 } from './api';
import { style } from './style.scss';

const { useCallback, useState, useMemo } = React;
const CANDAO_REFUSE_DIALOG = 'CANDAO_REFUSE_DIALOG';

function Reply(props) {
  const [refuseDialogVisible, setRefuseDialogVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [reason, setReason] = useState('');

  const { options = {}, replyAjax, primaryText, OptTypeCpn } = props;

  const isCandao = useMemo(() => isCandaoOrder(options.orderInfo), [options.orderInfo]);

  const closeCandaoRefuseDialog = useCallback(() => {
    setRefuseDialogVisible(false);
  }, []);

  const handleCandaoRefuse = useCallback(() => {
    if (!reason) return Notify.error('请选择一个拒单理由');

    setLoading(true);

    rejectOrderV2(options.orderInfo.mainOrderInfo.orderNo, reason)
      .then(() => {
        Notify.success('拒单成功');
        isFunction(options.reload()) && options.reload();
      })
      .catch(err => {
        Notify.error(err || '拒单失败');
      })
      .finally(() => {
        setLoading(false);
        setRefuseDialogVisible(false);
      });
  }, [reason, options]);

  const renderFooter = () => {
    return (
      <>
        <Button type="primary" loading={loading} onClick={handleCandaoRefuse}>
          确定
        </Button>
        <Button onClick={closeCandaoRefuseDialog}>取消</Button>
      </>
    );
  };

  const onConfirm = useCallback(() => {
    const orderNo = get(options, 'orderInfo.mainOrderInfo.orderNo');

    return replyAjax(orderNo)
      .then(() => {
        Notify.success(`${primaryText}成功！`);
        isFunction(options.reload) && options.reload();
      })
      .catch(err => {
        Notify.error(err.msg || `${primaryText}失败！`);
      });
  }, [options, primaryText, replyAjax]);

  const openContentDialog = () => {
    const data = get(options, 'orderInfo.itemInfo', []);
    const goodsElement = <GoodsList data={data} />;

    const dialogOptions = {
      content: goodsElement,
      title: `确认${primaryText}`,
      onConfirm,
      confirmText: `确认${primaryText}`,
      cancelText: '下次再说',
      className: style
    };

    Sweetalert.confirm(dialogOptions);
  };

  if (isCandao && primaryText === '拒单') {
    return (
      <>
        <OptTypeCpn onClick={() => setRefuseDialogVisible(true)} />
        <Dialog
          dialogId={CANDAO_REFUSE_DIALOG}
          visible={refuseDialogVisible}
          title="拒绝接单"
          onClose={closeCandaoRefuseDialog}
          footer={renderFooter()}
        >
          <CancelOrderContent
            value={reason}
            onChange={({ target: { value } }) => {
              setReason(value);
            }}
            isCandaoOrder
            isRefuseOrder
          />
        </Dialog>
      </>
    );
  }

  return <OptTypeCpn onClick={openContentDialog} />;
}

export default createOptComponent(Reply);
