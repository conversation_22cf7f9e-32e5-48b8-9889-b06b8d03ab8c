import { request } from '@youzan/retail-utils';

export interface IUpdateLogisticsRemarkParams {
  distId: string;
  remark: string;
}

export type IUpdateLogisticsRemarkResp = boolean;

/**
 * 获取换货单换货记录数据
 */
export function updateLogisticsRemark(
  data: IUpdateLogisticsRemarkParams
): Promise<IUpdateLogisticsRemarkResp> {
  return request({
    method: 'POST',
    contentType: 'application/json',
    url: '/youzan.retail.trademanager.abnormallogistics.remark.update/1.0.0',
    data
  });
}
