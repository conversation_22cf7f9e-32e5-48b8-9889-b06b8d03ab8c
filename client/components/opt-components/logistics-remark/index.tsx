import * as React from 'react';
import { ChangeEvent, useState } from 'react';
import { Button, Dialog, Input, Notify } from 'zent';
import { OperationComponentProps } from '../types';
import { updateLogisticsRemark } from './api';

function LogisticsRemark({ OptTypeCpn, operation, options }: OperationComponentProps) {
  const [value, setValue] = useState('');
  const [loading, setLoading] = useState<boolean>(false);
  const [visible, setVisible] = useState(false);

  const trimmedValue = value.trim();

  const open = () => {
    setValue(options.packInfo.expressDetail?.logisticsRemark || '');
    setVisible(true);
  };

  const close = () => {
    setValue('');
    setVisible(false);
  };

  const handleChange = (e: ChangeEvent<{ value: string }>) => setValue(e.target.value);

  const submit = async () => {
    setLoading(true);
    try {
      await updateLogisticsRemark({
        distId: options.packInfo.packId,
        remark: trimmedValue
      });
      options.reload();
      close();
    } catch (err) {
      Notify.error(err || '提交失败');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <OptTypeCpn
        {...operation}
        options={options}
        size="small"
        buttonType="default"
        onClick={open}
      />
      <Dialog
        className="remark-dialog"
        visible={visible}
        title="物流备注"
        footer={
          <div>
            <Button onClick={close}>取消</Button>
            <Button
              type="primary"
              style={{ marginLeft: 16 }}
              loading={loading}
              disabled={trimmedValue.length === 0}
              onClick={() => submit()}
            >
              确定
            </Button>
          </div>
        }
        onClose={close}
        maskClosable={false}
      >
        <Input
          className="remark-input"
          type="textarea"
          value={value}
          onChange={handleChange}
          maxLength={256}
          showCount
          autoSize
          placeholder="最多可输入 256 个字"
        />
      </Dialog>
    </>
  );
}

export default LogisticsRemark;
