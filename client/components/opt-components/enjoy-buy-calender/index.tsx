import React from 'react';
import { get } from 'lodash';
import { DeliveryCalender } from '../delivery-calender';

interface IEnjoyBuyCalenderProps {
  orderNo: string;
  options: {
    orderInfo: {
      itemInfo: {
        itemId: number;
      }[];
    };
  };
}

export default function EnjoyBuyCalender({ orderNo, options }: IEnjoyBuyCalenderProps) {
  const itemId = get(options, 'orderInfo.itemInfo[0].itemIdStr');
  return <DeliveryCalender itemId={itemId} orderNo={orderNo} />;
}
