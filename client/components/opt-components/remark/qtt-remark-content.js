import React, { PureComponent } from 'react';
import { get } from 'lodash';
import { Button, Dialog, Input, Notify } from 'zent';
import { isHqStore, isPartnerStore, isRetailMinimalistShop } from '@youzan/utils-shop';

import { submitRemark, saveRemarkForPC } from 'components/opt-components/remark/api';

class RemarkContent extends PureComponent {
  state = {
    remark: get(this.props, 'option.remark', ''),
    founderToMemberMark: get(this.props, 'option.founderToMemberMark', ''),
    loading: false
  };

  handleRemarkChange = ({ target: { value } }) => {
    const remark = value.slice(0, 256);
    this.setState({ remark });
  };

  handleQttRemarkChange = ({ target: { value } }) => {
    const founderToMemberMark = value.slice(0, 256);
    this.setState({ founderToMemberMark });
  };

  submit = () => {
    const { remark, founderToMemberMark } = this.state;
    const { orderNo, reload, reloadQtt } = this.props.option;

    this.setState({ loading: true });
    Promise.all([
      submitRemark({
        remark,
        order_no: orderNo
      }),
      saveRemarkForPC({
        detail: founderToMemberMark,
        orderNo
      })
    ])
      .then(() => {
        Notify.success('备注成功');
        this.props.onClose && this.props.onClose();
        reload && reload(remark);
        reloadQtt && reloadQtt(founderToMemberMark);
      })
      .catch(err => {
        const errMsg = err.msg || err;
        Notify.error(errMsg || '备注失败！');
      })
      .finally(() => {
        this.setState({ loading: false });
        Dialog.closeDialog(this.props.dialogId);
      });
  };

  render() {
    const { loading, remark, founderToMemberMark } = this.state;
    const {
      option: { disabled }
    } = this.props;
    const disabledOpt =
      disabled === undefined ? (isHqStore || isPartnerStore) && !isRetailMinimalistShop : disabled;
    return (
      <div>
        <div className="sub-title">卖家备注（仅开团团长可见）：</div>
        <Input
          disabled={disabledOpt}
          type="textarea"
          placeholder="最多输入256个字"
          value={remark}
          onChange={this.handleRemarkChange}
          autoSize
        />
        <div className="sub-title2">团长备注（对团员和帮卖团长可见）：</div>
        <Input
          disabled={disabledOpt}
          type="textarea"
          placeholder="最多输入256个字"
          value={founderToMemberMark}
          onChange={this.handleQttRemarkChange}
          autoSize
        />
        <div className="remark-submit-btn">
          <Button disabled={disabledOpt} type="primary" loading={loading} onClick={this.submit}>
            提交
          </Button>
        </div>
      </div>
    );
  }
}

export default RemarkContent;
