import React, { PureComponent } from 'react';
import { get } from 'lodash';
import { Button, Dialog, Input, Notify } from 'zent';
import { isHqStore, isPartnerStore, isRetailMinimalistShop } from '@youzan/utils-shop';

import { submitRemark, fulfillOrderRemark } from 'components/opt-components/remark/api';
import RemarkUploadField from './remark-upload-field';

class RemarkContent extends PureComponent {
  state = {
    remark: get(this.props, 'option.remark', ''),
    remarkPics: get(this.props, 'option.remarkPics', []),
    loading: false
  };

  handleRemarkChange = ({ target: { value } }) => {
    const remark = value.slice(0, 256);
    this.setState({ remark });
  };

  handleRemarkPicsChange = remarkPics => {
    this.setState({
      remarkPics
    });
  };

  submit = () => {
    const { remark, remarkPics } = this.state;
    const { orderNo, reload, isFulfillOrder, deliveryNo } = this.props.option;

    this.setState({ loading: true });

    let remarkApi = submitRemark;
    let params = {
      remark,
      remarkPics,
      order_no: orderNo
    };

    // 发货单备注接口以及参数不一样
    if (isFulfillOrder) {
      remarkApi = fulfillOrderRemark;
      params = {
        deliveryNo,
        orderNo,
        deliveryOrderRemark: remark
      };
    }

    remarkApi(params)
      .then(() => {
        Notify.success('备注成功');
        this.props.onClose && this.props.onClose();
        reload && reload(remark, remarkPics);
      })
      .catch(err => {
        const errMsg = err.msg || err;
        Notify.error(errMsg || '备注失败！');
      })
      .finally(() => {
        this.setState({ loading: false });
        Dialog.closeDialog(this.props.dialogId);
      });
  };

  render() {
    const { loading, remark, remarkPics } = this.state;
    const {
      option: { disabled }
    } = this.props;
    const disabledOpt =
      disabled === undefined ? (isHqStore || isPartnerStore) && !isRetailMinimalistShop : disabled;
    return (
      <div>
        <div className="remark-field">
          <div className="remark-field-label">备注</div>
          <div className="remark-field-control">
            <Input
              disabled={disabledOpt}
              type="textarea"
              placeholder="最多输入256个字"
              value={remark}
              onChange={this.handleRemarkChange}
              autoSize
            />
          </div>
        </div>
        <RemarkUploadField value={remarkPics} onChange={this.handleRemarkPicsChange} />
        <div className="remark-submit-btn">
          <Button disabled={disabledOpt} type="primary" loading={loading} onClick={this.submit}>
            提交
          </Button>
        </div>
      </div>
    );
  }
}

export default RemarkContent;
