import React, { useMemo } from 'react';
import UploadField from 'components/fields/picture-field/rf.js';
import withDnd from 'components/with-dnd';

function RemarkUploadField(props) {
  const { value, onChange } = props;
  const uploadValue = useMemo(() => {
    return (value || []).map(url => {
      return {
        url
      };
    });
  }, [value]);
  const handleChange = data => {
    onChange(data.map(item => item.url));
  };
  return (
    <div className="remark-field">
      <div className="remark-field-label">图片</div>
      <div className="remark-field-control">
        <UploadField
          input={{
            value: uploadValue,
            onChange: handleChange
          }}
          onlyNeedUrl
          maxLen={5}
          renderTrigger={props => <span {...props}>+</span>}
        />
      </div>
    </div>
  );
}

export default withDnd(RemarkUploadField);
