import React from 'react';
import { Dialog } from 'zent';
import { REMARK } from '../constant';
import RemarkContent from './remark-content';
import QttRemarkContent from './qtt-remark-content';
import createOptComponent from '../create-opt-component';
import style from './style.scss';

const { openDialog, closeDialog } = Dialog;

function Remark(props) {
  const { options } = props;

  const handleOpenDialog = () => {
    const onClose = () => closeDialog(REMARK);

    // 群团团订单备注走特殊组件
    let title = options.isFulfillOrder ? '发货单备注' : '商家备注';
    let RemarkContentName = RemarkContent;
    if (options.isQttOrder && !options.isFulfillOrder) {
      title = '备注';
      RemarkContentName = QttRemarkContent;
    }

    openDialog({
      dialogId: REMARK,
      title,
      className: `${style.remark} remark-dialog`,
      children: <RemarkContentName option={options} onClose={onClose} dialogId={REMARK} />
    });
  };

  return <a onClick={handleOpenDialog}>备注</a>;
}

export default createOptComponent(Remark);
