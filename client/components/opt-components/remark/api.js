import { global, request } from '@youzan/retail-utils';

const { USER_INFO } = global;

export const submitRemark = data =>
  request({
    url: '/youzan.mall.trade.seller.order.remark/1.0.0/add',
    method: 'post',
    data
  });

// 发货单备注固定参数
const remarkExtraParams = {
  from: 'RETAIL',
  role: 'SELLER', // 操作角色 1，系统  2，买家  3，卖家
  requestId: new Date().getTime(),
  operatorId: USER_INFO.adminId
};

export const fulfillOrderRemark = data =>
  request({
    url: '/youzan.trade.dc.operate/1.0.0/remarkdeliveryorder',
    method: 'post',
    data: Object.assign(data, remarkExtraParams)
  });

// 设置团长备注
export const saveRemarkForPC = data =>
  request({
    url: '/v2/order/qtt/saveRemarkForPC.json',
    method: 'post',
    data
  });
