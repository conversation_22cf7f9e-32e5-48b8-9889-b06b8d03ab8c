@import '~shared/style';
$color-main: #edf4ff;
$color-white: #fff;
$color-black: #323233;
$color-icon: #50d04d;
$color-icon-disabled: #d8d8d8;
$color-text-disabled: #969799;
$color-text: #155bd4;

$new-color-icon: #2da641;
$new-color-main: #f2f3f5;
$new-color-disabled: #c8c9cc;
:local(.rc-contact-customer-pop) {
  .zent-pop-v2-inner {
    padding: 0 !important;
  }
}

:local(.rc-contact-customer-v2) {
  user-select: none;

  .zent-pop-inner {
    padding: 4px 0;
  }

  .item {
    cursor: pointer;
    display: flex;
    flex-direction: column;
    padding: 10px 16px;
    font-size: 14px;
    line-height: 20px;
    max-width: 218px;
    min-width: 136px;
    color: $color-black;

    & + & {
      margin-top: 4px;
    }

    &-channel-name {
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;

      &-tips {
        font-size: 12px;
        color: $new-color-disabled;
        margin-left: 5px;
      }
    }

    &:not(&-disabled):hover {
      background-color: $new-color-main;
      color: $color-text;
    }

    &-info {
      display: flex;
      align-items: center;
    }

    &-icon {
      width: 18px;
      height: 18px;
      color: $new-color-icon;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      margin-right: 9px;
      flex: 0 0 18px;
    }

    &-disabled-message {
      font-size: 12px;
      line-height: 18px;
      color: $new-color-disabled;
      margin-left: 26px;
    }

    &-disabled {
      &:hover,
      &:focus,
      &:active,
      &:visited {
        color: $new-color-disabled !important;
      }

      color: $new-color-disabled;

      &-icon {
        width: 18px;
        height: 18px;
        color: $new-color-disabled;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 18px;
        margin-right: 9px;
        flex: 0 0 18px;
      }
    }

    &-forbidden {
      cursor: not-allowed;
    }
  }
}

:local(.rc-contact-customer-loading-wrapper) {
  user-select: none;

  width: 160px;
  height: 40px;
}

:local(.repeat-confirm-dialog) {
  width: 480px;
  height: 172px;
  min-width: unset !important;
}
