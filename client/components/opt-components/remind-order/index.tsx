import React from 'react';
import { Sweetalert, Notify, Popover, Pop, BlockLoading } from 'zent';
import { track } from '@youzan/retail-utils';
import { ITrackCommonProps } from '@youzan/retail-utils/es/track';
import { OrderManageListTableOperateColumnBtnEnum } from 'definition/order-info';
import Item from './item';
import style from './style.scss';
import { checkRepeatSend, fetchChannelList, promptOrder } from './api';

export type ContactChannel = 'wsc' | 'wechat' | 'mmp' | 'sms' | 'bigFans' | 'tripartite';

export interface IContactInfoBase<T extends ContactChannel> {
  channel: T;
  isActive: boolean;
  recommend: boolean;
  message?: string;
}

export interface IContactInfoWsc extends IContactInfoBase<'wsc'> {
  yzUid: string;
  conversationId: string;
}

export interface IContactInfoWechat<T extends 'wechat' | 'mmp' | 'bigFans'>
  extends IContactInfoBase<T> {
  fansId?: string;
}

export interface IContactInfoTripartite extends IContactInfoBase<'tripartite'> {
  fansId?: string;
}

export interface IContactInfoSms extends IContactInfoBase<'sms'> {
  mobile?: string;
}
export type IContactInfo =
  | IContactInfoWsc
  | IContactInfoWechat<'wechat'>
  | IContactInfoWechat<'mmp'>
  | IContactInfoWechat<'bigFans'>
  | IContactInfoSms
  | IContactInfoTripartite;

export interface IContactCustomerState {
  visible: boolean;
  list: IContactInfo[] | null;
}

interface ITrackProps extends Exclude<ITrackCommonProps, 'params'> {
  /** 事件类型 */
  et: string;
  params: {
    order_no?: string;
  } & {
    channel?: string;
  };
}

export interface IItemInfo {
  conversationId: string;
  mobile?: string;
  channel: string;
  orderNo: string;
}
/**
 * 催单按钮
 */
class RemindOrderButton extends React.Component<any> {
  popRef = React.createRef<any>();

  state: IContactCustomerState = {
    visible: false,
    list: null
  };

  componentDidMount() {
    this.log('pending_payment_view');
  }

  log(ei: string, ext: { channel?: string } = {}) {
    const { sourcePage } = this.props.operation.attributes;
    const { orderNo } = this.props.options.orderInfo.mainOrderInfo;
    const SOURCE_MAP: Record<string, string> = {
      orderlist: 'orderlist',
      orderdetail: 'orderdetail'
    };

    const LOG_MAP: Record<string, ITrackProps> = {
      pending_payment_view: {
        et: 'view', // 事件类型
        ei: 'pending_payment_view', // 事件标识
        en: '催付按钮曝光', // 事件名称
        pt: SOURCE_MAP[sourcePage],
        params: {
          order_no: orderNo
        } // 事件参数
      },

      pending_payment_click: {
        et: 'click', // 事件类型
        ei: 'pending_payment_click', // 事件标识
        en: '催付按钮点击', // 事件名称
        pt: SOURCE_MAP[sourcePage], // 页面类型
        params: {
          order_no: orderNo
        } // 事件参数
      },

      choose_channel: {
        et: 'click', // 事件类型
        ei: 'choose_channel', // 事件标识
        en: '选择渠道', // 事件名称
        pt: SOURCE_MAP[sourcePage], // 页面类型
        params: {
          channel: ext?.channel
        } // 事件参数
      }
    };
    track({
      ...LOG_MAP[ei]
    });
  }

  _fetchChannelList() {
    const { orderNo } = this.props.options.orderInfo.mainOrderInfo;
    fetchChannelList({ orderNo }).then((list: IContactInfo[]) => {
      const newlist = list.filter(item => item.channel !== 'bigFans');
      this.setState({ list: newlist });
      this.popRef.current.adjustPosition();
    });
  }

  onItemClick = async (item: IItemInfo) => {
    const { beforeOperateClick } = this.props?.options || {};
    let isContinue = true;
    if (beforeOperateClick) {
      isContinue = await beforeOperateClick(OrderManageListTableOperateColumnBtnEnum.RemindPay);
    }
    if (!isContinue) {
      return;
    }
    this.setState({ visible: false });
    const { orderNo } = this.props.options.orderInfo.mainOrderInfo;
    checkRepeatSend({ orderNo, channel: item.channel }).then(isRepeat => {
      if (isRepeat) {
        Sweetalert.confirm({
          className: style['repeat-confirm-dialog'],
          content: <p>买家已收到催付提醒，确认再次发送提醒？</p>,
          closeBtn: true,
          onConfirm: () => this.remindOrder(item)
        });
      } else {
        this.remindOrder(item);
      }
    });
  };

  onVisibleChange = () => {
    this.setState((prevState: { visible: boolean }) => {
      return {
        visible: !prevState.visible
      };
    });
  };

  onShow = () => {
    this.setState({ list: null });
    this._fetchChannelList();
    this.log('pending_payment_click');
  };

  remindOrder = ({ conversationId, channel, ...params }: IItemInfo) => {
    const { orderNo } = this.props.options.orderInfo.mainOrderInfo;
    promptOrder({
      conversationId,
      channel,
      mobile: params?.mobile,
      orderNo
    })
      .then(() => {
        this.setState({ visible: false });
        Notify.success('发送成功');
        this.log('choose_channel', { channel });
      })
      .catch((err: { code: number; msg: string; extra: any }) => {
        Notify.error(err.msg);
      });
  };

  render() {
    const { list, visible } = this.state;
    const { position } = this.props.operation.attributes;
    return (
      <Pop
        ref={this.popRef}
        trigger="click"
        position={position || Popover.Position.AutoBottomLeft}
        onShow={this.onShow}
        onVisibleChange={this.onVisibleChange}
        className={style['rc-contact-customer-pop']}
        visible={visible}
        content={
          <>
            {list && list.length > 0 ? (
              list.map(it => <Item key={it.channel} item={it} onClick={this.onItemClick} />)
            ) : (
              <BlockLoading loading iconSize={32}>
                <div className={style['rc-contact-customer-loading-wrapper']} />
              </BlockLoading>
            )}
          </>
        }
      >
        <a>催付</a>
      </Pop>
    );
  }
}

export default RemindOrderButton;
