import { IconType } from 'zent';

export const TEXT: Record<string, string> = {
  wsc: '商城在线客服',
  wechat: '微信公众号消息',
  mmp: '小程序客服消息',
  sms: '短信',
  tripartite: 'App开店客服消息'
};

export const DEFAULT_CHANNEL_ICON_TYPE = 'more';

export const NEW_CHANNEL_ICON_TYPE: Record<string, IconType> = {
  wsc: 'customer-service',
  bigFans: 'customer-service',
  mmp: 'mini-apps',
  wechat: 'wechat',
  sms: 'message',
  tripartite: 'shop'
};

export const DEFAULT_CHANNEL_MESSAGE: Record<string, string> = {
  bigFans: '该用户未注册',
  wsc: '该用户未注册',
  wechat: '微信粉丝最后对话时间超过48小时',
  mmp: '客户未与小程序互动',
  tripartite: '客户未使用过 App 开店',
  sms: '手机号信息未授权'
};
