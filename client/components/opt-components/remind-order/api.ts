import { request } from '@youzan/retail-utils';
import { IItemInfo, IContactInfo } from './index';
// 获取催单渠道
export function fetchChannelList(data: { orderNo: string }): Promise<IContactInfo[]> {
  return request({
    url: '/v2/order/query/channel-list.json',
    method: 'get',
    data
  });
}

// 检查是否提醒重复发送
export function checkRepeatSend(data: { orderNo: string; channel: string }): Promise<boolean> {
  return request({
    url: '/v2/order/query/check-repeat-send.json',
    method: 'post',
    data
  });
}

// 发送催付消息
export function promptOrder(data: IItemInfo): Promise<string> {
  return request({
    url: '/v2/order/query/prompt.json',
    method: 'post',
    data
  });
}
