import * as React from 'react';
import cx from 'classnames';
import { Icon } from 'zent';
import style from './style.scss';

import {
  TEXT,
  NEW_CHANNEL_ICON_TYPE,
  DEFAULT_CHANNEL_ICON_TYPE,
  DEFAULT_CHANNEL_MESSAGE
} from './constants';
import { IItemInfo } from './index';

export interface IContactItemProps {
  item: any;
  onClick?: (channel: IItemInfo) => void;
}
export interface IChannelIconProps {
  channel: string;
  disabled: boolean;
}
const ChannelIcon: React.FC<IChannelIconProps> = ({ channel, disabled }) => {
  return (
    <div className={disabled ? 'item-disabled-icon' : 'item-icon'}>
      <Icon type={NEW_CHANNEL_ICON_TYPE[channel] || DEFAULT_CHANNEL_ICON_TYPE} />
    </div>
  );
};

const ContactItem = ({ item, onClick }: IContactItemProps) => {
  const disabled = !item.isActive;

  return (
    <div className={style['rc-contact-customer-v2']}>
      <a
        className={cx('item', [`item-${item.channel}`], {
          'item-disabled': disabled
        })}
        onClick={() => {
          if (!disabled) {
            onClick?.(item);
          }
        }}
      >
        <div className="item-info">
          <ChannelIcon channel={item.channel} disabled={disabled} />
          <p className="item-channel-name">
            {TEXT[item.channel]}
            {item.channel === 'sms' && <span className="item-channel-name-tips">(计费发送)</span>}
          </p>
        </div>
        {!item.isActive && (
          <div className="item-disabled-message">
            {item.message || DEFAULT_CHANNEL_MESSAGE[item.channel]}
          </div>
        )}
      </a>
    </div>
  );
};

export default ContactItem;
