@import '~shared/style';

:local(.pop-content) {
  width: 318px;

  .badge-radio-item {
    width: 45px;
    height: 32px;
    display: inline-block;
    text-align: center;
    line-height: 32px;
    margin: 4px;
    font-size: 14px;
    cursor: pointer;
    box-sizing: border-box;
  }

  .content__title {
    line-height: 20px;
    margin-bottom: 8px;
    margin-left: 4px;
  }

  .content__input {
    margin-bottom: 12px;
  }

  .content__actions {
    display: flex;
    justify-content: flex-end;
    padding-right: 4px;
    /* border-bottom: 1px solid $border-color-base; / */
  }

  .content__actions-cancel-btn {
    background-color: rgba(0, 0, 0, 0.06);
    color: #333;
    border: none;
  }

  .content__actions-confirm-btn {
    border: none;
  }
}
