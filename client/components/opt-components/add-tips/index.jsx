/* eslint-disable react/jsx-props-no-spreading */

import React from 'react';
import { get, isNil } from 'lodash';
import { Pop, Button, Notify } from 'zent';
import { keepDecimal, div } from '@youzan/retail-utils';

import BadgeRadio from 'components/badge-radio';
import { SupportParallelCallInLocalDelivery } from 'common/constant';
import typeMap from '../type-map';
import style from './style.scss';
import { addTips, getTipsAndInfo } from './api';

class AddTips extends React.Component {
  state = {
    showPop: false,
    tips: null,
    loading: false,
    tipArr: []
  };

  static defaultProps = {
    operation: {},
    options: {}
  };

  // 格式化价格
  formatPrice = price => keepDecimal(price, 1);

  // 打开气泡弹窗
  handleOpenPop = () => {
    const keyPath = 'options.packInfo.takeoutExpressDetail';

    const deliveryChannel = get(this.props, `${keyPath}.deliveryChannel`);
    const appId = get(this.props, `${keyPath}.appId`);
    const packId = get(this.props, 'options.packInfo.packId');
    const orderId = get(this.props, 'options.orderNo');

    const payload = { deliveryChannel, appId };
    if (SupportParallelCallInLocalDelivery) {
      payload.orderNo = orderId;
      payload.packId = packId;
    }
    (this.state.tipArr ?? []).length <= 0 &&
      getTipsAndInfo(payload).then(rst => {
        this.setState({ tipArr: rst.tips });
      });
    this.setState({ showPop: true });
  };

  // 关闭气泡
  closePop = () => {
    this.setState({ showPop: false });
  };

  // 加价
  handleAddTips = () => {
    this.setState({ loading: true });
    const { tips } = this.state;
    const packId = get(this.props, 'options.packInfo.packId');
    const orderId = get(this.props, 'options.orderNo');
    const warehouseId = get(this.props, 'options.packInfo.warehouseId');
    addTips({
      packId,
      tips,
      orderId,
      warehouseId
    })
      .then(() => {
        Notify.success('加价成功！');
      })
      .catch(err => {
        Notify.error(err.msg || '加价失败！');
      })
      .finally(() => {
        this.closePop();
        this.setState({ loading: false });
        this.props.options.reload && this.props.options.reload();
      });
  };

  // 弹窗内容
  renderPopContent = () => {
    const ds = (this.state.tipArr ?? []).map(ele => ({
      value: ele,
      text: `${div(ele, 100)}元`
    }));
    return (
      <div className={style['pop-content']}>
        <p className="content__title">小费只能加一次</p>
        <div className="content__input">
          {ds.length > 0 && (
            <BadgeRadio
              value={this.state.tips}
              onChange={val => {
                this.setState({ tips: val });
              }}
              dataSource={ds}
            />
          )}
        </div>
        <div className="content__actions">
          <Button onClick={this.closePop} className="content__actions-cancel-btn">
            取消
          </Button>
          <Button
            disabled={isNil(this.state.tips)}
            type="primary"
            onClick={this.handleAddTips}
            loading={this.state.loading}
            className="content__actions-confirm-btn"
          >
            确定
          </Button>
        </div>
      </div>
    );
  };

  render() {
    const { operation } = this.props;
    const OptTypeCpm = typeMap[operation.type];
    const { showPop } = this.state;
    return (
      <Pop content={this.renderPopContent()} visible={showPop} position="left-top">
        <OptTypeCpm {...operation} buttonType="default" onClick={this.handleOpenPop} />
      </Pop>
    );
  }
}

export default AddTips;
