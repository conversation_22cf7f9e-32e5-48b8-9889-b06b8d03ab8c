import React, { Component } from 'react';
import { Button } from 'zent';

class Footer extends Component {
  state = { loading: false };

  render() {
    return (
      <>
        <Button
          className="button"
          type="primary"
          loading={this.state.loading}
          onClick={() => {
            this.setState({ loading: true });
            this.props.doexpress(this.props.close);
          }}
        >
          确定发货
        </Button>
        <Button
          onClick={() => {
            this.props.close();
            this.props.closefather();
          }}
          className="button"
        >
          取消
        </Button>
      </>
    );
  }
}

export default Footer;
