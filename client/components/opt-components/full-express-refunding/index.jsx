import React, { Component } from 'react';
import { Dialog, Button, Notify } from 'zent';

import { DeliveryDialog } from '@youzan/order-domain-pc-components';
import Footer from './footer';
import style from './style.scss';

import '@youzan/order-domain-pc-components/css/index.css';

const { openDialog, closeDialog } = Dialog;

function getSelectItemsNames({ props, state }) {
  const { itemInfos = [] } = props.data;
  const targetItems = itemInfos.filter(
    item => state.selectedItemIds.indexOf(item.itemIdStr) > -1 && item.refundState === 'refunding'
  );
  return {
    targetTitles: targetItems.map(item => item.title),
    allSelectedLength: state.selectedItemIds.length
  };
}

export default class FullExpressRefunding extends Component {
  beforeOpenExpressDialog = opendialog => {
    openDialog({
      dialogId: 'expressInRefunding',
      title: '退款中商品发货',
      children:
        '请确保已与买家协商一致，买家同意发货。发货后退款申请将自动关闭，操作带来的后果由商家自行承担。',
      footer: (
        <>
          <Button
            className="button"
            type="primary"
            onClick={() => {
              closeDialog('expressInRefunding');
              opendialog();
            }}
          >
            发货
          </Button>
          <Button
            className="button"
            onClick={() => {
              closeDialog('expressInRefunding');
            }}
          >
            取消
          </Button>
        </>
      ),
      className: style['before-express-dialog']
    });
  };

  beforeDoExpress = (doexpress, info, closefather = () => {}) => {
    const { targetTitles: arr = [], allSelectedLength = 0 } = getSelectItemsNames(info);
    if (allSelectedLength < 1) {
      return Notify.error('没有选择任何商品');
    }
    if (arr.length === 0) {
      return doexpress();
    }
    openDialog({
      dialogId: 'doexpressinrefunding',
      title: '提醒',
      children: (
        <div>
          发货后，以下商品的退款申请都将自动关闭，操作带来的后果由商家自行承担。是否确认发货？
          {arr.map(ele => (
            <div className="goods-name">{ele}</div>
          ))}
        </div>
      ),
      mask: null,
      footer: (
        <Footer
          doexpress={doexpress}
          closefather={closefather}
          close={() => {
            closeDialog('doexpressinrefunding');
          }}
        />
      ),
      style: { width: '400px' },
      className: style['before-express-dialog']
    });
  };

  render() {
    return (
      <DeliveryDialog
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...this.props}
        fromRefund
        beforeOpenDialog={this.beforeOpenExpressDialog}
        beforeDoExpress={this.beforeDoExpress}
      />
    );
  }
}
