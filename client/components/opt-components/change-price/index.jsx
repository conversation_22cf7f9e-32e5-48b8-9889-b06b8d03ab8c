import React from 'react';
import { get } from 'lodash';

import ChangePriceModal from './content';

import { optTypeMap } from '../type-map';

class ChangePrice extends React.Component {
  state = { visible: false };

  change = visible => {
    this.setState({ visible });
  };

  render() {
    const {
      operation,
      options: { orderInfo, reload }
    } = this.props;
    const OptTypeCpm = optTypeMap[operation.type];
    return (
      <>
        <OptTypeCpm
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...operation}
          buttonType="primary"
          outline={false}
          onClick={() => {
            this.change(true);
          }}
        />
        <ChangePriceModal
          orderNo={get(orderInfo, 'mainOrderInfo.orderNo')}
          callback={reload}
          change={this.change}
          visible={this.state.visible}
        />
      </>
    );
  }
}

export default ChangePrice;
