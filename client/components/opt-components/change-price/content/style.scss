@import '~shared/style';

:local(.change-price) {
  min-width: 822px !important;

  .zent-dialog-r-footer {
    position: absolute;
    bottom: 24px;
    right: 0;
  }

  .change-price-rule {
    float: right;
  }

  .change-price-info {
    margin-bottom: 5px;

    .warm-caution {
      color: #268d37;
    }
  }

  .green {
    color: $color-success;
  }

  .grey {
    color: $color-text-secondary;
  }

  .subtitle {
    display: block;
    padding-top: 5px;
    cursor: pointer;
    text-align: center;
    font-size: 12px;
  }

  .postage {
    margin-top: 17px;
  }

  footer {
    text-align: right;

    button {
      min-width: 68px;
    }
  }

  .zent-input-wrapper.zent-input--size-normal {
    max-width: 80px;

    input {
      width: 40px;
      max-width: 40px;
      min-width: 60px;
    }
  }
}
