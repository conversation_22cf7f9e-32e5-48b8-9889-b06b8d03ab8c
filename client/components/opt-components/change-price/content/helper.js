import { times, keepDecimal, minus } from '@youzan/retail-utils';
import { get } from 'lodash';
import { convertYenToFen } from 'common/helper';

import { EditPriceType } from './constants';

/**
 * 拼发货地址
 */
export function getReceiveLocation(orderInfo) {
  const { province, city, area: county, community, address: addressDetail } = orderInfo;
  return [province, city, county, community, addressDetail].join(' ');
}

/**
 *
 * 获取商品改价的价格
 */
export function getGoodsChangePrice(orderInfo) {
  const { pay_no_postage: payNoPostage } = orderInfo;
  const goodsPrice = get(orderInfo, 'items', []).reduce(
    (total, item) => total + times(item.num, item.pay_price),
    0
  );
  const changePrice = minus(payNoPostage, goodsPrice);
  return keepDecimal(changePrice, 2);
}

/**
 * 将前端组件内的数据，转换为后端接口能接收的 序列化的 JSON 对象
 * @param {object} goodsDelta 商品级别改价信息
 * @return {string} 序列化(价格信息转换为 分)后的 改价信息
 */
export const getGoodsDeltaQuery = goodsDelta => {
  const ret = Object.keys(goodsDelta).map(key => {
    return {
      orderItemId: key,
      deltaModifiedPrice: convertYenToFen(goodsDelta[key].deltaModifiedPrice)
    };
  });

  return JSON.stringify(ret);
};

/**
 * 将 NaN 转化为 0, 避免影响计算
 * @param {string} str
 * @return {number}
 */
// eslint-disable-next-line no-self-compare
export const SafeNumber = str => (+str !== +str ? 0 : +str);

/**
 * 计算总的 涨价 / 减价
 */
export const getTotalChange = ({ editPriceType, smallChange, goodsDelta }) => {
  if (editPriceType === EditPriceType.byOrder) {
    return smallChange;
  }

  return Object.values(goodsDelta)
    .map(item => item.deltaModifiedPrice)
    .reduce((prev, acc) => SafeNumber(prev) + SafeNumber(acc), 0);
};
