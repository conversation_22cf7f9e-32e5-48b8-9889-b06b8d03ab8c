import React, { Component } from 'react';
import { isEmpty } from 'lodash';
import { Dialog, Notify, Button, BlockLoading } from 'zent';
import { transformYuan, convertFenToYen } from 'common/fns/format';
import { convertYenToFen } from 'common/helper';

import * as api from './api';
import ChangePriceContent from './change-price-table';
import { EditPriceType } from './constants';
import { getGoodsDeltaQuery } from './helper';
import style from './style.scss';

export default class ChangePriceModal extends Component {
  state = {
    loading: false,
    orderInfo: {},
    /** 整单涨价/减价 */
    smallChange: 0,

    /** 存储 input 生成的 商品级别的改价信息 */
    goodsDelta: {},

    /** 缓存接口得到的 商品级别的改价信息 */
    items: [],

    postage: 0,
    originChangePrice: 0
  };

  componentDidUpdate(prevProps) {
    const { visible } = this.props;
    if (prevProps.visible !== visible && visible) {
      this.fetchChangePriceData();
    }
  }

  fetchChangePriceData = () => {
    this.setState({
      orderInfo: {}
    });
    api
      .fetchChangePriceData({ orderNo: this.props.orderNo })
      .then(data => {
        const {
          changePrice: changePriceOrigin,
          editOriginalItemsPrice,
          discount,
          postage,
          items = [],
          editPriceType
        } = data;
        const changePrice = changePriceOrigin || 0;
        const editOriginalItemsPriceShow = transformYuan(editOriginalItemsPrice);
        const discountShow = transformYuan(discount);
        const postageShow = transformYuan(postage);
        const originChangePrice = transformYuan(changePrice);
        const goodsDelta = items.reduce(
          (prev, curr) =>
            Object.assign(prev, {
              [curr.itemId]: { deltaModifiedPrice: convertFenToYen(+curr.changePrice) }
            }),
          {}
        );

        this.setState({
          originChangePrice,
          smallChange: originChangePrice,
          postage: postageShow,
          editPriceType,
          items,
          goodsDelta,
          orderInfo: {
            ...data,
            editOriginalItemsPriceShow,
            discountShow,
            postageShow
          }
        });
      })
      .catch(msg => {
        Notify.error(msg || '获取改价数据失败');
      });
  };

  // 关闭当前弹窗
  handleCloseDialog = () => {
    this.props.change(false);
  };

  /**
   * 包括 smallChange / postage
   */
  handleChangePrice = newPrice => {
    this.setState({ ...newPrice });
  };

  isValEmpty = ({ val }) => {
    if (val === '' || Number.isNaN(+val)) {
      // Notify.error(`${info}不能为空`);
      return true;
    }
    return false;
  };

  handleChangePriceSubmit = () => {
    const { callback } = this.props;
    const { smallChange, postage, orderInfo, editPriceType, goodsDelta } = this.state;
    console.log(smallChange);
    let changeVal = smallChange;
    let postageVal = postage;

    // TODO: numberInput返回值为空问题规避
    if (this.isValEmpty({ info: '涨价或减价', val: smallChange })) {
      changeVal = 0;
    }
    if (this.isValEmpty({ info: '运费', val: postage })) {
      postageVal = 0;
    }

    this.setState({ loading: true });
    const param = {
      orderNo: orderInfo.orderNo,
      modifiedPostage: convertYenToFen(postageVal)
    };

    if (editPriceType === EditPriceType.byOrder) {
      param.deltaModifiedPrice = convertYenToFen(changeVal);
    }

    if (editPriceType === EditPriceType.byGoods) {
      param.deltaModifiedPriceDetailJson = getGoodsDeltaQuery(goodsDelta);
    }

    api
      .submitChangePrice(param)
      .then(() => {
        Notify.success('改价成功');
        this.handleCloseDialog();
        callback && callback();
      })
      .catch(({ msg }) => {
        Notify.error(msg);
      })
      .finally(() => {
        // cpn which isn't unmounted can call setState
        !this.isMountedNow &&
          this.setState({
            loading: false
          });
      });
  };

  componentWillUnmount = () => {
    this.isMountedNow = true;
  };

  dialogFoot = () => {
    const { loading } = this.state;

    return (
      <div>
        <Button type="primary" loading={loading} onClick={this.handleChangePriceSubmit}>
          保存
        </Button>
        <Button onClick={this.handleCloseDialog}>取消</Button>
      </div>
    );
  };

  render() {
    const { orderInfo, originChangePrice, editPriceType, items, goodsDelta } = this.state;
    const { visible } = this.props;

    return (
      <Dialog
        visible={visible}
        className={`${style['change-price']} change-price-dialog`}
        onClose={this.handleCloseDialog}
        title="改价"
        footer={this.dialogFoot()}
      >
        {isEmpty(orderInfo) ? (
          <BlockLoading loading />
        ) : (
          <ChangePriceContent
            originChangePrice={originChangePrice}
            orderInfo={orderInfo}
            onChange={this.handleChangePrice}
            editPriceType={editPriceType}
            items={items}
            goodsDelta={goodsDelta}
          />
        )}
      </Dialog>
    );
  }
}
