import { request } from '@youzan/retail-utils';

/**
 * 获取订单详情
 */
export const getOrderDetail = data =>
  request({
    url: '/youzan.retail.trademanager.pcorder/1.0.0/get',
    data
  });

/**
 * 获取改价数据
 */
export const fetchChangePriceData = data =>
  request({
    url: '/youzan.retail.trade.seller.order/1.0.0/changepricewindow',
    data
  });

/**
 * 提交改价
 */
export const submitChangePrice = data =>
  request({
    url: '/youzan.retail.trade.seller.order/1.0.0/changeprice',
    method: 'post',
    data
  });
