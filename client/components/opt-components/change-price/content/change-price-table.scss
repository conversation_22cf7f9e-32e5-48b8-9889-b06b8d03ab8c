@import '~shared/style';

:local(.change-price-content) {
  margin-top: 16px;
  margin-bottom: 20px;
  vertical-align: middle;
  text-align: center;
  width: 790px;
  box-sizing: border-box;

  thead {
    border-top: none;
    background: $background-color-base;
  }

  .blue {
    color: $color-link;
  }

  tr {
    border-bottom: 1px solid $border-color-base;

    td {
      padding: 10px;

      &.separate,
      &.preference {
        border-left: 1px solid $border-color-base;

        .red {
          // 这样写, 是因为 zent 自带的样式层级太高了, 导致 <span color="red"> 会失效
          color: #d40000;
        }
      }

      .subtitle {
        display: block;
        padding-top: 5px;
        cursor: pointer;
        text-align: center;
        font-size: 12px;
      }
    }
  }
}
