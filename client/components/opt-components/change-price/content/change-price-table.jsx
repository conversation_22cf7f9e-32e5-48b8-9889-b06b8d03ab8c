import React, { Component } from 'react';
import { Icon, Alert, NumberInput } from 'zent';
import { setUrlDomain, div } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import { convertFenToYen } from 'common/fns/format';

import * as Helper from './helper';
import { EditPriceType } from './constants';
import style from './change-price-table.scss';

export default class ChangePriceContent extends Component {
  state = {
    postage: this.props.orderInfo.postageShow,
    smallChange: this.props.originChangePrice,
    discountShow: this.props.orderInfo.discountShow,
    goodsDelta: this.props.goodsDelta
  };

  numberConfig = {
    showStepper: true,
    decimal: 2,
    stepperVal: 0.01,
    max: 10000000,
    min: -10000000
  };

  /** 「整单级别」 涨价或减价 */
  renderOrderPriceChange(index, length) {
    const {
      orderInfo: { editOriginalItemsPriceShow }
    } = this.props;
    const { smallChange } = this.state;
    return (
      +index === 0 && (
        <td rowSpan={length} className="separate">
          <NumberInput
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...this.numberConfig}
            value={smallChange}
            min={-editOriginalItemsPriceShow}
            onChange={this.onChangeNum('smallChange')}
          />
        </td>
      )
    );
  }

  /**
   * 「商品级别」 商品改价
   */
  renderGoodsPriceChange(index) {
    const { items } = this.props;
    const { goodsDelta } = this.state;
    const { itemId, price, num } = items[index];
    const min = +convertFenToYen(-(+price * num));

    return (
      <td key={itemId} className="preference">
        <NumberInput
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...this.numberConfig}
          min={min}
          value={goodsDelta[itemId].deltaModifiedPrice}
          onChange={value => {
            const patched = { ...goodsDelta, [itemId]: { deltaModifiedPrice: value } };
            this.onChangeNum('goodsDelta')(patched);
          }}
        />
      </td>
    );
  }

  /**
   * 「商品级别」 优惠信息
   */
  renderGoodsPromotion(index) {
    const { items } = this.props;
    const item = items[index];
    return (
      <td key={`${item.itemId}-preference`} className="preference">
        {convertFenToYen(item.discount)}
      </td>
    );
  }

  /** 「运费价格修改」 */
  renderPostageChange(index, length) {
    const { postage } = this.state;

    return (
      +index === 0 && (
        <td rowSpan={length} className="preference">
          <div className="postage">
            <NumberInput
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...this.numberConfig}
              min={0}
              value={postage}
              onChange={this.onChangeNum('postage')}
            />
            <div className="link subtitle blue" onClick={() => this.onChangeNum('postage')(0)}>
              免运费
            </div>
          </div>
        </td>
      )
    );
  }

  /** 「整单级别」 优惠信息 */
  renderOrderPreference(index, length) {
    const tmpOrderPreference = this.state.discountShow;

    return (
      +index === 0 && (
        <td rowSpan={length} className="preference">
          {tmpOrderPreference}
        </td>
      )
    );
  }

  /** 渲染 「实付金额」 */
  renderRealPay = (index, length) => {
    const { orderInfo, editPriceType, goodsDelta } = this.props;
    const { editOriginalItemsPriceShow } = orderInfo;
    const { smallChange, postage } = this.state;

    const totalChange = Helper.getTotalChange({ editPriceType, smallChange, goodsDelta });
    const totalPrice = (+editOriginalItemsPriceShow + +postage + +totalChange).toFixed(2);

    return (
      +index === 0 && (
        <td rowSpan={length} className="preference">
          <span className="red">{totalPrice}</span>
        </td>
      )
    );
  };

  /** 商品列表 */
  renderGoodsList = items => {
    const { editPriceType } = this.props;
    const titles = [
      '商品',
      '单价(元)',
      '数量',
      '小计(元)',
      '优惠',
      '涨价或减价',
      '运费(元)',
      '实付金额'
    ];

    return (
      <table className={style['change-price-content']}>
        <thead>
          <tr>
            {titles.map((title, index) => (
              <td key={index}>{title}</td>
            ))}
          </tr>
        </thead>
        <tbody>
          {items.map((item, index) => {
            item.sku && (item.skuShow = JSON.parse(item.sku));
            return (
              <tr key={index}>
                <td>
                  <a target="_blank" rel="noopener noreferrer" href={item.url}>
                    {item.title}
                  </a>
                  <div className="subtitle">
                    {item.skuShow &&
                      item.skuShow.map((skuItem, i) => (
                        <span className="grey" key={i}>
                          {`${skuItem.k}：${skuItem.v}`}
                        </span>
                      ))}
                  </div>
                </td>
                <td>{convertFenToYen(item.price)}</td>
                <td>{item.num}</td>
                <td>
                  <span>{convertFenToYen(item.totalPrice)}</span>
                </td>
                {editPriceType === EditPriceType.byOrder
                  ? this.renderOrderPreference(index, items.length)
                  : this.renderGoodsPromotion(index)}
                {editPriceType === EditPriceType.byOrder
                  ? this.renderOrderPriceChange(index, items.length)
                  : this.renderGoodsPriceChange(index)}
                {this.renderPostageChange(index, items.length)}
                {this.renderRealPay(index, items.length)}
              </tr>
            );
          })}
        </tbody>
      </table>
    );
  };

  onChangeNum = key => value => {
    const { onChange } = this.props;
    this.setState(
      {
        [key]: value
      },
      () => {
        onChange({
          smallChange: this.state.smallChange,
          postage: this.state.postage,
          goodsDelta: this.state.goodsDelta
        });
      }
    );
  };

  render() {
    const { orderInfo } = this.props;
    const { items } = orderInfo;
    const receiveLocation = Helper.getReceiveLocation(orderInfo);
    const changePriceUrl = setUrlDomain('/qa?cat_sys=K#/menu/2118/detail/690?_k=if5ckh', 'help');

    return (
      <div>
        <Alert type="warning">
          只有订单未付款时才支持改价，改价后请联系买家刷新订单核实订单金额后再支付。
          <BlankLink href={changePriceUrl}>查看改价规则</BlankLink>
        </Alert>

        {this.renderGoodsList(items)}

        <div className="change-price-info">
          <span className="warm-caution">
            <Icon type="error-circle-o" />
            当有多个商品时，建议你合理分配订单改价金额，确保与顾客已协商一致
          </span>
        </div>
        <div className="change-price-info">
          收货地址：
          {receiveLocation}
        </div>
        <div className="change-price-info">实付金额 = 小计 + 店铺优惠 + 小计涨减价 + 运费</div>
        <div className="change-price-info">
          店铺优惠会均摊到每个商品中，商品小计加上店铺优惠和小计改价金额后不能小于0
        </div>
      </div>
    );
  }
}
