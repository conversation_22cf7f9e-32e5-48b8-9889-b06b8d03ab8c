import React from 'react';
import { get } from 'lodash';
import { setUrlDomain, track } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import { isSingleStore } from '@youzan/utils-shop';

export default function ExchangeDetail(props) {
  const exchangeNos = get(props.options, 'orderInfo.exchangeGoodsDetail.exchangeGoodsIds', []);
  const orderNo = get(props.options, 'orderInfo.mainOrderInfo.orderNo');

  let url = `/v2/order/exchange/detail#/?order_no=${orderNo}&exchange_no=${get(
    exchangeNos,
    '[0]'
  )}`;
  if (exchangeNos.length > 1) {
    if (isSingleStore) {
      url = `/v2/order/returnorder#/?orderNo=${orderNo}&tab=1`;
    } else {
      url = `/v4/trade/refunds?orderNo=${orderNo}`;
    }
  }
  url = setUrlDomain(url, 'store');

  const handleTrack = () => {
    track({
      et: 'click',
      ei: 'click_exchange_list_btn',
      en: '查看换货记录',
      params: {
        order_no: orderNo
      },
      pt: ''
    });
  };

  return (
    <BlankLink href={url} onClick={handleTrack}>
      换货记录
    </BlankLink>
  );
}
