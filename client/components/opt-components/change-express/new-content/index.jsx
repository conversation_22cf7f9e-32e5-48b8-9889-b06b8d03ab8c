import React, { Component } from 'react';
import { get } from 'lodash';
import { Notify, BlockLoading } from 'zent';
import { isPartnerStore } from '@youzan/utils-shop';
import { IsAdvancedVersion } from 'common/utils';
import openDefaultAddressDialog from 'components/modals/default-address';
import * as api from '../api';
import ExpressList from './express-list';

export default class Content extends Component {
  state = {
    packs: null
  };

  componentDidMount() {
    if (isPartnerStore) {
      this.getExpressList();
      return;
    }
    if (IsAdvancedVersion) {
      this.getExpressList();
    } else {
      api
        .fetchDefaultAddress({ type: 'return' })
        .then(data => {
          if (data) {
            this.getExpressList();
          } else {
            this.props.onClose();
            openDefaultAddressDialog();
          }
        })
        .catch(msg => {
          Notify.error(msg || '获取默认地址失败！');
          this.props.onClose();
        });
    }
  }

  getExpressList = () => {
    api
      .fetchPacksInfo({
        orderNo: get(this.props, 'orderInfo.mainOrderInfo.orderNo')
      })
      .then(({ expressInfo = {} }) => {
        const packs = (expressInfo.packs || [])
          .sort((a, b) => +a.packId - +b.packId)
          .map((item, index) => {
            const { expressDetail, ...others } = item;
            const packInfo = others;

            packInfo.packName = `包裹${index + 1}`;
            packInfo.expressId = expressDetail.expressId;
            packInfo.expressNo = expressDetail.expressNo;
            packInfo.expressName = expressDetail.expressName || others.sendTypeDesc;
            packInfo.systemExpressName = expressDetail.expressName;
            packInfo.systemExpressNo = expressDetail.expressNo;
            packInfo.expressStatus = expressDetail.expressStatus;
            packInfo.packNo = index + 1;

            return packInfo;
          });
        this.setState({ packs });
      })
      .catch(err => {
        Notify.error(err.msg || '获取包裹列表失败！');
      });
  };

  render() {
    const { packs } = this.state;
    const { orderInfo, onClose, reload } = this.props;

    if (!packs) {
      return <BlockLoading loading />;
    }
    return (
      <ExpressList orderInfo={orderInfo} packages={packs} onClose={onClose} callback={reload} />
    );
  }
}
