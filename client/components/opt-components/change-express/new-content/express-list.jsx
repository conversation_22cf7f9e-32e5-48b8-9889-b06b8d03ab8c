import React, { Component } from 'react';
import { Alert, Button, Notify, FormControl, Radio, Grid, Dialog } from 'zent';
import { get, pick, omit } from 'lodash';
import { Form } from '@zent/compat';
import { track } from '@youzan/retail-utils';

import ExpressExtraComponent from '@youzan/order-domain-pc-components/es/delivery-dialog/components/express-extra-component';
import {
  EXPRESS_SYSTEM_CALL,
  EXPRESS_SELF,
  NO_EXPRESS,
  JD_EXPRESS_CODE,
  EXPRESS_WAY_BILL_TYPES
} from '@youzan/order-domain-pc-components/es/delivery-dialog/constants';
import {
  WaybillVersionEnum,
  YZShippingStatusEnum
} from '@youzan/order-domain-pc-components/es/delivery-dialog/types';
import {
  getElectronWayBillServiceKdtId,
  getWaybillVersion
} from '@youzan/order-domain-pc-components/es/delivery-dialog/api';
import '@youzan/order-domain-pc-components/css/index.css';
import { doBatchPrint } from '@youzan/order-domain-pc-components';
import { beforeConfirmShipment } from '@youzan/order-domain-pc-components/es/delivery-dialog/components/express-extra-system-call/extra';

import * as api from '../api';
import ExpressItem from './express-item';

import '../style.scss';

const RadioGroup = Radio.Group;

const { createForm } = Form;
const { openDialog, closeDialog } = Dialog;

const columns = [
  {
    title: '包裹号',
    width: 100,
    name: 'packName'
  },
  {
    title: '商品信息',
    name: 'itemMessage',
    bodyRender: data => {
      const { distItemInfo = [], items = [] } = data;
      const itemList = distItemInfo.map(item => {
        const itemInfo = items.find(v => v.itemIdStr === item.itemId);
        item.title = (itemInfo || {}).title;
        return item;
      });
      const itemTotalNum = distItemInfo.reduce((ret, { num = 0 }) => ret + num, 0);
      return (
        <>
          {itemList.map(item => (
            <div style={{ display: 'flex', marginBottom: 10 }}>
              <div
                style={{
                  width: 200,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  marginRight: 10
                }}
              >
                {item.title}
              </div>
              <div>x{item.num}</div>
            </div>
          ))}
          <div>共{itemTotalNum}件商品</div>
        </>
      );
    }
  },
  {
    title: '物流公司',
    name: 'expressName',
    width: 100
  },
  {
    title: '运单号',
    name: 'expressNo',
    width: 200,
    bodyRender: data => data.expressNo || '/'
  }
];

const expressList = [
  {
    value: EXPRESS_SYSTEM_CALL,
    text: '在线下单'
  },
  {
    value: EXPRESS_SELF,
    text: '自己联系快递'
  },
  {
    value: NO_EXPRESS,
    text: '无需物流'
  }
];

@createForm({ scrollToError: true })
class LogisticsContent extends Component {
  state = {
    expressCompany: [],
    packages: this.props.packages,
    originPackages: this.props.packages,
    loading: false,
    currentExpress: expressList[0].value,
    selectedPack: [],
    selectedPackItems: [],
    stepper: 1,
    currentElectronWayBillServiceKdtId: 0,
    currentElectronWayBillServiceKdtName: '',
    electronWayBillServiceKdtIdMap: {},
    isShowBack: false,
    waybillVersion: 1,
    expressExtraData: {}
  };

  componentDidMount() {
    // 获取快递列表
    api
      .fetchLogisticsList()
      .then(res => {
        const { allExpress = [] } = res;
        this.setState({
          expressCompany: allExpress.filter(item => item.display)
        });
      })
      .catch(err => {
        Notify.error(err || '获取快递公司列表失败！');
      });
    // 单包裹的情况下或者是老版本，直接跳过第一步的勾选包裹环节
    if (this.state.packages.length === 1) {
      this.getCurrentKdtInfo({ stepper: 2 });
    }
  }

  getCurrentKdtInfo = ({ stepper, isShowBack = false }) => {
    const { currentExpress, packages } = this.state;
    const { orderInfo = {} } = this.props;
    if (stepper === 2) {
      if (currentExpress === 14) {
        const orderNo = get(orderInfo, 'mainOrderInfo.orderNo');
        const fulfillNo =
          get(orderInfo, 'mainOrderInfo.fulfillNos[0]') || get(orderInfo, 'fulfillOrder.fulfillNo');
        const deliveryNo = packages.find(p => p.deliveryNo)?.deliveryNo;
        const params = deliveryNo
          ? {
              orderNo,
              deliveryNo,
              sourceType: 1
            }
          : { orderNo, sourceType: 1 };
        getElectronWayBillServiceKdtId(params).then(electronWayBillServiceKdtIdMap => {
          // 获取当前电子面单服务店铺
          const currentElectronWayBillServiceKdtId = get(
            electronWayBillServiceKdtIdMap,
            ['fulfillExpressWayBillServeSupplyMap', fulfillNo, 'serveSupplyKdtId'],
            0
          );

          // 获取当前电子面单服务店铺名称
          const currentElectronWayBillServiceKdtName = get(
            electronWayBillServiceKdtIdMap,
            ['fulfillExpressWayBillServeSupplyMap', fulfillNo, 'serveSupplyShopName'],
            0
          );
          const newState = {
            electronWayBillServiceKdtIdMap,
            currentElectronWayBillServiceKdtId,
            currentElectronWayBillServiceKdtName,
            stepper,
            isShowBack
          };
          if (currentElectronWayBillServiceKdtId) {
            getWaybillVersion(currentElectronWayBillServiceKdtId)
              .then(waybillVersion => {
                newState.waybillVersion = waybillVersion;
                this.setState(newState);
              })
              .catch(() => this.setState(newState));
          } else {
            this.setState(newState);
          }
        });
      } else {
        this.setState({ stepper, isShowBack });
      }
    }
  };

  changeExpressItem = (packId, data) => {
    const { packages, selectedPackItems } = this.state;
    let cloneSelectedPackItems = selectedPackItems;
    if (cloneSelectedPackItems.length > 0) {
      cloneSelectedPackItems = cloneSelectedPackItems.map(item => {
        if (item.packId !== packId) {
          return item;
        }

        return {
          ...item,
          ...data
        };
      });
    }
    const clonePackages = packages.map(item => {
      if (item.packId !== packId) {
        return item;
      }

      return {
        ...item,
        ...data
      };
    });

    this.setState({ packages: clonePackages, selectedPackItems: cloneSelectedPackItems });
  };

  handleSubmit = async () => {
    const {
      packages,
      selectedPackItems,
      stepper,
      currentExpress,
      expressExtraData: express,
      waybillVersion
    } = this.state;

    const isNewWayBill = waybillVersion === WaybillVersionEnum.New;

    // 多包裹的情况下，点击下一步
    if (stepper === 1) {
      this.getCurrentKdtInfo({ isShowBack: true, stepper: 2 });
      return;
    }

    // 单包裹的情况下，selectedPackItem是空的，所以直接获取items
    // 多包裹，直接拿selectedPackItem
    const changedData = selectedPackItems.length === 0 ? packages : selectedPackItems;

    const packs = changedData.map(item => {
      const params = pick(item, [
        'packId',
        'packNo',
        'expressId',
        'expressNo',
        'version',
        'expressStatus'
      ]);

      params.deliveryType = currentExpress;

      const paymentType = get(this.props, 'orderInfo.packInfo.expressDetail.paymentType');
      const waybillVersion = get(this.props, 'orderInfo.packInfo.expressDetail.waybillVersion');
      if (!paymentType) {
        params.paymentType = get(item, 'distOrderDTO.expressInfo.expressDetail.paymentType');
      }
      if (!waybillVersion) {
        params.waybillVersion = get(item, 'distOrderDTO.expressInfo.expressDetail.waybillVersion');
      }
      // 在线下单
      if (currentExpress === EXPRESS_SYSTEM_CALL) {
        const {
          weight,
          printerDeviceNo,
          eWaybillTemplateId,
          expressId,
          printerInfo = {}
        } = express || {};
        params.express = {
          ...omit(express, ['eWaybillTemplateId', 'eWaybillTemplateOptions']),
          weight: isNewWayBill ? weight : weight * 1000,
          // 打印机型号
          printerChannel: printerInfo.equipmentTypeId,
          // 设备编号
          printerDeviceNo,
          // 设备密钥
          printerKey: printerInfo.equipmentKey,
          ...(isNewWayBill || expressId === JD_EXPRESS_CODE
            ? {
                templateUrl: eWaybillTemplateId
              }
            : {})
        };
      }
      return params;
    });

    const param = {
      orderNo: get(this.props, 'orderInfo.mainOrderInfo.orderNo'),
      modifyExpressOrderJson: JSON.stringify(packs || [])
    };

    this.setState({
      loading: true
    });

    // 开通有赞寄件，准入失败弹出引导弹窗
    const { YZShoppingInfo } = express;
    if (isNewWayBill) {
      await beforeConfirmShipment(express).catch(() => {
        this.setState({ loading: false });
        return Promise.reject();
      });
    }

    api
      .changeExpressV2(param)
      .then(res => {
        const { details = [], msg } = res || {};
        const isAllSuccess = details.every(item => item.modifySuccess);
        const isSomeSuccess = details.some(item => item.modifySuccess);
        if (msg && isAllSuccess) {
          Notify.success(msg);
        }

        this.props.onClose && this.props.onClose();
        this.props.callback();

        // 是否全部包裹修改成功，如果不是，那就展示修改物流结果弹窗
        if (!isAllSuccess) {
          const dialogId = 'edit_express_dialog_result';
          openDialog({
            dialogId,
            title: '修改物流结果',
            style: { width: '700px' },
            footer: (
              <Button type="primary" onClick={() => closeDialog(dialogId)}>
                确认
              </Button>
            ),
            children: (
              <div>
                <Grid
                  rowKey="packName"
                  className="items-table"
                  columns={[
                    {
                      title: '包裹号',
                      width: 100,
                      name: 'packName',
                      bodyRender: data => `包裹${data.packNo}`
                    },
                    {
                      title: '修改结果',
                      width: 100,
                      name: 'result',
                      bodyRender: data => (data.modifySuccess === 'success' ? '成功' : '失败')
                    },
                    {
                      title: '失败原因',
                      width: 200,
                      name: 'result',
                      bodyRender: data =>
                        data.modifySuccess === 'success'
                          ? '-'
                          : data.msg || '未知原因，请稍后重试或向联系客服处理'
                    }
                  ]}
                  datasets={details}
                />
              </div>
            )
          });
        }

        if (isSomeSuccess) {
          try {
            const { deliveryType } = (packs || [])[0];

            const { waitJoin } = YZShoppingInfo || {};
            let yzShippingStatus = YZShippingStatusEnum.JOINED;
            if (waitJoin) {
              yzShippingStatus = YZShippingStatusEnum.WAIT_JOIN;
            }

            track({
              et: 'click', // 事件类型
              ei: 'edit_express_dialog_click', // 事件标识
              en: '弹窗保存点击', // 事件名称
              params: {
                express_type: deliveryType,
                yz_shipping_status: yzShippingStatus,
                component: 'edit_express_dialog'
              } // 事件参数
            });
            // eslint-disable-next-line no-empty
          } catch (err) {}
        }

        const updateSuccessList = details
          .filter(item => item.modifySuccess)
          .map(item => item.distId);
        // 在线下单模式 走菜鸟打印组件打印
        if (currentExpress === EXPRESS_SYSTEM_CALL) {
          // 筛选出：修改物流成功 && 新电子面单模式 && 发货类型不是快递员上门打印面单
          const filterChangedData = packs.filter(
            item =>
              updateSuccessList.includes(item.packId) &&
              +item.express?.waybillVersion === WaybillVersionEnum.New &&
              item.express?.sendType !== EXPRESS_WAY_BILL_TYPES.callCourierAndPrint.value &&
              item.express?.printerDeviceNo
          );
          if (filterChangedData.length > 0) {
            const printerId = filterChangedData[0].express.printerDeviceNo;
            this.afterSendExpressSuccess(printerId, details);
          }
        }
      })
      .catch(err => {
        Notify.error(err.msg || '物流修改失败');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  // 发货成功后的处理函数
  afterSendExpressSuccess = (printerId, res) => {
    if (printerId) {
      doBatchPrint(printerId, res)
        .then(() => {
          Notify.success('打印成功');
        })
        .catch(err => {
          Notify.error(`打印失败, ${err.message || err.msg || err}`);
        });
    }
  };

  handleExpressExtraChange = async data => {
    this.setState({ expressExtraData: data });
  };

  // 更改发货方式
  handleChangeExpress = e => {
    const currentExpress = e.target.value;
    const newState = { currentExpress };
    if (currentExpress !== EXPRESS_SYSTEM_CALL) {
      newState.expressExtraData = {};
    }
    this.setState(newState);
  };

  render() {
    const { onClose } = this.props;
    const {
      expressCompany,
      packages,
      currentExpress,
      stepper,
      selectedPack,
      selectedPackItems,
      isShowBack,
      currentElectronWayBillServiceKdtId,
      currentElectronWayBillServiceKdtName,
      electronWayBillServiceKdtIdMap,
      waybillVersion,
      loading,
      expressExtraData,
      originPackages
    } = this.state;
    const { orderInfo, zentForm } = this.props;
    // 是否展示多包裹选择表格
    const isShowSelectPack = stepper === 1 && packages.length > 1;
    const packList = selectedPackItems.length === 0 ? packages : selectedPackItems;
    const orderNo = get(orderInfo, 'mainOrderInfo.orderNo');
    const channelType = get(orderInfo, 'mainOrderInfo.channelType');
    const storeId = get(orderInfo, 'mainOrderInfo.storeId', 0);

    if (originPackages.length === 0) {
      return <div>暂无包裹</div>;
    }

    return (
      <div>
        <Alert type="warning">提醒：物流信息最多修改3次，请仔细填写并核对。</Alert>
        {/* 多包裹的情况下，第一步需要选择包裹 */}
        {isShowSelectPack && (
          <div style={{ marginBottom: 20 }}>
            <div style={{ margin: '10px 0' }}>选择包裹</div>
            <Grid
              rowKey="packName"
              className="items-table"
              columns={columns}
              datasets={originPackages}
              selection={{
                selectedRowKeys: selectedPack,
                onSelect: (selectedRowKeys, selectedRows) => {
                  if (selectedRows.length > 5) {
                    Notify.error('单次最多修改5个包裹');
                  }
                  this.setState({
                    selectedPack: selectedRowKeys,
                    selectedPackItems: selectedRows
                  });
                }
              }}
            />
          </div>
        )}
        {stepper === 2 && (
          <>
            <FormControl label="发货方式：" className="express-list-form-control">
              <RadioGroup onChange={this.handleChangeExpress} value={currentExpress}>
                {expressList.map(express => (
                  <Radio value={express.value}>{express.text}</Radio>
                ))}
              </RadioGroup>
            </FormControl>
            {currentExpress === EXPRESS_SYSTEM_CALL && (
              <div className="od-express-content" style={{ marginBottom: 20 }}>
                <ExpressExtraComponent
                  isEditExpress
                  data={{ supportDeliveryTypes: [{ id: EXPRESS_SYSTEM_CALL }], channels: [] }}
                  storeId={storeId}
                  placeType="order"
                  channelType={channelType}
                  orderNo={orderNo}
                  orderInfo={orderInfo}
                  waybillVersion={waybillVersion}
                  zentForm={zentForm}
                  electronWayBillServiceKdtIdMap={electronWayBillServiceKdtIdMap}
                  currentElectronWayBillServiceKdtId={currentElectronWayBillServiceKdtId}
                  currentElectronWayBillServiceKdtName={currentElectronWayBillServiceKdtName}
                  onExpressChange={this.handleExpressExtraChange}
                />
              </div>
            )}
            {/* 自己联系快递 */}
            {currentExpress === 12 &&
              packList.map((packInfo, index) => (
                <ExpressItem
                  key={packInfo.packId}
                  packNo={index + 1}
                  packInfo={packInfo}
                  expressCompany={expressCompany}
                  onChange={this.changeExpressItem}
                />
              ))}
          </>
        )}

        <footer>
          <>
            {isShowBack && (
              <Button
                onClick={() => {
                  const currentStepper = stepper - 1;
                  const topStepper = packages.length === 1 ? 2 : 1;
                  // 判断是否到最顶部的步骤了
                  if (currentStepper === topStepper) {
                    this.setState({ isShowBack: false });
                  }
                  this.setState({ stepper: currentStepper });
                }}
              >
                上一步
              </Button>
            )}
            <Button onClick={onClose}>取消</Button>
            <Button
              type="primary"
              onClick={this.handleSubmit}
              loading={loading}
              disabled={
                (isShowSelectPack && (selectedPack.length === 0 || selectedPack.length > 5)) ||
                expressExtraData?.isActionDisabled
              }
            >
              {isShowSelectPack ? '下一步' : '保存'}
            </Button>
          </>
        </footer>
      </div>
    );
  }
}

export default LogisticsContent;
