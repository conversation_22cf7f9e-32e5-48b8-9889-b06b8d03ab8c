import React, { useMemo } from 'react';
import { Input } from 'zent';
import { Select } from '@zent/compat';

import { EXPRESS_SYSTEM_CALL } from '@youzan/order-domain-pc-components/es/delivery-dialog/constants';

function ExpressItem(props) {
  const { packNo, onChange, packInfo = {}, expressCompany } = props;

  const createHandleChange = changeKey => e => {
    onChange(packInfo.packId, { [changeKey]: e.target.value });
  };

  const {
    distItemInfo = [],
    sendTypeDesc,
    sendType,
    expressId,
    expressNo,
    systemExpressName,
    systemExpressNo
  } = packInfo;

  const isSystemCall = sendType === EXPRESS_SYSTEM_CALL;

  const goodsNum = useMemo(
    () => distItemInfo.reduce((ret, { num = 0 }) => ret + num, 0),
    [distItemInfo]
  );

  const getSystemExpressInfo = (name, no) => {
    if (name && no) {
      return `${name}-${no}`;
    }
    return '';
  };

  return (
    <div className="express-item-content">
      <h3 className="package-info">
        包裹
        {packNo}：
        <span>
          共{goodsNum}
          件商品
        </span>
      </h3>
      <div className="logistics-pkg-group">
        <span className="logistics-pkg-group__label">
          {isSystemCall ? '已取消运单' : '发货方式'}：
        </span>
        <span>
          {isSystemCall ? getSystemExpressInfo(systemExpressName, systemExpressNo) : sendTypeDesc}
        </span>
      </div>
      <div>
        <div className="logistics-pkg-group">
          <span className="logistics-pkg-group__label">物流公司：</span>
          <Select
            className="express-company-select"
            popupClassName="express-company-select-pop"
            data={expressCompany}
            value={expressId}
            optionValue="id"
            optionText="name"
            filter={(item, keyword) => item.name.indexOf(keyword) > -1}
            onChange={createHandleChange('expressId')}
          />
        </div>
        <div className="logistics-pkg-group">
          <span className="logistics-pkg-group__label">运单号：</span>
          <Input
            className="express-input"
            placeholder="填写运单号"
            value={expressNo}
            onChange={createHandleChange('expressNo')}
          />
        </div>
      </div>
    </div>
  );
}

export default ExpressItem;
