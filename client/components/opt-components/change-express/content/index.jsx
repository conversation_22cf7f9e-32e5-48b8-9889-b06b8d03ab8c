import React, { Component } from 'react';
import { get, isArray } from 'lodash';
import { Notify, BlockLoading } from 'zent';
import { isPartnerStore } from '@youzan/utils-shop';
import { IsAdvancedVersion } from 'common/utils';
import openDefaultAddressDialog from 'components/modals/default-address';
import {
  NO_EXPRESS,
  EXPRESS_SYSTEM_CALL
} from '@youzan/order-domain-pc-components/es/delivery-dialog/constants';
import * as api from '../api';
import ExpressList from './express-list';
import style from '../style.scss';

export default class Content extends Component {
  state = {
    packs: null
  };

  componentDidMount() {
    if (isPartnerStore) {
      this.getExpressList();
      return;
    }
    if (IsAdvancedVersion) {
      this.getExpressList();
    } else {
      api
        .fetchDefaultAddress({ type: 'return' })
        .then(data => {
          if (data) {
            this.getExpressList();
          } else {
            this.props.onClose();
            openDefaultAddressDialog();
          }
        })
        .catch(msg => {
          Notify.error(msg || '获取默认地址失败！');
          this.props.onClose();
        });
    }
  }

  getExpressList = () => {
    api
      .fetchPacksInfo({
        orderNo: get(this.props, 'orderInfo.mainOrderInfo.orderNo')
      })
      .then(({ expressInfo = {} }) => {
        const packs = (expressInfo.packs || []).reduce((pre, pack) => {
          if (+pack.sendType === NO_EXPRESS || pack.isModifyExpressTemp) {
            return pre;
          }
          const { expressDetail, ...others } = pack;
          const packInfo = others;

          packInfo.expressId = expressDetail.expressId;
          packInfo.expressNo = expressDetail.expressNo;

          if (+pack.sendType === EXPRESS_SYSTEM_CALL) {
            packInfo.expressId = '';
            packInfo.expressNo = '';
            packInfo.expressName = expressDetail.expressName;

            packInfo.systemExpressName = expressDetail.expressName;
            packInfo.systemExpressNo = expressDetail.expressNo;
          }

          return [...pre, packInfo];
        }, []);
        this.setState({ packs });
      })
      .catch(err => {
        Notify.error(err.msg || '获取包裹列表失败！');
      });
  };

  render() {
    const { packs } = this.state;
    const { orderInfo, onClose, reload } = this.props;

    if (!packs) {
      return <BlockLoading loading />;
    }
    if ((isArray(packs) && packs.length === 0) || get(orderInfo, 'packInfo.isModifyExpressTemp')) {
      return <div className={style.nopacks}>请先取消上门取件再修改</div>;
    }
    return (
      <ExpressList orderInfo={orderInfo} packages={packs} onClose={onClose} callback={reload} />
    );
  }
}
