import React, { Component } from 'react';
import { <PERSON><PERSON>, But<PERSON>, Notify } from 'zent';
import { get, pick } from 'lodash';
import * as api from '../api';

import ExpressItem from './express-item';

export default class LogisticsContent extends Component {
  state = {
    expressCompany: [],
    packages: this.props.packages,
    loading: false
  };

  componentDidMount() {
    // 获取快递列表
    api
      .fetchLogisticsList()
      .then(res => {
        const { allExpress = [] } = res;
        this.setState({
          expressCompany: allExpress.filter(item => item.display)
        });
      })
      .catch(err => {
        Notify.error(err || '获取快递公司列表失败！');
      });
  }

  changeExpressItem = (packId, data) => {
    const { packages } = this.state;
    const clonePackages = packages.map(item => {
      if (item.packId !== packId) {
        return item;
      }

      return {
        ...item,
        ...data
      };
    });

    this.setState({ packages: clonePackages });
  };

  handleSubmit = () => {
    const { packages } = this.state;
    const packs = packages
      .filter(({ expressId }) => !!expressId)
      .map(item => pick(item, ['packId', 'expressId', 'expressNo', 'version']));
    const param = {
      orderNo: get(this.props, 'orderInfo.mainOrderInfo.orderNo'),
      modifyExpressOrderJson: packs
    };

    let paymentType = get(this.props, 'orderInfo.packInfo.expressDetail.paymentType');
    let waybillVersion = get(this.props, 'orderInfo.packInfo.expressDetail.waybillVersion');

    if (!paymentType) {
      paymentType = get(
        this.props,
        'orderInfo.orderExpressInfo.packs[0].distOrderDTO.expressInfo.expressDetail.paymentType'
      );
    }

    if (!waybillVersion) {
      waybillVersion = get(
        this.props,
        'orderInfo.orderExpressInfo.packs[0].distOrderDTO.expressInfo.expressDetail.waybillVersion'
      );
    }

    param.paymentType = paymentType;
    param.waybillVersion = waybillVersion;

    this.setState({
      loading: true
    });

    api
      .changeExpress(param)
      .then(() => {
        Notify.success('物流修改成功');
        this.props.onClose && this.props.onClose();
        this.props.callback();
      })
      .catch(err => {
        Notify.error(err.msg || '物流修改失败');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  render() {
    const { onClose } = this.props;
    const { expressCompany, loading, packages } = this.state;
    return (
      <div>
        <Alert type="warning">提醒：物流信息最多修改3次，请仔细填写并核对。</Alert>
        {packages.map((packInfo, index) => (
          <ExpressItem
            key={packInfo.packId}
            packNo={index + 1}
            packInfo={packInfo}
            expressCompany={expressCompany}
            onChange={this.changeExpressItem}
          />
        ))}
        <footer>
          <Button type="primary" loading={loading} onClick={this.handleSubmit}>
            保存
          </Button>
          <Button onClick={onClose}>取消</Button>
        </footer>
      </div>
    );
  }
}
