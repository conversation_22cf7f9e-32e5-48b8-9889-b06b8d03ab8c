import React from 'react';
import { Dialog } from 'zent';
import { track } from '@youzan/retail-utils';
import { OrderManageListTableOperateColumnBtnEnum } from 'definition/order-info';

import style from './style.scss';
import Content from './content';
import NewContent from './new-content';
import createOptComponent from '../create-opt-component';

const { openDialog, closeDialog } = Dialog;
@createOptComponent
class ChangeExpress extends React.Component {
  openDialog = async () => {
    const {
      operation: { code, attributes },
      options
    } = this.props;

    const { beforeOperateClick } = options || {};

    let isContinue = true;
    if (beforeOperateClick) {
      isContinue = await beforeOperateClick(
        OrderManageListTableOperateColumnBtnEnum.ModifyLogistics
      );
    }
    if (!isContinue) {
      return;
    }

    const { modifyVersion = '2' } = attributes || {};

    // 1为老版本
    if (modifyVersion === '1') {
      openDialog({
        dialogId: code,
        title: '修改物流',
        className: style['change-express'],
        onClose: () => closeDialog(code),
        children: <Content onClose={() => closeDialog(code)} {...options} />
      });
    }

    // 2为新版本，新版本支持电子面单
    if (modifyVersion === '2') {
      track({
        et: 'view', // 事件类型
        ei: 'edit_express_dialog_view', // 事件标识
        en: '弹窗曝光', // 事件名称
        params: {
          component: 'edit_express_dialog'
        } // 事件参数
      });
      const dialogId = `new-${code}`;
      openDialog({
        dialogId,
        title: '修改物流',
        style: { width: '700px' },
        className: style['change-express'],
        onClose: () => closeDialog(dialogId),
        children: <NewContent onClose={() => closeDialog(dialogId)} {...options} />
      });
    }
  };

  render() {
    const { OptTypeCpn } = this.props;
    const renderOptions = {
      buttonType: 'primary',
      outline: false
    };
    return <OptTypeCpn onClick={this.openDialog} {...renderOptions} />;
  }
}

export default ChangeExpress;
