import { setUrlDomain, request, global } from '@youzan/retail-utils';

const { USER_INFO } = global;

const urlMap = {
  fetchExpressListUrl: setUrlDomain('/trade/order/expressList.json', 'www'),
  fetchDefaultAddressUrl: setUrlDomain('/setting/shopAddress/defaultAddress.json', 'www'),
  fetchPacksInfo: '/youzan.retail.trademanager.order.expressinfo/1.0.0/get',
  fetchLogisticsListUrl: '/youzan.logistics.express/3.0.0/get',
  updateLogisticsUrl: setUrlDomain('/trade/order/updateExpress.json', 'www'),
  changeExpress: '/youzan.retail.trademanager.delivery/1.0.0/modify',
  changeExpressV2: '/youzan.retail.trademanager.modify.delivery/2.0.0'
};

/**
 * 获取物流包裹信息
 * data: orderNo
 */
export function fetchExpressList(data) {
  return request({
    url: urlMap.fetchExpressListUrl,
    withCredentials: true,
    data
  });
}

export function fetchPacksInfo(data) {
  return request({
    url: urlMap.fetchPacksInfo,
    data
  });
}

/**
 * 获取默认地址
 *
 */
export function fetchDefaultAddress(data) {
  return request({
    url: urlMap.fetchDefaultAddressUrl,
    withCredentials: true,
    data
  });
}

/**
 * 获取物流列表
 */
export function fetchLogisticsList() {
  return request({
    url: urlMap.fetchLogisticsListUrl
  });
}

/**
 * 修改物流submit
 */
export function updateLogisticsSubmit(data) {
  return request({
    url: urlMap.updateLogisticsUrl,
    method: 'post',
    withCredentials: true,
    data
  });
}

/**
 * 新修改物流接口
 */
export function changeExpress(data) {
  return request({
    url: urlMap.changeExpress,
    method: 'post',
    data: {
      operatorName: USER_INFO.staffName,
      ...data
    }
  });
}

/**
 * 新修改物流接口
 */
export function changeExpressV2(data) {
  return request({
    url: urlMap.changeExpressV2,
    method: 'post',
    data: {
      operatorName: USER_INFO.staffName,
      ...data
    }
  });
}
