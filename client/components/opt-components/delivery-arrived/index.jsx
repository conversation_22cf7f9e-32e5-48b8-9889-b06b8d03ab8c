import React, { Component } from 'react';
import { Notify } from 'zent';
import { global } from '@youzan/retail-utils';
import { get } from 'lodash';
import typeMap from '../type-map';
import * as api from './api';

import './index.scss';

const { USER_INFO } = global;
export default class DeliveryArrived extends Component {
  state = {
    loading: false
  };

  click = () => {
    this.setState({ loading: true });
    api
      .arrived({
        orderNo: get(this.props, 'options.orderNo'),
        packId: get(this.props, 'options.packInfo.packId'),
        kdtId: get(this.props, 'options.packInfo.shopId'),
        operator: USER_INFO
      })
      .then(() => {
        Notify.success('操作成功');
      })
      .catch(err => {
        Notify.error(err.msg || '操作失败');
      })
      .finally(() => {
        this.setState({ loading: false });
        this.props.options.reload();
      });
  };

  render() {
    const Opt = typeMap[get(this.props, 'operation.type')];
    return (
      <Opt
        className="delivery-arrived-button"
        text={get(this.props, 'operation.text')}
        loading={this.state.loading}
        onClick={this.click}
      />
    );
  }
}
