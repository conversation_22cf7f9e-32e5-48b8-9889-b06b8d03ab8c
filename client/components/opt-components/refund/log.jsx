import React from 'react';
import { Dialog } from 'zent';
import { findIndex, filter, get } from 'lodash';
import { formatDatetime } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import { isSingleStore } from '@youzan/utils-shop';
import { convertFenToYen } from 'common/fns/format';
import { ChannelType } from 'common/constants/common';

import GoodsInfo from 'components/goods-info';

import { divGoodsNum } from 'common/helper';
import { optTypeMap } from '../type-map';
import style from './style.scss';

const ONLINE = ChannelType.Online;

class RefundLog extends React.Component {
  renderRefundItem = (goodsInfo = {}, refundOrderInfo = {}) => {
    const isOnline = get(this.props, 'options.orderInfo.mainOrderInfo.saleWay') === ONLINE;
    // 退款数量
    const refundNum = goodsInfo.refundNum
      ? `${divGoodsNum(goodsInfo.refundNum)} ${goodsInfo.unit}`
      : '-';

    return (
      <div className="log-item__goods" key={goodsInfo.goodsId}>
        <GoodsInfo
          className="refund-log-dialog"
          {...goodsInfo}
          goodsSnapUrl={isOnline && goodsInfo.goodsSnapUrl}
        />
        <div className="goods-refund-item">{refundOrderInfo.remark || '-'}</div>
        <div className="goods-refund-item">{refundNum}</div>
        <div className="goods-refund-item">￥{convertFenToYen(goodsInfo.refundFee)}</div>
      </div>
    );
  };

  renderDialogContent = () => {
    const { refundInfo = [], item = {}, allItems = {}, orderInfo = {} } = this.props.options;
    const isOnline = orderInfo.mainOrderInfo.saleWay === ONLINE;
    const orderNo = get(orderInfo, 'mainOrderInfo.orderNo');
    const refundInfosForItem = isOnline
      ? filter(
          refundInfo,
          ({ refundOrderItemInfos = [] }) =>
            findIndex(refundOrderItemInfos, {
              itemIdStr: item.itemIdStr
            }) > -1
        )
      : refundInfo;

    return (
      <div className={style['refund-log-content']}>
        <div className="log-table">
          <nav className="log-table__head">
            <span className="goods-title">商品</span>
            <span>退款说明</span>
            <span>退货数量</span>
            <span>退款金额</span>
          </nav>
          {refundInfosForItem.map(({ refundOrderItemInfos = [], refundOrderInfo = {} }) => {
            const detailLink = isSingleStore
              ? `/v2/order/refunddetail#?order_no=${orderNo}&refund_id=${refundOrderInfo.refundId}`
              : `/v4/trade/refund/detail?orderNo=${orderNo}&refundId=${
                  refundOrderInfo.refundId
                }&itemId=${item.itemId || allItems[0].itemId}`; // 有些取不到item，通过allItems取itemId。allItems可能有若干数据但所有itemId相同，因此选第一个用了。
            return (
              <section className="log-item" key={refundOrderInfo.refundId}>
                <div className="log-item__basic">
                  <div className="refund-info">
                    退款编号：{refundOrderInfo.refundId}
                    <p className="grey">
                      操作人：{refundOrderInfo.operatorName} 操作时间：
                      {formatDatetime(refundOrderInfo.updateTime)}
                    </p>
                  </div>
                  <BlankLink className="log-item__link" href={detailLink}>
                    查看详情
                  </BlankLink>
                </div>
                {refundOrderItemInfos.map(refundItemInfo =>
                  this.renderRefundItem(refundItemInfo, refundOrderInfo)
                )}
              </section>
            );
          })}
        </div>
      </div>
    );
  };

  openDialog = () => {
    Dialog.openDialog({
      dialogId: 'REFUND_LOG',
      title: '退款记录',
      children: this.renderDialogContent()
    });
  };

  render() {
    const { operation } = this.props;

    const OptTypeCpm = optTypeMap[operation.type];
    return <OptTypeCpm {...operation} buttonType="default" onClick={this.openDialog} />;
  }
}

export default RefundLog;
