@import '~shared/style';

:local(.refund-log-content) {
  .log-table {
    min-width: 700px;

    &__head {
      line-height: 38px;
      background-color: $background-color-base;
      text-align: center;
      display: flex;
      padding: 0 20px;
      margin-bottom: 10px;

      span {
        flex: 1;

        &:last-child {
          text-align: right;
        }
      }

      .goods-title {
        text-align: left;
        flex-basis: 200px;
      }
    }

    .log-item {
      border: 1px solid $border-color-base;
      border-bottom: none;
      margin-bottom: 10px;

      &__link {
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translate(0, -50%);
      }

      &__basic {
        position: relative;
        height: 60px;
        background-color: $background-color-base;
        padding: 10px;
        border-bottom: 1px solid $border-color-base;

        .refund-info {
          @extend .log-item__link;

          left: 10px;
          line-height: 1.8;
          color: $color-text-primary;
        }
      }

      &__goods {
        display: flex;
        padding: 0 10px;
        align-items: center;
        border-bottom: 1px solid $border-color-base;

        .goods-refund-item {
          flex: 1;
          text-align: center;

          &:last-child {
            text-align: right;
            color: $color-alert;
          }
        }
      }
    }
  }
}

.fr {
  float: right;
}

.refund-log-dialog.goods-info {
  flex: 1;
  flex-basis: 200px;
  padding: 0;
  margin: 10px 0;
}
