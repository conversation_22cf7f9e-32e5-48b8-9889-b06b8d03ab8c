import { request } from '@youzan/retail-utils';

export const getExpressList = ({ orderNo, itemId }) =>
  request({
    url: 'youzan.retail.trademanager.multiperiod.calendar/1.0.0/fetch',
    data: {
      orderNo,
      itemId
    }
  });

export const confirmDelay = ({ orderNo, itemId, dateList = [], operatorName }) =>
  request({
    url: 'youzan.retail.trademanager.multiperiod/1.0.0/postpone',
    methods: 'post',
    data: {
      orderNo,
      itemId,
      dateList,
      operatorName
    }
  });

export const undoDelay = ({ orderNo, itemId, dateList = [], operatorName }) =>
  request({
    url: 'youzan.retail.trademanager.multiperiod.postpone/1.0.0/cancle',
    methods: 'post',
    data: {
      orderNo,
      itemId,
      dateList,
      operatorName
    }
  });

export const predictList = ({ orderNo, itemId, lastDate }) =>
  request({
    url: 'youzan.retail.trademanager.multiperiod.postpone/1.0.0/estimate',
    data: {
      orderNo,
      itemId,
      lastDate
    }
  });
