import React, { Component } from 'react';
import { isNumber, isFunction } from 'lodash';
import style from './style.scss';

const emptyFun = () => {};

export default class Selector extends Component {
  static defaultProps = {
    data: [],
    itemRenderer: () => null,
    onSelect: () => {},
    canItemSelect: () => {},
    checkCurrentSelectData: null
  };

  state = {
    endIdx: null,
    startIdx: null
  };

  getYears = () => {
    const { data } = this.props;
    const yearSet = new Set(data.map(ele => ele.year));
    const yearArr = Array.from(yearSet);
    yearArr.sort((a, b) => a - b);
    return yearArr;
  };

  onSelect = () => {
    const { onSelect } = this.props;
    const { startIdx, endIdx } = this.state;
    onSelect({ startIdx, endIdx });
  };

  canAllSelected(start, end) {
    const subArr = this.props.data.filter((ele, idx) => idx >= start && idx <= end);
    return !subArr.some(ele => !this.props.canItemSelect(ele));
  }

  setSelectState(state) {
    const { checkCurrentSelectData } = this.props;
    if (isFunction(checkCurrentSelectData)) {
      if (!checkCurrentSelectData({ ...this.state, ...state })) {
        return;
      }
    }
    this.setState(state, this.onSelect);
  }

  clk = idx => {
    const { startIdx, endIdx } = this.state;
    if (!isNumber(startIdx)) {
      this.setSelectState({ startIdx: idx });
    } else {
      if (idx === startIdx) {
        this.setSelectState({ startIdx: null, endIdx: null });
        return;
      }
      if (!isNumber(endIdx)) {
        if (startIdx >= idx) {
          this.setSelectState({ startIdx: idx, endIdx: startIdx });
        } else if (this.canAllSelected(startIdx, idx)) {
          this.setSelectState({ endIdx: idx });
        } else {
          this.setSelectState({ startIdx: idx });
        }
      } else {
        this.setSelectState({ startIdx: idx, endIdx: null });
      }
    }
  };

  isSelected = idx => {
    const { startIdx, endIdx } = this.state;
    if (idx === startIdx || idx === endIdx) return true;
    if (isNumber(startIdx) && isNumber(endIdx)) {
      return idx > startIdx && idx < endIdx;
    }
    return false;
  };

  render() {
    const years = this.getYears();
    const { data, canItemSelect, itemRenderer } = this.props;
    return years.map(y => (
      <YearBlock year={y} key={y}>
        {data.map((ele, idx) => {
          const key = `${ele.year} ${ele.month} ${ele.day}`;
          if (ele.year !== y) return null;
          const isSelected = this.isSelected(idx);
          const canSelect = canItemSelect(ele);
          const renderedEle = itemRenderer(ele, {
            isSelected,
            canSelect,
            idx,
            state: this.state,
            canItemSelect,
            data
          });
          if (!renderedEle) {
            return null;
          }
          return (
            <div
              key={key}
              onClick={canSelect ? () => this.clk(idx) : emptyFun}
              className="item-wrapper"
            >
              {renderedEle}
            </div>
          );
        })}
      </YearBlock>
    ));
  }
}

function YearBlock({ year, children }) {
  return (
    <div className={style['year-block']}>
      <div className="title">{year}</div>
      <div className="content">{children}</div>
    </div>
  );
}
