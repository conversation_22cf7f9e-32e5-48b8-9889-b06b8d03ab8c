@import '~shared/style';

:local(.year-block) {
  padding: 10px;
  background: $background-color-base;
  margin: 10px 0;
  padding-bottom: 20px;
  .title {
    text-align: center;
    font-size: 22px;
    line-height: 2;
  }
  .content {
    height: 200px;
    overflow: auto;
  }
  .item-wrapper {
    width: 120px;
    height: 70px;
    display: inline-block;
    box-sizing: border-box;
    .item {
      padding-top: 20px;
      font-size: 14px;
      text-align: center;
      .ball {
        margin: auto;
        margin-bottom: 10px;
        color: $color-white;
        width: 50px;
        height: 50px;
        background: $color-link;
        border: 1px solid $color-link;
        border-radius: 35px;
        line-height: 50px;
        text-align: center;
        position: relative;
        transition: background 0.2s ease;
        .dot {
          height: 16px;
          width: 16px;
          border: 2px solid $color-warn;
          box-sizing: border-box;
          position: absolute;
          right: -1px;
          top: -1px;
          border-radius: 10px;
          background: $color-white;
          transition: background 0.2s ease;
        }
        .dot.show-icon {
          background: $color-warn;
        }
        .dot.show-icon::before {
          content: '';
          height: 9px;
          width: 4px;
          border: 1px solid white;
          position: absolute;
          right: 3px;
          top: 0;
          border-top: none;
          border-left: none;
          transform: rotate(45deg);
        }
      }
      .ball.disabled {
        background: $border-color-base;
        color: $color-text-secondary;
        border-color: $border-color-base;
      }
      .ball.outline {
        background: $color-white;
        color: $color-link;
      }
    }
  }
}
