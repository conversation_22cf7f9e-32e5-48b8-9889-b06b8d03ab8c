import React from 'react';
import { get } from 'lodash';
import cx from 'classnames';
import {
  getSelectedLength,
  isAfterValidIdx,
  getNthAfter,
  isUnExpressedItem,
  isDelayedItem
} from '../helper';

export function ConfirmRenderer(
  { month, day, desc: statusDesc, distStatus },
  { isSelected, canSelect, idx, state: { startIdx, endIdx }, data }
) {
  let desc = statusDesc;

  if (isSelected) {
    desc = '待确认顺延';
  } else {
    const selectedLength = getSelectedLength(startIdx, endIdx);
    if (selectedLength > 0 && idx >= startIdx) {
      const stealFrom = idx - selectedLength;
      const { desc: descOrigin } = data[stealFrom];
      desc = descOrigin;
    }
  }

  return (
    <div className="item">
      <Ball
        text={`${month}-${day}`}
        isOutline={isSelected}
        iconSelected={isSelected}
        showIcon={canSelect}
        disabled={distStatus === 1}
      />
      <span>{desc}</span>
    </div>
  );
}

export function NormalRenderer({ month, day, desc, distStatus }) {
  return (
    <div className="item">
      <Ball text={`${month}-${day}`} disabled={distStatus === 1} />
      <span>{desc}</span>
    </div>
  );
}

export function UndoDelayRenderer(
  { month, day, desc, distStatus, couldOperate },
  { isSelected, canSelect, idx, state: { startIdx, endIdx }, data }
) {
  let realDesc = desc;
  if (canSelect) realDesc = '已顺延';
  // 被选中的第n个可取消顺延的节点，他的状态取他后面第n个有效期次
  if (isAfterValidIdx(startIdx, idx) && isSelected) {
    const offset = idx - startIdx + 1;
    const stealFromItem = getNthAfter(data, idx, offset, isUnExpressedItem);
    realDesc = get(stealFromItem, 'desc', '');
  }
  // 待发货节点，他的状态取要根据他前面是否有选中的节点
  if (isAfterValidIdx(startIdx, idx) && isUnExpressedItem(data[idx])) {
    realDesc = null;
    const offset = getSelectedLength(startIdx, endIdx);
    const stealFromItem = getNthAfter(data, idx + 1, offset, isUnExpressedItem);
    realDesc = get(stealFromItem, 'desc', null);
  }

  if (!realDesc || (!couldOperate && isDelayedItem(data[idx]))) return null;
  return (
    <div className="item">
      <Ball
        iconSelected={isSelected}
        showIcon={canSelect}
        isOutline={distStatus === 2 && !isSelected}
        text={`${month}-${day}`}
        disabled={distStatus === 1}
      />
      <span>{realDesc}</span>
    </div>
  );
}

function Ball({ text, isOutline, disabled, showIcon = false, iconSelected }) {
  return (
    <div
      className={cx({
        ball: true,
        disabled,
        outline: isOutline
      })}
    >
      {text}
      {showIcon && (
        <div
          className={cx({
            dot: true,
            'show-icon': iconSelected
          })}
        />
      )}
    </div>
  );
}
