@import '~shared/style';

:local(.dialog) {
  .bread-crumb {
    padding: 0;
  }
  .zent-dialog-r-title {
    margin-bottom: 10px;
    .special-red {
      color: $color-alert;
      vertical-align: baseline;
    }
    .special-red::after {
      display: none;
    }
  }
  .footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    margin-top: 30px;

    &-text {
      display: flex;
      flex-direction: column;
      font-size: 13px;
      line-height: 15px;
      text-align: right;

      .select-count__num {
        color: $color-alert;
        font-size: 15px;
        padding: 0 2px;
      }
    }
  }
}
