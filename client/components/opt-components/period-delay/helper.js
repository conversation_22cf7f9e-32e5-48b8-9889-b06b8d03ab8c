import { isNumber, get } from 'lodash';
import { format } from 'date-fns';

export function getSelectedLength(startIdx, endIdx) {
  if (!isNumber(startIdx)) {
    return 0;
  }
  if (!isNumber(endIdx)) {
    return 1;
  }
  return 1 + endIdx - startIdx;
}

export function isAfterValidIdx(startIdx, idx) {
  return isNumber(startIdx) && idx >= startIdx;
}

export function getNthAfter(data, idx, offsetOrigin, judger) {
  let offset = offsetOrigin;
  for (let i = idx; i < data.length; i++) {
    if (judger(data[i])) {
      offset--;
    }
    if (offset <= 0) {
      return data[i];
    }
  }
  return null;
}

export function isUnExpressedItem(item) {
  return item.distStatus === 0;
}

export function isDelayedItem(item) {
  return item.distStatus === 2;
}

export function getPredictTarget(data, selectedObj, mode) {
  if (mode === 1) {
    return get(data, `[${selectedObj.startIdx}]`, null);
  }
  if (mode === 2) {
    const arr = data.filter((item, idx) => idx >= selectedObj.startIdx && item.distStatus === 0);
    return arr[0];
  }
}

export const handleData = (data = []) =>
  data.map(ele => {
    const dateStr = format(ele.distTime, 'YYYY-MM-DD');
    const dateArr = dateStr.split('-');
    const desc = `${`第${ele.currentIssue}期 `}${ele.distStatusDesc}`;
    return {
      ...ele,
      year: dateArr[0],
      month: dateArr[1],
      day: dateArr[2],
      desc
    };
  });

export const canDelayItemSelect = item =>
  isUnExpressedItem(item) && item.couldPostpone && item.couldOperate;

export const canUndoItemSelect = item =>
  isDelayedItem(item) && item.couldPostpone && item.couldOperate;
