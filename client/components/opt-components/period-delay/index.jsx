import React, { Fragment } from 'react';
import { get } from 'lodash';
import { global } from '@youzan/retail-utils';
import { Notify } from 'zent';
import { optTypeMap } from '../type-map';
import * as api from './api';
import Content from './content';

const { USER_INFO } = global;

class PeriodDelay extends React.Component {
  state = {
    loading: false,
    data: null,
    active: false
  };

  onActive = () => {
    this.setState({ active: true });
  };

  showDialog = () => {
    const { getExpressList } = api;
    const orderInfo = get(this.props, 'options.orderInfo');
    const { mainOrderInfo, itemInfo = [] } = orderInfo;
    this.setState({ loading: true });
    getExpressList({
      orderNo: mainOrderInfo.orderNo,
      itemId: itemInfo[0].itemIdStr
    })
      .then(data => {
        this.setState({ data });
      })
      .catch(err => {
        Notify.error(err.msg);
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  render() {
    const {
      operation,
      options: { orderInfo, reload }
    } = this.props;
    const { mainOrderInfo, itemInfo = [] } = orderInfo;
    const OptTypeCpm = optTypeMap[operation.type];
    const name = USER_INFO.staffName || USER_INFO.account;
    return (
      <Fragment>
        <OptTypeCpm {...operation} loading={this.state.loading} onClick={this.showDialog} />
        {this.state.data && (
          <Content
            onActive={this.onActive}
            operatorName={name}
            rawData={this.state.data}
            close={() => {
              this.setState({ data: null }, () => {
                if (this.state.active) {
                  reload();
                }
              });
            }}
            orderNo={mainOrderInfo.orderNo}
            itemId={itemInfo[0].itemIdStr}
          />
        )}
      </Fragment>
    );
  }
}

export default PeriodDelay;
