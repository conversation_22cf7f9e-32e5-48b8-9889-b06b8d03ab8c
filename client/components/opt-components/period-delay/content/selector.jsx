import React, { Fragment } from 'react';
import { Button, Notify } from 'zent';
import { isNumber } from 'lodash';
import Selector from '../period-selector';
import { getSelectedLength } from '../helper';

function BasicSelector({
  data,
  canItemSelect,
  title,
  itemRenderer,
  onSelect,
  btnTitle,
  onClick,
  checkCurrentSelectData,
  footerText
}) {
  return (
    <Fragment>
      <span style={{ lineHeight: '30px' }}>{title}</span>
      <Selector
        data={data}
        canItemSelect={canItemSelect}
        itemRenderer={itemRenderer}
        onSelect={onSelect}
        checkCurrentSelectData={checkCurrentSelectData}
      />
      <div className="footer">
        {footerText && <span style={{ marginRight: 10, lineHeight: '30px' }}>{footerText}</span>}
        <Button onClick={onClick} type="primary">
          {btnTitle}
        </Button>
      </div>
    </Fragment>
  );
}

const fetch = ({
  itemId,
  orderNo,
  operatorName,
  beforeFetch,
  selectedData,
  data,
  onSuccess,
  cb,
  api,
  selectTitle
}) => {
  const { startIdx, endIdx } = selectedData;
  if (!isNumber(startIdx)) {
    return Notify.error(`请先选择需要${selectTitle}的期次`);
  }
  beforeFetch();
  const length = getSelectedLength(startIdx, endIdx);
  const dateList = data
    .filter((ele, idx) => idx >= startIdx && idx <= startIdx + length - 1)
    .map(ele => ele.distTime);
  api({
    itemId,
    orderNo,
    dateList,
    operatorName
  })
    .then(rst => {
      if (rst) onSuccess(rst);
    })
    .catch(err => {
      Notify.error(err.msg);
    })
    .finally(() => {
      cb();
    });
};

export function FetchBasicSelector(props) {
  return (
    <BasicSelector
      {...props}
      onClick={() => {
        fetch(props);
      }}
    />
  );
}
