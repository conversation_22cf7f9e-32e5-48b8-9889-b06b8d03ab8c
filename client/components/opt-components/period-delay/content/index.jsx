import React, { Component, Fragment } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>crumb, BlockLoading, Notify } from 'zent';
import { isNumber } from 'lodash';
import { format } from 'date-fns';
import { debounce } from '@youzan/retail-armor';
import Selector from '../period-selector';
import {
  getSelectedLength,
  isUnExpressedItem,
  getPredictTarget,
  handleData,
  canDelayItemSelect,
  canUndoItemSelect
} from '../helper';
import { Confirm<PERSON>enderer, NormalRenderer, UndoDelayRenderer } from '../period-selector/renderer';
import style from '../style.scss';
import { FetchBasicSelector } from './selector';
import * as api from '../api';

/**
 * @mode
 * 配送日历：0
 * 顺延：1
 * 取消顺延：2
 */
const MODE_CALENDAR = 0;
const MODE_DELAY = 1;
const MODE_UNDO = 2;
export default class Content extends Component {
  state = {
    periodData: handleData(this.props.rawData.detailList),
    mode: MODE_CALENDAR,
    loading: false,
    delayingIdxData: {
      startIdx: null,
      endIdx: null
    },
    total: this.props.rawData.totalPeriods,
    notExpressedCount: this.props.rawData.deliveredPeriods,
    undoDelayingIdxData: {
      startIdx: null,
      endIdx: null
    },
    predictText: null
  };

  /**
   * @取消顺延函数
   */
  // 后面没有有效期次的，不能取消顺延
  checkCanUndo = ({ startIdx, endIdx }) => {
    const len = getSelectedLength(startIdx, endIdx);
    const subArr = this.currentData.filter((ele, idx) => idx >= startIdx);
    const goodItemCount = subArr.reduce((total, curEle) => {
      let tempTotal = total;
      if (isUnExpressedItem(curEle)) {
        tempTotal++;
      }
      return tempTotal;
    }, 0);
    const canUndo = len <= goodItemCount;
    if (!canUndo) {
      Notify.error('剩余期数不足，无法选中');
    }
    return canUndo;
  };

  /**
   * @通用函数
   */
  get currentData() {
    const { mode, periodData } = this.state;
    return this.getCurrentData(periodData, mode);
  }

  getCurrentData = (data, mode) => {
    if (mode === MODE_CALENDAR) return data.filter(ele => ele.distStatus !== 2);
    if (mode === MODE_DELAY) return data.filter(ele => ele.distStatus !== 2);
    if (mode === MODE_UNDO) return data;
  };

  goTo = mode => {
    if (mode === MODE_CALENDAR) {
      return this.reload();
    }
    this.setState({ mode });
  };

  getSelectedCount = () => {
    const { mode, undoDelayingIdxData, delayingIdxData } = this.state;
    const data =
      {
        [MODE_DELAY]: delayingIdxData,
        [MODE_UNDO]: undoDelayingIdxData
      }[mode] || {};

    const { startIdx, endIdx } = data;
    const selectCount = getSelectedLength(startIdx, endIdx);
    if (selectCount <= 0) return null;
    return (
      <span className="select-count">
        已选
        <span className="select-count__num">{selectCount}</span>期
      </span>
    );
  };

  getFooterText = predictText => (
    <span className="footer-text">
      {this.getSelectedCount()}
      <span className="footer-text__tips">{predictText}</span>
    </span>
  );

  getUndoText(predictItem, firstSelectedItem) {
    const predictText = `第${predictItem.currentIssue}期将恢复至${format(
      firstSelectedItem.distTime,
      'YYYY年MM月DD日'
    )}配送，后续期次以此类推`;
    this.setState({
      predictText: this.getFooterText(predictText)
    });
  }

  @debounce(500)
  onCalc({ startIdx, endIdx }) {
    const { itemId, orderNo } = this.props;
    const lastIdx = startIdx + getSelectedLength(startIdx, endIdx) - 1;
    const predictItem = getPredictTarget(this.currentData, { startIdx, endIdx }, this.state.mode);
    if (!isNumber(startIdx)) return this.setState({ predictText: null });
    if (this.state.mode === MODE_UNDO)
      return this.getUndoText(predictItem, this.currentData[startIdx]);

    api
      .predictList({
        itemId,
        orderNo,
        lastDate: this.currentData[lastIdx].distTime
      })
      .then(date => {
        const text = `第${predictItem.currentIssue}期将顺延至${format(
          date,
          'YYYY年MM月DD日'
        )}配送，后续期次以此类推`;
        this.setState({ predictText: this.getFooterText(text) });
      })
      .catch(() => {
        this.setState({ predictText: null });
      });
  }

  reload = () => {
    this.setState({ loading: true });
    const { itemId, orderNo } = this.props;
    api
      .getExpressList({ itemId, orderNo })
      .then(rst => {
        this.setState({
          periodData: handleData(rst.detailList),
          total: rst.totalPeriods,
          notExpressedCount: rst.deliveredPeriods,
          delayingIdxData: { startIdx: null, endIdx: null },
          undoDelayingIdxData: { startIdx: null, endIdx: null }
        });
      })
      .finally(() => {
        this.setState({
          loading: false,
          mode: MODE_CALENDAR,
          predictText: null
        });
      });
  };

  setLoading = val => {
    this.setState({ loading: val });
  };

  getTitle = () => {
    const { mode } = this.state;
    if (mode === MODE_CALENDAR) {
      return (
        <Breadcrumb className="bread-crumb">
          <Breadcrumb.Item>
            <span>
              配送日历(共需配送
              <span className="special-red">{this.state.total}</span>
              期，已配送
              <span className="special-red">{this.state.notExpressedCount}</span>
              期)
            </span>
          </Breadcrumb.Item>
        </Breadcrumb>
      );
    }
    return (
      <Breadcrumb className="bread-crumb">
        <Breadcrumb.Item>{this.renderHomeBreadcrumb()}</Breadcrumb.Item>
        <Breadcrumb.Item>
          <span>{mode === MODE_DELAY ? '顺延配送' : '取消顺延'}</span>
        </Breadcrumb.Item>
      </Breadcrumb>
    );
  };

  renderHomeBreadcrumb = () => (
    <span
      onClick={() => {
        this.goTo(MODE_CALENDAR);
      }}
      style={{ color: '#155bd4', cursor: 'pointer' }}
    >
      配送日历
    </span>
  );

  render() {
    const { close, itemId, orderNo, operatorName, onActive } = this.props;
    const { loading, mode, delayingIdxData, undoDelayingIdxData } = this.state;
    const commonProps = {
      key: mode,
      data: this.currentData,
      itemId,
      orderNo,
      operatorName,
      beforeFetch: () => {
        onActive();
        this.setLoading(true);
      },
      cb: () => this.goTo(MODE_CALENDAR),
      footerText: this.state.predictText
    };
    return (
      <div>
        <Dialog
          className={style.dialog}
          title={this.getTitle()}
          visible
          style={{ width: 670, maxHeight: 700 }}
          onClose={close}
        >
          <BlockLoading loading={loading}>
            {mode === MODE_CALENDAR && (
              <Fragment>
                <Button onClick={() => this.goTo(MODE_DELAY)}>顺延配送</Button>
                <Button onClick={() => this.goTo(MODE_UNDO)}>取消顺延</Button>
                <Selector data={this.currentData} itemRenderer={NormalRenderer} />
              </Fragment>
            )}
            {mode === MODE_DELAY && (
              <FetchBasicSelector
                title="请选择需要顺延配送的日期"
                canItemSelect={canDelayItemSelect}
                itemRenderer={ConfirmRenderer}
                onSelect={selectedData => {
                  this.setState({ delayingIdxData: selectedData }, () =>
                    this.onCalc(this.state.delayingIdxData)
                  );
                }}
                btnTitle="确认顺延"
                selectTitle="顺延配送"
                api={api.confirmDelay}
                selectedData={delayingIdxData}
                onSuccess={() => Notify.success('确认顺延成功')}
                {...commonProps}
              />
            )}
            {mode === MODE_UNDO && (
              <FetchBasicSelector
                title="请选择需要取消顺延的日期"
                canItemSelect={canUndoItemSelect}
                itemRenderer={UndoDelayRenderer}
                onSelect={selectedData => {
                  this.setState({ undoDelayingIdxData: selectedData }, () =>
                    this.onCalc(this.state.undoDelayingIdxData)
                  );
                }}
                btnTitle="取消顺延"
                selectTitle="取消顺延"
                api={api.undoDelay}
                selectedData={undoDelayingIdxData}
                onSuccess={() => Notify.success('取消顺延成功')}
                checkCurrentSelectData={this.checkCanUndo}
                {...commonProps}
              />
            )}
          </BlockLoading>
        </Dialog>
      </div>
    );
  }
}
