import React from 'react';
import { get, mapKeys, camelCase, isEmpty } from 'lodash';
import { Button, Dialog, Notify, BlockLoading } from 'zent';

import {
  AGREE_BUYER_REFUND,
  AGREE_RETURN_GOODS,
  CONFIRM_GOODS_REFUND
} from 'components/opt-components/constant';
import {
  isRetailSingleStore,
  isRetailMinimalistShop,
  isHqStore,
  isPartnerStore
} from '@youzan/utils-shop';
import { notOptRemarkAndStar } from 'common/constant';

import createOptComponent from '../create-opt-component';
import Content from './content';
import DeliveryAttention from './delivery-attention';
import * as api from './api';
import style from './style.scss';

const { openDialog } = Dialog;

@createOptComponent
class BuyerRefund extends React.Component {
  state = {
    showAttentionDialog: false,
    showDialog: false,
    addressList: [],
    pagination: {
      current: 1,
      total: 0,
      pageSize: 50
    },
    loading: false,
    btnLoading: false,
    qttNotFx: {}
  };

  componentDidMount() {
    this.props.operation.code === AGREE_RETURN_GOODS && this.fetchRefundAddress();
  }

  useChainApi = () => {
    return !isRetailSingleStore;
  };

  getAddressListFromRes = (addressResInfo = {}) => {
    const list = addressResInfo.data;

    return list.map(item => {
      // 不要喷我，原本就这么乱, 最好是直接输出不做任何转换, 这里太乱了 (@自晓 留)
      const formattedItem = mapKeys(item, (v, k) => camelCase(k));
      const {
        contactName = '',
        isDefault,
        address = '',
        mobile = '',
        id,
        warehouseId,
        outAddressId,
        warehouseName = ''
      } = formattedItem;

      // 下面的 warehouse || id, 是为了处理 极简版 不存在 warehouseId 的情况
      return {
        ...formattedItem,
        id: isRetailSingleStore ? outAddressId || id : warehouseId || id,
        warehouseId,
        isDefault: !!+isDefault,
        paramsAddress: isRetailSingleStore ? address : `${warehouseName} ${address}`,
        name: contactName || mobile,
        mobile
      };
    });
  };

  fetchRefundAddress = (param = {}) => {
    const { pageSize } = this.state.pagination;
    let action = api.fetchAddressList;
    const current = param.current || 1;
    const query = {
      pageNo: current,
      pageSize,
      keyword: param.keyword,
      refundId: get(this.props, 'options.refundOrderInfo.refundId'),
      orderNo: get(this.props, 'options.refundOrderInfo.orderNo'),
      orderItemId: get(this.props, 'options.refundOrderItemInfos[0].itemIdStr')
    };
    // 连锁版接口及参数
    if (this.useChainApi()) {
      action = api.fetchChainRefundAddress;
    }
    const orderkdtId = get(this.props, 'options.simpleOrderInfo.kdtId');

    // !(isHqStore && !isRetailMinimalistShop) => 不是多网店总部
    const canAction =
      !(isHqStore || isPartnerStore) || isRetailMinimalistShop || !notOptRemarkAndStar(orderkdtId);
    if (!canAction) {
      return;
    }
    this.setState({ loading: true });
    action(query)
      .then(res => {
        const addressList = this.getAddressListFromRes(res);
        this.setState(preState => ({
          addressList,
          pagination: { ...preState.pagination, current, total: res.total || 0 }
        }));
      })
      .catch(err => {
        Notify.error(err.msg || '获取退货地址出错！');
      })
      .finally(() => {
        this.setState({ loading: false });
      });
  };

  openRefundAddressPage = () => {
    window.open('/shop/v2/setting/store/index#location', '_blank');
  };

  toFixRefundAddress = () => {
    openDialog({
      dialogId: 'refund_dialog_fix', // id is used to close the dialog
      title: '退货地址信息不足',
      children: <div>当前退货地址信息不足，请先前往补充退货地址信息！</div>,
      footer: (
        <Button type="primary" onClick={this.openRefundAddressPage}>
          前往补充
        </Button>
      )
    });
  };

  openDialog = async () => {
    const { options = {}, operation = {} } = this.props;
    const { isQttNotFenxiaoOrder } = options;
    const { code } = operation;

    if (isQttNotFenxiaoOrder && [AGREE_BUYER_REFUND, CONFIRM_GOODS_REFUND].includes(code)) {
      try {
        this.setState({ btnLoading: true });
        const res = await api.checkQttNotFxRefundCommisson({
          orderNo: get(options, 'refundOrderInfo.orderNo'),
          refundId: get(options, 'refundOrderInfo.refundId')
        });
        this.setState({
          btnLoading: false,
          qttNotFx: res
        });
      } catch (error) {
        this.setState({ btnLoading: false });
      }
    }

    const checkResult = this.checkNeedDeliveryAttention();

    if (checkResult.needAttention) {
      this.setState({ showAttentionDialog: true });
    } else {
      // 当同意退货时 且 收货地址为空则 强制跳转到店铺地址设置页面
      const { addressList } = this.state;
      if (code === AGREE_RETURN_GOODS && !this.useChainApi()) {
        const missingAddress =
          !addressList[0] ||
          isEmpty(addressList[0]) ||
          (addressList[0].mobile === '' && addressList[0].telephone === '');
        if (missingAddress) {
          this.toFixRefundAddress();
          return;
        }
      }
      this.setState({ showDialog: true });
    }
  };

  closeDialog = () => {
    this.setState({ showDialog: false });
  };

  /**
   * 检测是否需要提示“下单数量和发货数量不一致”的信息
   */
  checkNeedDeliveryAttention = () => {
    const { refundOrderItemInfos } = this.props.options;
    const differentItems = refundOrderItemInfos.filter(
      item => +item.deliveryedNum > 0 && +item.num !== +item.deliveryedNum
    );
    return {
      needAttention: differentItems.length > 0,
      items: differentItems
    };
  };

  hideAttentionDialog = () => this.setState({ showAttentionDialog: false });

  render() {
    const { operation = {}, options = {}, OptTypeCpn } = this.props;
    const { code, text } = operation;
    const {
      addressList,
      showDialog,
      pagination,
      loading,
      btnLoading,
      showAttentionDialog,
      qttNotFx
    } = this.state;
    const outline =
      [AGREE_RETURN_GOODS, AGREE_BUYER_REFUND, CONFIRM_GOODS_REFUND].indexOf(code) === -1;

    const dialogId = `optcomponent-dialog-${code}`;

    const dialogStyle = {
      width: code === AGREE_RETURN_GOODS ? 750 : 704
    };
    return (
      <div className={style['action-wrapper']}>
        <Dialog
          visible={showDialog}
          style={dialogStyle}
          title={text}
          onClose={() => this.setState({ showDialog: false })}
        >
          <BlockLoading loading={loading}>
            <Content
              options={options}
              code={code}
              fetchRefundAddress={this.fetchRefundAddress}
              addressList={addressList}
              pagination={pagination}
              dialogId={dialogId}
              qttNotFx={qttNotFx}
              onCancel={this.closeDialog}
            />
          </BlockLoading>
        </Dialog>
        <Dialog
          visible={showAttentionDialog}
          onClose={this.hideAttentionDialog}
          title={text}
          style={dialogStyle}
        >
          <DeliveryAttention
            data={this.checkNeedDeliveryAttention().items}
            onClose={this.hideAttentionDialog}
            onConfirm={() => {
              this.hideAttentionDialog();
              this.setState({ showDialog: true });
            }}
          />
        </Dialog>
        <OptTypeCpn outline={outline} loading={btnLoading} onClick={this.openDialog} />
      </div>
    );
  }
}

export default BuyerRefund;
