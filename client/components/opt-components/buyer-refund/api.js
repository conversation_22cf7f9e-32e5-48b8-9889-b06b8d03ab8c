import { request } from '@youzan/retail-utils';

/**
 * 获取(退货)退款信息
 * data: orderNo, itemId, type, isFenxiaoOrder
 */
export function fetchComplaint(data) {
  return request({
    withCredentials: true,
    url: `${window._global.url.www}/trade/safe/preCheck.json`,
    data
  });
}

/**
 * 获取退货地址列表
 */
export function fetchAddressList(data) {
  return request({
    url: '/youzan.retail.trademanager.query.refundaddresses/1.0.0',
    data
  });
}

/**
 * 同意退款
 * data: safeguardId safeNo isAssumedFenxiaoFee
 * if safeguardType === 2: name mobile address
 */
export function acceptRefund(data) {
  return request({
    withCredentials: true,
    url: '/v2/order/refunddetail/accept.json', // 同意退款申请
    method: 'POST',
    data
  });
}

/**
 * 拒绝退款
 * data: safeguardId safeNo rejectReason
 */
export function rejectRefund(data) {
  return request({
    withCredentials: true,
    url: '/v2/order/refunddetail/reject.json', // 拒绝退款
    method: 'POST',
    data
  });
}

/**
 * 确认收到退货
 * data: safeguardId safeNo isAssumedFenxiaoFee
 */
export function sign(data) {
  return request({
    withCredentials: true,
    url: `${window._global.url.www}/trade/safe/sign.json`, // 确认收到退货
    method: 'POST',
    data
  });
}

/**
 * 拒绝确认收货
 * data: safeguardId safeNo
 */
export function unsign(data) {
  return request({
    withCredentials: true,
    url: `${window._global.url.www}/trade/safe/unsign.json`, // 拒绝确认收货
    method: 'POST',
    data
  });
}

// 连锁版获取退货地址
export const fetchChainRefundAddress = data =>
  request({
    url: '/youzan.retail.trademanager.refund/1.0.0/addresses',
    data
  });

/**
 * 群团团非分销-校验退款佣金
 */
export function checkQttNotFxRefundCommisson(data) {
  return request({
    withCredentials: true,
    url: '/v2/order/refunddetail/qtt/checkRefundCommission.json',
    method: 'GET',
    data
  });
}

/**
 * 群团团非分销订单 只退佣金
 */
export function qttNotFxRefundCommission(data) {
  return request({
    withCredentials: true,
    url: '/v2/order/refunddetail/qtt/refundCommission.json',
    method: 'POST',
    data
  });
}

/**
 * 群团团非分销订单 校验是否可以取消退款
 */
export function qttNotFxRefuseCheck(data) {
  return request({
    withCredentials: true,
    url: '/v2/order/refunddetail/qtt/refuseCheck.json',
    method: 'POST',
    data
  });
}

/**
 * 获取随心团退款失败原因
 */
export function geSxtRefuseReasonData(data) {
  return request({
    url: '/youzan.retail.trademanager.operate.reason.get/1.0.0',
    data
  });
}
