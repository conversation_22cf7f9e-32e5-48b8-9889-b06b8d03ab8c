import { ReturnAddressSelectSource } from 'definition/delivery';
import { DeliveryChannelType } from 'common/constants/common';

export interface Operations {
  displayAtOtherKdt: boolean;
  code: string;
  disabled: boolean;
  text: string;
  priority: number;
  type: string;
}

export interface ConsultMessageInfoList {
  reason: string;
  role: number;
  phone: string;
  refundFee: string;
  time: number;
  title: string;
  demand: string;
  desc: string;
  status: string;
}

/**
 * 这个数据结构, 混合了两个接口的数据结构, 所以根据不同的店铺类型, 会有不同的数据结构
 * 你可能会觉得数据结构非常混乱、不可预测, 但是历史代码已经写成这样了, 暂时先不改了
 */
export interface AddressList {
  isDefault: boolean;
  id: number;
  isMinDistance: boolean;
  city: string;
  areaCode: string;
  county: string;
  regionId: number;
  createdAt: number;
  telephone: string;
  regionType: string;
  countryIndex: number;
  countryCode: string;
  kdtId: number;
  isDeleted: boolean;
  province: string;
  updatedAt: number;
  extensionNumber: string;
  warehouseId: number;
  paramsAddress: string;
  name: string;
  mobile: string;
  address: string;

  area: string;
  warehouseName: string;
  contactName: string;
}

export interface RefundStateAssemblyList {
  code: number;
  time: number;
  content: string;
  compareState: number;
}

export interface Tags {
  sTOCK_DEDUCTED: boolean;
  sHIPPED: boolean;
  fEEDBACK: boolean;
}

export interface RefundOrderItemInfo {
  pricingStrategy: number;
  orderNo: string;
  goodsInfo: string;
  itemIdStr: string;
  realPay: number;
  num: number;
  goodsNo: string;
  tagsInfo: string;
  title: string;
  feedback: number;
  isPresent: boolean;
  operations: any[];
  goodsRefund: number;
  deliveryedNum: number;
  extra: string;
  buyerMemo: string;
  supportDiff: boolean;
  sku: string;
  payPrice: number;
  itemId: number;
  buyerMemoDesc: string;
  goodsId: number;
  postageRefund: number;
  skuId: number;
  unitPrice: number;
  icons: any[];
  originUnitPrice: number;
  tags: Tags;
  unit: string;
  isReduce: boolean;
  imgUrl: string;
  refundFee: number;
  refundNum: number;
  goodsType: number;
  skuCode: string;
  goodsSnapUrl: string;
}

export interface RefundOrderInfo {
  orderNo: string;
  reason: string;
  demandDesc: string;
  operatorId: number;
  remark: string;
  saleWayDesc: string;
  refundStateProgress: number;
  payWay: string;
  updateTime: number;
  saleWay: number;
  goodsStatus: number;
  refundStatus: number;
  refundType: number;
  goodsStatusDesc: string;
  shopName: string;
  version: number;
  demand: number;
  refundId: string;
  operatorName: string;
  postage: number;
  payWayCode: number;
  refundMode: number;
  phone: string;
  refundFee: number;
  refundPhaseAndTypeDesc: string;
  createTimeStamp: number;
  status: string;
  fenXiaoRefundFee: string;
  channelType: DeliveryChannelType;
}

export interface Operation2 {
  displayAtOtherKdt: boolean;
  code: string;
  disabled: boolean;
  text: string;
  priority: number;
  type: string;
}

export interface TimeOutSecond {
  color: string;
  text: string;
  type: string;
}

export interface Extra {
  timeOutSecond: TimeOutSecond;
}

export interface RefundTipInfo {
  tipContent: string;
  timeOutSecond: number;
  extra: Extra;
  agreeRefundVirtualTicketDesc: string;
  tips: string;
}

export interface Express {
  expressId: number;
  expressName: string;
  expressNo: string;
}

export interface Tags2 {
  sTOCK_DEDUCTED: boolean;
  uSE_UMP_PROMOTION: boolean;
  iS_MEMBER: boolean;
  dELIVERY_OFC_ORDER: boolean;
  iS_SECURED_TRANSACTIONS: boolean;
  yZ_GUARANTEE: boolean;
  mESSAGE_NOTIFY: boolean;
  iS_PAYED: boolean;
  uSE_UMP_COUPON: boolean;
  hAS_CALL_SETTLE: boolean;
  fEEDBACK: boolean;
}

export interface SimpleOrderInfo {
  discounted: number;
  orderNo: string;
  realPay: number;
  bizType: number;
  realPayV2: number;
  buyerPhone: string;
  express: Express;
  buyerId: number;
  payTime: number;
  tags: Tags2;
  deductionPayment: number;
  postage: number;
  kdtId: number;
  nickName: string;
  teamType: number;
  expressType: number;
  channelType: number;
}

export interface MainOrderInfo {
  orderNo: string;
}

export interface OrderInfo {
  mainOrderInfo: MainOrderInfo;
}

export interface Options {
  consultMessageInfoList: ConsultMessageInfoList[];
  refundStateAssemblyList: RefundStateAssemblyList[];
  refundOrderItemInfos: RefundOrderItemInfo[];
  refundOrderInfo: RefundOrderInfo;
  returnAddressSelectSource: ReturnAddressSelectSource;
  operation: Operation2[];
  agreeRefundVirtualTicketDesc: string;
  refundExpress: {};
  refundTipInfo: RefundTipInfo;
  simpleOrderInfo: SimpleOrderInfo;
  orderInfo: OrderInfo;
}

export interface RootObject {
  operation: Operations;
  options: Options;
}

export interface IPagination {
  current: number;
  total: number;
  pageSize: number;
}

export type IOnAddressChange = (param: Record<string, any>) => never;
