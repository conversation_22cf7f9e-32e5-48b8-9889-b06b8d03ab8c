import * as React from 'react';
import { get, find } from 'lodash';
import { ICheckboxEvent } from 'zent';
import Form, { connect, getFormValues, createForm } from '@youzan/retail-form';
import { setUrlDomain } from '@youzan/retail-utils';
import NewLocation from 'components/location';
import { OrderGoodsType } from 'common/constants/common';

import { ReturnAddressSelectSource } from 'definition/delivery';
import {
  isUnifiedShop,
  isRetailMinimalistShop,
  isRetailSingleStore,
  isSingleStore
} from '@youzan/utils-shop';
import { IPagination, AddressList, IOnAddressChange, Options } from './interface';

import {
  AGREE_BUYER_REFUND,
  AGREE_RETURN_GOODS,
  REFUSE_REFUND_APPLY,
  REFUSE_RECEIVE_GOODS
} from '../../constant';
import Action from './action';
import ActionQtt from './action-qtt';
import RefundAddressPaginator from './components/refund-address-paginator';
import BaseRefundInfo from './components/basic-refund-info';
import RefundAddress from './components/refund-address';
import RefundExpress from './components/refund-express';
import RefuseReason from './components/refuse-reason';
import RefuseReasonSXT from './components/refuse-reason-sxt';
import { ChannelType } from './constants';

const VIRTUAL_TICKET = OrderGoodsType.VirtualTicket;

const RefundInfoDefaultProps = {
  addressList: [] as AddressList[],
  allValues: {}
};

type ComponentCodeMap = Record<string, React.ReactNode>;

type IRefundInfoProps = typeof RefundInfoDefaultProps & {
  options: Options;
  code: 750 | 600;
  fetchRefundAddress: IOnAddressChange;
  pagination: IPagination;
  dialogId: string;
  initialized: boolean;
  initialize: (params: object) => void;
  allValues: {
    addressId: number;
  };
};

/**
 * 退款信息
 * @class RefundInfo
 * @extends {React.Component}
 */
@createForm({
  form: 'refund-form'
})
// 这里其实应该写成 此 Form 的所有 Field name 的 Record<typeof NameOfThisForm, any>
// 但是太麻烦了, 而且没什么实质性影响，就写成 any 吧
@connect((state: any) => ({
  allValues: getFormValues('refund-form')(state)
}))
class RefundInfo extends React.Component<IRefundInfoProps> {
  static defaultProps = RefundInfoDefaultProps;

  state = {
    /** 控制同意退款后, 是否自动跳转到退款维权列表页面 */
    autoGotoList: true
  };

  componentDidMount() {
    this.initDefaultAddress();
  }

  componentDidUpdate() {
    this.initDefaultAddress();
  }

  handleAutoGotoList = ({ target }: ICheckboxEvent<false>) => {
    this.setState({ autoGotoList: target.checked });
  };

  goToList = () => {
    if (isSingleStore) {
      window.location.href = setUrlDomain('/v2/order/returnorder', 'store');
    } else {
      window.location.href = setUrlDomain('/v4/trade/refunds', 'store');
    }
  };

  initDefaultAddress = () => {
    const { addressList, allValues, initialized, initialize } = this.props;

    if (addressList.length > 0 && !initialized && !allValues.addressId) {
      let defaultAddress = find<AddressList[]>(addressList, { isDefault: true }) as AddressList;
      // 没有默认地址，选择数据第一个地址，连锁可以翻页不能这么做
      if (!defaultAddress && isRetailSingleStore) {
        [defaultAddress] = addressList;
      }
      defaultAddress && initialize({ addressId: defaultAddress.id });
    }
  };

  renderExtraInfo = (param: Record<string, any>) => {
    const { code, options, addressList, pagination, fetchRefundAddress } = this.props;
    const { isOnlineShipping } = param;
    const codeExtraInfoMap: ComponentCodeMap = {
      [AGREE_RETURN_GOODS]: [
        <RefundAddress
          key="refund-address"
          addressList={addressList}
          onChange={fetchRefundAddress}
          isOnlineShipping={isOnlineShipping}
        />,
        <RefundAddressPaginator
          key="refund-pag"
          pagination={pagination}
          onChange={fetchRefundAddress}
        />
      ],
      [REFUSE_REFUND_APPLY]: [
        options.simpleOrderInfo.channelType === ChannelType.TiktokSxt ? (
          <RefuseReasonSXT key="refund-reason" simpleOrderInfo={options.simpleOrderInfo} />
        ) : (
          <RefuseReason key="refund-reason" />
        )
      ],
      [REFUSE_RECEIVE_GOODS]: [
        <RefundExpress key="refund-express" simpleOrderInfo={options.simpleOrderInfo} />,
        <RefuseReason key="refund-reason" />
      ]
    };

    return codeExtraInfoMap[code] || null;
  };

  renderExtraInfoAhead() {
    const { code, options } = this.props;
    const isVirtual = get(options, 'refundOrderItemInfos[0].goodsType', null) === VIRTUAL_TICKET;
    const { agreeRefundVirtualTicketDesc } = options;
    const codeExtraInfoMap: ComponentCodeMap = {
      [AGREE_BUYER_REFUND]: [
        isVirtual ? <span style={{ color: 'gray' }}>{agreeRefundVirtualTicketDesc}</span> : null
      ]
    };

    return codeExtraInfoMap[code] || null;
  }

  render() {
    const { allValues, options, fetchRefundAddress } = this.props;
    const { autoGotoList } = this.state;
    const {
      refundOrderInfo,
      refundOrderItemInfos,
      returnAddressSelectSource,
      isQttNotFenxiaoOrder
    } = options;

    /** 判断是否为铺货模式 */
    const isOnlineShipping =
      returnAddressSelectSource === ReturnAddressSelectSource.AddressList &&
      (isUnifiedShop || isRetailMinimalistShop);

    return (
      <div>
        <div className="refund-info">
          <Form>
            {this.renderExtraInfoAhead()}
            <BaseRefundInfo
              refundOrderInfo={refundOrderInfo}
              refundOrderItemInfos={refundOrderItemInfos}
            />
            {this.renderExtraInfo({ isOnlineShipping })}
          </Form>
          {allValues.addressId === -1 && (
            <div className="new-address-form">
              <NewLocation
                submitText="保存"
                className="default-address-form"
                callback={fetchRefundAddress}
              />
            </div>
          )}
        </div>
        {/*
          TODO: 此功能不满足产品预期(后端难实现), 产品说暂时隐藏
        <Checkbox
          className="auto-go-to-list-checkbox"
          onChange={this.handleAutoGotoList}
          checked={autoGotoList}
        >
          操作完成后自动跳转至下一笔待处理退款维权申请
        </Checkbox> */}
        {/* eslint-disable-next-line react/jsx-props-no-spreading */}
        {isQttNotFenxiaoOrder ? (
          <ActionQtt
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...this.props}
            isOnlineShipping={isOnlineShipping}
            onSuccess={autoGotoList ? this.goToList : null}
          />
        ) : (
          <Action
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...this.props}
            isOnlineShipping={isOnlineShipping}
            onSuccess={autoGotoList ? this.goToList : null}
          />
        )}
      </div>
    );
  }
}

export default RefundInfo;
