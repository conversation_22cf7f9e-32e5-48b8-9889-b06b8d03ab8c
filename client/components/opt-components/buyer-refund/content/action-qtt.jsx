// 群团团操作（内部调用不同的接口，处理特殊case），为了剥离操作，copy了一份组件给群团团使用

import React from 'react';
import { Button, Notify, Dialog } from 'zent';
import { get, find, isFunction } from 'lodash';
import { connect, getFormValues } from '@youzan/retail-form';
import { isPartnerStore } from '@youzan/utils-shop';
import { openModal } from 'components/modals';
import isFxStore from 'common/is-fx-store';
import {
  AGREE_BUYER_REFUND,
  AGREE_RETURN_GOODS,
  CONFIRM_GOODS_REFUND,
  REFUSE_REFUND_APPLY,
  REFUSE_RECEIVE_GOODS
} from 'components/opt-components/constant';
import {
  openPrepaidRefundDialog,
  PrepaidRefundCode
} from 'components/opt-components/prepaid-refund-dialog';
import { track } from '@youzan/retail-utils';
import * as api from '../api';
import { CommissionStatusType, QttNotFxRefundTip } from './qtt-tips';

// code对应的操作以及附加信息
const CODE_ACTION_MAP = {
  // 同意退款
  [AGREE_BUYER_REFUND]: {
    // 群团团前置试算请求
    calcAction: api.checkQttNotFxRefundCommisson,
    getClacParamsFn: ({ options }) => {
      return {
        orderNo: get(options, 'refundOrderInfo.orderNo'),
        refundId: get(options, 'refundOrderInfo.refundId')
      };
    },

    // 群团团前置请求 refundCommission.json
    frontAction: api.qttNotFxRefundCommission,
    getFrontParamsFn: ({ options, qttNotFx }) => {
      const { commissionStatus, promoteFee = 0, inviteFee = 0, privateFee = 0 } = qttNotFx;
      const refundCommission = [
        CommissionStatusType.NOT_NEED_REFUND, // 无需退佣金（注：后端需要处理这种case）
        CommissionStatusType.CAN_REFUND // 可以退佣金
      ].includes(commissionStatus);

      return {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo,
        refundCommission,
        promoteFee,
        inviteFee,
        privateFee
      };
    },

    // 同意仅退款申请  accept.json
    action: api.acceptRefund,
    getParamsFn: ({ options }) => {
      return {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo
      };
    },
    buttonText: '同意退款'
  },
  // 同意退货退款申请
  [AGREE_RETURN_GOODS]: {
    // 同意退货退款申请 accept.json
    action: api.acceptRefund,
    getParamsFn: ({ options = {}, allValues = {}, addressList = [] }) => {
      const { addressId } = allValues;
      const targetAddress = find(addressList, { id: addressId });
      if (!targetAddress) {
        Notify.error('请选择退货地址');
        return;
      }
      const {
        id,
        name,
        mobile,
        paramsAddress: address,
        city,
        county,
        province,
        areaCode,
        telephone
      } = targetAddress;

      const query = {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo,
        name,
        mobile,
        address,
        areaCode,
        telephone
      };

      id && (query.warehouseId = id);
      province && (query.province = province);
      city && (query.city = city);
      county && (query.region = county);
      county && (query.county = county);

      return query;
    },
    buttonText: '同意并发送退货地址'
  },
  // 确认收货
  [CONFIRM_GOODS_REFUND]: {
    // 群团团前置试算请求
    calcAction: api.checkQttNotFxRefundCommisson,
    getClacParamsFn: ({ options }) => {
      return {
        orderNo: get(options, 'refundOrderInfo.orderNo'),
        refundId: get(options, 'refundOrderInfo.refundId')
      };
    },

    // 群团团前置请求 refundCommission.json
    frontAction: api.qttNotFxRefundCommission,
    getFrontParamsFn: ({ options, qttNotFx }) => {
      const { commissionStatus, promoteFee = 0, inviteFee = 0, privateFee = 0 } = qttNotFx;
      const refundCommission = [
        CommissionStatusType.NOT_NEED_REFUND, // 无需退佣金（注：后端需要处理这种case）
        CommissionStatusType.CAN_REFUND // 可以退佣金
      ].includes(commissionStatus);

      return {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo,
        refundCommission,
        promoteFee,
        inviteFee,
        privateFee
      };
    },

    // 确认收货 并退款 sign.json
    action: api.sign,
    getParamsFn: () => ({ is_assumed_fenxiao_fee: 0 }),
    buttonText: '确认收货并退款'
  },
  // 拒绝退款申请
  [REFUSE_REFUND_APPLY]: {
    // 群团团前置请求 refuseCheck.json
    frontAction: api.qttNotFxRefuseCheck,
    getFrontParamsFn: ({ options }) => {
      return {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo
      };
    },
    action: api.rejectRefund, // 拒绝退款申请 reject.json
    getParamsFn: ({ allValues = {}, options }) => {
      return {
        remark: allValues.rejectReason,
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo
      };
    },
    buttonText: '确定拒绝'
  },
  // 拒绝确认收货
  [REFUSE_RECEIVE_GOODS]: {
    // 群团团前置请求 refuseCheck.json
    frontAction: api.qttNotFxRefuseCheck,
    getFrontParamsFn: ({ options }) => {
      return {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo
      };
    },
    action: api.unsign, // 拒绝确认收货  unsign.json
    getParamsFn: ({ allValues = {} }) => ({
      reject_reason: allValues.rejectReason
    }),
    buttonText: '确定拒绝'
  }
};

class Action extends React.Component {
  calcFlag = false;

  state = {
    loading: false
  };

  /**
   * 弹窗确认
   * @param {*} time 同意退款、确认收货并退款时，first表示第一次手动换起，非first表示自动换起操作
   * @param {*} qttNotFxData 群团团参数
   * @returns
   */
  handleClick = async (time, qttNotFxData) => {
    let qttNotFxParams = qttNotFxData;
    const { code, ...otherProps } = this.props;
    const codeAction = CODE_ACTION_MAP[code];
    const baseParams = {
      // safeguard_id:,
      safe_no: get(otherProps, 'options.refundOrderInfo.refundId')
    };

    if (time === 'first') {
      this.setState({ loading: true });
    }

    // 群团团试算请求
    if (codeAction.calcAction && !this.calcFlag) {
      const calcParams = codeAction.getClacParamsFn(this.props);
      try {
        const res = await codeAction.calcAction(calcParams);
        this.calcFlag = true;
        qttNotFxParams = res; // 取最新数据
        const { commissionStatus } = res;
        if (commissionStatus === CommissionStatusType.NOT_ENOUGH) {
          this.agreeSecondTipDialog(qttNotFxParams, calcParams);
          this.props?.onCancel?.();
          if (time === 'first') {
            this.setState({ loading: false });
          }
          return;
        }
      } catch (err) {
        const errMsg = err.msg || err;
        Notify.error(errMsg || '操作失败');
        if (time === 'first') {
          this.setState({ loading: false });
        }
        return;
      }
    }

    // 群团团前置请求
    if (codeAction.frontAction) {
      // 前置请求参数
      const frontExtraParams = codeAction.getFrontParamsFn({
        ...otherProps,
        qttNotFx: qttNotFxParams
      });

      try {
        const res = await codeAction.frontAction(frontExtraParams);
        if ([AGREE_BUYER_REFUND, CONFIRM_GOODS_REFUND].includes(code)) {
          // 同意退款、同意退货退款
          const { success, checkRefundCommissionFail, commissionCheckResult } = res;
          qttNotFxParams = commissionCheckResult; // 取最新数据
          if (!success && checkRefundCommissionFail) {
            this.agreeSecondTipDialog(qttNotFxParams, frontExtraParams);
            this.props?.onCancel?.();
            if (time === 'first') {
              this.setState({ loading: false });
            }
            return;
          }
        }
      } catch (err) {
        const errMsg = err.msg || err;
        Notify.error(errMsg || '操作失败');
        if (time === 'first') {
          this.setState({ loading: false });
        }
        return;
      }
    }

    // 零售正常请求参数
    const extraParams = codeAction.getParamsFn({ ...otherProps, ...qttNotFxParams });
    if (!extraParams) {
      return;
    }

    // 正常请求
    codeAction
      .action({
        ...baseParams,
        ...extraParams
      })
      .then(() => {
        isFunction(otherProps.options.reload) && otherProps.options.reload();
        // 确认退货之后打开退货入库弹窗，其余提示操作成功
        if (
          code === CONFIRM_GOODS_REFUND &&
          !isFxStore &&
          !isPartnerStore &&
          this.props?.options?.needHandleReturnStockIn
        ) {
          openModal('stock', {
            refundId: get(otherProps, 'options.refundOrderInfo.refundId'),
            hasSupply: get(otherProps, 'options.hasSupply')
          });
        } else {
          Notify.success('操作成功');
          this.props.onSuccess?.();
        }
        Dialog.closeDialog(otherProps.dialogId);
      })
      .catch(err => {
        /** 适配 iron 接口 */
        if (
          err.code === PrepaidRefundCode.IronFrozen ||
          err.code === PrepaidRefundCode.IronCheckOut
        ) {
          const prepaidRefundCodeMap = new Map([
            [PrepaidRefundCode.IronFrozen, PrepaidRefundCode.Frozen],
            [PrepaidRefundCode.IronCheckOut, PrepaidRefundCode.CheckOut]
          ]);
          openPrepaidRefundDialog(
            {
              code: prepaidRefundCodeMap.get(err.code),
              /**
               * iron 应用 error.msg
               * QA 环境：Exception[20017] /data/project/iron/xxx [ line 68 ] : 该订单中使用..
               * PROD 环境：该订单中使用..
               */
              msg: err.msg.split(':')?.[1]?.trim() ?? err.msg ?? '操作失败'
            },
            {
              scene: '确认收到买家退货'
            }
          );
          return;
        }
        if (err.code === PrepaidRefundCode.Frozen || err.code === PrepaidRefundCode.CheckOut) {
          openPrepaidRefundDialog(err, { scene: '同意买家仅退款' });
          return;
        }
        const errMsg = err.msg || err;
        Notify.error(errMsg || '操作失败');
      })
      .finally(() => {
        if (time === 'first') {
          this.setState({ loading: false });
        }
      });
  };

  // 同意退款、确认收货并退款 操作二次弹窗提示
  agreeSecondTipDialog = (qttNotFxData, { orderNo, refundId }) => {
    const dialogId = `agree-second-tip-dialog_${Date.now()}`;

    const confirm = () => {
      track({
        et: 'click', // 事件类型
        ei: 'qtt_dialog_confirm_refund', // 事件标识
        en: '同意佣金不足，弹窗确认退款', // 事件名称
        pt: 'refundDetail', // 页面类型
        params: {
          kdtId: _global.kdtId,
          orderNo,
          refundId
        } // 事件参数
      });
      Dialog.closeDialog(dialogId);
      this.handleClick('second', qttNotFxData);
    };

    Dialog.openDialog({
      dialogId,
      title: '提示',
      children: (
        <>
          <p className="dialog-text">团长佣金无法退回，是否继续退款？</p>
          <QttNotFxRefundTip qttNotFx={qttNotFxData} />
        </>
      ),
      footer: (
        <>
          <Button onClick={() => Dialog.closeDialog(dialogId)}>暂不退款</Button>
          <Button type="primary" onClick={() => confirm()}>
            继续退款
          </Button>
        </>
      ),
      style: { width: '704px' }
    });
  };

  render() {
    const { code, handleSubmit, qttNotFx } = this.props;
    const codeAction = CODE_ACTION_MAP[code];

    if (!codeAction) {
      return null;
    }

    return (
      <div className="refund-action">
        <Button outline onClick={this.props.onCancel}>
          取消
        </Button>
        <Button
          onClick={handleSubmit(() => this.handleClick('first', qttNotFx))}
          type="primary"
          loading={this.state.loading}
        >
          {codeAction.buttonText}
        </Button>
      </div>
    );
  }
}

export default connect(state => ({
  allValues: getFormValues('refund-form')(state)
}))(Action);
