import React from 'react';
import { Alert } from 'zent';
import { isArray, get } from 'lodash';
import { BlankLink } from '@youzan/react-components';
import { QttNotFxRefundTip, CommissionStatusType } from './qtt-tips';

import {
  AGREE_BUYER_REFUND,
  AGREE_RETURN_GOODS,
  CONFIRM_GOODS_REFUND,
  REFUSE_REFUND_APPLY,
  REFUSE_RECEIVE_GOODS
} from '../../constant';

const agreeCodes = [AGREE_BUYER_REFUND, AGREE_RETURN_GOODS];
const confirmCodes = [CONFIRM_GOODS_REFUND];
const refuseCodes = [REFUSE_REFUND_APPLY, REFUSE_RECEIVE_GOODS];

// 拒绝（拒绝退款申请、拒绝收货）文案
const RefuseText = ({ onlyRefundMoney }) => {
  const diffText = onlyRefundMoney ? '退款申请后再次发起退款' : '退货物流后继续申请退款';
  return (
    <span>
      建议你与买家协商后，再确定是否拒绝退款。如你拒绝退款，买家可修改{diffText}
      。买家也可申请客服介入，有赞客满将会跟进协商处理。
    </span>
  );
};

// 同意（同意买家退款、同意退货）文案
const AgreeTip = ({ buyWayDesc, diffText, code, qttNotFx }) => (
  <>
    <div>
      该笔订单通过 “<span className="orange">{buyWayDesc}</span>” 付款，{diffText}
    </div>
    {code === AGREE_BUYER_REFUND ? <QttNotFxRefundTip qttNotFx={qttNotFx} /> : <></>}
  </>
);

// 确认（确认收货并退款）文案
const ConfirmTip = ({ qttNotFx }) => (
  <>
    <QttNotFxRefundTip qttNotFx={qttNotFx} />
  </>
);

// 同意的map
const agreeTextMap = {
  [AGREE_BUYER_REFUND]: [
    <span>
      你同意退款后，需要你自行到
      <BlankLink href="http://pay.weixin.qq.com" className="link">
        微信支付商户平台
      </BlankLink>
      操作退款。
    </span>,
    '你同意退款申请后，退款将自动原路退回至买家付款账户。'
  ],
  [AGREE_RETURN_GOODS]: [
    <span>
      你同意退款并且收到买家退货后，需要你自行到
      <BlankLink href="http://pay.weixin.qq.com" className="link">
        微信支付商户平台
      </BlankLink>
      操作退款。
    </span>,
    '需你同意退款申请后，买家才能退货给你；买家退货后你需再次确认收货后，退款将自动原路退回至买家付款账户。'
  ],
  [CONFIRM_GOODS_REFUND]: '你确认收货后，退款将自动原路退回至买家付款账户。'
};

// 拒绝的map
const refuseTextMap = {
  [REFUSE_REFUND_APPLY]: <RefuseText onlyRefundMoney />,
  [REFUSE_RECEIVE_GOODS]: <RefuseText />
};

/**
 * 这个就是一个显示文案，做法是根据code做映射，同时为了更多地复用相同的文案，才有了一些处理逻辑
 * @param {any} { code, options }
 * @returns
 */
const Tips = ({ code, options, qttNotFx }) => {
  const payWayCode = get(options, 'refundOrderInfo.payWayCode');
  const payWayDesc = get(options, 'refundOrderInfo.payWay');

  // @小弦说 只有微信自有支付需要走微信商户平台，自他的都是原路退回
  // payWayCode === 1 (微信自有支付)的支付类型  是@金明 给的
  const needSelfRefund = +payWayCode === 1;

  const defaultBuyWay = needSelfRefund ? '微信安全支付-自有' : '微信安全支付-代销';

  // 同意的code处理
  if (agreeCodes.indexOf(code) > -1) {
    const agreeText = agreeTextMap[code];
    let agreeDiffText = agreeText;

    if (isArray(agreeText)) {
      agreeDiffText = needSelfRefund ? agreeText[0] : agreeText[1];
    }

    return (
      <Alert type="warning">
        <AgreeTip
          diffText={agreeDiffText}
          buyWayDesc={payWayDesc || defaultBuyWay}
          qttNotFx={qttNotFx}
          code={code}
        />
      </Alert>
    );
  }

  // 确认的code处理
  if (confirmCodes.indexOf(code) > -1) {
    if (qttNotFx?.commissionStatus === CommissionStatusType.NOT_ENOUGH)
      return (
        <Alert type="warning">
          <ConfirmTip qttNotFx={qttNotFx} />
        </Alert>
      );
    return <></>;
  }

  // 拒绝的code处理
  if (refuseCodes.indexOf(code) > -1) {
    return <Alert type="warning">{refuseTextMap[code]}</Alert>;
  }

  return null;
};

export default Tips;
