@import '~shared/style';

.retail-form__error-desc {
  color: $color-alert;
}

.auto-go-to-list-checkbox.zent-checkbox-wrap {
  margin-left: 15px;
}

:local(.refund-content) {
  .zent-alert {
    line-height: 20px;
  }

  .zent-textarea-wrapper.zent-input-wrapper {
    width: auto;
  }

  .refund-info-item {
    display: flex;
    padding-top: 16px;

    &__wrapper-auto-height {
      height: auto;
    }

    &__label {
      margin-right: 5px;
      width: 70px;
    }
  }

  .retail-form__control-group {
    padding-top: 16px;
    display: flex;

    &-label {
      flex: 0 0 70px;
      width: 70px;
      line-height: 1;
      text-align: left;
    }

    .retail-form__wrapper {
      align-items: flex-start;
    }
  }

  .refund-address {
    .retail-form__control-group {
      display: flex;
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 10px;
      }
    }

    .retail-form__controls {
      flex: 1;

      .address-search-area {
        display: flex;
        align-items: baseline;

        .zent-input-wrapper {
          width: 220px;
          margin: 0 16px 8px 0;
        }
      }
    }

    .address-group .zent-radio-group {
      .address-select-area {
        width: 100%;
        max-height: 130px;
        overflow-y: scroll;
        padding: 15px 0 0;
        background-color: $background-color-base;
      }
    }

    .address-group.chain-address .zent-radio-group {
      max-height: none;
      overflow-y: auto;
    }

    .address-tips {
      font-size: $font-size-small;
      color: $color-text-secondary;
    }
  }

  .single-address {
    &__item {
      display: flex;
      padding: 0 0 15px 25px;
      font-size: $font-size-base;

      .zent-radio {
        line-height: 16px;
      }
    }

    &__new-address {
      padding: 10px 0 0 105px;

      &.retail-form__control-group {
        padding-top: 0;
      }
    }
  }

  .new-address-form {
    padding-left: 60px;

    .zent-select {
      vertical-align: top;
    }
  }

  .refund-action {
    display: flex;
    justify-content: flex-end;
  }
}
