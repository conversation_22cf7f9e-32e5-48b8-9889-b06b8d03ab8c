import React from 'react';

// 退款单状态
export const CommissionStatusType = {
  // 无需退佣金
  NOT_NEED_REFUND: 1,
  // 余额不足
  NOT_ENOUGH: 2,
  // 退款单已退过佣金
  REFUNDED: 3,
  // 可以退佣金
  CAN_REFUND: 4
};

// 获取群团团非分销订单提示语组件
export function QttNotFxRefundTip({ qttNotFx }) {
  if (!qttNotFx) {
    return <></>;
  }

  const {
    commissionStatus,
    privateFee = 0,
    promoteFee = 0,
    inviteFee = 0,
    promoterName,
    inviterName,
    promoterNoMoney,
    inviterNoMoney
  } = qttNotFx;

  let qttNameStr = '';
  if (promoterNoMoney && inviterNoMoney)
    qttNameStr = `帮卖团长${promoterName}和邀请团长${inviterName}`;
  else if (promoterNoMoney) qttNameStr = `帮卖团长${promoterName}`;
  else qttNameStr = `邀请团长${inviterName}`;

  // eslint-disable-next-line @youzan/yz-retail/no-direct-arithmetic
  const qttTotalFee = parseFloat(((promoteFee + privateFee + inviteFee) / 100).toFixed(2));

  if (commissionStatus === CommissionStatusType.NOT_ENOUGH) {
    return (
      <p>
        本次退款中{qttNameStr}
        账户余额不足，佣金暂时无法退回，本单佣金<span className="yellow">{qttTotalFee}</span>
        暂时无法退回，你可以选择暂不退款，或继续退款。若选择继续退款，系统将在帮卖团长余额足够后，为你扣回佣金。
      </p>
    );
  }
  return <></>;
}
