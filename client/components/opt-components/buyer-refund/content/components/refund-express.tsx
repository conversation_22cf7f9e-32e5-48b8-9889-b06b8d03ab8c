import * as React from 'react';
import { get } from 'lodash';

import RefundInfoItem from './refund-info-item';
import { SimpleOrderInfo } from '../interface';

interface IRefundExpress {
  simpleOrderInfo: SimpleOrderInfo;
}

// 退款物流
const RefundExpress = ({ simpleOrderInfo }: IRefundExpress) => {
  const expressName = get(simpleOrderInfo, 'express.expressName', '');
  const expressNo = get(simpleOrderInfo, 'express.expressNo', '');

  const displayInfo = `${expressName}  ${expressNo}`;
  return <RefundInfoItem label="退货物流" value={displayInfo} />;
};

export default RefundExpress;
