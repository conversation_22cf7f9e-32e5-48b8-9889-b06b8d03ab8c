import * as React from 'react';

interface IRefundInfoItem {
  prefix?: string;
  label: string;
  value?: React.ReactNode;
}

/**
 * item 组件, 包含样式
 */
const RefundInfoItem = ({ prefix = 'refund-info-item', label, value }: IRefundInfoItem) => (
  <div className={prefix}>
    <div className={`${prefix}__label`}>{label}：</div>
    <div className={`${prefix}__value`}>{value}</div>
  </div>
);

export default RefundInfoItem;
