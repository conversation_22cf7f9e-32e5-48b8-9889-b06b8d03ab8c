import * as React from 'react';
import cx from 'classnames';
import {
  isRetailSingleStore,
  isRetailMinimalistShop,
  isUnifiedShop,
  isPartnerStore
} from '@youzan/utils-shop';
import { debounce } from 'lodash';
import { Radio, Input } from 'zent';
import { Field } from '@youzan/retail-form';
import { LinkButton } from '@youzan/retail-components';
import { setUrlDomain } from '@youzan/retail-utils';

import { AddressList, IOnAddressChange } from '../interface';

const addressManageUrl = setUrlDomain('/setting/store/index#location', 'www');

interface IRefundAddress {
  addressList: AddressList[];
  onChange: IOnAddressChange;
  isOnlineShipping: boolean;
}

// 退货地址
const RefundAddress = ({ addressList, onChange, isOnlineShipping }: IRefundAddress) => {
  let placeholder;

  if (isOnlineShipping) {
    placeholder = '请输入地址';
  } else {
    placeholder = '请输入仓库/门店';
  }

  let debounceFunction: Function;
  const handleInputChange: React.ChangeEventHandler<HTMLInputElement> = e => {
    e.persist();
    if (!debounceFunction) {
      debounceFunction = debounce(({ target: { value } }) => {
        onChange({ keyword: value });
      }, 200);
    }
    debounceFunction(e);
  };

  return (
    <div className="refund-address">
      <Field
        label="退货地址："
        name="addressId"
        component="RadioGroupField"
        className={cx('address-group', { 'chain-address': !isRetailSingleStore })}
        autoHeight
      >
        <div className="address-search-area">
          <Input icon="search" placeholder={placeholder} onChange={handleInputChange} />
          {isOnlineShipping &&
            (!isPartnerStore ? (
              <LinkButton target="_blank" href={addressManageUrl}>
                管理地址
              </LinkButton>
            ) : (
              <p className="address-tips">退货地址维护，请前往对应网店的商家地址库进行维护</p>
            ))}
        </div>
        <div className="address-select-area">
          {addressList.map(addressInfo => {
            const { id, isDefault, warehouseName, mobile, name, paramsAddress, address } =
              addressInfo;
            let addressCombined: string;

            // 单店 || 铺货网店:【xxx 收】地址 手机号
            if (isRetailSingleStore || isOnlineShipping) {
              addressCombined = `【${name} 收】 ${paramsAddress} ${mobile} `;
            }

            // 供货网店: 【仓库】 地址 收件人 手机号
            if (!isOnlineShipping && (isRetailMinimalistShop || isUnifiedShop)) {
              addressCombined = `【${warehouseName}】 ${address} ${name} ${mobile} `;
            }

            return (
              <Radio value={id} className="single-address__item" key={id}>
                {addressCombined}
                {isDefault && isRetailSingleStore && <span className="orange">默认地址</span>}
              </Radio>
            );
          })}
        </div>
      </Field>
      {isRetailSingleStore && (
        <Field
          name="addressId"
          component="RadioGroupField"
          className="single-address__new-address"
          autoHeight
        >
          <Radio value={-1}>使用新地址</Radio>
        </Field>
      )}
    </div>
  );
};

export default RefundAddress;
