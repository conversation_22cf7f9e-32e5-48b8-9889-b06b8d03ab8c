import * as React from 'react';
import { Field } from '@youzan/retail-form';
import { geSxtRefuseReasonData } from '../../api';

interface IProps {
  simpleOrderInfo: {
    orderNo: string;
  };
}

// 随心团拒绝原因
const RefuseReason = ({ simpleOrderInfo }: IProps) => {
  const [refuseReasonData, setRefuseReasonData] = React.useState([]);
  React.useEffect(() => {
    geSxtRefuseReasonData({
      orderNo: simpleOrderInfo.orderNo,
      operationMethod: 6
    }).then(res => {
      setRefuseReasonData(
        (res.reasonModelList || []).map(({ reason }: any) => ({ text: reason, value: reason }))
      );
    });
  }, [simpleOrderInfo]);

  return (
    <Field
      label="拒绝原因："
      name="rejectReason"
      component="SelectField"
      data={refuseReasonData}
      props={{
        placeholder: '请选择拒绝原因'
      }}
      style={{ minWidth: 300 }}
      validate={[{ type: 'required' }]}
      autoHeight
    />
  );
};

export default RefuseReason;
