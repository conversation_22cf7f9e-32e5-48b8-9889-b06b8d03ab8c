import * as React from 'react';
import { plus } from '@youzan/retail-utils';
import { refundDemandEnum } from 'route/refunddetail/const';
import { convertFenToYen } from 'common/fns/format';
import { divGoodsNum } from 'common/helper';

import RefundInfoItem from './refund-info-item';
import { RefundOrderInfo, RefundOrderItemInfo } from '../interface';

// 基本退款信息
interface IBaseRefundInfo {
  refundOrderInfo: RefundOrderInfo;
  refundOrderItemInfos: RefundOrderItemInfo[];
}

const BasicRefundInfo = ({
  refundOrderInfo: { demandDesc, refundFee, fenXiaoRefundFee = '', demand },
  refundOrderItemInfos
}: IBaseRefundInfo) => {
  let showRefundNum = false;
  let refundNumber;
  if (demand === refundDemandEnum.REFUND_RETURN_GOODS) {
    showRefundNum = true;
    const refundNum = refundOrderItemInfos.reduce(
      (total, { refundNum: refundNumItem }) => plus(total, refundNumItem),
      0
    );
    refundNumber = Math.floor(divGoodsNum(refundNum)) || '-';
  }
  // 分销订单有显示文案
  const fenXiaoDesc = fenXiaoRefundFee
    ? `(买家申请退款${convertFenToYen(
        plus(+refundFee, +fenXiaoRefundFee)
      )}元，其中供货商承担${convertFenToYen(refundFee)}元，分销商承担${convertFenToYen(
        +fenXiaoRefundFee
      )}元)`
    : '';
  return (
    <div>
      <RefundInfoItem label="退款方式" value={demandDesc} />
      {showRefundNum && <RefundInfoItem label="退货数量" value={refundNumber} />}
      <RefundInfoItem
        label="退款金额"
        value={
          <span>
            <span className="orange">￥{convertFenToYen(refundFee)}</span>
            {fenXiaoDesc}
          </span>
        }
      />
    </div>
  );
};

BasicRefundInfo.defaultProps = {
  refundOrderInfo: {}
};

export default BasicRefundInfo;
