import * as React from 'react';
import { Pagination } from 'zent';
import { IPagination, IOnAddressChange } from '../interface';

interface IRefundAddressPaginator {
  pagination: IPagination;
  onChange: IOnAddressChange;
}

const RefundAddressPaginator = ({ pagination, onChange }: IRefundAddressPaginator) => {
  const { total, pageSize, current } = pagination ?? {};
  const needPag = total > pageSize;
  return needPag ? (
    <Pagination onChange={onChange} total={total} pageSize={pageSize} current={current} />
  ) : null;
};

export default RefundAddressPaginator;
