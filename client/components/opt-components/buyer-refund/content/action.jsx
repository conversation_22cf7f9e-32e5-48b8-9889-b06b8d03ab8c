import React from 'react';
import { But<PERSON>, Notify, Dialog } from 'zent';
import { get, find, isFunction } from 'lodash';
import { connect, getFormValues } from '@youzan/retail-form';
import { isPartnerStore } from '@youzan/utils-shop';
import { openModal } from 'components/modals';
import isFxStore from 'common/is-fx-store';
import {
  AGREE_BUYER_REFUND,
  AGREE_RETURN_GOODS,
  CONFIRM_GOODS_REFUND,
  REFUSE_REFUND_APPLY,
  REFUSE_RECEIVE_GOODS
} from 'components/opt-components/constant';
import {
  openPrepaidRefundDialog,
  PrepaidRefundCode
} from 'components/opt-components/prepaid-refund-dialog';
import openWeixinRefundAuthDialog from 'components/opt-components/weixin-refund-auth-dialog';
import { ChannelType } from './constants';

import * as api from '../api';

// code对应的操作以及附加信息
const CODE_ACTION_MAP = {
  [AGREE_BUYER_REFUND]: {
    action: api.acceptRefund, // 同意仅退款申请  accept.json
    getParamsFn: ({ options }) => {
      return {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo,
        wechatComplaintCheck: 0
      };
    },
    buttonText: '同意退款'
  },
  [AGREE_RETURN_GOODS]: {
    action: api.acceptRefund, // 同意退货退款申请 accept.json
    getParamsFn: ({ options = {}, allValues = {}, addressList = [] }) => {
      const { addressId } = allValues;
      const targetAddress = find(addressList, { id: addressId });
      if (!targetAddress) {
        Notify.error('请选择退货地址');
        return;
      }
      const {
        id,
        name,
        mobile,
        paramsAddress: address,
        city,
        county,
        province,
        areaCode,
        telephone,
        outAddressId
      } = targetAddress;

      const query = {
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo,
        name,
        mobile,
        address,
        areaCode,
        telephone,
        outAddressId,
        wechatComplaintCheck: 0
      };

      id && (query.warehouseId = id);
      province && (query.province = province);
      city && (query.city = city);
      county && (query.region = county);
      county && (query.county = county);

      return query;
    },
    buttonText: '同意并发送退货地址'
  },
  [CONFIRM_GOODS_REFUND]: {
    action: api.sign, // 确认收货 并退款 sign.json
    getParamsFn: () => ({ is_assumed_fenxiao_fee: 0 }),
    buttonText: '确认收货并退款'
  },
  [REFUSE_REFUND_APPLY]: {
    action: api.rejectRefund, // 拒绝退款申请 reject.json
    getParamsFn: ({ allValues = {}, options }) => {
      const baseParams = {
        remark: allValues.rejectReason,
        refundId: options.refundOrderInfo.refundId,
        orderNo: options.refundOrderInfo.orderNo
      };
      if (options.simpleOrderInfo.channelType === ChannelType.TiktokSxt) {
        return {
          ...baseParams,
          rejectReason: allValues.rejectReason
        };
      }
      return baseParams;
    },
    buttonText: '确定拒绝'
  },
  [REFUSE_RECEIVE_GOODS]: {
    action: api.unsign, // 拒绝确认收货  unsign.json
    getParamsFn: ({ allValues = {} }) => ({
      reject_reason: allValues.rejectReason
    }),
    buttonText: '确定拒绝'
  }
};

class Action extends React.Component {
  state = {
    loading: false
  };

  beforeHandleClick = () => {
    const { options = {}, dialogId } = this.props;
    const { hasWechatComplaint } = options.simpleOrderInfo;
    const { demand } = options.refundOrderInfo;

    const closeDialog = () => Dialog.closeDialog(dialogId);
    const refundText = demand === 1 ? '仅退款' : '退货退款';

    if (hasWechatComplaint) {
      openWeixinRefundAuthDialog({
        submit: this.handleClick,
        cancel: closeDialog,
        refundText
      });
    } else {
      this.handleClick();
    }
  };

  handleClick = () => {
    const { code, isOnlineShipping, ...otherProps } = this.props;
    const codeAction = CODE_ACTION_MAP[code];
    const baseParams = {
      // safeguard_id:,
      safe_no: get(otherProps, 'options.refundOrderInfo.refundId')
    };
    const extraParams = codeAction.getParamsFn(otherProps);

    if (!extraParams) {
      return;
    }
    this.setState({ loading: true });
    codeAction
      .action({
        ...baseParams,
        ...extraParams
      })
      .then(() => {
        isFunction(otherProps.options.reload) && otherProps.options.reload();
        // 确认退货之后打开退货入库弹窗，其余提示操作成功
        if (
          code === CONFIRM_GOODS_REFUND &&
          !isFxStore &&
          !isPartnerStore &&
          this.props?.options?.needHandleReturnStockIn
        ) {
          openModal('stock', {
            refundId: get(otherProps, 'options.refundOrderInfo.refundId'),
            hasSupply: get(otherProps, 'options.hasSupply')
          });
        } else {
          Notify.success('操作成功');
          this.props.onSuccess?.();
        }
        Dialog.closeDialog(otherProps.dialogId);
      })
      .catch(err => {
        /** 适配 iron 接口 */
        if (
          err.code === PrepaidRefundCode.IronFrozen ||
          err.code === PrepaidRefundCode.IronCheckOut
        ) {
          const prepaidRefundCodeMap = new Map([
            [PrepaidRefundCode.IronFrozen, PrepaidRefundCode.Frozen],
            [PrepaidRefundCode.IronCheckOut, PrepaidRefundCode.CheckOut]
          ]);
          openPrepaidRefundDialog(
            {
              code: prepaidRefundCodeMap.get(err.code),
              /**
               * iron 应用 error.msg
               * QA 环境：Exception[20017] /data/project/iron/xxx [ line 68 ] : 该订单中使用..
               * PROD 环境：该订单中使用..
               */
              msg: err.msg.split(':')?.[1]?.trim() ?? err.msg ?? '操作失败'
            },
            {
              scene: '确认收到买家退货'
            }
          );
          return;
        }
        if (err.code === PrepaidRefundCode.Frozen || err.code === PrepaidRefundCode.CheckOut) {
          openPrepaidRefundDialog(err, { scene: '同意买家仅退款' });
          return;
        }
        const errMsg = err.msg || err;
        Notify.error(errMsg || '操作失败');
      })
      .finally(() => this.setState({ loading: false }));
  };

  render() {
    const { code, handleSubmit } = this.props;
    const codeAction = CODE_ACTION_MAP[code];

    if (!codeAction) {
      return null;
    }
    return (
      <div className="refund-action">
        <Button outline onClick={this.props.onCancel}>
          取消
        </Button>
        <Button
          onClick={handleSubmit(this.beforeHandleClick)}
          type="primary"
          loading={this.state.loading}
        >
          {codeAction.buttonText}
        </Button>
      </div>
    );
  }
}

export default connect(state => ({
  allValues: getFormValues('refund-form')(state)
}))(Action);
