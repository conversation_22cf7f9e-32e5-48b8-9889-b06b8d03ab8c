import React from 'react';
import { Grid, Button, Alert } from 'zent';
import { isNumber } from 'lodash';
import { div } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import { getCorrectImgUrl } from 'common/helper';
import CenteredImage from 'cpn/centered-image';
import { style } from './style.scss';

export default function DeliveryAttention(props) {
  return (
    <div className={style}>
      <Alert type="warning">
        以下商品“买家下单数量与已发货数量”存在差异，若同意退款申请，退款将原路还给商家，有赞不跟踪已发货部分的退货，请自行联系物流公司拦截物流，或线下与买家协商，进行拒收或退款操作。请自行把控风险，避免损失！
      </Alert>
      <Grid
        className="refund-grid"
        datasets={props.data}
        rowKey="itemId"
        columns={[
          {
            title: '退款商品',
            width: '50%',
            bodyRender(data) {
              const { imgUrl, title, goodsNo, skuDesc, goodsSnapUrl } = data;
              return (
                <div className="goods-item">
                  <CenteredImage src={getCorrectImgUrl(imgUrl)} />
                  <div className="goods-item__info">
                    {goodsSnapUrl ? (
                      <BlankLink href={goodsSnapUrl}>{title}</BlankLink>
                    ) : (
                      <span>{title}</span>
                    )}
                    {skuDesc ? <span>{skuDesc}</span> : null}
                    {goodsNo ? <div className="grey">商家编码：{goodsNo}</div> : null}
                  </div>
                </div>
              );
            }
          },
          {
            title: '退货数量',
            width: '20%',
            bodyRender({ refundNum }) {
              if (isNumber(refundNum) && refundNum > 0) {
                return div(refundNum, 1000);
              }
              return '-';
            }
          },
          {
            title: '发货数量',
            width: '30%',
            bodyRender(data) {
              const { deliveryedNum, num, unit } = data;
              return `已发${div(deliveryedNum, 1000)}${unit}(下单${div(num, 1000)}${unit})`;
            }
          }
        ]}
      />
      <footer className="footer">
        <Button onClick={props.onClose}>取消</Button>
        <Button type="primary" onClick={props.onConfirm}>
          继续退款
        </Button>
      </footer>
    </div>
  );
}
