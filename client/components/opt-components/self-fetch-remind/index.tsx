import * as React from 'react';
import { Pop, Notify } from 'zent';
import type { IOperation } from 'definition/order-info';

import typeMap from '../type-map';
import { SELF_FETCH_REMIND, SELF_FETCH_REMIND_REPEAT } from '../constant';
import { remind } from './api';
import { style } from './index.scss';

interface ISelfFetchRemindProps {
  operation: IOperation;
  options: {
    reload: () => void;
    orderInfo: {
      fulfillOrder: {
        fulfillNo: string;
      };
    };
  };
}

const code2title = {
  [SELF_FETCH_REMIND]: '确定后，会发送消息给买家尽快上门提货，确认发送消息？',
  [SELF_FETCH_REMIND_REPEAT]: '你已发送过该提货消息，确认再次发送提醒？'
};

export default function SelfFetchRemind({ operation, options }: ISelfFetchRemindProps) {
  const { type, text, code } = operation;
  const Opt = typeMap[type];

  const handleConfirm = () => {
    const { reload, orderInfo } = options;
    const { fulfillNo } = orderInfo.fulfillOrder;

    return remind({ fulfillNo })
      .then(() => {
        Notify.success('消息已发送');
        reload();
      })
      .catch(err => Notify.error(err.msg || '消息发送失败'));
  };

  return (
    <Pop
      trigger="click"
      header={code2title[code]}
      content={<span className="pop-tip">说明：只有关注公众号的买家，才能接收到该消息。</span>}
      confirmText="发送"
      onConfirm={handleConfirm}
      className={style}
    >
      <Opt text={text} />
    </Pop>
  );
}
