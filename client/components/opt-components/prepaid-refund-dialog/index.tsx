import React from 'react';
import { Button, Dialog } from 'zent';
import { isBranchStore } from '@youzan/utils-shop';

import { IError } from 'definition/common';

import { trackPrepaidRefundFail, trackPrepaidRefundFailClick } from './track';

const { openDialog, closeDialog } = Dialog;

export enum PrepaidRefundCode {
  /** 已冻结 */
  Frozen = 1020200017,
  /** 已退卡 */
  CheckOut = 1020200018,
  /** 已冻结，iron 接口用 */
  IronFrozen = 20017,
  /** 已退卡，iron 接口用 */
  IronCheckOut = 20018
}

export const openPrepaidRefundDialog = (error: IError, trackData: { scene: string }) => {
  const { msg, code } = error;
  const { scene } = trackData;

  // 该订单支付所用的储值卡已被冻结，需要先解冻再退款。
  if (code === PrepaidRefundCode.Frozen) {
    const dialogId = 'prepaid-refund-frozen';
    openDialog({
      dialogId,
      title: '退款说明',
      style: { width: '500px' },
      footer: (
        <>
          <Button onClick={() => closeDialog(dialogId)}>取消</Button>
          <Button
            type="primary"
            onClick={() => {
              closeDialog(dialogId);
              trackPrepaidRefundFailClick({ scene, reason: '已冻结' });
            }}
            href="/v4/prepaid/query/trades"
            target="_blank"
          >
            去解冻
          </Button>
        </>
      ),
      children: msg
    });
    trackPrepaidRefundFail({ scene, reason: '已冻结' });
    return;
  }
  if (code === PrepaidRefundCode.CheckOut) {
    const dialogId = 'prepaid-refund-checkout';
    openDialog({
      dialogId,
      title: '退款说明',
      style: { width: '500px' },
      // 分店无法处理退卡，引导去总部操作
      footer: isBranchStore ? (
        <Button type="primary" onClick={() => closeDialog(dialogId)}>
          我知道了
        </Button>
      ) : (
        <>
          <Button onClick={() => closeDialog(dialogId)}>取消</Button>
          <Button
            type="primary"
            onClick={() => {
              closeDialog(dialogId);
              trackPrepaidRefundFailClick({ scene, reason: '已退卡' });
            }}
            href="/v4/prepaid/manage/refund/pending?cardType=gift"
            target="_blank"
          >
            去处理
          </Button>
        </>
      ),
      children: msg
    });
    trackPrepaidRefundFail({ scene, reason: '已退卡' });
  }
};
