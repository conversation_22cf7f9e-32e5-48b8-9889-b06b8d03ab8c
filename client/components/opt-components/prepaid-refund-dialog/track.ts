import { track, trackClick, ITrackCommonProps } from '@youzan/retail-utils';

type TrackPropsType = ITrackCommonProps & { et: string };

export const trackPrepaidRefundFail = ({ scene, reason }: { scene: string; reason: string }) => {
  track({
    et: 'custom',
    ei: 'prepaid_refund_fail',
    en: '储值支付退款失败',
    params: {
      scene,
      reason
    }
  } as unknown as TrackPropsType);
};

export const trackPrepaidRefundFailClick = ({
  scene,
  reason
}: {
  scene: string;
  reason: string;
}) => {
  trackClick({
    ei: 'prepaid_refund_fail',
    en: '储值支付退款失败',
    params: {
      scene,
      reason
    }
  } as unknown as TrackPropsType);
};
