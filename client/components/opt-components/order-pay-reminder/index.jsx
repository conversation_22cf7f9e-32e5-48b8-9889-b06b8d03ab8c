import * as React from 'react';
import BeforeAfterActionHoc from 'components/before-after-action';

import { Dialog } from 'zent';
import DialogContext from 'components/details-cpn/pay-dialog/dialog-context';
import { optTypeMap } from '../type-map';

const { openDialog } = Dialog;

class PayDialog extends React.Component {
  open = () => {
    const { orderNo, buyerPhone, salesKdtId } = this.props;
    openDialog({
      parentComponent: this,
      title: '通知用户付款',
      style: {
        width: '560px',
        height: '720px'
      },
      children: <DialogContext orderNo={orderNo} buyerPhone={buyerPhone} salesKdtId={salesKdtId} />
    });
  };

  render() {
    const { operation = {} } = this.props;
    const OptTypeCpm = BeforeAfterActionHoc(optTypeMap[operation.type]);
    const defaultOperation = {
      code: 'order_pay_reminder',
      type: 'button',
      buttonType: 'primary',
      outline: false,
      text: '通知用户付款'
    };
    const operationWithDefault = Object.assign(defaultOperation, operation);

    return (
      <OptTypeCpm
        actionName="OpenDialog"
        {...operationWithDefault}
        onClick={this.open}
        beforeOpenDialog={this.props.beforeOpenDialog}
      />
    );
  }
}

export default PayDialog;
