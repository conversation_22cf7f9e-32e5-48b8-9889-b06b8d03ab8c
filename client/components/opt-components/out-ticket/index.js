import React, { useState, useCallback } from 'react';
import { Notify } from 'zent';
import { Button as SamButton } from '@youzan/sam-components';

import { outTicket } from './api';

export default function OutTicket({ options }) {
  const [loading, setLoading] = useState(false);

  const sendOutTicket = useCallback(() => {
    if (loading) return;

    setLoading(true);

    outTicket(options.orderInfo.mainOrderInfo.orderNo)
      .then(() => {
        options.reload();
      })
      .catch(err => {
        Notify.error(err.msg || '出票失败，请重试');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [options, loading]);

  return <SamButton name="出票" type="primary" loading={loading} onClick={sendOutTicket} />;
}
