import React, { useState, useCallback } from 'react';
import { Notify, Dialog, Button } from 'zent';
import { get } from 'lodash';

import { STORING_DELIVERY, STORE_COMPLETE, STORE_COMPLETE_DELIVERY } from '../constant';
import { optTypeMap } from '../type-map';
import { changeStockState } from './api';

const { openDialog, closeDialog } = Dialog;
const STOCK_DISABLED = 'STOCK_DISABLED';

const completeCode = [STORE_COMPLETE, STORE_COMPLETE_DELIVERY];
const fulfillCode = [STORING_DELIVERY, STORE_COMPLETE_DELIVERY];

const Stock = ({ options, operation }) => {
  const [loading, setLoading] = useState(false);

  const handleStockChange = useCallback(() => {
    const { attributes = {}, code, disabled, text } = operation;
    const { reasonDesc = '功能不可用' } = attributes;

    // 在这里处理 disabled 的情况
    if (disabled) {
      openDialog({
        dialogId: STOCK_DISABLED,
        title: '提示',
        children: <p>{`部分商品处于退款中状态，需商家将退款申请处理完结或买家撤销退款申请后，才可以点击“${text}”。`}</p>,
        footer: <Button onClick={() => closeDialog(STOCK_DISABLED)}>我知道了</Button>
      });
      return;
    }

    const { orderInfo, reload } = options;
    const complete = completeCode.indexOf(code) > -1;
    const isFulfill = fulfillCode.indexOf(code) > -1;

    const data = {
      stockUpStatus: complete ? 30 : 20,
      ...(isFulfill
        ? {
            fulfillNo: get(orderInfo, 'fulfillOrder.fulfillNo')
          }
        : {
            orderNo: get(orderInfo, 'mainOrderInfo.orderNo')
          })
    };

    setLoading(true);
    changeStockState(data, isFulfill)
      .then(() => {
        reload && reload();
      })
      .catch(err => {
        Notify.error(err.msg);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [operation, options]);

  const OptTypeCpm = optTypeMap[operation.type];

  return (
    <OptTypeCpm
      buttonType="primary"
      outline={false}
      {...operation}
      disabled={false}
      loading={loading}
      onClick={handleStockChange}
    />
  );
};

export default Stock;
