/* eslint-disable react/no-danger */
import React, { Component } from 'react';
import { get } from 'lodash';
import { Dialog } from 'zent';
import typeMap from '../type-map';
import style from './style.scss';

export default class CallLocaldeliverCompany extends Component {
  state = {
    visible: false
  };

  click = () => {
    this.setState({ visible: true });
  };

  close = () => {
    this.setState({ visible: false });
  };

  handleContactMessagesList = list => {
    let str = '';
    list.forEach(item => {
      const { desc = '', highlight = '' } = item;
      if (desc !== '' && highlight !== '' && desc.includes(highlight)) {
        str += desc.replace(highlight, `<span class="info-text">${highlight}</span>`);
      } else {
        str += desc;
      }
      str += '<br/>';
    });
    return str;
  };

  render() {
    const company = get(this.props, 'options.packInfo.takeoutExpressDetail.companyName', '-');
    const companyTel = get(this.props, 'options.packInfo.takeoutExpressDetail.companyTel', '-');
    const contactMessagesList = get(
      this.props,
      'options.packInfo.takeoutExpressDetail.contactMessagesList',
      []
    );
    const contactMessagesText = this.handleContactMessagesList(contactMessagesList);

    const companyDeliverNo = get(
      this.props,
      'options.packInfo.takeoutExpressDetail.outDeliveryNo',
      '-'
    );
    const yzDeliverNo = get(this.props, 'options.packInfo.takeoutExpressDetail.deliveryNo', '-');
    const Opt = typeMap.link_button;
    return (
      <>
        <Opt
          {...this.props}
          text={get(this.props, 'operation.text', '联系')}
          onClick={this.click}
        />
        <Dialog
          mask={false}
          onClose={this.close}
          visible={this.state.visible}
          title={`联系${company}`}
          className={style['call-company-dialog']}
        >
          <div className="line big">
            <span>有赞单号：</span>
            <span className="info-text">{yzDeliverNo}</span>
          </div>
          <div className="line big">
            <span>{company}</span>
            配送单号：
            <span className="info-text">{companyDeliverNo}</span>
          </div>
          {contactMessagesList.length !== 0 ? (
            <div className="line small" dangerouslySetInnerHTML={{ __html: contactMessagesText }} />
          ) : (
            <div className="line small">
              配送单状态咨询、反馈建议可以联系
              <span className="info-text">{companyTel} </span>
              报配送单号了解
            </div>
          )}
        </Dialog>
      </>
    );
  }
}
