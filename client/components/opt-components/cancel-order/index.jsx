import React from 'react';
import { Di<PERSON>, But<PERSON>, Notify, Sweetalert } from 'zent';
import { get } from 'lodash';
import {
  isOuterOrder,
  isCandaoOrder,
  checkIsJdwmOrder,
  checkIsElemeOrder,
  checkIsMeituanPlatformOrder,
  checkIsTiktokSxtOrderByChannelType
} from 'common/biz-helper';
import { OrderManageListTableOperateColumnBtnEnum } from 'definition/order-info';

import * as api from './api';
import CancelOrderContent from './cancel-order-content';
import { CANCEL_ORDER } from '../constant';
import { optTypeMap } from '../type-map';
import { button } from './style.scss';

const dialogId = `${CANCEL_ORDER}-dialog`;

const { openDialog, closeDialog } = Dialog;

class CancelOrder extends React.Component {
  state = {
    loading: false,
    reason: ''
  };

  get isOuterOrder() {
    return isOuterOrder(get(this.props, 'options.orderInfo', {}));
  }

  /**
   * 判断是否为餐道来源的订单
   */
  get isCandao() {
    return isCandaoOrder(this.props.options.orderInfo);
  }

  // 是否为美团平台
  get isMeituanPlatform() {
    const channelType = get(this.props, 'options.orderInfo.mainOrderInfo.channelType');
    return checkIsMeituanPlatformOrder(channelType);
  }

  get isEleme() {
    return checkIsElemeOrder(this.props.options.orderInfo);
  }

  get isTiktokSXT() {
    const channelType = get(this.props, 'options.orderInfo.mainOrderInfo.channelType');
    return checkIsTiktokSxtOrderByChannelType(channelType);
  }

  get isJdwm() {
    const channelType = get(this.props, 'options.orderInfo.mainOrderInfo.channelType');
    return checkIsJdwmOrder(channelType);
  }

  get orderNo() {
    return get(this.props, 'options.orderInfo.mainOrderInfo.orderNo');
  }

  // 取消原因
  handleReasonChange = ({ target: { value } }) => {
    this.setState({ reason: value });
  };

  // 弹窗底部按钮
  renderDialogFooter = () => {
    const { loading } = this.state;

    return (
      <div>
        <Button type="primary" loading={loading} onClick={this.handleCancelOrderSubmit}>
          确定
        </Button>
        <Button onClick={() => closeDialog(dialogId)}>取消</Button>
      </div>
    );
  };

  get cancelOrderParam() {
    if (this.isMeituanPlatform || this.isEleme || this.isCandao || this.isTiktokSXT || this.isJdwm)
      return 'rejectReason';
    if (this.isOuterOrder) return 'refuseReason';

    return 'cancelReason';
  }

  get cancelOrderApi() {
    if (this.isMeituanPlatform || this.isEleme || this.isCandao || this.isTiktokSXT || this.isJdwm)
      return api.rejectOrder;
    if (this.isOuterOrder) return api.outerCancelOrder;
    return api.cancelOrder;
  }

  // 取消订单
  handleCancelOrderSubmit = () => {
    const {
      options: { orderInfo, reload }
    } = this.props;

    const { reason } = this.state;

    if (!reason) {
      Notify.error('请选择一个取消订单理由');
      return;
    }

    this.setState({ loading: true });

    const param = {
      orderNo: get(orderInfo, 'mainOrderInfo.orderNo'),
      [this.cancelOrderParam]: reason
    };

    this.cancelOrderApi(param)
      .then(() => {
        Notify.success('取消订单成功');
        reload && reload();
      })
      .catch(err => {
        Notify.error(err.msg || err || '取消订单失败！');
      })
      .finally(() => {
        this.setState({ loading: false });
        closeDialog(dialogId);
      });
  };

  openCancelDialog = () => {
    openDialog({
      dialogId,
      onClose: () => closeDialog(dialogId),
      title: '取消订单',
      className: 'cancel-order-dialog',
      children: (
        <CancelOrderContent
          value={this.state.reason}
          onChange={this.handleReasonChange}
          isOuterOrder={this.isOuterOrder}
          isCandaoOrder={this.isCandao}
          isMeituan={this.isMeituanPlatform}
          isEleme={this.isEleme}
          isTiktokSXT={this.isTiktokSXT}
          isJdwm={this.isJdwm}
          orderNo={this.orderNo}
        />
      ),
      footer: this.renderDialogFooter()
    });
  };

  // 打开弹窗
  handleOpenDialog = async () => {
    const { beforeOperateClick } = this.props?.options || {};
    let isContinue = true;
    if (beforeOperateClick) {
      isContinue = await beforeOperateClick(OrderManageListTableOperateColumnBtnEnum.CancelOrder);
    }
    if (!isContinue) {
      return;
    }
    // 增加取消订单前的判断，如果是部分发货，增加提示
    // https://xiaolv.qima-inc.com/#/demand/search?show=true&ids=128735
    const itemInfo = get(this.props, 'options.orderInfo.itemInfo', []);
    // hasExpressed 为 1 表示「已发货」
    const expressedItemsNum = itemInfo.filter(item => item.hasExpressed === 1).length;
    // 有已发货的商品且发货商品数量少于总商品数
    const shouldNotifyCancelOrder = expressedItemsNum > 0 && expressedItemsNum !== itemInfo.length;

    if (shouldNotifyCancelOrder) {
      Sweetalert.confirm({
        content: '该订单已部分发货，如果取消，已发货商品库存不会回补，是否确定取消？',
        cancelText: '取消',
        confirmText: '确定',
        onConfirm: this.openCancelDialog
      });
    } else {
      this.openCancelDialog();
    }
  };

  render() {
    const { operation } = this.props;
    const OptTypeCpn = optTypeMap[operation.type];

    return (
      <OptTypeCpn
        {...operation}
        buttonType="primary"
        outline={false}
        className={button}
        onClick={this.handleOpenDialog}
      />
    );
  }
}

export default CancelOrder;
