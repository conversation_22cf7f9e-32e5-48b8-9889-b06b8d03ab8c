import { request, global } from '@youzan/retail-utils';

const { adminId: userId } = global.USER_INFO;

export const cancelOrder = (data: unknown): Promise<unknown> =>
  request({
    url: 'youzan.retail.trademanager.order/1.0.0/cancel',
    method: 'POST',
    data
  });

// 第三方订单的取消订单就是拒单的接口
export const outerCancelOrder = ({
  orderNo,
  resufeReason
}: {
  orderNo: string;
  resufeReason: string;
}): Promise<unknown> =>
  request({
    url: '/youzan.trade/1.0.0/closeorder',
    method: 'POST',
    data: {
      orderNo,
      resufeReason,
      userId
    }
  });

/**
 * 拒绝接单（含 取消订单），2020-08 餐道项目新增
 *
 * @param orderNo
 * @param rejectReason
 */
export const rejectOrder = ({
  orderNo,
  rejectReason
}: {
  orderNo: string;
  rejectReason: number;
}): Promise<unknown> => {
  return request({
    url: 'com.youzan.retail.trademanager.order.reject/1.0.0',
    method: 'POST',
    data: {
      orderNo,
      rejectReason
    }
  });
};

/**
 * 获取订单操作的理由
 *
 * @param method
 * @param orderNo
 * @returns
 */
export const getOperationReason = (method: number, orderNo: string) => {
  return request({
    url: '/v2/order/manager/reason.json',
    data: {
      method,
      orderNo
    }
  });
};
