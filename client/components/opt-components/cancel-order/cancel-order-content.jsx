import React, { useEffect, useState } from 'react';
import { Select } from '@zent/compat';
import { createSelectOptions } from 'common/utils';
import { getOperationReason } from './api';

import './style.scss';

// 取消理由
const DEFAULT_REASON_LIST = createSelectOptions({
  10: '无法联系上买家',
  11: '买家误拍或重拍了',
  12: '买家无诚意完成交易',
  13: '已通过银行线下汇款',
  14: '已通过同城见面交易',
  15: '已通过货到付款交易',
  16: '已通过网上银行直接汇款',
  17: '已经缺货无法交易'
});

// 第三方订单的取消理由
const OUTER_CANCEL_LIST = createSelectOptions({
  外卖不要了: '外卖不要了'
});

const CANDAO_CANCEL_LIST = createSelectOptions({
  101: '超时未接单',
  102: '超出配送范围',
  104: '重复订单',
  105: '联系不上用户',
  107: '门店送错漏送',
  109: '餐品变质',
  201: '用户取消',
  202: '用户测试',
  203: '支付超时',
  204: '用户下错单',
  301: '配送延迟',
  302: '配送异常',
  401: '系统异常',
  402: '调试或测试单',
  403: '平台取消',
  501: '其他原因'
});

const CANDAO_REFUSE_LIST = createSelectOptions({
  103: '售罄',
  106: '餐厅繁忙',
  108: '餐厅休息中'
});

function getSelectedData(isOuterOrder, isCandaoOrder, isRefuseOrder) {
  if (isCandaoOrder) {
    if (isRefuseOrder) return CANDAO_REFUSE_LIST;
    return CANDAO_CANCEL_LIST;
  }
  if (isOuterOrder) return OUTER_CANCEL_LIST;
  return DEFAULT_REASON_LIST;
}

const CancelOrderContent = ({
  value,
  onChange,
  orderNo,
  isOuterOrder,
  isCandaoOrder,
  isRefuseOrder,
  isMeituan,
  isEleme,
  isTiktokSXT,
  isJdwm
}) => {
  const [selectData, setSelectData] = useState([]);
  useEffect(() => {
    if (!isMeituan && !isEleme && !isTiktokSXT && !isJdwm) {
      setSelectData(getSelectedData(isOuterOrder, isCandaoOrder, isRefuseOrder));
    } else {
      const CANCEL_METHOD = 3;

      getOperationReason(CANCEL_METHOD, orderNo).then(({ reasonModelList }) => {
        setSelectData(
          reasonModelList?.map(item => {
            return {
              text: item.reason,
              value: item.code
            };
          })
        );
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMeituan, isEleme, orderNo, isTiktokSXT, isJdwm]);

  return (
    <div>
      <span>取消理由：</span>
      <Select
        className="cancel-reason__select"
        popupClassName="cancel-reason__pop"
        data={selectData}
        value={value}
        onChange={onChange}
      />
    </div>
  );
};

export default CancelOrderContent;
