import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import { omit } from 'lodash';
import { Notify } from 'zent';
import { Select } from '@zent/compat';

import style from './style.scss';

class FetchSelect extends React.Component {
  static propTypes = {
    fetchAjax: PropTypes.func
  };

  state = {
    data: []
  };

  componentDidMount = () => {
    const { fetchAjax } = this.props;
    fetchAjax && this.fetchOptions();
  };

  fetchOptions = () => {
    const { fetchAjax } = this.props;

    fetchAjax()
      .then(data => {
        this.setState({ data });
      })
      .catch(err => {
        err.msg && Notify.error(err.msg);
      });
  };

  render() {
    const selectProps = omit(this.props, 'fetchAjax');
    const { data } = this.state;

    const className = cx(style['fetch-select'], selectProps.className);

    return <Select {...selectProps} data={data} className={className} />;
  }
}

export default FetchSelect;
