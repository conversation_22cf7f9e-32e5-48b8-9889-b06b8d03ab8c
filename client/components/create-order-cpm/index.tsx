import type { IOrderInfo } from 'definition/order-info';

import * as React from 'react';
import { pick, isArray } from 'lodash';

function createOrderCpm<
  T extends Partial<{ orderInfo: Partial<IOrderInfo> } & Partial<IOrderInfo>>
>(fieldsConfig?: string[]) {
  function orderCpm<U>(WrappedComponent: U): U;
  function orderCpm(WrappedComponent: any): any {
    function ctor(props: T) {
      const {
        orderInfo = {
          orderExpressInfo: {},
          mainOrderInfo: {},
          itemInfo: [],
          orderAddressInfo: {},
          buyerInfo: {},
          remarkInfo: {},
          fulfillOrder: {},
          tips: {}
        },
        ...others
      } = props;
      const partOrderInfo: any = isArray(fieldsConfig) ? pick(orderInfo, fieldsConfig) : orderInfo;
      // eslint-disable-next-line react/jsx-props-no-spreading
      return <WrappedComponent {...partOrderInfo} {...others} />;
    }

    return ctor as any;
  }

  return orderCpm;
}

export default createOrderCpm;
