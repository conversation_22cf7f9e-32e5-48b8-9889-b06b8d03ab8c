import * as React from 'react';
import { get, map, uniq, filter } from 'lodash';
import classNames from 'classnames';
import { Pop, Button, BlockLoading, Notify, IPopProps, Popover } from 'zent';
import { LinkButton } from '@youzan/retail-components';
import { safeJsonParse } from 'common/utils';
import { ExpressType, OrderStateType } from 'common/constants/common';
import { fetchBirthdayNet, updateBirthdayRelation } from '../mark-relation-person/api';

import * as style from './style.scss';

interface IContentProps extends Record<'pop', Popover> {
  orderNo: string;
  selectRelation: number[];
  setSelectRelation: (selectRelation: number[]) => void;
}

const Content: React.FC<IContentProps> = ({ orderNo, selectRelation, setSelectRelation, pop }) => {
  const [allRelation, setAllRelation] = React.useState([]);
  const [currentRelation, setCurrentRelation] = React.useState<any>([]);
  const [loading, setLoading] = React.useState(false);

  React.useEffect(() => {
    setLoading(true);
    fetchBirthdayNet({ orderNo })
      .then(res => {
        setAllRelation(res.allOrderRelationList || []);
        setCurrentRelation(res.orderRelationList || []);
      })
      .catch(err => {
        Notify.error(err.msg || '关系人获取失败');
        pop.close();
      })
      .finally(() => [setLoading(false)]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderNo]);

  return (
    <BlockLoading loading={loading} icon="circle">
      <div className={style.btnGroup}>
        {map(allRelation, (value, key) => {
          return (
            <Button
              key={value}
              size="small"
              onClick={() => {
                if (currentRelation.includes(value) === false) {
                  if (selectRelation.includes(value)) {
                    setSelectRelation(
                      uniq(
                        filter(selectRelation, selectValue => {
                          return selectValue !== value;
                        })
                      )
                    );
                  } else {
                    setSelectRelation(uniq([...selectRelation, value]));
                  }
                } else {
                  Notify.error('暂不支持删除已标记关系人');
                }
              }}
              className={classNames({
                [`${style.activeBtn}`]:
                  selectRelation.includes(value) || currentRelation.includes(value)
              })}
            >
              {key}
            </Button>
          );
        })}
      </div>
    </BlockLoading>
  );
};

interface IMarkRelationPersonProps {
  extraInfo: string | undefined;
  orderNo: string;
  position?: IPopProps['position'];
  newState?: number;
  expressType?: number;
}

const MarkRelationPerson: React.FC<IMarkRelationPersonProps> = ({
  extraInfo,
  orderNo,
  position = 'bottom-right',
  newState,
  expressType
}) => {
  const [selectRelation, setSelectRelation] = React.useState<number[]>([]);
  const birthdayRelationMark = get(safeJsonParse(extraInfo), 'BIRTHDAY_RELATION_NET_ORDER_MARK');

  // 部分订单状态不展示标记关系人
  if (birthdayRelationMark !== 'true') return null;
  if (expressType === ExpressType.Express && newState !== OrderStateType.Success) return null;
  if (newState === OrderStateType.Cancel || newState === OrderStateType.WaitPay) return null;

  return (
    <Pop
      trigger="click"
      position={position}
      content={
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        <Content
          orderNo={orderNo}
          selectRelation={selectRelation}
          setSelectRelation={setSelectRelation}
        />
      }
      onConfirm={() => {
        return updateBirthdayRelation({ orderNo, relationList: selectRelation })
          .then(() => {
            Notify.success('标记关系人成功');
          })
          .catch(err => {
            Notify.error(err.msg || '标记关系人失败');
          });
      }}
      onClose={() => {
        setSelectRelation([]);
      }}
    >
      <LinkButton>标记关系人</LinkButton>
    </Pop>
  );
};

export default MarkRelationPerson;
