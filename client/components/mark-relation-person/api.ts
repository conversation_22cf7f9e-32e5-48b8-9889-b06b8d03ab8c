import { request } from '@youzan/retail-utils';

export const fetchBirthdayNet = (data: { orderNo: string }): Promise<any> =>
  request({
    url: '/youzan.scrm.birthday.net.query/1.0.0',
    data
  });

export const updateBirthdayRelation = (data: {
  orderNo: string;
  relationList: number[];
}): Promise<any> => {
  return request({
    url: '/youzan.scrm.order.relation.update/1.0.0',
    data,
    method: 'post'
  });
};
