:local(.activeBtn) {
  &.zent-btn[data-zv] {
    color: var(--theme-primary-5, #3773da);
    border-color: var(--theme-primary-5, #3773da);
    text-decoration: none;
  }
}

:local(.btnGroup) {
  width: 210px;
  min-height: 50px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start; /* 水平对齐方式，左对齐 */
  align-items: flex-start; /* 垂直对齐方式，上对齐 */

  .zent-btn[data-zv] {
    margin-left: 0;
    margin-right: 8px;
    margin-bottom: 8px;
    &:nth-child(4n) {
      margin-right: 0;
    }
    &:nth-last-child(-n + 4) {
      margin-bottom: 0; /* 最后一行的元素底部间距为0 */
    }
  }
}
