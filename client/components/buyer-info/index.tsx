import * as React from 'react';
import { Tag } from 'zent';
import { BlankLink } from '@youzan/react-components';
import { isGYYOrder, isOuterOrder } from 'common/biz-helper';
import { customerDetailURL } from 'common/url';
import { isFenXiao } from 'common/helper';
import { IBuyerInfo, IOrderAddressInfo, IOrderInfo } from 'definition/order-info';
import { orderStateValue } from 'common/constants/models';
import { styleUtils, UserPrivacyViewButton } from 'components/user-privacy';
import MarkRelationPerson from 'components/mark-relation-person';

interface IBuyerInfoProps extends Partial<IOrderInfo> {
  withBuyer?: boolean;
  isModifyLogistics?: boolean;
  orderState?: number;
  onDecrypt?: () => Promise<any>;
  extraInfo?: string;
  orderNo: string;
  expressType?: number;
  newState?: number;
}

function BuyerInfo(props: IBuyerInfoProps) {
  const {
    buyerInfo = {} as IBuyerInfo,
    orderAddressInfo = {} as IOrderAddressInfo,
    withBuyer = true,
    isModifyLogistics = false,
    orderState,
    onDecrypt,
    extraInfo,
    orderNo,
    expressType,
    newState
  } = props;
  const { buyerId, name, nickName, buyerPhone } = buyerInfo;

  let nickNamePure = nickName;
  //  处理后的微信昵称
  if (nickNamePure && nickNamePure.length > 0) {
    // 检查是否以 ")" 结尾
    if (nickNamePure.endsWith(')')) {
      // 检查是否包含 "(" 并且以 ")" 结尾
      const startIndex = nickNamePure.indexOf('(');
      if (startIndex !== -1) {
        // 移除括号及其内容
        nickNamePure =
          nickNamePure.slice(0, startIndex) + nickNamePure.slice(nickNamePure.indexOf(')') + 1);
      } else {
        // 如果没有 "("，只移除末尾的 ")"
        nickNamePure = nickNamePure.slice(0, -1);
      }
    }
  }

  let displayName = '';
  if (name && nickNamePure && !(name === '匿名用户' || nickName === '匿名用户')) {
    displayName = `${name}(${nickNamePure})`;
  } else {
    displayName = name || nickNamePure || buyerPhone || '匿名用户';
  }

  const { receiverName = '', receiverTel = '' } = orderAddressInfo;

  const showBuyerLink =
    !isFenXiao(props) &&
    !isOuterOrder(props) &&
    buyerId &&
    displayName &&
    !isGYYOrder(props) &&
    (+buyerId < 567970816 || +buyerId > 567971839); // buyerId 处在这个[567970816,567971839]范围的, 是匿名用户, 有问题找后端 @陈可

  const buyerName = showBuyerLink ? (
    <BlankLink href={`${customerDetailURL}${buyerId}`}>{displayName}</BlankLink>
  ) : (
    <span>{displayName}</span>
  );

  const privacyButton = onDecrypt ? (
    <UserPrivacyViewButton className={styleUtils.privacyButtonGap} onDecrypt={onDecrypt} />
  ) : null;

  return (
    <div>
      {withBuyer && (
        <p>
          {buyerName}
          {privacyButton}
        </p>
      )}
      <p>
        {`${receiverName} ${receiverTel}`}
        {!withBuyer ? privacyButton : null}
      </p>
      <MarkRelationPerson
        extraInfo={extraInfo}
        orderNo={orderNo}
        expressType={expressType}
        newState={newState}
      />
      {isModifyLogistics &&
        [orderStateValue.TO_PAY, orderStateValue.TO_SEND].includes(orderState as number) && (
          <Tag theme="yellow" outline>
            信息变更
          </Tag>
        )}
    </div>
  );
}

export default BuyerInfo;
