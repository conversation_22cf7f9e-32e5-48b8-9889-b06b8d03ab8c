import React, { useMemo, useCallback } from 'react';
import { get, find } from 'lodash';
// import { Button } from 'zent';
import { Button as SamButton, samify } from '@youzan/sam-components';
import { Actions } from '@youzan/retail-form';
import { LinkButton } from '@youzan/retail-components';
import ExportDialog from 'components/export-dialog';

const SamLinkButton = samify(LinkButton);

const useFilterActions = (
  {
    onSearch,
    loading,
    onExport,
    exportLink,
    allValues,
    defaultValue,
    initialize,
    onSubmit,
    onReset,
    onBeforeReset,
    doFilterOnClear,
    renderCustomExportDialog
  },
  exportDisplayConfig,
  { cashierList, storeList, salerList, tableIdList }
) => {
  /**
   * 重置表单
   * @memberof OrderFilter
   */
  const handleClear = useCallback(async () => {
    let isContinue = true;
    if (onBeforeReset) {
      isContinue = await onBeforeReset();
    }
    if (!isContinue) {
      return;
    }
    initialize(defaultValue);
    onReset && onReset();
    doFilterOnClear && setTimeout(onSubmit);
  }, [defaultValue, doFilterOnClear, initialize, onReset, onSubmit, onBeforeReset]);

  const getExportDisplayInfo = useMemo(
    () =>
      onExport &&
      (() => {
        const { cashierId, storeId, salesId, tableId } = allValues;
        return onExport({
          cashierName: cashierId
            ? get(find(cashierList, { value: cashierId }), 'text', '全部')
            : '',
          storeName: storeId ? get(find(storeList, { value: storeId }), 'text', '全部') : '',
          salesName: salesId ? get(find(salerList || [], { adminId: salesId }), 'staffName') : '',
          tableIdName: tableId
            ? get(find(tableIdList || [], { value: tableId }), 'text', '全部')
            : ''
        });
      }),
    [allValues, cashierList, onExport, salerList, storeList, tableIdList]
  );

  const actions = useMemo(
    () => (
      <div className="filter-actions">
        <Actions>
          {onSearch && <SamButton type="primary" htmlType="submit" loading={loading} name="筛选" />}
          {getExportDisplayInfo &&
            (renderCustomExportDialog ? (
              renderCustomExportDialog({
                getFilterDisplayInfo: getExportDisplayInfo
              })
            ) : (
              <ExportDialog
                getDisplayInfo={getExportDisplayInfo}
                showConfig={exportDisplayConfig}
              />
            ))}
          {exportLink && (
            <SamLinkButton
              href={exportLink}
              target="_blank"
              className="filter-actions__link"
              name="导出"
            >
              查看已导出列表
            </SamLinkButton>
          )}
          <LinkButton
            className="clear filter-actions__link"
            onClick={handleClear}
            disabled={loading}
          >
            重置筛选条件
          </LinkButton>
        </Actions>
      </div>
    ),
    [
      exportDisplayConfig,
      exportLink,
      getExportDisplayInfo,
      handleClear,
      loading,
      onSearch,
      renderCustomExportDialog
    ]
  );

  return [actions];
};

export default useFilterActions;
