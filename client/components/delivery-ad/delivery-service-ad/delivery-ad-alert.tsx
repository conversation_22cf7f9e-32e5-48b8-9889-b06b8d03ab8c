import { ReactNode, useEffect, useState } from 'react';
import styled from 'styled-components';
import React from 'react';
import DelivertAdTask from './delivery-service-ad-task';
import { trackTradeSettingAdOpen, trackTradeSettingAdView } from '../track';

// eslint-disable-next-line @youzan/domain/forbid-hardcode-domain-name
const OpenLink = `https://store.youzan.com/fulfillment/delivery#/local-delivery/${_global.kdtId}/service`;
const PromptContent =
  '闪送、达达、蜂鸟等多家配送公司提供服务，配送进展即时查看，更有不定期折扣活动。';
const AlertWrapper = styled.div`
  margin-top: 6px;
  line-height: 18px;
  margin-bottom: 20px;
  span {
    font-size: 12px;
    font-weight: 400;
    color: #999;
  }
  a {
    font-size: 12px;
    font-weight: 400;
    color: #155bd4;
  }
`;
function DeliveryAdAlert() {
  const openHandler = () => {
    trackTradeSettingAdOpen();
    window.open(OpenLink, '_blank');
  };
  return (
    <AlertWrapper>
      <span>{PromptContent}</span>
      <a onClick={openHandler} target="_blank" rel="noreferrer">
        一键开通
      </a>
    </AlertWrapper>
  );
}
function DeliveryServiceAd({ namespace }: { namespace: string }): JSX.Element | ReactNode {
  const [visible, setVisible] = useState(false);
  const [init, setInit] = useState(false);

  useEffect(() => {
    const delivertAdTask = new DelivertAdTask(namespace, 3);
    delivertAdTask.runTask().then(visible => {
      setVisible(visible);
    });
  }, [namespace]);

  useEffect(() => {
    // 显示的情况下只统计一次
    if (visible && !init) {
      setInit(true);
      trackTradeSettingAdView?.();
    }
  }, [init, visible]);

  return visible && <DeliveryAdAlert />;
}

export default DeliveryServiceAd;
