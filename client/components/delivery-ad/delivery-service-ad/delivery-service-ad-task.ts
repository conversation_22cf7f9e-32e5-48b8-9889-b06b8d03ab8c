import { formatDatetime } from '@youzan/retail-utils';
import expirePlugin from 'store/plugins/expire';
import { checkDeliveryChannelIsHandle, queryChannelsList } from './api';

import DelivertAdStore, { IDelivertAdVal } from './delivery-service-ad-store';

function composeTask(tasks: Array<() => Promise<boolean>>): Promise<boolean> {
  return new Promise(resolve => {
    let index = 0;
    function loop() {
      const task = tasks[index];
      // 链式执行函数，遇到返回的结果为 false，立即返回。
      // 否则继续执行。
      task().then(bol => {
        if (bol && index === tasks.length - 1) {
          return resolve(true);
        }
        if (bol) {
          index++;
          return loop();
        }

        resolve(false);
      });
    }
    loop();
  });
}

class DelivertAdTask extends DelivertAdStore {
  defaultVal = { showCount: 0, closed: false };

  constructor(namespace: string, maxShowCount = 3) {
    super(namespace, maxShowCount);
    this.addPlugin(expirePlugin);
  }

  /** {kdtId}-{userId 生成店铺角色id标识码 * */
  getParentUuid = () => {
    const { kdtId } = _global;
    const userId = _global.business.userInfo.id;
    return `${kdtId}-${userId}`;
  };

  /** {kdtId}-{userId}_{toDay} 生成的唯一码 * */
  getUuid = () => {
    const toDay = formatDatetime(new Date(), 'YYYY-MM-DD');
    const uuid = `${this.getParentUuid()}_${toDay}`;
    return uuid;
  };

  /** 检测是否永久关闭 * */
  runTaskWithClose = (closed: boolean | undefined): boolean => {
    // 这里要做取反操作，最终的结果 true 为展示广告位
    return !closed;
  };

  /** 检测当天是否超过 maxShowCount 次 * */
  runTaskWithShowCount = (count: number): boolean => {
    const bol = count <= this.maxShowCount;
    return bol;
  };

  /**  检测是否是未开通任何三方配送渠道的商家，未开通任何为 true * */
  runTaskWithDeliveryChannel = (): Promise<boolean> => {
    return new Promise(resolve => {
      queryChannelsList(0, window._global.kdtId)
        .then((channels: any) => {
          resolve(!checkDeliveryChannelIsHandle(channels));
        })
        .catch(() => {
          resolve(false);
        });
    });
  };

  clearHistory = () => {
    const parentUuid = this.getParentUuid();
    // 可以把当天之前的数据全部清除
    this.store.each((_, key) => {
      // key: 79823351-692302050_2023-02-10
      const [storeParentUuid, date] = key.split('_');
      const toDay = formatDatetime(new Date(), 'YYYY-MM-DD');
      // 当前店铺的账号，今天之前的时间清除
      // eslint-disable-next-line @youzan/yz-retail/no-new-date
      if (parentUuid === storeParentUuid && new Date(date) < new Date(toDay)) {
        this.store.remove(key);
      }
    });
  };

  checkIsClosed = () => {
    const parentUuid = this.getParentUuid();
    const parentVal = this.get(parentUuid);
    return parentVal?.closed;
  };

  /** 检测是否符合展示条件 * */
  runTask = (key?: string): Promise<boolean> => {
    this.clearHistory();
    // 首先检查是否永久关闭，永久关闭不用继续往下走
    if (this.checkIsClosed()) return Promise.resolve(false);

    const parentUuid = this.getParentUuid();
    const uuid = key || this.getUuid();
    return new Promise(resolve => {
      let parentVal = this.get(parentUuid);
      let val = this.get(uuid);

      if (!parentVal) {
        parentVal = this.initParentVal();
      }

      // 当天第一次加载
      if (!val) {
        val = this.initVal(uuid);
      }

      const tasks = [
        () => Promise.resolve(this.runTaskWithClose(parentVal.closed)),
        () => Promise.resolve(this.runTaskWithShowCount(val.showCount + 1)),
        () => Promise.resolve(this.runTaskWithDeliveryChannel())
      ];
      composeTask(tasks).then(isShow => {
        resolve(isShow);
        // 展示了一次，showCount 次数保持 +1
        if (isShow) {
          this.updateShowCount(parentUuid, {
            showCount: parentVal.showCount + 1,
            closed: parentVal.closed
          });
          this.updateShowCount(uuid, { showCount: val.showCount + 1, closed: val.closed });
        }
      });
    });
  };

  initParentVal = () => {
    const parentUuid = this.getParentUuid();
    this.set(parentUuid, this.defaultVal);
    return this.defaultVal;
  };

  initVal = (key?: string) => {
    const uuid = key || this.getUuid();
    this.set(uuid, this.defaultVal);
    return this.defaultVal;
  };

  updateShowCount = (key: string, val: IDelivertAdVal) => {
    this.set(key, val);
  };

  // 主动关闭
  updateColsed = (key?: string) => {
    const parentUuid = this.getParentUuid();
    const uuid = key || this.getUuid();
    const parentVal = this.get(parentUuid);
    const val = this.get(uuid);

    this.set(parentUuid, { ...parentVal, closed: true });

    this.set(uuid, {
      ...val,
      closed: true
    });
  };
}

export default DelivertAdTask;
