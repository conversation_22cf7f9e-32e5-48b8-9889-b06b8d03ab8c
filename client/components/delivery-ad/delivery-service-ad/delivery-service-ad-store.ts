/* eslint-disable no-underscore-dangle */
import BaseStore from '../store';

export interface IDelivertAdVal {
  showCount?: number; // 每天同一个商家的账号最多展示的次数
  closed?: boolean; // 是否永久关闭，跟时间无关了。true 在任何情况下均关闭
}

class DelivertAdStore extends BaseStore<IDelivertAdVal> {
  maxShowCount: number;

  constructor(namespace: string, maxShowCount = 3) {
    super(namespace);
    this.maxShowCount = maxShowCount;
  }

  set(key: string, val: IDelivertAdVal) {
    this.store.set(key, val);
  }

  get(key: string) {
    const val = this.store.get(key);

    return val;
  }

  getShowCountPlus(count: number) {
    return count + 1;
  }

  updateKeyAndVal(key: string, val: IDelivertAdVal) {
    this.store.set(key, val);
  }
}

export default DelivertAdStore;
