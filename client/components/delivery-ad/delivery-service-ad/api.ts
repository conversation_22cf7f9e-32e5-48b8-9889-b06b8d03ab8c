import { request } from '@youzan/retail-utils';

interface IOuterQueryProviderList {
  localPartners: IDeliveryChannel[];
  kdtId: number;
}

export enum DeliveryChannelCode {
  /** 达达 */
  Dada = 1,
  /** 蜂鸟 */
  FengNiao = 2,
  /** 点我达 */
  DianWoDa = 3,
  /** 顺丰同城 */
  ShunFeng = 4,
  /** 美团 */
  MeiTuan = 5,
  /** 达达自结算 */
  DadaSettlement = 11,
  /** 顺丰自结算 */
  ShunFengSettlement = 12,
  /** 蜂鸟自结算 */
  FengNiaoSettlement = 13,
  /** 餐道聚合配送 */
  CanDao = 14,
  /** 美团自结算 */
  MeiTuanSettlement = 15,
  /** 智选服务商 */
  IntelligentDeliveryChannel = 100
}
export interface ISpecialBusinessType {
  specialBusiness: BusinessType;
  goodsCategoryIdList: number[];
}
export interface IDeliveryChannelInfo {
  business: BusinessType;
  specialBusinessType: ISpecialBusinessType;
}
export interface IDeliveryChannel {
  auditPeriod: string;
  code?: number;
  deliveryChannel: DeliveryChannelCode;
  deliveryChannelName: string;
  enable: number;
  expired: boolean;
  isCitySupport: boolean;
  kdtId: number;
  partnerId?: string;
  payment: DeliveryChannelPayment;
  reason: string;
  shopId: string;
  sourceId: number;
  status: DeliveryChannelAuditStatus;
  storeId: number;
  operationView?: IOperationObject;
  statusView?: IOperationObject;
  isCloudTag: boolean;
  appId?: string;
  sortId: number;
}

export interface IDeliveryChannelDTO {
  channel: IDeliveryChannel['deliveryChannel'];
  appId?: IDeliveryChannel['appId'];
}

/**
 * suspendedTips：悬浮提示 一般只有顺丰有这个值
 */
interface IOperationObject {
  operations: Array<{ type: OperationType }>;
  suspendedTips: Array<{ [key: string]: string }>;
  tips: Array<{ content: string }>;
}
export enum OperationType {
  /** 去授权 */
  GotoAuthorize = 'GOTO_AUTHORIZE',
  /** 刷新授权结果 */
  RefreshAuthorizationResult = 'REFRESH_AUTHORIZATION_RESULT',
  /** 解除授权 */
  ReleaseAuthorization = 'RELEASE_AUTHORIZATION',
  /** 绑定门店 */
  BindShop = 'BIND_SHOP',
  /** 更换门店 */
  ChangeShop = 'CHANGE_SHOP',
  /** 开启服务 */
  OpenService = 'OPEN_SERVICE',
  /** 关闭服务 */
  CloseService = 'CLOSE_SERVICE',
  /**
   * 关闭专人直送服务
   */
  CloseDirectDeliveryService = 'CLOSE_DIRECT_DELIVERY_SERVICE',
  /**
   * 开启专人直送服务
   */
  OpenDirectDeliveryServicce = 'OPEN_DIRECT_DELIVERY_SERVICE',
  /**
   * 申请开通
   */
  ApplyPartner = 'APPLY_PARTNER',
  /**
   * 前往续订
   */
  RenewSubscription = 'RENEW_SUBSCRIPTION',
  /**
   * 去订购
   */
  Subscribe = 'SUBSCRIBE'
}

/** 行业类型 */
export enum BusinessType {
  None = 0, // 未选择
  FoodAndSnacks = 1, //
  FoodAndBeverage = 2, //
  Flower = 3, // 鲜花
  PrintingTicket = 8, // 文印票务
  SupermarketConvenienceStore = 9, // 商超便利店
  FreshFruit = 13, //
  Medicine = 20,
  Cake = 21,
  Wine = 24,
  Clothing = 26,
  AutoRepair = 27,
  Digital = 28,
  Other = 5
}

/** 服务商开通状态 */

/** 同城配送渠道 */

export interface IDeliveryStrategy {
  /** 是否开启 */
  deliveryStrategySwitch: boolean;
  /** 选择模式 */
  deliveryStrategy: number;
}

export enum LocalDeliveryTemplate {
  Map = 0,
  Text = 1
}

// 结算模式
export enum DeliveryChannelPayment {
  // 统一结算
  Unified = 0,
  // 自结算
  Self = 1
}

/** 服务商开通状态 */
export enum DeliveryChannelAuditStatus {
  /** 未申请过 */
  NoAudit,
  /** 待审核 */
  Pending,
  /** 开通成功 */
  Success,
  /** 开通失败 */
  Fail,
  // 其他状态码表示 待审核
  /** 没有订购 */
  NoOrder = 5
}

export enum DeliveryChannelEnable {
  Off,
  On
}

export interface IMeiTuanSettlementAuthorize {
  /** 开发者账号 */
  developerId: number;
  /** 开发者 appKey */
  appKey: string;
  /** 开发者 secret */
  secret: string;
  /** 美团绑定门店 Id */
  shopId: string;
  /** 服务产品编码 */
  deliveryServiceCode: string;
  /** 结算模式 */
  payTypeCode?: MeiTuanPaymentType;
}

// 美团结算模式
export enum MeiTuanPaymentType {
  Account, // 账期结算
  Realtime // 实时结算
}

export type IInnerDeliveryChannelShowResp = IDeliveryChannel & GridMergeShow;
type GridMergeShow = { showRow?: 'show' | 'hide' };

interface IOuterQueryProviderList {
  localPartners: IDeliveryChannel[];
  kdtId: number;
}

export const transformDeliveryChannel = (list: IDeliveryChannel[]) => {
  const needList: IInnerDeliveryChannelShowResp[] = JSON.parse(JSON.stringify(list));
  list
    .filter(item => item.sortId !== undefined)
    .forEach((value, index) => {
      if (index < needList.length - 1 && value.sortId === list[index + 1].sortId) {
        needList[index].showRow = 'show';
        needList[index + 1].showRow = 'hide';
      }
    });
  return needList;
};

const queryProviderListAdaptor = (list: IDeliveryChannel[]): IInnerDeliveryChannelShowResp[] => {
  return transformDeliveryChannel(list);
};

// 检测开通服务商列表是否被操作（列表的每一项状态为未开通且操作为申请开通）
export const checkDeliveryChannelIsHandle = (channels: IDeliveryChannel[]) => {
  let flag = false;
  try {
    channels.forEach(item => {
      if (
        item.status !== DeliveryChannelAuditStatus.NoAudit &&
        item.status !== DeliveryChannelAuditStatus.NoOrder
      ) {
        flag = true;
      }
    });
  } catch (error) {
    flag = false;
  }
  return flag;
};

/**
 * 查询服务商信息
 *
 * @param {*} warehouseType
 * @param {*} warehouseId
 */
export const queryChannelsList = (warehouseType: number, warehouseId: number) => {
  return request({
    url: '/youzan.retail.mananger.localdelivery.channel/1.0.0/query',
    data: {
      warehouseId,
      type: warehouseType
    }
  }).then((res: IOuterQueryProviderList) => {
    return queryProviderListAdaptor(res.localPartners);
  });
};
