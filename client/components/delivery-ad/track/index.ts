/* eslint-disable @typescript-eslint/ban-ts-comment */
import { track } from '@youzan/retail-utils';

//  设置-订单设置 同城配送广告预览
export function trackTradeSettingAdView() {
  track({
    et: 'view', // 事件类型
    ei: 'view_trade_setting_ad', // 事件标识
    en: '设置-订单设置【广告曝光】', // 事件名称
    pt: 'orderTradeSetting' // 页面类型
  });
}

//  设置-订单设置 同城配送广告点击开通按钮
export function trackTradeSettingAdOpen() {
  track({
    et: 'click', // 事件类型
    ei: 'open_trade_setting_ad', // 事件标识
    en: '设置-订单设置（点击去开通服务）', // 事件名称
    pt: 'orderTradeSetting' // 页面类型
  });
}
