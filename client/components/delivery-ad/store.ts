import store from 'store/src/store-engine';
import localStorage from 'store/storages/localStorage';

class BaseStore<T> {
  store: StoreJsAPI & {
    set: (key: string, val: T, expiration?: number) => void;
    _namespacePrefix?: string;
  };

  constructor(namespace: string) {
    // 使用默认的 localStorage store
    this.store = store.createStore([localStorage], null, namespace);
    // this.init();
  }

  // init() {
  // 这过期插件有问题
  // this.addPlugin(expirePlugin);
  // }

  addPlugin(plugin: FunctionConstructor): void {
    this.store.addPlugin(plugin);
  }
}

export default BaseStore;
