@import '~shared/style';

:local(.gift-express-dialog) {
  width: 800px;

  .goods__img {
    width: 70px;
  }

  .goods-iemi {
    color: $color-link;
  }

  .express-field {
    display: flex;

    &.receiver-info {
      margin-bottom: 30px;
    }

    &__label {
      width: 70px;
      font-weight: bold;
    }

    &__controls {
      flex: 1;
    }
  }

  .extra-info {
    padding-left: 70px;

    &__field {
      display: inline-block;

      .express-no-input {
        display: inline-block;
        vertical-align: middle;
      }
    }
  }

  .margin-bottom10 {
    margin-bottom: 10px;
  }
}
