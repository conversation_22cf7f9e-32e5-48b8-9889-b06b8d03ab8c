import React from 'react';
import ReactDOM from 'react-dom';

import GiftExpress from './gift-express';
import './style.scss';

const onComponentClose = containerOrigin => {
  let container = containerOrigin;
  if (!container) {
    return;
  }
  ReactDOM.unmountComponentAtNode(container);
  container = undefined;
};

const openGiftExpressDialog = (options = {}) => {
  const { callback, orderInfo = {} } = options;
  const modalEle = document.createElement('div');

  ReactDOM.render(
    <GiftExpress
      visible
      onCloseDialog={() => onComponentClose.call(this, modalEle)}
      callback={callback}
      orderInfo={orderInfo}
      title="送礼订单发货"
    />,
    modalEle
  );
};

export default openGiftExpressDialog;
