import { request } from '@youzan/retail-utils';

import mapKeysToSnakeCase from '@youzan/utils/string/mapKeysToSnakeCase';

// 获取物流列表
export function fetchExpressList() {
  return request({
    url: '/youzan.logistics.express/3.0.0/get'
  });
}

// 发货请求
export function sendGoods(data) {
  return request({
    url: `${window._global.url.www}/trade/order/express.json`,
    method: 'post',
    data: mapKeysToSnakeCase(data)
  });
}

// 发货请求
export function newSendGoods(data) {
  const url = '/youzan.retail.trademanager.delivery/1.0.0/send';
  return request({
    url,
    method: 'post',
    data
  });
}
