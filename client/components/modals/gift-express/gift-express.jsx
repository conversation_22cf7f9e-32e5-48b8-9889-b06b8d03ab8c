import React from 'react';
import { Dialog, Button, Grid, Radio, Input, Notify } from 'zent';
import { Select } from '@zent/compat';
import { get, omit } from 'lodash';
import { global } from '@youzan/retail-utils';
import { isRetailSingleStore } from '@youzan/utils-shop';

import { expressTypeNumMap } from 'common/constants';
import * as api from 'components/modals/gift-express/api';
import {
  EXPRESS_FOR_COMPANY,
  NO_EXPRESS
} from '@youzan/order-domain-pc-components/es/delivery-dialog/constants';
import IemiInputPop from 'components/iemi-input-pop';
import style from './style.scss';

const RadioGroup = Radio.Group;
const { USER_INFO } = global;

class GiftExpress extends React.Component {
  state = {
    noExpress: 0,
    expressId: '',
    expressNo: '',
    companys: [],
    idArr: []
  };

  componentDidMount() {
    this.fetchLogisticsCompanys();
  }

  // 获取物流公司数据列表
  fetchLogisticsCompanys = () => {
    api
      .fetchExpressList()
      .then(resp => {
        const { allExpress = [] } = resp;
        this.setState({ companys: allExpress.filter(item => item.display) });
      })
      .catch(err => {
        Notify.error(err.msg || '物流公司数据请求失败！');
      });
  };

  // 默认onchange事件
  defaultOnChange = (e, key) => {
    const value = get(e, 'target.value', e);
    this.setState({ [key]: value });
  };

  // 获取发货的参数
  getAjaxParams = () => {
    const { noExpress, expressId, expressNo } = this.state;
    const {
      orderInfo: { orderNo = '', isNewOrder, items = [], parentOrderNo }
    } = this.props;
    const itemIds = items.map(item => (isNewOrder ? item.itemIdStr : item.itemId));
    const expressData = noExpress ? { noExpress } : omit(this.state, 'companys');
    const params = {
      orderNo,
      itemIds,
      parentOrderNo,
      ...expressData
    };

    /**
     * 送礼订单只会在单店中产生，还有一个情况是单店升级连锁带了原来的送礼订单，因此这些订单的发货应该都要走单店的接口；
     * isNewOrder 应该一直是 true 了，暂时这样做兼容处理单店逻辑不变，再观察。 2019-12-27 by changlong
     */
    if (!isNewOrder) {
      if (!isRetailSingleStore) {
        params.useExpress = !params.noExpress;
        delete params.noExpress;
        params.source = 0;
        return params;
      }

      return params;
    }

    const deliveryItems = items.map(({ itemIdStr: itemId, isSerialItem }) => {
      if (isSerialItem) {
        return {
          itemId,
          num: 1000,
          isSerialItem: true,
          serialNos: this.state.idArr
        };
      }
      return { itemId, num: 1000 };
    });
    const express = { expressId, expressNo };
    return {
      operatorName: USER_INFO.staffName,
      orderNo,
      parentOrderNo,
      deliveryItemsJson: JSON.stringify(deliveryItems),
      deliveryInfoJson: JSON.stringify({
        deliveryType: noExpress ? NO_EXPRESS : EXPRESS_FOR_COMPANY,
        express
      })
    };
  };

  // 发货
  handleSend = () => {
    const ajaxParams = this.getAjaxParams();
    const { callback, onCloseDialog, orderInfo } = this.props;
    const action = orderInfo.isNewOrder ? api.newSendGoods : api.sendGoods;
    action(ajaxParams)
      .then(() => {
        Notify.success('发货成功');
        callback && callback();
        onCloseDialog && onCloseDialog();
      })
      .catch(err => {
        Notify.error(err.msg || '发货失败！');
      });
  };

  // 商品列表Columns
  getGoodsColumns = () => [
    {
      title: '商品图片',
      width: 25,
      bodyRender: data => (
        <img className="goods__img" alt={data.title} src={data.imageUrl || data.imgUrl} />
      )
    },
    {
      title: '商品名称',
      bodyRender: data => (
        <a href={data.goodsUrl || data.goodsSnapUrl} alt={data.title}>
          {data.title}
        </a>
      )
    },
    {
      title: '数量',
      name: 'num',
      width: 30,
      textAlign: 'center',
      bodyRender: ({ num, isSerialItem }) => (
        <div>
          {this.props.orderInfo.isNewOrder ? 1 : num}
          <br />
          {isSerialItem && (
            <IemiInputPop
              onChange={ids => {
                this.setState({ idArr: ids });
              }}
              value={this.state.idArr}
            >
              <span className="goods-iemi">
                管理唯一码(
                {this.state.idArr.length})
              </span>
            </IemiInputPop>
          )}
        </div>
      )
    }
  ];

  // 发货方式为物流发货时,显示快递公司和订单编号
  renderExpressExtraInfo = () => {
    const { companys } = this.state;
    return (
      <div className="extra-info">
        <div className="extra-info__field">
          <label>物流公司：</label>
          <Select
            data={companys}
            optionText="name"
            optionValue="id"
            filter={(item, keyword) => item.name.indexOf(keyword) > -1}
            onChange={e => {
              this.defaultOnChange(e, 'expressId');
            }}
          />
        </div>
        <div className="extra-info__field">
          <label>运单号：</label>
          <Input
            className="express-no-input"
            placeholder="填写运单号"
            onChange={e => {
              this.defaultOnChange(e, 'expressNo');
            }}
          />
        </div>
      </div>
    );
  };

  // 发货方式
  renderExpressWay = () => {
    const { noExpress } = this.state;
    return (
      <div className="express-way">
        <div className="express-field margin-bottom10">
          <label className="express-field__label">发货方式</label>
          <div className="express-field__controls express-type">
            <RadioGroup
              onChange={e => {
                this.defaultOnChange(e, 'noExpress');
              }}
              value={noExpress}
            >
              <Radio value={0}>快递发货</Radio>
              <Radio value={1}>无需物流</Radio>
            </RadioGroup>
          </div>
        </div>
        {+noExpress === 0 && this.renderExpressExtraInfo()}
      </div>
    );
  };

  // 收货人信息
  renderReceiverInfo = () => {
    const { orderInfo = {} } = this.props;
    const { expressType, province, city, county, community, addressDetail, userName, tel } =
      orderInfo;
    return (
      <div className="receiver-info express-field">
        <div className="express-field__label">
          <label>配送信息：</label>
        </div>
        <div className="express-field__controls">
          <p className="margin-bottom10">
            配送方式：
            {expressTypeNumMap[expressType]}
          </p>
          <p className="margin-bottom10">
            收货人：
            {[userName, tel].join(', ')}
          </p>
          <p>
            收货地址：
            {[province, city, county, community, addressDetail].join(' ')}
          </p>
        </div>
      </div>
    );
  };

  render() {
    const { visible, onCloseDialog, title, orderInfo = {} } = this.props;
    const { items = [] } = orderInfo;
    return (
      <Dialog
        className={style['gift-express-dialog']}
        visible={visible}
        maskClosable={false}
        onClose={onCloseDialog}
        title={title}
        footer={<Button onClick={this.handleSend}>发货</Button>}
      >
        <Grid columns={this.getGoodsColumns()} datasets={items} />
        {this.renderReceiverInfo()}
        {this.renderExpressWay()}
      </Dialog>
    );
  }
}

export default GiftExpress;
