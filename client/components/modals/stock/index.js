import React, { Component, PureComponent } from 'react';
import { setUrlDomain, trackClick } from '@youzan/retail-utils';
import { isRetailMinimalistShop } from '@youzan/utils-shop';
import { Alert, Button, Dialog, Notify } from 'zent';
import { passToStock } from './api';
import { footer as footerStyle } from './index.scss';

const RefundModalDialogId = 'REFUND_MODAL_DIALOG';

class RefundModal extends (PureComponent || Component) {
  componentDidMount() {
    this.props.onComponentOpen && this.props.onComponentOpen();
    this.open();
  }

  getTrackParams(type) {
    return {
      et: 'click', // 事件类型
      ei: 'refund_stock_actions', // 事件标识
      en: '退货入库操作', // 事件名称
      pt: 'orderdetail', // 页面类型
      params: {
        type
      }
    };
  }

  getFooterActions = () => {
    const { hasSupply, refundId } = this.props;

    const actions = [
      <Button
        onClick={() => {
          Dialog.closeDialog(RefundModalDialogId);
          trackClick(this.getTrackParams('cancel'));
        }}
      >
        暂不处理
      </Button>
    ];

    // 零售 D 版，或者非零售 D 版 & 铺货模式，可以去编辑退货入库
    if (isRetailMinimalistShop || hasSupply) {
      actions.push(
        <Button
          type="primary"
          onClick={() => {
            Dialog.closeDialog(RefundModalDialogId);
            window.open(
              setUrlDomain(`/v2/warehouse/receive-manage#/refund/check/${refundId}`, 'store')
            );
            trackClick(this.getTrackParams('edit'));
          }}
        >
          编辑退货入库
        </Button>
      );
    }

    actions.push(
      <Button
        type="primary"
        onClick={() => {
          passToStock({
            refundId
          })
            .then(() => {
              Notify.success('入库成功');
              Dialog.closeDialog(RefundModalDialogId);
            })
            .catch(err => Notify.error(err?.msg || '入库失败'))
            .finally(() => {
              trackClick(this.getTrackParams('return'));
            });
        }}
      >
        确认入库
      </Button>
    );

    return actions;
  };

  open = () => {
    const { mask = true } = this.props;

    Dialog.openDialog({
      dialogId: RefundModalDialogId,
      title: '退货商品入库',
      children: (
        <>
          <Alert>点击“确认入库”后，当开启“退货审批入库”时，审批自动通过。</Alert>
          <p style={{ marginTop: '10px' }}>已退货退款成功，确认将退货商品原路退回库存？</p>
        </>
      ),
      footer: <footer className={footerStyle}>{this.getFooterActions()}</footer>,
      onClose: this.props.onComponentClose,
      mask
    });
  };

  render() {
    return <div />;
  }
}

export default RefundModal;
