import React from 'react';
import ReactDOM from 'react-dom';
// eslint-disable-next-line import/no-cycle
import Stock from './stock';
import BatchExpress from './batch-express';

const modalTypes = [
  'refund', // 退款
  'remark', // 商家备注
  'stock', // 商品重新入库
  'batch-express' // 批量发货
];

export const openModal = (type, options = {}) => {
  if (modalTypes.indexOf(type) < 0) {
    return;
  }

  let ModalClass;
  switch (type) {
    case 'stock':
      ModalClass = Stock;
      break;
    case 'batch-express':
      ModalClass = BatchExpress;
      break;
    default:
      return;
  }
  let container = document.createElement('div');
  options.onComponentClose = () => {
    if (!container) {
      return;
    }
    ReactDOM.unmountComponentAtNode(container);
    container = undefined;
  };
  ReactDOM.render(React.createElement(ModalClass, options), container);
};
