import React from 'react';
import { Dialog, Alert } from 'zent';
import AddressForm from 'components/location';
import './style.scss';

const { openDialog, closeDialog } = Dialog;
const id = 'edit_address_dialog';

const DefaultAddress = () => (
  <div>
    <Alert type="warning" className="edit-address-alert">
      你还未填写默认退货地址，为保证消费体验，请先填写默认退货地址再进行发货哦～
    </Alert>
    <AddressForm
      submitText="保存"
      className="default-address-form"
      callback={() => closeDialog(id)}
    />
  </div>
);

const open = () => {
  openDialog({
    dialogId: id,
    title: '修改退货地址',
    className: 'edit-default-address-dialog',
    children: <DefaultAddress />
  });
};

export default open;
