/**
 * 模块内部ajax请求
 */
import { request } from '@youzan/retail-utils';

const urlMap = {
  fetchPrinterListUrl: '/youzan.retail.peripheral.equipment/1.0.0/querypage',
  fetchExpressAddressUrl: '/youzan.logistics.waybill.available.address/1.0.0/get',
  fetchExpressListUrl: '/youzan.logistics.waybill.express/1.0.0/get',
  batchExpressUrl: 'youzan.retail.trademanager/1.0.0/batchsend'
};

/**
 * 获取打印机列表(这里写死参数)
 * @param {Object} params
 */
const defaultParams = {
  peripheralTypeId: 1001, // 表示设备类型是打印机
  pageSize: 20,
  pageNo: 1,
  /**
   * 电子面单目前只支持：
   * 1001001: 365 云打印-M2
   * 1001003: 映美云 CLP-180
   */
  equipmentTypeIds: [1001001, 1001003]
};

export function fetchPrinterList(params) {
  return request({
    url: urlMap.fetchPrinterListUrl,
    data: params || defaultParams
  });
}

/**
 * 获取对应物流公司的收货列表
 * @param {Object} params 请求参数
 */
export function fetchExpressAddress(params) {
  return request({
    url: urlMap.fetchExpressAddressUrl,
    data: params
  });
}

/**
 * 获取物流公司列表
 * @param {Object} params 请求参数
 */
export function fetchExpressList(params) {
  return request({
    url: urlMap.fetchExpressListUrl,
    data: params
  });
}

/**
 * 批量发货
 * @param {Object} params 请求参数
 */
export function batchExpress(params) {
  return request({
    url: urlMap.batchExpressUrl,
    method: 'post',
    data: params
  });
}

// 获取电子面单余额
export const fetchExpressSheetNum = ({ expressId, auditNo }) =>
  request({
    url: '/youzan.logistics.waybill.balance/1.0.0/get',
    data: { expressId, auditNo }
  });
