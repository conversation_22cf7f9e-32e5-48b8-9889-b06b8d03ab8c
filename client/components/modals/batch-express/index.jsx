import React, { Component, PureComponent } from 'react';
import { Dialog } from 'zent';
import BatchExpressModalCnt from './batch-express-modal-cnt';

import style from './style.scss';

const dialogId = 1;
class BatchExpressModal extends (PureComponent || Component) {
  static defaultProps = {
    title: '批量发货',
    onHideBtn: () => null
  };

  componentDidMount() {
    this.open();
  }

  open = () => {
    Dialog.openDialog({
      dialogId,
      maskClosable: false,
      className: style['batch-express-cnt'],
      title: this.props.title,
      children: <BatchExpressModalCnt {...this.props} dialogId={dialogId} />,
      onClose: () => {
        this.props.onComponentClose();
        this.props.callback && this.props.callback();
      }
    });
  };

  render() {
    return <span />;
  }
}

export default BatchExpressModal;
