import React, { Component } from 'react';
import { <PERSON><PERSON>oa<PERSON>, <PERSON><PERSON>, Dialog, Notify, Radio } from 'zent';
import { Form, Select } from '@zent/compat';
import { isArray, values, omit, pick, filter, find, invoke, isNumber, get } from 'lodash';
import { setUrlDomain, global, div } from '@youzan/retail-utils';
import { BlankLink } from '@youzan/react-components';
import {
  ONLY_DZMD,
  SYSTEM_CALL_TYPE_MAP,
  JD_EXPRESS_CODE,
  SF_EXPRESS_CODE
} from '@youzan/order-domain-pc-components/es/delivery-dialog/constants';
import TimeSelect from '@youzan/order-domain-pc-components/es/delivery-dialog/components/time-select';
import '@youzan/order-domain-pc-components/css/index.css';

import * as api from './api.js';
import style from './style.scss';

const { USER_INFO } = global;
const { Option } = Select;
const { FormSelectField, createForm, FormRadioGroupField, Field, unknownProps, getControlGroup } =
  Form;

const TimeSelectField = getControlGroup(props => {
  const passableProps = omit(props, unknownProps);
  return <TimeSelect {...passableProps} onChange={props.onChange} value={props.value} />;
});

@createForm()
class BatchExpressModalCnt extends Component {
  state = {
    loading: false,
    printers: [],
    expressOptList: [],
    addressOptList: [],
    printerOptList: [],
    showTimeSelect: false,
    expressList: [],
    systemSendTypes: [],

    errMessage: ''
  };

  componentDidMount() {
    this.fetch();
  }

  fetch = () => {
    this.setState({ loading: true });
    Promise.all([api.fetchExpressList(), api.fetchPrinterList()])
      .then(res => {
        this.setExpressListData(res[0]);
        this.setPrinterListData(res[1].items);
      })
      .catch(err => {
        Notify.error(err.msg || '数据请求失败');
      })
      .finally(() => this.setState({ loading: false }));
  };

  /**
   * 构造option 数组
   * @param {Array} list 原始数据列表
   * @param {String|Array} valueKeys 需要提取出来值的key
   * @param {String|Array} textKeys option 文案的key
   */
  createOptionsFromList(list = [], vkeys, tKeys) {
    let valueKeys = vkeys;
    let textKeys = tKeys;
    if (!valueKeys || !textKeys) return [];
    if (isArray(valueKeys) && !valueKeys.length) return [];
    if (isArray(textKeys) && !textKeys.length) return [];
    if (typeof valueKeys === 'string') {
      valueKeys = [valueKeys];
    }
    if (typeof textKeys === 'string') {
      textKeys = [textKeys];
    }
    const optionsDataSets = list.map(itm => ({
      value: values(pick(itm, valueKeys)).join(''),
      text: values(pick(itm, textKeys)).join('')
    }));
    return optionsDataSets;
  }

  /**
   * 设置打印机列表数据到state
   * @param {Array} resData 服务端返回打印机原始列表数据
   */
  setPrinterListData(resData) {
    const printerOptList = this.createOptionsFromList(resData, 'id', 'name');
    this.setState({
      printers: resData,
      printerOptList
    });
  }

  /**
   * 设置物流公司列表options到state
   * @param {Array} resData 物流公司列表
   */
  setExpressListData(expressList) {
    const expressOptList = this.createOptionsFromList(expressList, 'expressId', 'expressName');
    this.setState({
      expressList,
      expressOptList
    });
  }

  /**
   * 设置发货地址options
   * @param {Array} resData 发货地址列表
   */
  setAddressListData(resData) {
    const addressOptList = this.createOptionsFromList(resData, 'auditNo', [
      'provinceName',
      'cityName',
      'countyName',
      'address'
    ]);
    this.setState({ addressOptList });
  }

  handleExpressIdChange = expressId => {
    const expressConfig = find(this.state.expressList, { expressId: +expressId }) || {};
    const systemSendTypes = invoke(expressConfig, 'allType.map', value => ({
      value,
      text: SYSTEM_CALL_TYPE_MAP[value]
    }));

    const defaultSendType = get(systemSendTypes, '[0].value');
    const newState = {
      systemSendTypes,
      showTimeSelect: defaultSendType !== ONLY_DZMD,
      errMessage: ''
    };

    this.setState(newState, () => {
      if (isNumber(defaultSendType)) {
        this.props.zentForm.setFieldsValue({ sendType: defaultSendType });
      }
    });
    this.fetchExpressAddress(expressId);
  };

  fetchExpressAddress = expressId => {
    api
      .fetchExpressAddress({ expressId })
      .then(res => {
        this.setAddressListData(res);
      })
      .catch(err => {
        Notify.error(err.msg || '获取发货地址失败');
      })
      .finally(() => this.setState({ loading: false }));
  };

  handleBatchExpress = vals => {
    const { startTime, expressId, printerId, ...others } = vals;
    const { dialogId, checkedOrderNos, onBatchTaskCreateSuccess } = this.props;
    const { printers, expressOptList } = this.state;

    const express = filter(expressOptList, { value: expressId });
    const printerList = filter(printers, { id: +printerId });

    const printer = printerList[0];
    const printerParams = {
      printerKey: printer.equipmentKey,
      printerDeviceNo: printer.equipmentNumber,
      printerChannel: printer.equipmentTypeId
    };

    const params = {
      ...printerParams,
      ...others,
      expressId,
      orderNoArr: checkedOrderNos.join(','),
      operatorName: USER_INFO.staffName,
      expressName: express[0].text
    };

    if (startTime) {
      const pickUpStartTime = div(startTime, 1000);
      params.startTime = pickUpStartTime;
      params.endTime = pickUpStartTime + 3600;
    }

    api
      .batchExpress(params)
      .then(res => {
        if (res.result) {
          onBatchTaskCreateSuccess && onBatchTaskCreateSuccess();
        }
      })
      .catch(err => {
        Notify.error(err.msg || '获取发货地址失败');
      })
      .finally(() => {
        Dialog.closeDialog(dialogId);
      });
  };

  handleSendTypeChange = ({ target: { value } }) => {
    const showTimeSelect = +value !== ONLY_DZMD;
    this.setState({ showTimeSelect });
  };

  getTimeSelect = () => {
    const { showTimeSelect } = this.state;
    const { expressId } = this.props.zentForm.getFormValues();

    if (!showTimeSelect) {
      return null;
    }

    if (+expressId === SF_EXPRESS_CODE) {
      return <Field name="startTime" label="取件时间:" component={TimeSelectField} />;
    }

    return (
      <div className="zent-form__control-group">
        <span className="zent-form__control-label">取件时间:</span>
        <div className="zent-form__controls">
          <Select value={0}>
            <Option value={0}>尽快上门取件</Option>
          </Select>
        </div>
      </div>
    );
  };

  handleAddressChange = (_, auditNo) => {
    const { selectedCount, zentForm } = this.props;
    const { expressId } = zentForm.getFormValues();

    api
      .fetchExpressSheetNum({ expressId, auditNo })
      .then(({ remainNum, isLimit }) => {
        const errMessage =
          isLimit && remainNum < selectedCount ? '面单余量不足，请联系网点充值。' : '';

        this.setState({ errMessage });
      })
      .catch(err => {
        Notify.error(err.msg || '获取面单失败！');
      });
  };

  render() {
    const { selectedCount = 0, handleSubmit } = this.props;
    const {
      loading,
      expressOptList,
      addressOptList,
      printerOptList,
      systemSendTypes = [],
      errMessage
    } = this.state;

    if (loading) {
      return <BlockLoading loading />;
    }

    return (
      <div>
        <p>
          已选择
          {selectedCount} 个订单
        </p>
        <Form
          className={style['batch-express-form']}
          onSubmit={handleSubmit(this.handleBatchExpress)}
          horizontal
        >
          <FormSelectField
            label="物流公司:"
            name="expressId"
            className="express-field"
            width={178}
            autoWidth
            data={expressOptList.filter(({ value }) => +value !== JD_EXPRESS_CODE)}
            onChange={this.handleExpressIdChange}
            required
            validations={{ required: true }}
            validationErrors={{ required: '请选择物流公司' }}
          />
          {systemSendTypes.length > 0 && (
            <FormRadioGroupField
              name="sendType"
              label="发货类型:"
              required
              onChange={this.handleSendTypeChange}
              validationErrors={{ required: '请选择发货类型' }}
            >
              {systemSendTypes.map(({ text, value }) => (
                <Radio value={value} key={value}>
                  {text}
                </Radio>
              ))}
            </FormRadioGroupField>
          )}
          {this.getTimeSelect()}
          <div className="address-wrapper">
            <FormSelectField
              label="发货地址:"
              placeholder="请选择发货地址"
              name="auditNo"
              className="address-field"
              width={400}
              autoWidth
              data={addressOptList}
              required
              validations={{ required: true }}
              onChange={this.handleAddressChange}
              validationErrors={{ required: '请选择发货地址' }}
            />
            <BlankLink href={setUrlDomain('/setting/store/index#location', 'www')}>
              新增发货地址
            </BlankLink>
            {errMessage && <div className="error-msg">{errMessage}</div>}
          </div>
          <div className="printer-wrapper">
            <FormSelectField
              label="打印机:"
              name="printerId"
              placeholder="选择打印机"
              helpDesc={
                <span className="printer-help">
                  请确认打印机里使用的是“100*180mm”规格的电子面单纸
                </span>
              }
              className="printer-field"
              data={printerOptList}
              required
              validations={{ required: true }}
              validationErrors={{ required: '请选择打印机' }}
            />
            <BlankLink href={setUrlDomain('/setting/common/device#/', 'store')}>新增</BlankLink>
          </div>
          <Button
            className="express-btn"
            type="primary"
            htmlType="submit"
            disabled={!!errMessage}
            loading={loading}
          >
            发货
          </Button>
        </Form>
      </div>
    );
  }
}

export default BatchExpressModalCnt;
