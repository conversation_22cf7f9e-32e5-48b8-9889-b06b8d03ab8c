declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.scss';

declare module '@youzan/retail-lazyload';

// eslint-disable-next-line
declare var Raven: any;

// eslint-disable-next-line
declare var _global: {
  waybillVersion: any;
  url: Url;
  env: Env;
  isSuperStore: boolean;
  kdtId: number;
  shopAbilityInfos: ShopAbilityInfo[];
  business: Business;
  shopAbilityInfo: { [key: string]: ShopAbilityInfo };
  supportBrandClassify: boolean;
  enableMicroApp?: boolean;
};

declare interface Url {
  store: string;
  scrm: string;
  wsc: string;
  v4: string;
  www: string;
  imgqn: string;
  cdn_static: string;
  cdn_static_retail: string;
  materials: string;
  youzan: string;
  v2: string;
  wap: string;
  bbs: string;
  fuwu: string;
  help: string;
  im: string;
  login: string;
  account: string;
  fx: string;
  h5: string;
}

interface IInvoiceProvider {
  providerCode: string;
  providerName: string;
}

interface IInvoiceTypeConfigItem {
  name: string;
  code: string;
  homepage: string; // 发票服务首页链接
  supportSpecialTicket: boolean; // 是否支持专屁哦啊哦
  specialTicketTips: string; // 如果不支持专票，提示语
  supportTaxControl: true; // 专票是否支持税控盘票
  taxControlTips: string; // 如果不支持税控盘票，提示语
}
interface InvoiceTypeConfig {
  [key: string]: IInvoiceTypeConfigItem;
}

interface Env {
  runmode: string;
  appName: string;
  xServiceChainName: string;
}

interface ShopAbilityInfo {
  abilityCode: string;
  abilityStatus: number;
  invalidCode: string;
  invalidReason: string;
  kdtId: number;
  valid: boolean;
}

interface Business {
  kdtId: number;
  shopInfo: ShopInfo;
  invoiceTypeConfig?: InvoiceTypeConfig;
  invoiceProviderList?: IInvoiceProvider[];
  shopConfig: ShopConfig;
  shopMetaInfo: ShopMetaInfo;
  userInfo: UserInfo;
  serverTime: string;
  menu: Menu;
  hasCategoryAbility: boolean;
  isQuYeShop: boolean;
  staffInfo: StaffInfo;
  canSetCustomVipPrice: boolean;
  skuTree: SkuTree[];
  isShelfLifeWarningOpen: number;
  isSerialNoOpen: boolean;
  isMaterialOpen: number;
  isDifferencePriceOpen: number;
  validLifeCycleList: ValidLifeCycleList[];
  goodsLifeCycleList: GoodsLifeCycleList[];
  isStoreSync: boolean;
  online_product_display_status: string;
  offline_product_display_status: string;
  stockConfig: {
    allowPlannedStock: boolean;
    standaloneSaleStock: boolean;
    plannedStock: boolean;
  };
  tradeUserPrivacyInfoDisplay: boolean;
  [key: string]: any;
}

interface ShopInfo {
  kdtId: number;
  shopRole: number;
  shopType: number;
  shopName: string;
  lockStatus: number;
  createdTime: string;
  contactCountryCode: string;
  contactMobile: string;
  contactName: string;
  businessName: string;
  logo: string;
  intro: string;
  province: string;
  city: string;
  county: string;
  address: string;
  countyId: number;
  setChainOnlineShopMode: boolean;
  setJoinType: boolean;
  setKdtId: boolean;
  setLockStatus: boolean;
  setOfflineShopOpen: boolean;
  setOnlineShopOpen: boolean;
  setParentKdtId: boolean;
  setRootKdtId: boolean;
  setSaasSolution: boolean;
  setShopName: boolean;
  setShopRole: boolean;
  setShopTopic: boolean;
  setShopType: boolean;
  setSubSolution: boolean;
  shopTopic: number;
  alias: string;
}

interface ShopConfig {
  weixinMenu: number;
  weixinCertificate: number;
  weixinBind: number;
  weixinNo: number;
  weixinCertsub: number;
  isDelete: number;
  weixinOldsub: number;
  teamClose: number;
  weixinPay: number;
  weixinSubscribe: number;
  weixinServer: number;
  isShopTypeModifying: number;
  shopOptLock: number;
}

interface ShopMetaInfo {
  rootKdtId: number;
  kdtId: number;
  shopRole: number;
  shopType: number;
  shopName: string;
  lockStatus: number;
  createdTime: string;
  contactCountryCode: string;
  contactMobile: string;
  contactName: string;
  businessName: string;
  logo: string;
  intro: string;
  province: string;
  city: string;
  county: string;
  address: string;
  countyId: number;
  setChainOnlineShopMode: boolean;
  setJoinType: boolean;
  setKdtId: boolean;
  setLockStatus: boolean;
  setOfflineShopOpen: boolean;
  setOnlineShopOpen: boolean;
  setParentKdtId: boolean;
  setRootKdtId: boolean;
  setSaasSolution: boolean;
  setShopName: boolean;
  setShopRole: boolean;
  setShopTopic: boolean;
  setShopType: boolean;
  setSubSolution: boolean;
  shopTopic: number;
  alias: string;
}

interface UserInfo {
  id: number;
  adminId: number;
  account: string;
  requestIp: string;
  nickName: string;
  avatar: string;
  mobile: string;
  userId: number;
  linkPhone: string;
  status: string;
  staffId: number;
  roles: Role[];
  roleIds: number[];
  roleNames: string[];
  staffName: string;
  isAdmin: boolean;
}

interface Role {
  roleId: number;
  roleTypeEnum: string;
  name: string;
}

interface Menu {
  widgetList: WidgetList;
}

interface WidgetList {
  '601092': N601092[];
}

interface N601092 {
  accessible: boolean;
  bizCode: string;
  category: string;
  description: string;
  extProperties: ExtProperties;
  icon: string;
  menuAccessible: boolean;
  menuItemId: number;
  menuItemKey: string;
  menuItemType: number;
  name: string;
  parentId: number;
  rootId: number;
  script: string;
  sort: number;
  subtitle: string;
  url: string;
  version: string;
  isActive: boolean;
  isAccessible: boolean;
  abilityStatus: number;
  visibleStatus: number;
}

interface ExtProperties {
  shopAbility: string;
}

interface StaffInfo {
  createTime: number;
}

interface SkuTree {
  text: string;
  kdt_id: number;
  _id: number;
}

interface ValidLifeCycleList {
  autoReverse: number;
  autoReverseDays: number;
  autoReversePoint: number;
  autoReverseType: number;
  createdAt: number;
  id: number;
  kdtId: number;
  name: string;
  order: number;
  origin: number;
  relateCode: string;
  status: number;
  type: number;
  updatedAt: number;
  version: number;
}

interface GoodsLifeCycleList {
  autoReverse: number;
  autoReverseDays: number;
  autoReversePoint: number;
  autoReverseType: number;
  createdAt: number;
  id: number;
  kdtId: number;
  name: string;
  order: number;
  origin: number;
  relateCode: string;
  status: number;
  type: number;
  updatedAt: number;
  version: number;
}

declare interface IPageInfoParams {
  pageNo: number;
  pageSize: number;
}

interface IPaginator {
  totalCount: number;
  page: number;
  pageSize: number;
}

declare interface IPageDataWrapper<T> {
  items: T[];
  paginator: IPaginator;
}
